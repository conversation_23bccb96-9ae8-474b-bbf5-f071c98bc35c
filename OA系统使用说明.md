# OA办公自动化系统使用说明

## 系统概述

基于若依框架开发的OA办公自动化系统，支持收文审批流程、公文管理、个人办公等核心功能。

## 功能特性

### 1. 工作流管理
- ✅ 流程定义管理
- ✅ 流程实例监控
- ✅ 任务分配与处理
- ✅ 流程统计分析
- ✅ 并行审批支持

### 2. 公文管理
- ✅ 收文管理（支持并行审批）
- ✅ 发文管理
- ✅ 公文查询与统计
- ✅ 文档模板管理
- ✅ PDF文档生成

### 3. 个人办公
- ✅ 个人信息管理
- ✅ 日程管理
- ✅ 通讯录管理
- ✅ 工作报告

### 4. 系统管理
- ✅ 用户权限管理
- ✅ 组织机构管理
- ✅ 菜单权限控制
- ✅ 数据字典管理

## 技术架构

### 后端技术栈
- Spring Boot 2.5.15
- MyBatis-Plus
- Spring Security + JWT
- Flowable 6.7.2（工作流引擎）
- iText PDF（文档生成）
- MinIO（文件存储）
- Redis（缓存）
- MySQL（数据库）

### 前端技术栈
- Vue 2.6.12
- Element UI 2.15.14
- Vue Router
- Vuex
- Axios

## 安装部署

### 1. 环境要求
- JDK 1.8+
- MySQL 8.0+
- Redis 6.0+
- Node.js 14+
- Maven 3.6+

### 2. 数据库初始化
```sql
-- 执行若依框架的初始化脚本
source ry_20240701.sql;

-- 执行OA系统的初始化脚本
source sql/oa_system_init.sql;
```

### 3. 后端部署
```bash
# 1. 克隆项目
git clone [项目地址]

# 2. 修改数据库配置
# 编辑 ruoyi-admin/src/main/resources/application-druid.yml
# 修改数据库连接信息

# 3. 编译打包
mvn clean package -Dmaven.test.skip=true

# 4. 启动应用
java -jar ruoyi-admin/target/ruoyi-admin.jar
```

### 4. 前端部署
```bash
# 1. 进入前端目录
cd ruoyi-ui

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev

# 4. 构建生产版本
npm run build:prod
```

## 使用指南

### 1. 系统登录
- 默认管理员账号：admin/admin123
- 访问地址：http://localhost:80

### 2. 收文流程操作

#### 2.1 创建收文
1. 进入"公文管理" -> "收文管理"
2. 点击"新增"按钮
3. 填写收文信息：
   - 文档标题
   - 文档编号
   - 来文单位
   - 收文日期
   - 紧急程度
   - 密级
   - 文档内容
4. 保存草稿

#### 2.2 提交审批
1. 在收文列表中找到草稿状态的收文
2. 点击"提交审批"按钮
3. 系统自动启动收文审批流程
4. 相关审批人员会收到待办任务

#### 2.3 审批处理
1. 审批人员登录系统
2. 进入"工作台"查看待办任务
3. 点击"处理"按钮进入审批页面
4. 填写审批意见，选择审批结果
5. 提交审批

#### 2.4 并行审批
- 系统支持多个部门同时审批
- 各审批分支互不影响
- 所有分支完成后流程结束

### 3. 工作流管理

#### 3.1 流程定义
1. 进入"流程管理" -> "流程定义"
2. 可以查看、新增、修改流程定义
3. 支持BPMN 2.0标准的流程设计

#### 3.2 流程监控
1. 进入"流程管理" -> "流程监控"
2. 查看所有流程实例的执行状态
3. 支持流程终止、转办等操作

### 4. 个人办公

#### 4.1 日程管理
1. 进入"个人办公" -> "日程管理"
2. 可以新增、修改、删除个人日程
3. 支持日程提醒功能

#### 4.2 工作报告
1. 进入"个人办公" -> "工作报告"
2. 支持日报、周报、月报
3. 可以查看下属的工作报告

## 系统配置

### 1. 流程配置
```yaml
# application.yml
flowable:
  # 是否自动部署流程定义
  check-process-definitions: false
  # 数据库更新策略
  database-schema-update: true
  # 异步执行器
  async-executor-activate: false
```

### 2. 文件存储配置
```yaml
# MinIO配置
minio:
  endpoint: http://localhost:9000
  accessKey: minioadmin
  secretKey: minioadmin
  bucketName: oa-files
```

### 3. 权限配置
系统基于若依框架的RBAC权限模型，支持：
- 菜单权限控制
- 按钮权限控制
- 数据权限控制
- 接口权限控制

## 常见问题

### 1. 流程引擎初始化失败
**问题**：启动时报Flowable相关错误
**解决**：检查数据库连接配置，确保数据库用户有创建表的权限

### 2. 文件上传失败
**问题**：附件上传时报错
**解决**：检查文件存储路径配置，确保目录存在且有写权限

### 3. 前端页面空白
**问题**：访问OA页面显示空白
**解决**：检查前端路由配置，确保API接口正常

### 4. 权限访问被拒绝
**问题**：操作时提示权限不足
**解决**：检查用户角色权限配置，确保分配了相应的菜单和按钮权限

## 开发指南

### 1. 新增业务模块
1. 在ruoyi-oa模块下创建对应的包结构
2. 创建实体类、Mapper、Service、Controller
3. 编写对应的前端页面和API接口
4. 配置菜单权限

### 2. 自定义工作流
1. 使用BPMN设计器设计流程图
2. 导出BPMN XML文件
3. 在流程定义管理中导入流程
4. 配置流程变量和任务处理人

### 3. 扩展审批功能
1. 继承BaseWorkflowService类
2. 实现自定义的审批逻辑
3. 配置流程监听器
4. 添加自定义表单

## 技术支持

如有问题，请通过以下方式联系：
- 邮箱：<EMAIL>
- QQ群：123456789
- 官网：http://www.example.com

## 更新日志

### v1.0.0 (2024-07-01)
- ✅ 完成基础框架搭建
- ✅ 实现工作流引擎集成
- ✅ 完成收文管理功能
- ✅ 实现并行审批流程
- ✅ 完成个人办公模块
- ✅ 实现PDF文档生成

### 后续版本规划
- 📋 发文管理功能完善
- 📋 移动端支持
- 📋 消息推送功能
- 📋 报表统计功能
- 📋 系统监控功能
