<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cfpamf.ms.framework</groupId>
        <artifactId>cfpamf-framework-parent</artifactId>
        <version>1.0.10-SNAPSHOT</version>
    </parent>
    <groupId>com.cfpamf.inf</groupId>
    <artifactId>erp-services-biz</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>erp-services</name>
    <description>erp project for Spring Boot</description>
    <properties>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.cfpamf.ms.framework</groupId>
            <artifactId>cfpamf-framework-web-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cfpamf.ms.framework</groupId>
            <artifactId>cfpamf-framework-mybatisplus-starter</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.github.yulichang</groupId>-->
<!--            <artifactId>mybatis-plus-join-boot-starter</artifactId>-->
<!--            <version>1.5.2</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.cfpamf.ms.framework</groupId>
            <artifactId>cfpamf-framework-bms-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms.framework</groupId>
            <artifactId>cfpamf-framework-excle-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>easyexcel</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms.framework</groupId>
            <artifactId>cfpamf-framework-redis-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms.framework</groupId>
            <artifactId>cfpamf-framework-oss-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cdfinance.cipherer</groupId>
            <artifactId>cipherer-spring-boot-starter</artifactId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>bms-service-facade</artifactId>
            <version>1.0.1.20240528</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>3.2</version>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms.mallshop</groupId>
            <artifactId>mall-shop-facade</artifactId>
            <version>1.0.97-1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>gexin-rp-sdk-http</artifactId>
                    <groupId>com.gexin.platform</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
               <!--  <exclustion>
                	<groupId>com.cfpamf.smartid</groupId>
					<artifactId>smartid-client</artifactId>
                </exclustion>-->
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.jexcelapi</groupId>
            <artifactId>jxl</artifactId>
            <version>2.6.12</version>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
            <version>1.3.0.Final</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <version>2.2.1.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--seata-->
        <!--        <dependency>-->
        <!--            <groupId>com.alibaba.cloud</groupId>-->
        <!--            <artifactId>spring-cloud-starter-alibaba-seata</artifactId>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>seata-all</artifactId>-->
        <!--                    <groupId>io.seata</groupId>-->
        <!--                </exclusion>-->
        <!--                &lt;!&ndash;排除依赖seata-spring-boot-starter 不然或报错 SeataDataSourceBeanPostProcessor异常&ndash;&gt;-->
        <!--                <exclusion>-->
        <!--                    <groupId>io.seata</groupId>-->
        <!--                    <artifactId>seata-spring-boot-starter</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>io.seata</groupId>-->
        <!--            <artifactId>seata-all</artifactId>-->
        <!--            <version>1.4.1</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>io.seata</groupId>-->
        <!--            <artifactId>seata-spring-boot-starter</artifactId>-->
        <!--            <version>1.4.1</version>-->
        <!--        </dependency>-->
        <!-- knife4j接口文档 start -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>2.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.cfpamf</groupId>
            <artifactId>athena-core</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--HR系统依赖-->
        <!-- <dependency>
             <groupId>com.cdfinance</groupId>
             <artifactId>hrms-facade</artifactId>
             <version>1.0.5-SNAPSHOT</version>
         </dependency>-->
        <dependency>
            <groupId>com.cdfinance</groupId>
            <artifactId>hrms-facade</artifactId>
            <version>1.0.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>ms-common</artifactId>
                    <groupId>com.cfpamf.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>ocr20191230</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>third-gateway-facade</artifactId>
            <version>1.2.5-SNAPSHOT</version>
        </dependency>
        <!--消息模板推送-->
        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>messagepush-service-facade</artifactId>
            <version>1.2.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.cfpamf.common</groupId>
                    <artifactId>ms-common</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ms-common</artifactId>
                    <groupId>com.cfpamf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--canal、kafka集成-->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>2.4.1</version>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>mall-file-center-facade</artifactId>
            <version>20241230-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>

        <dependency>
            <groupId>com.cdfinance.ms</groupId>
            <artifactId>ms-contract-service-facade</artifactId>
            <version>1.0.20240927.01-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-spring-boot-starter</artifactId>
            <version>4.4.0</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
			<groupId>com.cfpamf.smartid</groupId>
			<artifactId>smartid-client</artifactId>
			<version>1.0.2-SNAPSHOT</version>
		</dependency>
        <dependency>
			<groupId>com.cfpamf.inf</groupId>
			<artifactId>rabbit-mq-plus</artifactId>
			<version>0.0.7-SNAPSHOT</version>
		</dependency>
    </dependencies>

    <build>
        <finalName>erp-services-biz-1.0.0-SNAPSHOT</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.3.RELEASE</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <!-- jacoco版本不应该低于0.8.2 -->
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
                <!-- 配置代码覆盖率排除项(这里建议仅去除POJO相关内容),如果要排除指定类,后缀应该是.class -->
                <configuration>
                    <excludes>
                        <exclude>**/po/**</exclude>
                        <exclude>**/vo/**</exclude>
                        <exclude>**/dto/**</exclude>
                    </excludes>
                </configuration>
                <!-- 默认执行 prepare-agent -->
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <!-- 绑定 test 后生成聚合报告 -->
                    <execution>
                        <id>report-aggregate</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.xlsx</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>*.yml</include>
                </includes>
                <excludes>
                    <exclude>**/*.xlsx</exclude>
                </excludes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
    </build>

    <!-- yml配置指向 -->
    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <nacos.config.enable>false</nacos.config.enable>
                <package.environment>local</package.environment>
                <config_uri>http://nacoco.tsg.cfpamf.com</config_uri>
                <namespace>6a137026-68f4-4887-9ac6-4084187e9a98</namespace>
                <password>nacos</password>
                <username>nacos</username>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <nacos.config.enable>true</nacos.config.enable>
                <package.environment>dev</package.environment>
                <config_uri>mse-b01f8ee2-nacos-ans.mse.aliyuncs.com</config_uri>
                <namespace>d53f3903-f759-4b15-b202-3ab1ee7c36e7</namespace>
                <password></password>
                <username></username>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <nacos.config.enable>true</nacos.config.enable>
                <package.environment>test</package.environment>
                <config_uri>mse-b81bd222-nacos-ans.mse.aliyuncs.com</config_uri>
                <namespace>6a137026-68f4-4887-9ac6-4084187e9a98</namespace>
                <password></password>
                <username></username>
            </properties>
        </profile>
        <profile>
            <id>staging</id>
            <properties>
                <nacos.config.enable>true</nacos.config.enable>
                <package.environment>staging</package.environment>
                <config_uri>mse-d67aed82-nacos-ans.mse.aliyuncs.com</config_uri>
                <namespace>04c41245-14cc-4546-b742-3b08b5d1879a</namespace>
                <password></password>
                <username></username>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <nacos.config.enable>true</nacos.config.enable>
                <package.environment>prod</package.environment>
                <config_uri>mse-c04b0392-nacos-ans.mse.aliyuncs.com</config_uri>
                <namespace>e819c0f6-d14f-4899-959b-ea1d25be6579</namespace>
                <password></password>
                <username></username>
            </properties>
        </profile>
    </profiles>
</project>

