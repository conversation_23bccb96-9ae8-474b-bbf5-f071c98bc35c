#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OA系统Word文档生成器安装和运行脚本
自动安装依赖并生成Word文档
"""

import subprocess
import sys
import os

def install_requirements():
    """安装所需的Python包"""
    print("正在安装所需的Python包...")
    
    packages = [
        'python-docx',
        'Pillow'  # 用于处理图片
    ]
    
    for package in packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")
            return False
    
    print("✅ 所有依赖包安装完成")
    return True

def run_generator():
    """运行Word文档生成器"""
    print("\n开始生成Word文档...")
    
    try:
        # 导入并运行生成器
        from generate_word_doc import OADocumentGenerator
        
        generator = OADocumentGenerator()
        output_path = generator.generate_document()
        
        print(f"\n🎉 Word文档生成成功!")
        print(f"📄 文件路径: {os.path.abspath(output_path)}")
        print(f"📊 文件大小: {os.path.getsize(output_path) / 1024:.1f} KB")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保 generate_word_doc.py 文件在同一目录下")
        return False
    except Exception as e:
        print(f"❌ 生成文档失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🏢 OA办公自动化系统 - Word文档生成器")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 需要Python 3.6或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {sys.version}")
    
    # 安装依赖
    if not install_requirements():
        print("❌ 依赖安装失败，程序退出")
        sys.exit(1)
    
    # 生成文档
    if not run_generator():
        print("❌ 文档生成失败，程序退出")
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("🎉 程序执行完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()
