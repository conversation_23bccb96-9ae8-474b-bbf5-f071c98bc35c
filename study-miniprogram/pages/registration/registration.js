const app = getApp()

Page({
  data: {
    classList: [],
    myRegistrations: [],
    loading: false,
    activeTab: 0, // 0: 可报名班次, 1: 我的报名
    userInfo: {},
    debugInfo: '', // 调试信息
    hasLoaded: false // 标记是否已经加载过
  },

  onLoad() {
    this.loadUserInfo()
    // 初始化时只加载第一个标签页的数据
    this.loadAvailableClasses()
    this.setData({ hasLoaded: true })
  },

  onShow() {
    // 只有在已经加载过的情况下才刷新数据
    if (this.data.hasLoaded) {
      if (this.data.activeTab === 0) {
        this.loadAvailableClasses()
      } else {
        this.loadMyRegistrations()
      }
    }
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({ userInfo })
    } else {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/login/login'
        })
      }, 1500)
    }
  },

  // 切换标签页
  switchTab(e) {
    console.log('switchTab被调用', e)
    
    const dataset = e.currentTarget.dataset
    console.log('dataset:', dataset)
    
    const index = parseInt(dataset.index)
    const currentTab = this.data.activeTab
    
    console.log('切换标签页:', currentTab, '->', index, '类型:', typeof index, typeof currentTab)
    
    // 如果点击的是当前标签页，直接返回
    if (currentTab === index) {
      console.log('点击的是当前标签页，不切换')
      return
    }
    
    // 先更新标签页状态
    this.setData({ 
      activeTab: index,
      loading: false,
      debugInfo: `正在切换到标签页${index}`
    })
    
    console.log('标签页已切换到:', index)
    
    // 根据标签页加载对应数据
    if (index === 0) {
      console.log('加载班次列表')
      this.loadAvailableClasses()
    } else if (index === 1) {
      console.log('加载我的报名')
      this.loadMyRegistrations()
    }
  },

  // 加载可报名班次
  async loadAvailableClasses() {
    console.log('开始加载班次列表，当前activeTab:', this.data.activeTab)
    
    this.setData({ 
      loading: true,
      debugInfo: '正在加载班次列表...'
    })
    
    try {
      const res = await app.request({
        url: '/study/class/list',
        data: {
          pageNum: 1,
          pageSize: 50
        }
      })

      console.log('班次列表接口响应:', res)

      if (res && res.rows) {
        const classList = res.rows.map(item => ({
          ...item,
          canRegister: this.canRegister(item),
          statusText: this.getClassStatusText(item)
        }))
        
        this.setData({ 
          classList,
          debugInfo: `班次列表加载成功，共${classList.length}条数据，当前标签页：${this.data.activeTab}`
        })
        console.log('班次列表数据已更新，共', classList.length, '条')
      } else {
        console.warn('班次列表数据格式异常:', res)
        this.setData({ 
          classList: [],
          debugInfo: '班次列表数据格式异常'
        })
      }
    } catch (error) {
      console.error('加载班次列表失败:', error)
      wx.showToast({
        title: '加载班次失败',
        icon: 'none'
      })
      this.setData({ 
        classList: [],
        debugInfo: `加载班次失败: ${error}`
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载我的报名
  async loadMyRegistrations() {
    console.log('开始加载我的报名，当前activeTab:', this.data.activeTab)
    
    const userInfo = this.data.userInfo
    if (!userInfo || !userInfo.userId) {
      wx.showToast({
        title: '用户信息异常',
        icon: 'none'
      })
      this.setData({ debugInfo: '用户信息异常，无法加载报名数据' })
      return
    }

    this.setData({ 
      loading: true,
      debugInfo: '正在加载我的报名...'
    })
    
    try {
      const res = await app.request({
        url: '/study/registration/list',
        data: {
          pageNum: 1,
          pageSize: 50,
          userId: userInfo.userId
        }
      })

      console.log('我的报名接口响应:', res)

      if (res && res.rows) {
        const myRegistrations = res.rows.map(item => ({
          ...item,
          statusText: this.getRegistrationStatusText(item.status),
          statusClass: this.getRegistrationStatusClass(item.status),
          className: item.studyClass?.className || item.className || '未知班次'
        }))
        
        this.setData({ 
          myRegistrations,
          debugInfo: `我的报名加载成功，共${myRegistrations.length}条数据，当前标签页：${this.data.activeTab}`
        })
        console.log('我的报名数据已更新，共', myRegistrations.length, '条')
      } else {
        console.warn('我的报名数据格式异常:', res)
        this.setData({ 
          myRegistrations: [],
          debugInfo: '我的报名数据格式异常'
        })
      }
    } catch (error) {
      console.error('加载我的报名失败:', error)
      wx.showToast({
        title: '加载报名记录失败',
        icon: 'none'
      })
      this.setData({ 
        myRegistrations: [],
        debugInfo: `加载我的报名失败: ${error}`
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 判断是否可以报名
  canRegister(classItem) {
    const now = new Date()
    const enrollStart = new Date(classItem.enrollStartDate)
    const enrollEnd = new Date(classItem.enrollEndDate)
    
    return now >= enrollStart && now <= enrollEnd
  },

  // 获取班次状态文字
  getClassStatusText(classItem) {
    const now = new Date()
    const enrollStart = new Date(classItem.enrollStartDate)
    const enrollEnd = new Date(classItem.enrollEndDate)
    const classStart = new Date(classItem.classStartDate)
    
    if (now < enrollStart) {
      return '报名未开始'
    } else if (now > enrollEnd) {
      return '报名已结束'
    } else if (now > classStart) {
      return '培训中'
    } else {
      return '可报名'
    }
  },

  // 获取报名状态文字
  getRegistrationStatusText(status) {
    const statusMap = {
      '0': '待审核',
      '1': '已通过',
      '2': '已拒绝'
    }
    return statusMap[status] || '未知'
  },

  // 获取报名状态样式类
  getRegistrationStatusClass(status) {
    const classMap = {
      '0': 'status-pending',
      '1': 'status-approved',
      '2': 'status-rejected'
    }
    return classMap[status] || ''
  },

  // 报名
  async handleRegister(e) {
    const { classItem } = e.currentTarget.dataset
    
    if (!this.canRegister(classItem)) {
      wx.showToast({
        title: '不在报名时间内',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '确认报名',
      content: `确定要报名参加"${classItem.className}"吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '报名中...' })
            
            const userInfo = wx.getStorageSync('userInfo')
            await app.request({
              url: '/study/registration/add',
              method: 'POST',
              data: {
                classId: classItem.classId,
                userId: userInfo.userId,
                status: '0' // 待审核
              }
            })

            wx.showToast({
              title: '报名成功',
              icon: 'success'
            })

            // 刷新数据
            this.loadAvailableClasses()
            this.loadMyRegistrations()

          } catch (error) {
            wx.showToast({
              title: error || '报名失败',
              icon: 'none'
            })
          } finally {
            wx.hideLoading()
          }
        }
      }
    })
  },

  // 查看班次详情
  viewClassDetail(e) {
    const { classItem } = e.currentTarget.dataset
    
    wx.showModal({
      title: classItem.className,
      content: `报名时间：${classItem.enrollStartDate} ~ ${classItem.enrollEndDate}\n培训时间：${classItem.classStartDate} ~ ${classItem.classEndDate}\n${classItem.description || ''}`,
      showCancel: false
    })
  },

  // 查看报名详情
  viewRegistrationDetail(e) {
    const { registration } = e.currentTarget.dataset
    
    if (!registration) {
      wx.showToast({
        title: '数据异常',
        icon: 'none'
      })
      return
    }
    
    const className = registration.className || registration.studyClass?.className || '未知班次'
    const registrationTime = registration.registrationTime || registration.createTime || '未知时间'
    const statusText = this.getRegistrationStatusText(registration.status)
    
    let content = `班次：${className}\n状态：${statusText}\n报名时间：${registrationTime}`
    
    // 添加培训时间信息
    if (registration.studyClass) {
      const classInfo = registration.studyClass
      if (classInfo.classStartDate && classInfo.classEndDate) {
        content += `\n培训时间：${classInfo.classStartDate} ~ ${classInfo.classEndDate}`
      }
      if (classInfo.requiredHours) {
        content += `\n总学时：${classInfo.requiredHours}小时`
      }
    }
    
    // 添加已获学时
    if (registration.achievedHours) {
      content += `\n已获学时：${registration.achievedHours}小时`
    }
    
    // 添加备注
    if (registration.remark) {
      content += `\n备注：${registration.remark}`
    }
    
    wx.showModal({
      title: '报名详情',
      content: content,
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  // 测试方法
  testLoadClasses() {
    console.log('测试加载班次被调用')
    this.setData({ debugInfo: '测试：手动调用加载班次' })
    this.loadAvailableClasses()
  },

  testLoadRegistrations() {
    console.log('测试加载报名被调用')
    this.setData({ debugInfo: '测试：手动调用加载报名' })
    this.loadMyRegistrations()
  },

  testSwitchTab0() {
    console.log('测试切换到班次被调用')
    this.setData({ 
      activeTab: 0,
      debugInfo: '测试：手动切换到班次标签页'
    })
    this.loadAvailableClasses()
  },

  testSwitchTab1() {
    console.log('测试切换到报名被调用')
    this.setData({ 
      activeTab: 1,
      debugInfo: '测试：手动切换到报名标签页'
    })
    this.loadMyRegistrations()
  }
}) 