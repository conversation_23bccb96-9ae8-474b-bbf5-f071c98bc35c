<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-card card">
    <view class="user-avatar">
      <image class="avatar" src="{{userInfo.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
    </view>
    <view class="user-details">
      <text class="user-name">{{userInfo.nickName || userInfo.userName}}</text>
      <text class="user-phone">{{userInfo.phonenumber}}</text>
      <text class="user-dept">{{userInfo.deptName}}</text>
    </view>
  </view>

  <!-- 学习统计 -->
  <view class="stats-card card">
    <view class="card-title">学习统计</view>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-value">{{stats.totalHours || 0}}</text>
        <text class="stat-label">累计学时</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{stats.totalDays || 0}}</text>
        <text class="stat-label">学习天数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{stats.completionRate || 0}}%</text>
        <text class="stat-label">完成率</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{stats.ranking || '--'}}</text>
        <text class="stat-label">当前排名</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="viewAttendanceHistory">
      <image class="menu-icon" src="/images/history.png"></image>
      <text class="menu-text">考勤记录</text>
      <text class="menu-arrow">></text>
    </view>
    
    <view class="menu-item" bindtap="viewLeaveHistory">
      <image class="menu-icon" src="/images/leave.png"></image>
      <text class="menu-text">请假记录</text>
      <text class="menu-arrow">></text>
    </view>
    
    <view class="menu-item" bindtap="viewCertificates">
      <image class="menu-icon" src="/images/certificate.png"></image>
      <text class="menu-text">学习证书</text>
      <text class="menu-arrow">></text>
    </view>
    
    <view class="menu-item" bindtap="viewSettings">
      <image class="menu-icon" src="/images/settings.png"></image>
      <text class="menu-text">设置</text>
      <text class="menu-arrow">></text>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="logout">退出登录</button>
  </view>
</view> 