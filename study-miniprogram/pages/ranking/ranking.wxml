<view class="container">
  <!-- 排行榜类型选择 -->
  <view class="ranking-tabs">
    <view class="tab-item {{activeTab === 'substantive' ? 'active' : ''}}" bindtap="switchTab" data-tab="substantive">
      实职领导
    </view>
    <view class="tab-item {{activeTab === 'section' ? 'active' : ''}}" bindtap="switchTab" data-tab="section">
      科级干部
    </view>
    <view class="tab-item {{activeTab === 'ordinary' ? 'active' : ''}}" bindtap="switchTab" data-tab="ordinary">
      普通党员
    </view>
    <view class="tab-item {{activeTab === 'other' ? 'active' : ''}}" bindtap="switchTab" data-tab="other">
      其他人员
    </view>
  </view>

  <!-- 我的排名 -->
  <view class="my-ranking card">
    <view class="card-title">我的排名</view>
    <view class="my-rank-info">
      <view class="rank-item">
        <text class="rank-label">当前排名</text>
        <text class="rank-value">第 {{myRanking.rank || '--'}} 名</text>
      </view>
      <view class="rank-item">
        <text class="rank-label">累计学分</text>
        <text class="rank-value">{{myRanking.score || 0}} 学分</text>
      </view>
    </view>
  </view>

  <!-- 排行榜列表 -->
  <view class="ranking-list">
    <view wx:if="{{rankingData.length > 0}}">
      <view class="ranking-item" wx:for="{{rankingData}}" wx:key="userId">
        <view class="rank-number">
          <text wx:if="{{item.rank <= 3}}" class="medal {{getMedalClass(item.rank)}}">{{item.rank}}</text>
          <text wx:else class="rank-text">{{item.rank}}</text>
        </view>
        
        <view class="user-info">
          <text class="user-name">{{item.nickName}}</text>
          <text class="user-dept">{{item.deptName}}</text>
<!--          <text class="user-type">{{item.cadreTypeName}}</text>-->
        </view>

        <view class="score-column">
          <text class="score-label">学分</text>
          <text class="score-value">{{item.score}}</text>
        </view>

        <view class="score-column">
          <text class="score-label">学分指标</text>
          <text class="score-value">{{item.completedScore}}</text>
        </view>

        <view class="score-column">
          <text class="score-label">完成率</text>
          <text class="score-value">{{item.completionRate}}%</text>
        </view>

      </view>

    </view>
    
    <view wx:else class="empty-state">
      <text>暂无排行数据</text>
    </view>
  </view>

  <!-- 加载更多 -->
  <view wx:if="{{hasMore && rankingData.length > 0}}" class="load-more" bindtap="loadMore">
    <text>{{loading ? '加载中...' : '加载更多'}}</text>
  </view>
</view> 