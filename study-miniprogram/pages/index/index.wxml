<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-card card">
    <view class="user-info">
      <text class="user-name">{{userInfo.nickName || userInfo.userName}}</text>
      <text class="user-dept">{{userInfo.deptName || '未设置部门'}}</text>
    </view>
    <view class="user-stats">
      <view class="stat-item">
        <text class="stat-value">{{totalHours}}</text>
        <text class="stat-label">累计学时</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{completionRate}}%</text>
        <text class="stat-label">完成率</text>
      </view>
    </view>
  </view>

  <!-- 快捷功能 -->
  <view class="quick-actions">
    <view class="action-item" bindtap="goToAttendance">
      <image class="action-icon" src="/images/attendance.svg"></image>
      <text class="action-text">签到打卡</text>
    </view>
    <view class="action-item" bindtap="goToLeave">
      <image class="action-icon" src="/images/leave.svg"></image>
      <text class="action-text">请假申请</text>
    </view>
    <view class="action-item" bindtap="goToRanking">
      <image class="action-icon" src="/images/ranking.svg"></image>
      <text class="action-text">学时排行</text>
    </view>
    <view class="action-item" bindtap="goToProfile">
      <image class="action-icon" src="/images/profile.svg"></image>
      <text class="action-text">个人中心</text>
    </view>
  </view>

  <!-- 最近学习记录 -->
  <view class="recent-study">
    <view class="section-title">最近学习</view>
    <view wx:if="{{recentRecords.length > 0}}">
      <view class="list-item" wx:for="{{recentRecords}}" wx:key="attendanceId">
        <view class="record-info">
          <text class="record-course">{{item.courseName}}</text>
          <text class="record-time">{{item.checkInTime}}</text>
        </view>
        <view class="record-status">
          <text class="status-tag {{item.attendanceStatus === '正常' ? 'status-success' : 'status-warning'}}">
            {{item.attendanceStatus}}
          </text>
        </view>
      </view>
    </view>
    <view wx:else class="empty-state">
      <text>暂无学习记录</text>
    </view>
  </view>

  <!-- 通知公告 -->
  <view class="notices">
    <view class="section-title">通知公告</view>
    <view wx:if="{{notices.length > 0}}">
      <view class="list-item" wx:for="{{notices}}" wx:key="id" bindtap="viewNotice" data-notice="{{item}}">
        <view class="notice-content">
          <text class="notice-title">{{item.title}}</text>
          <text class="notice-date">{{item.createTime}}</text>
        </view>
        <text class="notice-arrow">></text>
      </view>
    </view>
    <view wx:else class="empty-state">
      <text>暂无通知</text>
    </view>
  </view>
</view> 