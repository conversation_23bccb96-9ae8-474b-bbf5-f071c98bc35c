/* pages/index/index.wxss */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-card {
  background: linear-gradient(135deg, #409EFF 0%, #337ecc 100%);
  color: white;
  margin-bottom: 30rpx;
}

.user-info {
  margin-bottom: 30rpx;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.user-dept {
  font-size: 26rpx;
  opacity: 0.8;
}

.user-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-item {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
}

.action-text {
  display: block;
  font-size: 28rpx;
  color: #333;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.recent-study,
.notices {
  margin-bottom: 30rpx;
}

.record-info {
  flex: 1;
}

.record-course {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.notice-content {
  flex: 1;
}

.notice-title {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.notice-date {
  font-size: 24rpx;
  color: #999;
}

.notice-arrow {
  font-size: 32rpx;
  color: #ccc;
}

.empty-state {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
  font-size: 28rpx;
}

/* 状态标签样式 */
.record-status {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 100rpx;
}

.status-tag {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  text-align: center;
  min-width: 80rpx;
  height: 48rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  box-sizing: border-box;
  font-weight: 500;
  white-space: nowrap;
}

.status-success {
  background-color: #67C23A;
}

.status-warning {
  background-color: #E6A23C;
}

.status-danger {
  background-color: #F56C6C;
}

.status-info {
  background-color: #909399;
} 