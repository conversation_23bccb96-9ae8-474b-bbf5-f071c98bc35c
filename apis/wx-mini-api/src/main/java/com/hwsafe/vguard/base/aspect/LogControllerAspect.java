package com.hwsafe.vguard.base.aspect;

import com.alibaba.fastjson.JSON;
import com.hwsafe.common.SpringContextHolder;
import com.hwsafe.enterprise.domain.EntContacts;
import com.hwsafe.enterprise.domain.EntOrg;
import com.hwsafe.enterprise.service.EntContactsService;
import com.hwsafe.enterprise.service.EntOrgService;
import com.hwsafe.log.config.LogServiceConfig;
import com.hwsafe.log.domain.LogLogin;
import com.hwsafe.log.enums.LogLoginSourceEnum;
import com.hwsafe.log.service.LogLoginService;
import com.hwsafe.utils.IPUtil;
import com.hwsafe.vguard.base.utils.Context;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> 拦截服务执行时间 说明： 1、在类的主体上加上注解定义切面并申明 @Aspect 定义切面
 *         2、@Pointcut("execution(* com.hwsafe.controller..*.*(..))")
 *         定义切入点，一般使用表达式申明切入的范围 如com.hwsafe.service 包下的所有方法都会被拦截切面到
 *         3、@Before:切入点开始执行前处理的方法 4、@After:切入点结尾执行的方法
 *         5、@AfterReturning:在切入点return数据后执行的方法(一般用于对返回数据的包装)
 *         6、@Around:在切入点前后执行的方法 7、@AfterThrowing:抛出异常执行的方法
 */
@Aspect
@Component
public class LogControllerAspect {
    private final static Logger log = LoggerFactory
            .getLogger(LogControllerAspect.class);

    @Autowired
    private LogServiceConfig logServiceConfig;
    @Autowired
    private LogLoginService logLoginService;
    @Autowired
    private EntContactsService entContactsService;
    @Autowired
    private EntOrgService entOrgService;

    // 定义切点Pointcut
    @Pointcut("execution(* com.hwsafe.*.controller..*.*(..))")
    public void excudeController() {
    }

    @Around("excudeController()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        StringBuilder param = new StringBuilder();

        if (log.isInfoEnabled()) {
            ServletRequestAttributes attributes =
                    (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();

            param.append("[").append(request.getMethod()).append("].[")
                    .append(request.getServletPath()).append("]")
                    .append(" PARAM : [");

            Object[] args = joinPoint.getArgs();
            Signature signature = joinPoint.getSignature();
            MethodSignature methodSignature = (MethodSignature) signature;
            String[] parameterNames = methodSignature.getParameterNames();

            for (int i = 0, s = parameterNames.length; i < s; i++) {
                param.append(parameterNames[i]).append(":")
                        .append(JSON.toJSONString(joinPoint.getArgs()[i]))
                        .append(" | ");
            }
            param.append("]");
        }

        long startTime = System.currentTimeMillis();
        Object result = null;
        try {
            result = joinPoint.proceed();
        } catch (Exception e) {
            if (log.isInfoEnabled()) {
                log.info(param.toString());
            }
            throw e;
        }

        if (log.isInfoEnabled()) {
            //打印响应时间
            param.insert(0, "[" + (System.currentTimeMillis() - startTime) + "].");

            // 返回参数 INFO级别不打印, DEBUG打印 避免日志量过大
            // 如线上需要看RESPONSE可临时调整日志级别为DEBUG
            if (log.isDebugEnabled()) {
                param.append(" RESPONSE : ").append(JSON.toJSON(result));
            }
            log.info(param.toString());
        }
        // 登录日志记录
        logLoginRecord();
        return result;
    }

    public void logLoginRecord() {
        // 添加登录日志记录
        if (Context.getSysUser() != null) {
            boolean logLoginFlag = true;
            if (StringUtils
                    .isNoneBlank(logServiceConfig.getIgnoreLoginUser())) {
                for (String str : logServiceConfig.getIgnoreLoginUser()
                        .split(",")) {
                    if (Context.getSysUser().getUsername().trim()
                            .equals(str.trim())) {
                        logLoginFlag = false;
                        break;
                    }
                }
            }
            if (logLoginFlag
                    && logLoginService
                            .verificationUserLogin(
                                    Context.getSysUser().getUserid(),
                                    LogLoginSourceEnum.APP.getCode(),
                                    IPUtil.getIpAddress(SpringContextHolder
                                            .getHttpServletRequest()))
                            .isEmpty()) {
                // APP 每天访问算登录 一次 只记录一次
                LogLogin logLogin = new LogLogin();
                EntContacts entContacts = entContactsService
                        .selectByUserId(Context.getSysUser().getUserid());
                if (entContacts != null) {
                    logLogin.setContactsId(entContacts.getContactsid());
                    logLogin.setContactsName(entContacts.getContactsname());

                    EntOrg entOrg = entOrgService
                            .getById(entContacts.getEntorgid());
                    if (entOrg != null) {
                        logLogin.setOrgId(entOrg.getEntorgid());
                        logLogin.setOrgName(entOrg.getEntorgname());
                    }
                }

                logLogin.setIp(IPUtil.getIpAddress(
                        SpringContextHolder.getHttpServletRequest()));
                logLogin.setSource(LogLoginSourceEnum.APP.getCode());
                logLogin.setUserId(Context.getSysUser().getUserid());
                logLogin.setUserName(Context.getSysUser().getUsername());

                logLoginService.saveLogLogin(logLogin);

            }
        }
    }
}
