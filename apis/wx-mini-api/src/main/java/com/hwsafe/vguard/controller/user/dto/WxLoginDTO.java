package com.hwsafe.vguard.controller.user.dto;

import java.io.Serializable;

import lombok.Data;

/**
 * 
 * 项目名称：wx-mini-api
 * 类名称：WxLoginDTO
 * 类描述：微信小程序登录DTO类
 * 创建人：liuxb
 * 创建时间：2021年3月11日 下午1:40:19
 * 修改人：
 * 修改时间：
 * 修改备注：
 * @version 
 *
 */
@Data
public class WxLoginDTO implements Serializable {
	
	/**
	 * serialVersionUID:TODO（用一句话描述这个变量表示什么）
	 *
	 * @since Ver 1.1
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 小程序运行需要的微信官方的openid参数
	 */
	private String openId;
	
	/**
	 * 小程序运行需要的微信官方的key参数
	 */
	private String sessionKey;
	
	/**
	 * 小程序前后端通信header内的鉴权参数
	 */
	private String authorization;
	
}