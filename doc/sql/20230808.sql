ALTER TABLE `mall_order`.`bz_order_after_service`
ADD COLUMN `product_last_refund` tinyint(4) NULL DEFAULT 0 COMMENT '该笔退单是否是该商品行的最后一笔退款，即该退款单完成后，该商品行商品全退完成：1-是，0-否' AFTER `product_delivery_state`;

ALTER TABLE `mall_order`.`bz_order_after_service`
ADD COLUMN `order_last_refund` tinyint(4) NULL DEFAULT 0 COMMENT '该笔退单是否是该订单的最后一笔退款，即该退款单完成后，该订单所有商品行商品全退完成：1-是，0-否' AFTER `product_last_refund`;

ALTER TABLE `mall_order`.`bz_order`
ADD COLUMN `commission_service_fee` decimal(10, 2) NOT NULL DEFAULT 0 COMMENT '订单佣金服务费' AFTER `business_commission`;

ALTER TABLE `mall_order`.`bz_order_return`
ADD COLUMN `commission_service_fee` decimal(10, 2) NOT NULL DEFAULT 0 COMMENT '佣金服务费' AFTER `business_commission`;