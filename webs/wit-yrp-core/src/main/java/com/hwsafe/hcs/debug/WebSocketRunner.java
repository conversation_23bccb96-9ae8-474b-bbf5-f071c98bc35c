/**
 * 文件名：testWs.java
 *
 * 版本信息：
 * 日期：2022年3月23日
 * Copyright 河南汉威电子股份有限公司软件部 Corporation 2022
 * 版权所有
 *
 */
package com.hwsafe.hcs.debug;

import java.net.URI;

import org.java_websocket.WebSocket;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 *
 * 项目名称：service-device 类名称：testWs 类描述： 创建人：zhangxn 创建时间：2022年3月23日 下午3:43:41
 * 修改人：zhangxn 修改时间：2022年3月23日 下午3:43:41 修改备注：
 *
 * @version
 *
 */
@Component
public class WebSocketRunner implements ApplicationRunner {

    @Value("${webSocket.mornitor.alarm.server.url}")
    private String serverUrl;
    @Value("${webSocket.mornitor.alarm.server.switch}")
    private boolean serverSwitch;

//    public static void main(String[] args) {
//        try {
//            mwTest myClient = new mwTest(new URI("ws://192.168.111.139:8369"));
//            myClient.connect();
//            while (!myClient.getReadyState().equals(WebSocket.READYSTATE.OPEN)) {
//                System.out.println("连接中。。。");
//                Thread.sleep(1000);
//            }
//            // 连接成功往websocket服务端发送数据
//            JSONObject object = new JSONObject();
//            object.put("type", "alarm");
//            myClient.send(object.toJSONString());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    @Override
    public void run(ApplicationArguments args){
        if(serverSwitch){
            try {
                URI uri = new URI("ws://" + serverUrl);
                WebSocketServer myClient = new WebSocketServer(uri);
                myClient.setConnectionLostTimeout(0);
                myClient.connect();
                while (!myClient.getReadyState().equals(WebSocket.READYSTATE.OPEN)) {
                    System.out.println("连接中。。。");
                    Thread.sleep(1000);
                }
                // 连接成功往websocket服务端发送数据
                JSONObject object = new JSONObject();
                object.put("type", "alarm");
                myClient.send(object.toJSONString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
