#\u672C\u5730\u6D4B\u8BD5\u4E13\u7528
server.port=9001
#\u6F14\u793A\u7CFB\u7EDFV3.5
spring.datasource.url=*****************************************
spring.datasource.username=YSYQ_PARK
spring.datasource.password=YSYQ_PARK
spring.datasource.driver-class-name=oracle.jdbc.driver.OracleDriver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource

#druid
#\u521D\u59CB\u5316\u8FDE\u63A5\u5927\u5C0F
spring.druid.initialSize=5
#\u6700\u5C0F\u8FDE\u63A5\u6C60\u6570\u91CF
spring.druid.minIdle=5
#\u6700\u5927\u8FDE\u63A5\u6C60\u6570\u91CF
spring.druid.maxActive=200
#\u83B7\u53D6\u8FDE\u63A5\u65F6\u6700\u5927\u7B49\u5F85\u65F6\u95F4\uFF0C\u5355\u4F4D\u6BEB\u79D2
spring.druid.maxWait=60000
#\u914D\u7F6E\u95F4\u9694\u591A\u4E45\u624D\u8FDB\u884C\u4E00\u6B21\u68C0\u6D4B\uFF0C\u68C0\u6D4B\u9700\u8981\u5173\u95ED\u7684\u7A7A\u95F2\u8FDE\u63A5\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.druid.timeBetweenEvictionRunsMillis=60000
#\u914D\u7F6E\u4E00\u4E2A\u8FDE\u63A5\u5728\u6C60\u4E2D\u6700\u5C0F\u751F\u5B58\u7684\u65F6\u95F4\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.druid.minEvictableIdleTimeMillis=300000
#\u6D4B\u8BD5\u8FDE\u63A5
spring.druid.validationQuery=SELECT 1 FROM DUAL
#\u7533\u8BF7\u8FDE\u63A5\u7684\u65F6\u5019\u68C0\u6D4B\uFF0C\u5EFA\u8BAE\u914D\u7F6E\u4E3Atrue\uFF0C\u4E0D\u5F71\u54CD\u6027\u80FD\uFF0C\u5E76\u4E14\u4FDD\u8BC1\u5B89\u5168\u6027
spring.druid.testWhileIdle=true
#\u83B7\u53D6\u8FDE\u63A5\u65F6\u6267\u884C\u68C0\u6D4B\uFF0C\u5EFA\u8BAE\u5173\u95ED\uFF0C\u5F71\u54CD\u6027\u80FD
spring.druid.testOnBorrow=false
#\u5F52\u8FD8\u8FDE\u63A5\u65F6\u6267\u884C\u68C0\u6D4B\uFF0C\u5EFA\u8BAE\u5173\u95ED\uFF0C\u5F71\u54CD\u6027\u80FD
spring.druid.testOnReturn=false
#\u662F\u5426\u5F00\u542FPSCache\uFF0CPSCache\u5BF9\u652F\u6301\u6E38\u6807\u7684\u6570\u636E\u5E93\u6027\u80FD\u63D0\u5347\u5DE8\u5927\uFF0Coracle\u5EFA\u8BAE\u5F00\u542F\uFF0Cmysql\u4E0B\u5EFA\u8BAE\u5173\u95ED
spring.druid.poolPreparedStatements=true
#\u5F00\u542FpoolPreparedStatements\u540E\u751F\u6548
spring.druid.maxPoolPreparedStatementPerConnectionSize=20
#\u914D\u7F6E\u6269\u5C55\u63D2\u4EF6\uFF0C\u5E38\u7528\u7684\u63D2\u4EF6\u6709=>stat:\u76D1\u63A7\u7EDF\u8BA1  log4j:\u65E5\u5FD7  wall:\u9632\u5FA1sql\u6CE8\u5165(\u5BF9\u4E8ESQL\u5408\u6CD5\u6027\u8FDB\u884C\u9A8C\u8BC1)
spring.druid.filters=stat,log4j
#\u901A\u8FC7connectProperties\u5C5E\u6027\u6765\u6253\u5F00mergeSql\u529F\u80FD;\u6162SQL\u8BB0\u5F55
spring.druid.connectionProperties='druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000'

#\u79DF\u6237\u7684\u914D\u7F6E\u4FE1\u606F
#\u79DF\u6237\u5F00\u5173 true \u5F00 false \u5173
tenant.switch.flag=true
#\u5B9A\u65F6\u4EFB\u52A1\u914D\u7F6E true\u5F00\u542F false \u5173
qurtz.auto.start=false
