package com.base.common.utils.word;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Set;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件压缩工具类
 * Created by <PERSON><PERSON><PERSON> on 2019/05/29.
 */
@Slf4j
public class ZipMultiFile {

    public static void zipFiles(Set<File> srcFiles, File zipFile) {
        // 判断压缩后的文件存在不，不存在则创建
        if (!zipFile.exists()) {
            try {
                zipFile.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        // 创建 FileOutputStream 对象
        FileOutputStream fileOutputStream = null;
        // 创建 ZipOutputStream
        ZipOutputStream zipOutputStream = null;

        try {
            // 实例化 FileOutputStream 对象
            fileOutputStream = new FileOutputStream(zipFile);
            // 实例化 ZipOutputStream 对象
            zipOutputStream = new ZipOutputStream(fileOutputStream);
            // 创建 ZipEntry 对象
            ZipEntry zipEntry = null;
            // 遍历源文件数组
            for (File file : srcFiles) {
                if (file != null && file.exists()) {
                    FileInputStream fileInputStream = null;
                    try {
                        // 将源文件数组中的当前文件读入 FileInputStream 流中
                        // 创建 FileInputStream 对象
                        fileInputStream = new FileInputStream(file);
                        // 实例化 ZipEntry 对象，源文件数组中的当前文件
                        zipEntry = new ZipEntry(file.getName());
                        zipOutputStream.putNextEntry(zipEntry);
                        // 该变量记录每次真正读的字节个数
                        int len;
                        // 定义每次读取的字节数组
                        byte[] buffer = new byte[1024];
                        while ((len = fileInputStream.read(buffer)) > 0) {
                            zipOutputStream.write(buffer, 0, len);
                        }
                    } catch (Exception ex) {
                        log.error("fileInputStreamWriteEx,ex={}", ex);
                        throw ex;
                    } finally {
                        if (fileInputStream != null) {
                            fileInputStream.close();
                        }
                    }
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (zipOutputStream != null) {
                    zipOutputStream.closeEntry();
                }
            } catch (Exception ex) {
                log.warn("streamCloaseException,ex{}", ex);
            }

            try {
                if (zipOutputStream != null) {
                    zipOutputStream.close();
                }
            } catch (Exception ex) {
                log.warn("streamCloaseException,ex{}", ex);
            }

            try {
                if (fileOutputStream != null) {
                    fileOutputStream.close();
                }
            } catch (Exception ex) {
                log.warn("streamCloaseException,ex{}", ex);
            }
        }
    }
}
