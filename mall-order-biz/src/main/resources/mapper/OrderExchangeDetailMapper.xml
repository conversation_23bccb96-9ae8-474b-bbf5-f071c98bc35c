<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.mallorder.mapper.OrderExchangeDetailMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.mallorder.po.OrderExchangeDetailPO">
        <result column="exchange_detail_id" property="exchangeDetailId"/>
        <result column="exchange_sn" property="exchangeSn"/>
        <result column="order_sn" property="orderSn"/>
        <result column="order_product_id" property="orderProductId"/>
        <result column="product_name" property="productName"/>
        <result column="product_num" property="productNum"/>
        <result column="exchange_order_sn" property="exchangeOrderSn"/>
        <result column="exchange_order_product_id" property="exchangeOrderProductId"/>
        <result column="exchange_product_name" property="exchangeProductName"/>
        <result column="exchange_product_num" property="exchangeProductNum"/>
        <result column="afs_sn" property="afsSn"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="actual_refund_amount" property="actualRefundAmount"/>
        <result column="xz_card_amount" property="xzCardAmount"/>
        <result column="platform_voucher_amount" property="platformVoucherAmount"/>
        <result column="platform_activity_amount" property="platformActivityAmount"/>
        <result column="store_voucher_amount" property="storeVoucherAmount"/>
        <result column="store_activity_amount" property="storeActivityAmount"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="enabled_flag" property="enabledFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        exchange_detail_id
        ,exchange_sn,order_sn,order_product_id,product_name,product_num,
        exchange_order_sn,exchange_order_product_id,exchange_product_name,exchange_product_num,
        afs_sn,refund_amount,actual_refund_amount,xz_card_amount,platform_voucher_amount,platform_activity_amount,store_voucher_amount,store_activity_amount,
        create_by, update_by,create_time,update_time, enabled_flag
    </sql>

    <select id="getOrderExchangeDetailList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bz_order_exchange_detail
        <include refid="whereCondition"/>
    </select>

    <select id="getExchangeDetailByExchangeSn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bz_order_exchange_detail
        where exchange_sn = #{exchangeSn,jdbcType=VARCHAR}
    </select>

    <sql id="whereCondition">
        <if test="example != null">
            <trim prefix="WHERE" prefixOverrides="AND|OR">
                AND enabled_flag =1
            </trim>
            <if test="example.orderProductIdIn != null">
                AND `order_product_id` in
                <foreach collection="example.orderProductIdIn" item="orderProductId" open="(" separator="," close=")">
                    #{orderProductId}
                </foreach>
            </if>
            <if test="example.exchangeSn != null">
                AND `exchange_sn` = #{example.exchangeSn}
            </if>
            <if test="example.orderProductId != null">
                AND `order_product_id` = #{example.orderProductId}
            </if>
            <if test="example.exchangeOrderSn != null">
                AND `exchange_order_sn` = #{example.exchangeOrderSn}
            </if>
            <if test="example.orderSn != null">
                AND `order_sn` = #{example.orderSn}
            </if>
            <if test="example.afsSn != null">
                AND `afs_sn` = #{example.afsSn}
            </if>
        </if>
    </sql>


    <select id="getOrderExchangeListVOByPage" resultType="com.cfpamf.ms.mallorder.vo.OrderExchangeListVO">
        select boe.exchange_sn,boe.exchange_order_state,boe.buyer_confirm_flag,boe.store_id,boe.create_time,
        boed.order_sn, boed.refund_amount,boed.order_product_id,boed.exchange_order_sn,
        bop.goods_name,bop.product_image,bop.product_show_price*boed.product_num orderAmount,
        bo.user_no,bo.member_name,bo.order_state,bo.order_sn exchange_order_sn,
        boet.customer_id,boet.customer_name
        from bz_order_exchange boe
        left join bz_order_exchange_detail boed on boe.exchange_sn = boed.exchange_sn
        left join bz_order_product bop on boed.order_product_id = bop.order_product_id
        left join bz_order bo on boed.order_sn = bo.order_sn
        left join bz_order_extend boet on boed.order_sn = boet.order_sn
        <include refid="whereListCondition" />
        <include refid="limit"/>
    </select>


    <select id="getCountOrderExchangeList" parameterType="com.cfpamf.ms.mallorder.request.OrderExchangeListExample"
            resultType="java.lang.Integer">
        select count(*)
        from bz_order_exchange boe
            left join bz_order_exchange_detail boed on boe.exchange_sn = boed.exchange_sn
            left join bz_order_product bop on boed.order_product_id = bop.order_product_id
            left join bz_order bo on boed.order_sn = bo.order_sn
            left join bz_order_extend boet on boed.order_sn = boet.order_sn
        <include refid="whereListCondition"/>
    </select>

    <select id="getOrderExchangeDetail" resultType="com.cfpamf.ms.mallorder.dto.ExchangeOrderDetailDTO">
        select boe.exchange_sn,
               boe.exchange_order_state,
               boe.buyer_confirm_flag,
               boe.store_id,
               boe.create_time,
               boed.order_sn,
               boed.refund_amount,
               boed.order_sn,
               boed.order_product_id,
               boed.product_num,
               boed.afs_sn,
               boed.exchange_order_sn,
               boed.exchange_order_product_id,
               boed.exchange_product_num
        from bz_order_exchange boe,bz_order_exchange_detail boed
        where boe.exchange_sn = boed.exchange_sn
            AND boe.enabled_flag =1
            <if test="example.exchangeSn != null">
                AND boe.exchange_sn = #{example.exchangeSn}
            </if>
            <if test="example.afsSn != null">
                AND boed.afs_sn = #{example.afsSn}
            </if>
    </select>


    <sql id="whereListCondition">
        <if test="example != null">
            <trim prefix="WHERE" prefixOverrides="AND|OR">
                AND boe.enabled_flag =1 and bo.order_type != 11
            </trim>
            <if test="example.orderSn != null">
                AND boed.order_sn = #{example.orderSn}
            </if>
            <if test="example.exchangeSn != null">
                AND boe.exchange_sn = #{example.exchangeSn}
            </if>
            <if test="example.exchangeOrderState != null">
                AND boe.exchange_order_state = #{example.exchangeOrderState}
            </if>
            <if test="example.orderState != null">
                AND bo.order_state = #{example.orderState}
            </if>
            <if test="example.orderState != null">
                AND bo.order_state = #{example.orderState}
            </if>
            <if test="example.storeId != null">
                AND bo.store_id = #{example.storeId}
            </if>
            <if test="example.memberName != null">
                AND bo.member_name = #{example.memberName}
            </if>
            <if test="example.orderProductId != null">
                AND boed.order_product_id = #{example.orderProductId}
            </if>
            <if test="example.createTimeBefore != null">
                AND boed.create_time <![CDATA[ <= ]]> #{example.createTimeBefore}
            </if>
            <if test="example.createTimeAfter != null">
                AND boed.create_time >= #{example.createTimeAfter}
            </if>
        </if>
    </sql>

    <select id="getExchangeOrderDetailByExample" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bz_order_exchange_detail
        <include refid="whereCondition"/>
    </select>


    <!--分页条件-->
    <sql id="limit">
        <if test="size != null and size &gt; 0">
            limit #{startRow},#{size}
        </if>
    </sql>


    <select id="getProductExchangeCount" resultType="com.cfpamf.ms.mallorder.dto.OrderExchangeCountDTO">

        select d.order_product_id as orderProductId,sum(d.product_num) as exchangedNum
        from bz_order_exchange e,bz_order_exchange_detail d
        where e.exchange_sn = d.exchange_sn
            <if test="orderProductIdList != null and orderProductIdList.size() > 0">
                and d.order_product_id in
                <foreach collection="orderProductIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and e.exchange_order_state in (0,3,4)
            and e.enabled_flag = 1
        group by d.order_product_id
    </select>

</mapper>
