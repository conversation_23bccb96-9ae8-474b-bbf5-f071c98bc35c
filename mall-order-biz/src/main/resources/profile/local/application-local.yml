spring:
  redis:
    host: r-2zef4d5b5cdab4e4.redis.rds.aliyuncs.com
    port: 6379
    password: Db123456
    database: 99
    timeout: 100000
    jedis:
      pool:
        max-wait: 2000ms
        min-idle: 2
        max-idle: 8
  rabbitmq:
    host: rabbitmq.tsg.cfpamf.com
    port: 5672
    username: admin
    password: donttelldev
    template:
      receive-timeout: 2000
      reply-timeout: 2000
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  cloud:
    nacos:
      discovery:
        namespace: 6a137026-68f4-4887-9ac6-4084187e9a98
        server-addr: http://nacoco.tsg.cfpamf.com
        group: MALL_GROUP
        username: nacos
        password: nacos

seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: seata_newmall_tx_group
  enableAutoDataSourceProxy: true
  config: #从配置中心获取seata service的配置
    type: nacos #type 默认为file
    nacos:
      serverAddr: http://nacoco.tsg.cfpamf.com
      group: SEATA_GROUP
      username: nacos
      password: nacos
  registry: # 从注册中心获取seata-server服务端
    type: nacos #type 默认为file
    nacos:
      application: seata-server
      server-addr: http://nacoco.tsg.cfpamf.com
      group: SEATA_GROUP
      username: nacos
      password: nacos
  client:
    rm:
      tableMetaCheckEnable: true

feign:
  client:
    config:
      default:
        # FeignClientConfiguration
        connectTimeout: 20000 # Feign的连接建立超时时间
        readTimeout: 20000 # Feign的请求处理超时时间
        loggerLevel: full #

cfpamf:
  multiple:
    dataSource:
      enabled: true
  smartid:
    server: smartid.test-public
    token: 0f673adf80504e2eaa552f5d791b644c

  ##配置数据源
  jdbc:
    dataSource:
      masterdb:
        jdbcUrl: jdbc:mysql://************:31002/mall_order?useUnicode=true&characterEncoding=UTF-8&rewriteBatchedStatements=true&serverTimezone=GMT%2B8
        #        username: bbc
        #        password: Zhnx#BBC@T
        username: cd_mall
        password: Cd_Mall1
        hikariPool:
          maximumPoolSize: 10
          driverClassName: com.mysql.cj.jdbc.Driver
       
      pg:
        jdbcUrl: ******************************************************************************************************************
        username: devops
        password: devops123
        maxWait: 2000
        hikariPool:
          maximumPoolSize: 10
          driverClassName: org.postgresql.Driver
          connectionInitSql: SELECT 'X'   
          
  ##配置mybatis plus
  mybatis:
    masterdb:
      basePackage: com.cfpamf.ms.mallorder.mapper
      typeAliasesPackage: com.cfpamf.ms.mallorder.po
      mapperLocations: classpath:mapper/**/*.xml
      configuration:
        # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
        default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
      type-enums-package: com.cfpamf.ms.mallorder.constant.enums
      pageProperties:
        overflow: true
        dialectType: mysql
      metaObjectHandler: com.cfpamf.ms.mallorder.common.handler.MyMetaObjectHandler
     
    pg:
      basePackage: com.cfpamf.ms.mallorder.pgMapper
      typeAliasesPackage: com.cfpamf.ms.mallorder.po.pgrpt
      mapperLocations: classpath:pgMapper/**/*.xml
      configuration:
        # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
        default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
      type-enums-package: com.cfpamf.ms.mallorder.common.enums.pgrpt
      pageProperties:
        overflow: true
        dialectType: postgresql 
      
  ##配置swagger
  swagger:
    dockets:
      demo:
        groupName: 订单中心
        basePackage: com.cfpamf.ms.mallorder.controller
        author: 毛亮
        title: 订单中心

  ##OSS配置
  oss:
    bucket: "mall-sld-test"
    endPoint: "https://oss-cn-beijing.aliyuncs.com"
    accessKeyId: "LTAI5tFeQgPuGg3fY6wycs1k"
    accessKeySecret: "******************************"
    oss-expiretime: 600

log4jdbc:
  sqltiming:
    warn:
      threshold: 300
    error:
      threshold: 2000
  dump.sql.select: true

xxl:
  job:
    version: 2.0
  newjob:
    admin:
      addresses: http://xxl-job2.tsg.cfpamf.com/xxl-job-admin
    executor:
      appname: ${spring.application.name}
      ip:
      port: 9911
      logpath: /data/applogs/xxl-job/jobhandler/
      logretentiondays: 5
    accessToken:


mall-logistic:
  #url: http://mall-logistic.tsg.cfpamf.com
  url: http://mall-logistic.tsg.cfpamf.com
mall-order-biz:
  url: http://mall-order.tsg.cfpamf.com
mall-payment:
  url: http://mall-payment.tsg.cfpamf.com
  notify: ${mall-order-biz.url}/front/orderPayCallback/notify
  refundNotify: ${mall-order-biz.url}/front/orderPayCallback/refundNotify
  loanNotify: ${mall-order-biz.url}/front/orderPayCallback/loanNotify
  wxpay-key: caa08fe60b014a14b5503fb2ea60aae1
ms-service-customer:
  url: ms-customer.tsg.cfpamf.com
platform-collection:
  account: **********
  name: 河北电子服务商

ms-bizconfig-service:
  url: http://ms-bizconfig.tsg.cfpamf.com/
ms-service-loan:
  url: http://ms-loan.tsg.cfpamf.com
agric-host-order:
  url: http://agric-host-order.tsg.cfpamf.com/
bms:
  api:
    url: http://bms-service.tsg.cfpamf.com/
core-trade-service:
  url: http://core-trade.tsg.cfpamf.com
hrms-salary:
  url: http://hrms-salary.tsg.cfpamf.com
#hr服务
hrms-biz:
  url: http://hrms-biz.tsg.cfpamf.com
dbc-service:
  url: http://dbc-service.tsg.cfpamf.com
auditors:
  platform: ***********,***********
mall-biz:
  url: http://mall-biz.tsg.cfpamf.com
wms-service:
  url: http://wms-service.tsg.cfpamf.com
ms-promotion-service:
  url: http://ms-promotion.tsg.cfpamf.com
#UDC消息发送
ms-messagepush-service:
  url: http://ms-messagepush-service.test-capp
ding-talk:
  url: https://oapi.dingtalk.com/robot/send?access_token=ab8bdb02cfb902cab5c1d757732c00d6f85a09ea4d1a39ff42c2363ac43bc425
dts-center:
  url: http://dts-center.tsg.cfpamf.com
oms-base-service:
  url: http://oms-base.tsg.cfpamf.com
cashier-service:
  url: http://mall-cashier.tsg.cfpamf.com
erp-service:
  url: http://erp-services.tsg.cfpamf.com
mall-stock:
  url: http://mall-stock.tsg.cfpamf.com
crawler-service:
  url: http://crawler-service.tsg.cfpamf.com
dayEnd:
  start: 2330
  end: 100
enable:
  tcc: false
aliyun:
  img:
    url: https://mall-sld-test.oss-cn-beijing.aliyuncs.com/

channel-fee-rate:
  mappedRate:
    2-WXPAY: 0.006
    3-ALIPAY: 0.006
    4-BANK_PAY: 0
    1-BANK_TRANSFER: 0
    1-CARD: 0
    1-CARD_VOUCHER: 0
    1-ENJOY_PAY: 0
    1-FOLLOW_HEART: 0
    1-ONLINE: 0
    5-WXPAY: 0.006
    5-ALIPAY: 0.006
    5-CARD: 0
    5-CARD_VOUCHER: 0
    5-ENJOY_PAY: 0
    5-FOLLOW_HEART: 0
    5-ONLINE: 0
    5-BANK_TRANSFER: 0
    4-AGREED_PAY: 0
    4-ONLINE: 0

wx-combine-xzcard:
  orderList: [****************,****************,****************,****************,****************,****************,****************]

#订单退款黑名单，拦截订单退款
refund-black-list:
  orderList: [****************,****************,****************]

presell:
  paymethods:
    deposit: WXPAY,BANK_TRANSFER,ALIPAY
    remain: WXPAY,ENJOY_PAY,FOLLOW_HEART,BANK_TRANSFER,ALIPAY

spring.cloud.nacos.elegant.offline.enabled: true

bkTransfer:
  expire-url: https://2i1i.cn/g7Di
order:
  offline:
    privilege: 2030006,1140041,2930008,870002,2140002,290007,1140004,4220003,2670005,6120002,6040003,6710002,5860006
home:
  service:
    auto:
      receive:
        days: 1
logging:
  level:
    com.cfpamf.ms.mallorder.mapper: debug

store-for-deliver:
  storeId: 230002,1140006,2670005,2930008,1140041,4220003,6710002,540004,5710003,5860006,6800002,1140004
store-for-self-order-deliver:
  storeId: 2030006,5970003,1140004,2140002,1140041,4220003,6710002

#是否允许客户经理签收
store-for-receive:
  storeId: 230002,1140006,2670005,1140004,1140041,4220003,6880005,6710002,5710003

#代客下单贷款类支付是否需要扫脸
face-scan-pay:
  enabled: false

#超期售后时间
after.sale.common.delay.days: 300

#超期支付宝时间
after.sale.alipay.delay.days: 80

funds-borrow-day:
  days: 3
  closeStoreDays: 3
  preAlertDays: 1
  factoryNo: 100101001

#商品自提自动发货
self-lift-order:
  auto-delivery-store-list: 2670005,7230009,6710002,

order-rate-config:
  maxSharingRate: 30

after:
  sale:
    loanpay:
      delay:
        days: 450

show:
  insurance:
    categoryId: 1000001701

commission-incentive-rule-config:
  whitelistsStore: [8390002,7230009]