package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallorder.vo.AfsApplyInfoVO;

import java.math.BigDecimal;

public interface IFrontAfterSaleApplyService {


    /**
     * 退款申请信息
     * todo：方法重复，计算逻辑复杂且在做无用功，可删除
     * @param memberId
     * @param orderSn
     * @param orderProductId
     * @return
     */
    AfsApplyInfoVO getAfsApplyInfoVO(Integer memberId, String orderSn, Long orderProductId);

    /**
     * 根据订单号，获取预付订金类型的退款金额
     * @param orderSn
     * @param amount 退款金额
     * @return
     */
    BigDecimal getDepositProductRefundMoney(String orderSn,BigDecimal amount);
}
