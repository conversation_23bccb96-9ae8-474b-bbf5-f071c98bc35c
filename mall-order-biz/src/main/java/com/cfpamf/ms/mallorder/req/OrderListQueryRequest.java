package com.cfpamf.ms.mallorder.req;

import com.cfpamf.ms.mallorder.dto.UserDTO;
import com.cfpamf.ms.mallorder.req.base.PageRequest;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @Author: zml
 * @CreateTime: 2022/7/6 9:31
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class OrderListQueryRequest  extends PageRequest {

    @ApiModelProperty("文件导出条件，非必填")
    String applyCondition;
    @ApiModelProperty("订单号")
    @JsonProperty("orderSn")
    private String orderSnLike;
    @ApiModelProperty("订单Id")
    private Integer orderId;
    @ApiModelProperty("会员名称")
    @JsonProperty("memberName")
    private String memberNameLike;
    @ApiModelProperty("商品名称")
    @JsonProperty("goodsName")
    private String goodsNameLike;
    @ApiModelProperty("下单开始时间")
    @JsonProperty("endTime")
    private Date createTimeBefore;
    @ApiModelProperty("下单结束时间")
    @JsonProperty("startTime")
    private Date createTimeAfter;
    @ApiModelProperty("交易成功开始时间")
    private Date finishTimeAfter;
    @ApiModelProperty("交易成功结束时间")
    private Date finishTimeBefore;
    @ApiModelProperty("支付方式编码")
    private String paymentCode;
    @ApiModelProperty("退款状态：1-退款中")
    private Integer orderReturnState;
    @ApiModelProperty("订单状态：0-已取消；5-待支付订金；10-未付款订单；20-已付款；30-已发货；40-已完成;")
    @JsonProperty("orderState")
    private Integer orderStateParam;
    @ApiModelProperty("订单类型：OrderTypeEnum")
    private List<Integer> orderType;
    @ApiModelProperty("客户编码")
    private String customerId;
    @ApiModelProperty("客户名称")
    private String customerName;
    @ApiModelProperty("店铺名称")
    @JsonProperty("storeName")
    private String storeNameLike;
    @ApiModelProperty("引荐商ID")
    private String recommendStoreId;
    @ApiModelProperty("分支名")
    @JsonProperty("branchName")
    private String setBranchNameLike;
    @ApiModelProperty("区域名")
    @JsonProperty("areaName")
    private String areaNameLike;
    @ApiModelProperty("收货人姓名")
    @JsonProperty("receiverName")
    private String receiverNameLike;
    @ApiModelProperty("收货人手机号")
    private String receiverMobile;
    @ApiModelProperty("买家手机号")
    private String userMobile;
    @ApiModelProperty("订单模式：1-C端订单，2-采购订单 4-卡券订单")
    private Integer orderPattern;
    @ApiModelProperty("下单操作渠道：H5-浏览器H5；APP-乡助APP；WE_CHAT-微信浏览器；MINI_PRO-小程序；OMS-运管物资")
    private String channel;
    @ApiModelProperty("是否配销订单 0-否 1-是")
    private Integer distribution;
    @ApiModelProperty(value = "履约模式，0-内部履约模式，1-供应商履约模式",hidden = true)
    private Integer performanceMode;
    @ApiModelProperty("客户经理名称")
    @JsonProperty("managerName")
    private String managerNameLike;

    @ApiModelProperty("客户经理名称集合")
    @JsonProperty("managerNameList")
    private List<String> managerNameList;

    @ApiModelProperty("客户经理编码")
    @JsonProperty("manager")
    private String manager;
    @ApiModelProperty("分支编码list")
    @JsonProperty("branchCodeList")
    private List<String> branchCodeIn;
    @ApiModelProperty("商户Id")
    private Long storeId;
    @ApiModelProperty("供应商编码")
    private String supplierCode;
    @ApiModelProperty("采购订单-入驻类型，见PurchaseOrderSettledTypeEnum")
    private String settledType;

    @ApiModelProperty("采购订单-企业名称")
    private String companyNameLike;

    @ApiModelProperty("采购订单-店铺所属分支编码")
    private List<String> storeBranchCodeIn;
    
    @ApiModelProperty("客户确认状态code：0-无需确认，1-草稿，2-待确认，3-已确认 4-拒绝 见枚举CustomerConfirmStatusEnum")
    private Integer customerConfirmStatus;

    @ApiModelProperty("订单换货列表标识，1-标识是从订单换货列表过来的")
    private Integer orderExchangeFlag;

    @ApiModelProperty("商户用户角色")
    private String rolesName;

    @ApiModelProperty("操作人信息")
    private UserDTO userDTO;

    @ApiModelProperty(value = "文件导出模块")
    private String bizModule;

    @ApiModelProperty("履约服务：0-常规，1-供应商履约，2-安装服务，3-自提，4-农机厂商")
    private String performanceService;

    @ApiModelProperty("商品分类编码")
    private List<Integer> goodsCategoryIdList;

    @ApiModelProperty(value = "收款标签，默认未收款1，0-默认值（普通订单），1-待收款，2-部分收款，3-已收款")
    private Integer paymentTag;

    @ApiModelProperty(value = "线下补录订单开票标签 0-未开票，1-已开票")
    private Integer invoiceStatus;

    @ApiModelProperty(value = "自提点id")
    private Long pointId;

    @ApiModelProperty(value = "是否开启自提")
    private Integer isSelf;

    @ApiModelProperty(value = "签收确认书上传状态,0-未上传,1-已上传")
    private Integer receiveMaterialStatus;

    @ApiModelProperty("订单货品id")
    private Integer orderProductId;

}
