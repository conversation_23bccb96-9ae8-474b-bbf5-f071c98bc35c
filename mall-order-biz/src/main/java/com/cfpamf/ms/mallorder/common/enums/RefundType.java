package com.cfpamf.ms.mallorder.common.enums;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.core.enums.IEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款类型枚举
 *
 * @version V1.0
 * @author: maoliang
 * @date: 2020/7/17
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum RefundType implements IEnum<Integer> {

    OTHER(0, "其他", "OTHER", "其他"),
    ACCOUNT_REGULATION(2, "调账退款", "ACCOUNT_REGULATION", "调账退款"),
    ASSIST_PAYMENT(1, "代还退款", "FOLLOW_HEART", "随心取"),
    RESTORE_LIMIT(3, "恢复额度", "ENJOY_PAY", "用呗"),
    WITHDRAW_DEPOSIT(4, "提现退款", "WITHDRAW_DEPOSIT", "提现退款"),
    APLIPAY_REFUND(5, "支付宝退款", "alipay", "支付宝"),
    WXPAY_REFUND(6, "微信退款", "WXPAY", "微信"),
    AGREED_REFUND(7, "协议退款", "AGREED_REFUND", "协议退款"),
    OFFLINE_REFUND(8, "线下退款", "OFFLINE_REFUND", "线下退款"),
    CARD_VOUCHER_REFUND(9, "卡券退款", "CARD_VOUCHER_REFUND", "卡券退款"),
    BANK_REFUND(10, "银行卡退款", "BANK_REFUND", "银行卡退款"),
    CARD_REFUND(11, "乡助卡退款", "CARD_REFUND", "乡助卡退款"),
    TRANSFER_REFUND(12, "银行卡汇款退款", "TRANSFER_REFUND", "银行卡汇款退款"),
    COMBINATION_REFUND(13, "组合退款", "COMBINATION_REFUND", "组合退款"),
    ZERO_YUAN_REFUND(14, "0元退款", "ZERO_YUAN_REFUND", "0元退款"),
    SQUARE_OFFLINE_REFUND(15, "商家线下退款", "SQUARE_OFFLINE_REFUND", "商家线下退款");

    private Integer value;
    private String desc;
    private String aresAfterSaleValue;
    private String aresAfterSaleDesc;

    @Override
    public Integer getValue() {
        return value;
    }

    public static RefundType value(int value) {
        for (RefundType ps : RefundType.values()) {
            if (value == ps.value) {
                return ps;
            }
        }
        return null;
    }

    public static String getDesc(Integer value) {
        for (RefundType v : RefundType.values()) {
            if (value.equals(v.value)) {
                return v.desc;
            }
        }
        return null;
    }
    
    /**
     * 是否组合退款
     * 
     * @param value
     * @return
     */
    public static boolean isCombinationRefund(Integer value) {
        if (COMBINATION_REFUND.value.equals(value)) {
            return true;
        }
        return false;
    }

    /**
     * 是否是除微信退款、支付宝退款、银行卡转账、恢复额度（新增）退款的三类退款方式
     *
     * @param refundType
     *            退款方式
     * @return true/false
     */
    public static boolean isThirdRefundType(RefundType refundType) {
        return OTHER == refundType || ACCOUNT_REGULATION == refundType || ASSIST_PAYMENT == refundType
            || WITHDRAW_DEPOSIT == refundType || AGREED_REFUND == refundType || OFFLINE_REFUND == refundType
            || CARD_VOUCHER_REFUND == refundType || BANK_REFUND == refundType || CARD_REFUND == refundType;
    }

    public static boolean isLoanPayRefundType(RefundType refundType) {
        return refundType == ASSIST_PAYMENT || refundType == RESTORE_LIMIT;
    }

}
