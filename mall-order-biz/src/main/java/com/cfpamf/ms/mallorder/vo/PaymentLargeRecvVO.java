package com.cfpamf.ms.mallorder.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 大额订单创建返回
 * <AUTHOR>
 */
@Data
public class PaymentLargeRecvVO {

    @ApiModelProperty("订单主单号")
    private String mainOrderNo;

    @ApiModelProperty("业务订单号")
    private String orderNo;

    @ApiModelProperty("付款款方户账号")
    private String payBankAcctNo;

    @ApiModelProperty("付款款方户名")
    private String payBankAcctName;

    @ApiModelProperty("大额订单号，银联侧生成")
    private String largePaymentNo;

    @ApiModelProperty(value = "收款账号")
    private String receiptAccount;

    @ApiModelProperty("收款方户名")
    private String recvBankAcctName;

    @ApiModelProperty("收款方银行")
    private String recvBankName;

    @ApiModelProperty("收款方银行备注")
    private String recvBankNameRemark;

    @ApiModelProperty("有效时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expiredAt;

    @ApiModelProperty("支付状态,0：待支付，1：成功，2：支付中，3：失败")
    private Integer payStatus;

    @ApiModelProperty("订单金额")
    private BigDecimal payAmount;
}
