package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.ms.mall.filecenter.component.FileComponent;
import com.cfpamf.ms.mall.filecenter.domain.dto.ColumnDTO;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.cfpamf.ms.mall.filecenter.domain.vo.ExcelAsyncExportVO;
import com.cfpamf.ms.mall.filecenter.service.impl.AbstractExcelDataExportServiceImpl;
import com.cfpamf.ms.mallorder.builder.OrderQueryBuilder;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.CommonEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderExportDTO;
import com.cfpamf.ms.mallorder.dto.UserDTO;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.BzOrderProductInstallPO;
import com.cfpamf.ms.mallorder.po.OrderPromotionDetailPO;
import com.cfpamf.ms.mallorder.req.OrderListQueryRequest;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.service.IOrderInfoService;
import com.cfpamf.ms.mallorder.service.IOrderPerformanceBelongsService;
import com.cfpamf.ms.mallorder.service.IOrderPromotionDetailService;
import com.cfpamf.ms.mallorder.service.OrderExcelDataExportService;
import com.cfpamf.ms.mallorder.v2.domain.dto.OrderPresellDTO;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.google.common.collect.Maps;
import com.slodon.bbc.core.response.PagerInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderExcelDataExportServiceImpl
		extends AbstractExcelDataExportServiceImpl<OrderExportDTO, OrderListQueryRequest>
		implements OrderExcelDataExportService {

	@Autowired
	private FileComponent fileComponent;
	@Autowired
	private OrderModel orderModel;
	@Autowired
	private IOrderInfoService orderInfoService;
	@Autowired
	private IOrderPerformanceBelongsService orderPerformanceBelongsService;

	@Autowired
	private IOrderPromotionDetailService promotionDetailService;

	@Autowired
	private StoreFeignClient storeFeignClient;

	@Value("${spring.application.name}")
	private String appName;

	@Override
	public FileDTO executeAsyncExportExcel(OrderListQueryRequest orderListQueryRequest) throws Exception {
		// 搜索条件，订单号不为空，去除时间作为必须的搜索条件
		if (Objects.isNull(orderListQueryRequest) || (Objects.isNull(orderListQueryRequest.getOrderSnLike())
				&& Objects.isNull(orderListQueryRequest.getUserMobile())
				&& Objects.isNull(orderListQueryRequest.getStoreId())
				&& Objects.isNull(orderListQueryRequest.getStoreNameLike())
				&& Objects.isNull(orderListQueryRequest.getGoodsNameLike())
				&& Objects.isNull(orderListQueryRequest.getCustomerId()))) {
			BizAssertUtil.isTrue((orderListQueryRequest.getCreateTimeAfter() == null
					|| orderListQueryRequest.getCreateTimeBefore() == null), "下单起始时间不能为空");
			BizAssertUtil.isTrue(DateUtil.getDaysBetweenTwoDate(orderListQueryRequest.getCreateTimeAfter(),
					orderListQueryRequest.getCreateTimeBefore()) > 370, "下单时间跨度不能超过一年");
		}
		UserDTO userDTO = orderListQueryRequest.getUserDTO();
		ExcelAsyncExportVO excelAsyncExportVO = new ExcelAsyncExportVO();
		String bizModule = orderListQueryRequest.getBizModule();
		if(StringUtils.isEmpty(bizModule)) {
			bizModule = "订单列表";
		}
		excelAsyncExportVO.setBizModule(bizModule);
		excelAsyncExportVO.setUserId(userDTO.getUserId());
		excelAsyncExportVO.setUserName(userDTO.getUserName());
		excelAsyncExportVO.setUserType(getUserType(userDTO.getUserRole()));
		excelAsyncExportVO.setApplicationCondition(orderListQueryRequest.getApplyCondition());
		return fileComponent.executeAsyncExportExcelWithAnnotation(excelAsyncExportVO, appName, CommonConst.ORDER_EXCEL_SHEET_NAME,
				this, orderListQueryRequest, 10000, null, "orderProductId");
	}

	private Integer getUserType(Integer userRole) {
		int result = 2;
		if (userRole == null) {
			return result;
		}
		if (userRole == 1) {
			return 1;
		} else if (userRole == 2) {
			return 0;
		}
		return result;
	}

	@Override
	public Integer getDataCounts(OrderListQueryRequest query) {
		OrderExample orderExample = OrderQueryBuilder.buildOrderExportDataQueryCondition(query, query.getStoreId());
		Integer total = orderModel.countOrderExportList(orderExample);
		log.info("【订单导出】getDataCounts，total：{}", total);
		return total;
	}

	@Override
	public List<ColumnDTO> createTemplate(OrderListQueryRequest param) {
		List<ColumnDTO> columnDTOS = new ArrayList<>();
		// 创建模板 columnName - 列标题， columnProperties - 列属性值， index - 列顺序
		columnDTOS.add(new ColumnDTO("编号", "orderProductId", 0));
		columnDTOS.add(new ColumnDTO("引荐商户", "recommendStoreName", 1));
		columnDTOS.add(new ColumnDTO("店铺名称", "storeName", 2));
		columnDTOS.add(new ColumnDTO("店铺id", "storeId", 3));
		columnDTOS.add(new ColumnDTO("订单号", "orderSn", 4));
		columnDTOS.add(new ColumnDTO("支付流水号", "tradeSn", 5));
		columnDTOS.add(new ColumnDTO("交易流水号", "paySn", 6));
		columnDTOS.add(new ColumnDTO("渠道交易流水号", "bankPayTrxNo", 7));
		columnDTOS.add(new ColumnDTO("结算单号", "billSn", 8));
		columnDTOS.add(new ColumnDTO("订单状态", "orderState", 9));
		columnDTOS.add(new ColumnDTO("客户确认状态", "customerConfirmStatusDesc", 10));
		columnDTOS.add(new ColumnDTO("订单模式", "orderPatternDesc", 11));
		columnDTOS.add(new ColumnDTO("履约渠道", "performanceChannelDesc", 12));
		columnDTOS.add(new ColumnDTO("履约服务", "performanceService", 12));
		columnDTOS.add(new ColumnDTO("是否退款中/退款成功", "", 13));
		columnDTOS.add(new ColumnDTO("下单时间", "createTime", 14));
		columnDTOS.add(new ColumnDTO("支付时间", "payTime", 15));
		columnDTOS.add(new ColumnDTO("完成时间", "finishTime", 16));
		columnDTOS.add(new ColumnDTO("收货人姓名", "receiverName", 17));
		columnDTOS.add(new ColumnDTO("收货人电话", "receiverMobile", 18));
		columnDTOS.add(new ColumnDTO("收货省", "receiverProvinceCode", 19));
		columnDTOS.add(new ColumnDTO("收货市", "receiverCityCode", 20));
		columnDTOS.add(new ColumnDTO("收货区/县", "receiverDistrictCode", 21));
		columnDTOS.add(new ColumnDTO("收货街道/镇", "receiverTownCode", 22));
		columnDTOS.add(new ColumnDTO("收货详细地址", "receiverAddress", 23));
		columnDTOS.add(new ColumnDTO("商品SPU", "goodsId", 24));
		columnDTOS.add(new ColumnDTO("商品名称", "goodsName", 25));
		columnDTOS.add(new ColumnDTO("商品发货数量", "deliveryNum", 26));
		columnDTOS.add(new ColumnDTO("发货仓库", "deliverWarehouseName", 27));
		/*columnDTOS.add(new ColumnDTO("属性1", "property1", 26));
		columnDTOS.add(new ColumnDTO("属性2", "property2", 27));
		columnDTOS.add(new ColumnDTO("属性3", "property3", 28));
		columnDTOS.add(new ColumnDTO("属性4", "property4", 29));*/
		columnDTOS.add(new ColumnDTO("商品分类", "goodsCategoryPath", 30));
		columnDTOS.add(new ColumnDTO("商品SKU", "productId", 31));
		columnDTOS.add(new ColumnDTO("物料编码", "skuMaterialCode", 32));
		columnDTOS.add(new ColumnDTO("物料名称", "skuMaterialName", 32));
		// 增加物料规格编码导出
		columnDTOS.add(new ColumnDTO("物料规格编码", "channelNewSkuId", 33));
		columnDTOS.add(new ColumnDTO("供应商", "supplierName", 33));
		columnDTOS.add(new ColumnDTO("商品发货状态", "deliverState", 34));
		columnDTOS.add(new ColumnDTO("商品退款状态", "productStatusDesc", 35));
		/*columnDTOS.add(new ColumnDTO("配销商品id", "distributeParent", 36));*/
		columnDTOS.add(new ColumnDTO("商品规格", "specValues", 37));
		columnDTOS.add(new ColumnDTO("商品数量", "productNum", 38));
		columnDTOS.add(new ColumnDTO("商品有效数量", "validNum", 39));
		columnDTOS.add(new ColumnDTO("重量属性（吨）", "weightStr", 40));
		columnDTOS.add(new ColumnDTO("有效重量（吨）", "validWeightStr", 41));
		// columnDTOS.add(new ColumnDTO("商品成本", "cost", 42));
		columnDTOS.add(new ColumnDTO("销售成本（落地价）", "landingPrice", 43));
		columnDTOS.add(new ColumnDTO("商品单价(元)", "productShowPrice", 44));
		columnDTOS.add(new ColumnDTO("商品有效单价", "productEffectivePrice", 45));
		columnDTOS.add(new ColumnDTO("商品应付金额", "productShouldPay", 46));
		columnDTOS.add(new ColumnDTO("商品实付金额", "moneyAmount", 47));
		columnDTOS.add(new ColumnDTO("商品有效交易额", "productEffectiveGmv", 48));
		columnDTOS.add(new ColumnDTO("商品订金金额", "deposit", 49));
		columnDTOS.add(new ColumnDTO("商品尾款金额", "balance", 50));
		columnDTOS.add(new ColumnDTO("商品优惠金额", "goodsActivityDiscountAmount", 51));
		columnDTOS.add(new ColumnDTO("商品优惠券出售金额", "retailPrice", 52));
		columnDTOS.add(new ColumnDTO("平台优惠金额", "platformDiscountAmount", 52));
		columnDTOS.add(new ColumnDTO("店铺优惠金额", "storeDiscountAmount", 53));
		columnDTOS.add(new ColumnDTO("积分抵扣金额", "integralCashAmount", 54));
		columnDTOS.add(new ColumnDTO("乡助卡金额", "xzCardAmount", 55));
		columnDTOS.add(new ColumnDTO("乡助卡订单抵扣运费金额", "xzCardExpressFeeAmount", 56));
		columnDTOS.add(new ColumnDTO("物流费用", "expressFee", 57));
		columnDTOS.add(new ColumnDTO("订单应付金额", "orderNeedAmount", 58));
		columnDTOS.add(new ColumnDTO("订单实际应付金额", "orderAmount", 59));
		columnDTOS.add(new ColumnDTO("订单优惠金额", "activityDiscountAmount", 62));
		columnDTOS.add(new ColumnDTO("订单优惠券出售金额", "retailPriceOrder", 62));
		columnDTOS.add(new ColumnDTO("优惠券出资方", "funderOrder", 62));
		columnDTOS.add(new ColumnDTO("订单佣金", "commission", 62));
		columnDTOS.add(new ColumnDTO("订单商品佣金", "productOrderCommission", 63));
		columnDTOS.add(new ColumnDTO("税率", "taxRate", 64));
		columnDTOS.add(new ColumnDTO("支付方式(名称)", "paymentName", 65));
		columnDTOS.add(new ColumnDTO("订单来源", "channel", 66));
		columnDTOS.add(new ColumnDTO("订单类型", "orderType", 67));
		columnDTOS.add(new ColumnDTO("发货方式", "deliverType", 68));
		columnDTOS.add(new ColumnDTO("发货时间", "productDeliverTime", 69));
		columnDTOS.add(new ColumnDTO("物流公司编码", "expressCode", 70));
		columnDTOS.add(new ColumnDTO("物流公司", "expressName", 71));
		columnDTOS.add(new ColumnDTO("发货单号", "expressNumber", 72));
		columnDTOS.add(new ColumnDTO("发货人姓名", "deliverName", 73));
		columnDTOS.add(new ColumnDTO("发货人电话", "deliverMobile", 74));
		columnDTOS.add(new ColumnDTO("发货地址", "deliverAreaInfo", 75));
		columnDTOS.add(new ColumnDTO("发票", "invoiceInfo", 76));
		columnDTOS.add(new ColumnDTO("买家id", "memberId", 77));
		columnDTOS.add(new ColumnDTO("买家名称", "memberName", 78));
		columnDTOS.add(new ColumnDTO("客户编号", "customerId", 79));
		columnDTOS.add(new ColumnDTO("客户名称", "customerName", 80));
		columnDTOS.add(new ColumnDTO("信贷管护分支编号", "branch", 81));
		columnDTOS.add(new ColumnDTO("信贷管护分支名称", "branchName", 82));
		columnDTOS.add(new ColumnDTO("片区编号", "zoneCode", 83));
		columnDTOS.add(new ColumnDTO("片区名称", "zoneName", 84));
		columnDTOS.add(new ColumnDTO("区域编号", "areaCode", 85));
		columnDTOS.add(new ColumnDTO("区域名称", "areaName", 86));
		columnDTOS.add(new ColumnDTO("信贷管护客户经理工号", "manager", 87));
		columnDTOS.add(new ColumnDTO("信贷管护客户经理名称", "managerName", 88));
		columnDTOS.add(new ColumnDTO("用户工号", "userCode", 89));
		/*columnDTOS.add(new ColumnDTO("SPU外码", "spuOutId", 89));
		columnDTOS.add(new ColumnDTO("货号", "productCode", 90));
		columnDTOS.add(new ColumnDTO("条形码", "barCode", 91));*/
		columnDTOS.add(new ColumnDTO("备注", "orderRemark", 92));
		columnDTOS.add(new ColumnDTO("付款组织", "payOrg", 93));
		// 预付订单需要 展示订金订金金额、尾款金额、支付方式
		if (!CollectionUtils.isEmpty(param.getOrderType()) && param.getOrderType().contains(OrderTypeEnum.PRE_SELL_DEPOSIT.getValue())) {
			columnDTOS.add(new ColumnDTO("订单订金金额", "depositAmount", 60));
			columnDTOS.add(new ColumnDTO("订单尾款金额", "remainAmount", 61));
			columnDTOS.add(new ColumnDTO("组合支付", "composePayName", 92));
		}
		columnDTOS.add(new ColumnDTO("是否可发货", "deliverable", 93));
		columnDTOS.add(new ColumnDTO("分类编码", "categoryCode", 94));
		columnDTOS.add(new ColumnDTO("分类名称", "categoryName", 95));
		columnDTOS.add(new ColumnDTO("发货地址", "deliverAreaInfo", 96));
		columnDTOS.add(new ColumnDTO("订单物流费用", "estimateExpressFeeDesc", 97));
		columnDTOS.add(new ColumnDTO("发货时间要求", "purchaseOrderDeliveryTime", 98));
		columnDTOS.add(new ColumnDTO("金融规则编号", "financeRuleCode", 99));
		columnDTOS.add(new ColumnDTO("金融规则标签", "ruleTag", 100));
		columnDTOS.add(new ColumnDTO("批次号", "batchNo", 101));
		columnDTOS.add(new ColumnDTO("商品规格详情", "productSpecJson", 102));
		columnDTOS.add(new ColumnDTO("单品活动id", "promotionId", 103));
		if (OrderConst.XZ_STORE_ID.equals(param.getStoreId())) {
			columnDTOS.add(new ColumnDTO("回款设置", "retMoneyModelDesc", 104));
		}
		columnDTOS.add(new ColumnDTO("销售季编码", "salesSeasonCode", 105));
		columnDTOS.add(new ColumnDTO("业绩归属人姓名", "belongerName", 106));
		columnDTOS.add(new ColumnDTO("业绩归属人工号", "belongerEmployeeNo", 107));
		columnDTOS.add(new ColumnDTO("业绩归属分支名称", "employeeBranchName", 108));
		columnDTOS.add(new ColumnDTO("业绩归属分支编码", "employeeBranchCode", 109));
		columnDTOS.add(new ColumnDTO("物料分类编码", "productCategoryPath", 110));
		columnDTOS.add(new ColumnDTO("物料分类名称", "productCategoryPathName", 111));
		UserDTO userDTO = param.getUserDTO();
		Integer isSelf = param.getIsSelf();
		if (null != userDTO && userDTO.getUserRole() == OrderConst.LOG_ROLE_VENDOR && Objects.equals(isSelf, CommonEnum.YES.getCode())){
			// 商家端导出、且店铺开启了自提
			columnDTOS.add(new ColumnDTO("自提点编码", "pointId", 112));
			columnDTOS.add(new ColumnDTO("自提点名称", "pointName", 113));
		}
		columnDTOS.add(new ColumnDTO("安装商名称", "installSupplierName", 114));
		columnDTOS.add(new ColumnDTO("安装商联系方式", "installSupplierMobile", 115));
		return columnDTOS;
	}

	@Override
	public List<OrderExportDTO> getDataByPage(OrderListQueryRequest query, Integer pageNum, Integer pageSize) {
		pageNum = 1;
		OrderExample orderExample = OrderQueryBuilder.buildOrderExportDataQueryCondition(query, query.getStoreId());
		orderExample.setPager(new PagerInfo(pageSize, pageNum));
		// 查询订单数据
		List<OrderExportDTO> exportOrderList = orderModel.getExportOrderListByPage(orderExample);
		if (CollectionUtils.isNotEmpty(exportOrderList)) {
			Map<String, OrderPresellDTO> orderPresellDTOMap = null;
			List<String> bizSnList = exportOrderList.stream().map(OrderExportDTO::getOrderSn)
					.collect(Collectors.toList());
			Map<String, String> billList = orderModel.orderBillList(bizSnList);
			// 预付订单需要 展示订金订金金额、尾款金额、支付方式
			if (!CollectionUtils.isEmpty(query.getOrderType())
					&& query.getOrderType().contains(OrderTypeEnum.PRE_SELL_DEPOSIT.getValue())
					&& CollectionUtils.isNotEmpty(bizSnList)) {
				orderPresellDTOMap = orderModel.getPreSellOrderDetailByOrderSnList(bizSnList);
			}
			Map<String, List<OrderPromotionDetailPO>> promotionDetailList = Maps.newHashMap();
			List<OrderPromotionDetailPO> orderPromotionDetailByOrderSnList = promotionDetailService.getOrderPromotionDetailByOrderSnList(bizSnList);
			if (!CollectionUtils.isEmpty(orderPromotionDetailByOrderSnList)){
				promotionDetailList = orderPromotionDetailByOrderSnList.stream().collect(Collectors.groupingBy(OrderPromotionDetailPO::getOrderSn));
			}
			// 获取安装商信息
			List<Long> orderProductIdList = exportOrderList.stream().map(OrderExportDTO::getOrderProductId).distinct().collect(Collectors.toList());
			Map<String, List<BzOrderProductInstallPO>> installSupplierMap = orderModel.buildOrderProductInstallSupplierInfo(orderProductIdList);
			OrderQueryBuilder.buildOrderOrderExportDTO(exportOrderList, billList, orderPresellDTOMap, promotionDetailList, installSupplierMap);
		}
		log.info("【订单导出】getDataByPage，pageNum：{} exportOrderListSize:{}", pageNum,exportOrderList.size());
		return exportOrderList;
	}

}
