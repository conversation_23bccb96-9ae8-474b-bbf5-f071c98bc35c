package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.cmis.common.base.CommonResult;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.req.msg.SendAllMessageRequest;
import com.cfpamf.msgpush.facade.request.MessageHistoryRequest;
import com.cfpamf.msgpush.facade.request.MessagePushRequest;
import com.cfpamf.msgpush.facade.request.MessagePushV2Request;
import com.cfpamf.msgpush.facade.request.SmsRequest;
import com.cfpamf.msgpush.facade.request.SmsV2Request;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "ms-messagepush-service", url = "${ms-messagepush-service.url}")
public interface MessagePushFacade {

    /**
     * 消息推送接口（新）
     *
     * <AUTHOR>
     * @date 2021/6/22 17:38
     * @param request
     * @return com.cfpamf.ms.ercp.biz.fegin.resp.CommonResult
     */
    @PostMapping(value = "/v1/message/sendAllMessageToChannels")
    CommonResult sendAllMessageToChannels(@RequestBody SendAllMessageRequest request);
    
    @PostMapping(value = "/v1/message/sendMessageToChannelsV2")
    CommonResult sendMessageToChannelsV2(@RequestBody MessagePushV2Request request);

    @PostMapping(value = "/v1/message/sendMessageToChannels")
    CommonResult sendMessageToChannels(@RequestBody MessagePushRequest request);

    @PostMapping({"/v1/sms/sendVCode"})
    CommonResult sendVerificationCode(@RequestBody SmsRequest request);

    @PostMapping({"/v1/sms/checkVCode"})
    CommonResult checkVerificationCode(@RequestBody SmsRequest request);

    @PostMapping({"/v1/sms/v2/checkVCode"})
    CommonResult checkVerificationCodeV2(@RequestBody SmsV2Request request);

    @PostMapping({"/v1/notification/sendToAlias"})
    CommonResult sendToAlias(@RequestBody SmsRequest request);

    @PostMapping(value = "/v1/message/repeatPushHistoryMessage")
    CommonResult<Result> repeatPushHistoryMessage(@RequestBody MessageHistoryRequest request);

}
