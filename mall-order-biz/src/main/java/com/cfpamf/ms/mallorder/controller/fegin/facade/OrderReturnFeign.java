package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.ms.mallorder.common.constant.OrdersAfsConst;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.dto.HistoryOrderReturn;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.po.OrderReturn;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.req.ExchangeApplyListReq;
import com.cfpamf.ms.mallorder.request.OrderReturnExample;
import com.cfpamf.ms.mallorder.request.req.AfterSalesRequest;
import com.cfpamf.ms.mallorder.service.IOrderExchangeService;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.cfpamf.ms.mallorder.vo.OrderExchangeReturnListVO;
import com.cfpamf.ms.mallorder.vo.OrderReturnVOV2;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.response.SldResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单退货表feign
 */
@RestController
@Slf4j
public class OrderReturnFeign {

    @Resource
    private IOrderReturnService orderReturnService;

    @Resource
    private OrderReturnModel orderReturnModel;

    @Resource
    private IOrderExchangeService orderExchangeService;

    /**
     * 根据returnId获取订单退货表详情
     *
     * @param returnId returnId
     * @return
     */
    @GetMapping("/v1/feign/business/orderReturn/get")
    public OrderReturnPO getOrderReturnByReturnId(@RequestParam("returnId") Integer returnId) {
        return orderReturnModel.getOrderReturnByReturnId(returnId);
    }

    /**
     * 根据获取订单退货表详情
     *
     * @param afsSn afsSn
     * @return
     */
    @GetMapping("/v1/feign/business/orderReturn/getAfsSn")
    public OrderReturnPO getOrderReturnByAfsSn(@RequestParam("afsSn") String afsSn) {
        return orderReturnModel.getOrderReturnByAfsSn(afsSn);
    }

    /**
     * 获取条件获取订单退货表列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/orderReturn/getList")
    public List<OrderReturnPO> getOrderReturnList(@RequestBody OrderReturnExample example) {
        return orderReturnModel.getOrderReturnList(example, example.getPager());
    }


    /**
     * 根据条件分页查询订单退货
     *
     * @param request 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/orderReturn/getPage")
    public JsonResult<PageVO<OrderReturnVOV2>> getOrderReturnPage(@RequestBody AfterSalesRequest request) {
        PagerInfo pager = request.getPager();
        String memberIdsStr = StringUtils.join(request.getMemberIdList(),",");
        OrderReturnExample example = new OrderReturnExample();
        example.setPager(pager);
        example.setMemberIdIn(memberIdsStr);
        if(Objects.isNull(request.getReturnType())){
            example.setReturnTypeIn(OrdersAfsConst.RETURN_TYPE_1 + "," + OrdersAfsConst.RETURN_TYPE_2);
        }
        List<OrderReturnVOV2> voList = orderReturnModel.getOrderReturnListWithJoin(example, pager);
        return SldResponse.success(new PageVO<>(voList, pager));
    }

    /**
     * 根据条件分页查询换货单
     *
     * @param request 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/exchangeOrder/getPage")
    public JsonResult<PageVO<OrderExchangeReturnListVO>> getExchangeOrderPage(@RequestBody AfterSalesRequest request) {
        PagerInfo pager = request.getPager();
        ExchangeApplyListReq req = new ExchangeApplyListReq();
        req.setAgric(true);
        req.setMemberIdList(request.getMemberIdList());
        List<OrderExchangeReturnListVO> voList = orderExchangeService.getOrderExchangeReturnList(req, pager);
        return SldResponse.success(new PageVO<>(voList, pager));
    }

    /**
     * 新增订单退货表
     *
     * @param orderReturnPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderReturn/addOrderReturn")
    public Integer saveOrderReturn(@RequestBody OrderReturnPO orderReturnPO) {
        return orderReturnModel.saveOrderReturn(orderReturnPO);
    }

    /**
     * 根据returnId更新订单退货表
     *
     * @param orderReturnPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderReturn/updateOrderReturn")
    public Integer updateOrderReturn(@RequestBody OrderReturnPO orderReturnPO) {
        return orderReturnModel.updateOrderReturn(orderReturnPO);
    }

    /**
     * 根据returnId删除订单退货表
     *
     * @param returnId returnId
     * @return
     */
    @PostMapping("/v1/feign/business/orderReturn/deleteOrderReturn")
    public Integer deleteOrderReturn(@RequestParam("returnId") Integer returnId) {
        return orderReturnModel.deleteOrderReturn(returnId);
    }

    /**
     * 拼团订单取消
     *
     * @param orderSns      订单号
     */
    @PostMapping("/v1/feign/business/orderReturn/cancelSpellOrders")
    public void cancelSpellOrders(@RequestParam("orderSns") List<String> orderSns) {
        orderReturnService.cancelSpellOrders(orderSns);
    }

    @GetMapping("/v1/feign/business/orderReturn/getByAfsSn")
    public JsonResult<OrderReturn> getByAfsSn(@RequestParam("afsSn") String afsSn) {
        OrderReturnPO entity = orderReturnService.getByAfsSn(afsSn);
        if (Objects.isNull(entity)) {
            return SldResponse.fail(ErrorCodeEnum.S.DATA_NOT_FOUND.toString(),
                    String.format("查询退货信息为空，请检查退款单号:%s",afsSn));
        }
        return SldResponse.success(orderReturnService.entityToOrderReturn(entity));
    }

    @GetMapping("/v1/feign/business/orderReturn/getByOrderSn")
    public JsonResult<List<HistoryOrderReturn>> getByOrderSn(@RequestParam("orderSn") String orderSn) {
        List<OrderReturnPO> entityList = orderReturnService.getByOrderSn(orderSn);
        if (CollectionUtils.isEmpty(entityList)) {
            return SldResponse.fail(ErrorCodeEnum.S.DATA_NOT_FOUND.toString(),
                    String.format("查询退货信息为空，请检查退款单号:%s",orderSn));
        }
        return SldResponse.success(entityList.stream().map(e -> {
            HistoryOrderReturn orderReturn =new HistoryOrderReturn();
            orderReturn.setOrderSn(e.getOrderSn());
            orderReturn.setAfsSn(e.getAfsSn());
            orderReturn.setStoreId(e.getStoreId());
            return orderReturn;
        }).collect(Collectors.toList()));
    }

}