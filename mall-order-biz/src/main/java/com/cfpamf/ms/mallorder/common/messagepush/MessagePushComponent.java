package com.cfpamf.ms.mallorder.common.messagepush;

import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.cfpamf.cmis.common.base.CommonResult;
import com.cfpamf.ms.mallorder.controller.fegin.facade.MessagePushFacade;
import com.cfpamf.msgpush.facade.request.MessagePushV2Request;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Component
public class MessagePushComponent {

    @Resource
    private MessagePushFacade messagePushFacade;

	public Boolean send(MessagePushV2Request request) throws Exception {
		if(Objects.isNull(request)) {
			log.warn("【MessagePushComponent.send】MessagePushV2Request参数为空，拒绝发送消息通知");
			return false;
		}
		@SuppressWarnings("unchecked")
		CommonResult<Void> result = messagePushFacade.sendMessageToChannelsV2(request);
        return "000000".equals(result.getCode());
    }
}
