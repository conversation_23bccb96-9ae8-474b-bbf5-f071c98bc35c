package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallshop.resp.Vendor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 订单正向操作 service 接口
 *
 * <AUTHOR>
 * @date 2021/5/30 17:00
 * @return
 */
public interface IOrderPlacingService extends IService<OrderPO> {

    /**
     * 渠道订单提交接口 200-下单成功, 6601003-库存不足
     * @param dto
     * @return
     */
    ChannelOrderSubmitVO submitChannelOrder(@RequestBody ChannelOrderSubmitDTO dto);

    /**
     * 创建订单接口
     *
     * @param req
     * @return long
     * <AUTHOR>
     * @date 2021/5/30 17:01
     */
    List<String> createOrder(OrderSubmitMqConsumerDTO req);

    /**
     * 订单分账
     *
     * <AUTHOR>
     * @date 2021/7/18 15:00
     * @return
     */
    void profitWithBackup(String orderSn);

    /**
     * 订单状态-关闭
     *
     * @param orderSn
     * @return boolean
     * <AUTHOR>
     * @date 2021/08/04 17:01
     */
    boolean closeOrderStatus(String orderSn);
    boolean updateOrderStatus(String orderSn,int orderState);

    OrderPO getByOrderSn(String orderSn);


    /**
     * 采购订单确认下单
     *
     * @param submitParam   入参信息
     * @param member        用户信息
     * @return              提交订单页视图
     */
    OrderSubmitPageVO confirmPurchaseOrder(OrderSubmitParamDTO submitParam, Member member);


    /**
     * 下单前校验
     *
     * @param submitParam   提交的入参
     * @param member        用户信息
     * @return              校验结果
     */
    OrderSubmitCheckVO checkPurchaseOrder(OrderSubmitParamDTO submitParam, Member member);

    /**
     * 采购订单提交订单
     *
     * @param submitParam   入参信息
     * @param member        用户信息
     * @return              提交订单页视图
     */
    Map<String, String> submitPurchaseOrder(OrderSubmitParamDTO submitParam, Member member);

    /**
     * 卡券订单提交订单
     *
     * @param submitParam 入参信息
     * @param member      用户信息
     * @return 提交订单页视图
     */
    ChannelOrderSubmitVO submitCouponOrder(OrderSubmitParamDTO submitParam, Member member, ChannelCouponOrderSubmitDTO channelSubmitDTO);
    
    
    /**
     * 线下补录订单
     * 
     * @param dto
     * @return
     */
    OrderSubmitVO createOfflineOrder(OrderOfflineParamDTO dto);

    /**
     * 导入线下补录订单
     * @return
     */
    ExportingFailedDataToExcelVO importOfflineOrder(Vendor vendor, MultipartFile excelFile) throws Exception;

    /**
     * 创建批量订单
     * @param dto
     * @return
     */
	OrderSubmitVO createBatchOrder(@Valid OrderParamDTO dto);

	/**
	 * 创建拼单订单
	 * @param dto
	 * @return
	 */
    OrderSubmitVO createGroupOrder(GroupBuyingOrderSubmitDTO dto, UserDTO userDTO);

    /**
     * 创建返利赠品订单
     *
     * @param dto 参数dto
     * @return 创建结果
     */
    OrderSubmitVO createRebateGiftOrder(RebateOrderSubmitDTO dto);

    /**
     * 缓存员工分享确认订单信息
     * @param dto
     */
    FastOrderConfirmVO cacheConfirm(OrderCacheConfirmDTO dto);


    /**
     * 快捷下单-获取分享缓存数据
     * @param dto
     * @return
     */
    FastOrderConfirmVO getCacheConfirm(OrderGetCacheConfirmDTO dto,Integer memberId);
}
