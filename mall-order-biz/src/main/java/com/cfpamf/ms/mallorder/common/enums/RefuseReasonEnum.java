package com.cfpamf.ms.mallorder.common.enums;

import com.alibaba.fastjson.annotation.JSONType;
import lombok.Getter;

/**
 * 平台端终止原因枚举
 */
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum RefuseReasonEnum {

    SETTLE_WITH_OFFLINE_REFUND("1", "借据已结清，需线下退款"),
    SETTLE_WITHOUT_OFFLINE_REFUND("2", "借据已结清，无需线下退款"),
    OVERDUE_REFUND("3", "支付宝/微信退款超期，需线下退款"),
    BUSINESS_CONFIRMATION_TERMINATION("4", "业务确认终止"),
    OTHER_REASON("5", "其它原因");

    private String value;
    private String desc;

    RefuseReasonEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static RefuseReasonEnum getEnumByValue(String value) {
        for (RefuseReasonEnum e : RefuseReasonEnum.values()) {
            if (value.equals(e.value)) {
                return e;
            }
        }
        return null;
    }

}
