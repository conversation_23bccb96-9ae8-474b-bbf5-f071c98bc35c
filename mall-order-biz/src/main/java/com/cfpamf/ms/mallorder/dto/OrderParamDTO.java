package com.cfpamf.ms.mallorder.dto;

import java.io.Serializable;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.cfpamf.ms.mallorder.enums.OrderPlaceUserRole;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class  OrderParamDTO implements java.io.Serializable {


	private static final long serialVersionUID = 683011422865812319L;

	@NotBlank(message = "下单渠道，不能为空")
	@ApiModelProperty(value = "下单渠道：H5-浏览器H5, APP-乡助APP, WE_CHAT-微信浏览器" +
			", MINI_PRO-小程序, OMS-运管平台, SELLER_WEB-商家后台，BAPP-员工APP" +
			", XXAPP-乡信APP,XX_MINI_PRO-乡信小程序", required = true)
	private String channel;

	@ApiModelProperty(value = "订单模式：1、C端订单（默认值），2、采购订单")
	private Integer orderPattern = 1;

	@ApiModelProperty("订单类型: 1-普通订单; 103-预售订单; 104- 秒杀订单; 107-预付订单")
	private Integer orderType = 1;

	@ApiModelProperty(value = "1==立即购买、去结算;2==提交订单页，修改优惠券、修改积分、修改地址，更新页面数据;3==提交订单", required = true)
	@NotNull(message = "订单source不能为空")
	private Integer source;

	@ApiModelProperty(value = "使用多张平台优惠券编码")
	private List<String> platformCouponCodeList;
	@Valid
	@NotEmpty(message = "下单店铺,不能为空")
	@ApiModelProperty(value = "下单店铺信息")
	private List<StoreInfo> storeInfoList;

	@Valid
    //@NotNull(message = "收件人信息，不能为空")  协助下单支持自提订单，此处去掉收货地址必传校验
	@ApiModelProperty(value = "收件人信息", required = true)
	private OrderAddressDTO address;
	
	@Valid
	@NotNull (message = "下单用户角色不能为空")
	@ApiModelProperty(value = "下单用户角色:本人：SELF，客户经理（BAPP客户经理代客下单传入）：CUSTOMER_MANAGER，站长：STATION_MASTER", required = true)
	private OrderPlaceUserRole orderPlaceUserRole;

	@ApiModelProperty(value = "贷款支付人：SELF-本人(社员);STATION_MASTER-站长;下单渠道为乡信APP或乡信小程序则必填")
	private String loanPayer;

	@ApiModelProperty(value = "贷款确认方式：READ-完成订单阅读确认(客户端);FACE_DETECTION-完成订单人脸识别（站长端）;下单渠道为乡信APP或乡信小程序则必填")
	private String loanConfirmMethod;

	@ApiModelProperty(value = "附件", required = true)
	private List<String> attachmentUrls;

	@ApiModelProperty(value = "验证码")
	private String verifyCode;

	@ApiModelProperty(value = "活动相关信息参数，暂时存放的预售信息")
	private OrderPromotionParamDTO orderPromotionInfo;

    @ApiModelProperty(value = "自提点id,自提订单使用")
	private Long pointId;
    /*
     * 金融规则以店铺为维度区分,不同店铺金融规则可以不一样
     *
     */
	@Data
	public static class StoreInfo implements Serializable {
		private static final long serialVersionUID = 79094261106392571L;
		@ApiModelProperty(value = "店铺id", required = true)
		private Long storeId;

		@ApiModelProperty(value = "会员发票id")
		private Integer invoiceId;

		@ApiModelProperty(value = "使用多张店铺优惠券编码")
		private List<String> storeCouponCodeList;

		@ApiModelProperty(value = "给商家留言")
		private String remark;

		@ApiModelProperty(value = "下单地区编码")
		private String areaCode;

		@ApiModelProperty(value = "金融规则编号")
		private String financeRuleCode;

		@Valid
		@NotEmpty(message = "购买商品，不能为空")
		@ApiModelProperty(value = "购买商品信息，不能为空", required = true)
		private List<OrderSkuInfoDTO> skuInfoList;
	}

	@ApiModelProperty(value = "下单附加信息（乡信app）")
	private OrderAgricInfoDto orderAgricInfoDto;

}
