package com.cfpamf.ms.mallorder.v2.service.impl;

import java.time.format.DateTimeFormatter;
import java.util.*;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.po.OrderPayRecordPO;
import com.cfpamf.ms.mallorder.v2.service.OrderPayRecordService;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.mapper.OrderPresellMapper;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderPresellPO;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.cfpamf.ms.mallorder.v2.domain.dto.OrderPresellDTO;
import com.cfpamf.ms.mallorder.v2.domain.vo.OperationUserVO;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.vo.OrderPresellVO;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.slodon.bbc.core.exception.BusinessException;

import lombok.extern.slf4j.Slf4j;



@Service
@Slf4j
public class OrderPresellServiceImpl extends ServiceImpl<OrderPresellMapper, OrderPresellPO>
    implements OrderPresellService {

    public static final String FULL_FORMATTER_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern(FULL_FORMATTER_PATTERN);

    @Autowired
    private IOrderReturnService orderReturnService;

    @Autowired
    private OrderModel orderModel;

    @Resource
    private OrderPresellMapper orderPresellMapper;
    @Autowired
    private OrderPayRecordService orderPayRecordService;

    @Override
    public boolean verifyPayOrderAllFinishByPaySn(String paySn) {
        if (StringUtils.isEmpty(paySn)) {
            return false;
        }
        QueryWrapper<OrderPresellPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("pay_sn", paySn);
        queryWrapper.eq("pay_status", CommonConst.PAY_STATUS_1);// 待支付
        int total = super.count(queryWrapper);
        return total == 0;
    }
    
    @Override
    public boolean verifyPayOrderFinishByPaySn(String paySn,Integer type) {
        if (StringUtils.isEmpty(paySn)) {
            return false;
        }
        QueryWrapper<OrderPresellPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("pay_sn", paySn);
        queryWrapper.eq("pay_status", CommonConst.PAY_STATUS_1);// 待支付
        queryWrapper.eq(Objects.nonNull(type),"type",type);
        int total = super.count(queryWrapper);
        return total > 0;
    }

    @Override
    public boolean verifyOrderWaitPayBalanceByPaySn(String paySn) {
        if (StringUtils.isEmpty(paySn)) {
            return false;
        }
        QueryWrapper<OrderPresellPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("pay_sn", paySn);
        queryWrapper.eq("pay_status", CommonConst.PAY_STATUS_1);// 待支付
        queryWrapper.eq("type",2);
        int total = super.count(queryWrapper);
        return total >0;
    }

    @Override
    public boolean updatePaySuccessByPayNo(OrderPresellPO orderPresell) {
        if (ObjectUtils.isEmpty(orderPresell)) {
            return false;
        }
        if (StringUtils.isEmpty(orderPresell.getPayNo())) {
            return false;
        }
        if (ObjectUtils.isEmpty(orderPresell.getPayStatus())) {
            return false;
        }
        LambdaUpdateWrapper<OrderPresellPO> updateOrderPresellWrapper = Wrappers.lambdaUpdate();
        updateOrderPresellWrapper.eq(OrderPresellPO::getPayNo, orderPresell.getPayNo());
        updateOrderPresellWrapper.in(OrderPresellPO::getPayStatus, Arrays.asList(CommonConst.PAY_STATUS_1,CommonConst.PAY_STATUS_2));
        return super.update(orderPresell, updateOrderPresellWrapper);
    }

    @Override
    public void cancelPresellOrder(OrderPO orderPo, OperationUserVO operator) {
        log.info("cancelPresellOrder 预售订单取消 >>>>>>>>>>>>>> 订单信息:【{}】", orderPo.toString());
        if (orderPo.getOrderType() != PromotionConst.PROMOTION_TYPE_107) {
            log.warn(">>>>>>>>>>>>>>>>> 非预售订单进入预售订单取消，订单号:【{}】", orderPo.getOrderSn());
            return;
        }
        // 预售订单，未付定金取消
        if (orderPo.getOrderState().equals(OrderStatusEnum.WAIT_PAY_DEPOSIT.getValue())) {
            orderReturnService.orderCancelWithoutRefund(orderPo, operator.getOperationRemark(), "",
                    (long) operator.getOperationUserId(), operator.getOperationUserName());
        } else if (orderPo.getOrderState().equals(OrderStatusEnum.WAIT_PAY.getValue())) {
            BizAssertUtil.isTrue(operator.getReturnBy() == OrderConst.RETURN_BY_2, "商家禁止取消只支付定金的预付订单");
            // 预售订单，已付定金未付尾款取消
            orderModel.orderCancelInsertAfterServiceAndReturn(orderPo, operator.getOperationRemark(), operator.getOperationRemark(),
                    operator.getReturnBy(), operator.getOperationRole(), (long) operator.getOperationUserId(), operator.getOperationUserName(), null);
        } else {
            throw new BusinessException(String.format("预付订单%s当前状态为%s不支持取消，请检查~", orderPo.getOrderSn(), OrderStatusEnum.valueOf(orderPo.getOrderState()).getDesc()));
        }

    }

    @Override
    public boolean closeBankTransferOrder(String paySn) {
        log.info("cancelPresellOrder 预售订单取消 >>>>>>>>>>>>>>关闭银行卡汇款 订单信息:【{}】", paySn);
        this.lambdaUpdate().eq(OrderPresellPO::getPaymentCode, PayMethodEnum.BANK_TRANSFER.getValue())
                .eq(OrderPresellPO::getPayStatus, CommonConst.PAY_STATUS_2)
                .eq(OrderPresellPO::getPaySn,paySn)
                .set(OrderPresellPO::getPayStatus, CommonConst.PAY_STATUS_1)
                .update();
        orderPayRecordService.lambdaUpdate().eq(OrderPayRecordPO::getPaymentCode, PayMethodEnum.BANK_TRANSFER.getValue())
                .eq(OrderPayRecordPO::getPayStatus, CommonConst.PAY_STATUS_2)
                .eq(OrderPayRecordPO::getPaySn,paySn)
                .set(OrderPayRecordPO::getPayStatus, CommonConst.PAY_STATUS_1)
                .update();
        return true;
    }

    @Override
    public List<OrderPresellVO> listByOrderSn(String orderSn,Integer type) {
    	if (StringUtils.isEmpty(orderSn)) {
            return null;
        }
        LambdaQueryWrapper<OrderPresellPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPresellPO::getOrderSn,orderSn);
        if(null!=type){
            queryWrapper.eq(OrderPresellPO::getType,type);
        }
        List<OrderPresellPO> entityList = this.list(queryWrapper);
        if (ObjectUtils.isEmpty(entityList)) {
            return null;
        }
        List<OrderPresellVO> voList = new ArrayList<>(entityList.size());
        for (OrderPresellPO entity : entityList) {
            voList.add(this.entity2VO(entity));
        }
        return voList;
    }
    
    @Override
    public OrderPresellPO queryByPayNo(String payNo) {
        if (StringUtils.isEmpty(payNo)) {
            return null;
        }
        LambdaQueryWrapper<OrderPresellPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPresellPO::getPayNo,payNo);
        OrderPresellPO entity = this.getOne(queryWrapper);
        if (ObjectUtils.isEmpty(entity)) {
            return null;
        }
        return entity;
    }
    
    @Override
    public OrderPresellPO queryBalanceInfoByOrderSn(String orderSn) {
    	if (StringUtils.isEmpty(orderSn)) {
            return null;
        }
        LambdaQueryWrapper<OrderPresellPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPresellPO::getOrderSn,orderSn);
        queryWrapper.eq(OrderPresellPO::getType,2);
        return super.getOne(queryWrapper);
    }
    
    @Override
    public OrderPresellPO queryBalanceInfoByPayNo(String payNo) {
    	if (StringUtils.isEmpty(payNo)) {
           return null;
        }
        LambdaQueryWrapper<OrderPresellPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPresellPO::getPayNo,payNo);
        queryWrapper.eq(OrderPresellPO::getType,2);
        return super.getOne(queryWrapper);
    }

    @Override
    public OrderPresellVO findByPayNo(String payNo) {
    	if (StringUtils.isEmpty(payNo)) {
            return null;
        }
        LambdaQueryWrapper<OrderPresellPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPresellPO::getPayNo,payNo);
        OrderPresellPO entity = this.getOne(queryWrapper);
        if (ObjectUtils.isEmpty(entity)) {
            return null;
        }
        return this.entity2VO(entity);
    }

    private OrderPresellVO entity2VO(OrderPresellPO entity){
        OrderPresellVO vo = new OrderPresellVO();
        vo.setId(entity.getId());
        vo.setOrderSn(entity.getOrderSn());
        vo.setPaySn(entity.getPaySn());
        vo.setPayNo(entity.getPayNo());
        vo.setTotalAmount(entity.getTotalAmount());
        vo.setAmount(entity.getAmount());
        vo.setDiscountAmount(entity.getDiscountAmount());
        vo.setPayAmount(entity.getPayAmount());
        vo.setChannelServiceFee(entity.getChannelServiceFee());
        vo.setChannelServiceRate(entity.getChannelServiceRate());
        if(null != entity.getDeadTime()){
            vo.setDeadTime(entity.getDeadTime().format(DATETIME_FORMATTER));
        }
        vo.setType(entity.getType());
        vo.setPaymentCode(entity.getPaymentCode());
        vo.setPaymentName(entity.getPaymentName());
        vo.setPayStatus(entity.getPayStatus());
        if(null != entity.getPayTime()){
            vo.setPayTime(entity.getPayTime().format(DATETIME_FORMATTER));
        }
        return vo;
    }

    /**
     * 根据订单号获取预售订单信息
     * */
    @Override
    public OrderPresellDTO getDepositOrderDetail(String orderSn){
        OrderPresellDTO orderPresellDTO = new OrderPresellDTO();
        orderPresellDTO.setOrderSn(orderSn);
        List<OrderPresellPO> orderPreSellPOList = orderPresellMapper.getPreSellOrderDetailByOrderSn(orderSn);
        orderPreSellPOList.sort(Comparator.comparing(OrderPresellPO::getType));

        if(CollectionUtils.isNotEmpty(orderPreSellPOList)){
            orderPresellDTO = convertOrderPresellPOToDTO(orderPreSellPOList);
        }

        return orderPresellDTO;
    }

    /**
     * 根据订单号获取预售订单信息列表
     * @param orderPreSellPOList 订单号列表
     * @return List<OrderPresellDTO>
     * */
    @Override
    public OrderPresellDTO convertOrderPresellPOToDTO(List<OrderPresellPO> orderPreSellPOList){
        OrderPresellDTO orderPresellDTO = new OrderPresellDTO();
        for(OrderPresellPO orderPresellPO : orderPreSellPOList){
            orderPresellDTO.setOrderSn(orderPresellPO.getOrderSn());
            //订金
            if(orderPresellPO.getType() == 1){
                orderPresellDTO.setDepositPaymentCode(orderPresellPO.getPaymentCode());
                orderPresellDTO.setDepositPayStatus(orderPresellPO.getPayStatus());
                orderPresellDTO.setDepositPayAmount(orderPresellPO.getPayAmount());
                orderPresellDTO.setDepositPayNo(orderPresellPO.getPayNo());
                continue;
            }
            //尾款
            if(orderPresellPO.getType() == 2){
                orderPresellDTO.setRemainPaymentCode(orderPresellPO.getPaymentCode());
                orderPresellDTO.setRemainPayStatus(orderPresellPO.getPayStatus());
                orderPresellDTO.setRemainPayAmount(orderPresellPO.getPayAmount());
                orderPresellDTO.setRemainTotalAmount(orderPresellPO.getAmount());
                orderPresellDTO.setRemainPayNo(orderPresellPO.getPayNo());
                continue;
            }
        }
        return orderPresellDTO;
    }

    @Override
    public JsonResult<Boolean> checkPresellPayMethod(String paySn) {
        List<OrderPresellPO> list = lambdaQuery()
                .eq(OrderPresellPO::getPaySn, paySn)
                .eq(OrderPresellPO::getPayStatus, CommonConst.PAY_STATUS_2)
                .eq(OrderPresellPO::getPaymentCode, PayMethodEnum.BANK_TRANSFER.getValue())
                .list();
        return SldResponse.success(CollectionUtils.isNotEmpty(list));
    }

}
