package com.cfpamf.ms.mallorder.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.ms.mallorder.common.config.OrderPerformanceStoreConfig;
import com.cfpamf.ms.mallorder.common.config.OrderReceiveCodeConfig;
import com.cfpamf.ms.mallorder.common.config.OrderReceiveTimeDelay;
import com.cfpamf.ms.mallorder.common.constant.PerformanceBelongsConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.constant.PromotionConst;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.erp.ERPIntegration;
import com.cfpamf.ms.mallorder.integration.facade.TemplateMessageFacade;
import com.cfpamf.ms.mallorder.integration.facade.dto.ErpStockProductDTO;
import com.cfpamf.ms.mallorder.integration.hrms.HrmsIntegration;
import com.cfpamf.ms.mallorder.integration.messagepush.MessagePushIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.mapper.OrderPromotionDetailMapper;
import com.cfpamf.ms.mallorder.model.OrderExtendModel;
import com.cfpamf.ms.mallorder.model.OrderLogModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.OrderDeliveryReq;
import com.cfpamf.ms.mallorder.request.OrderPromotionDetailExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.vo.hrmsVO.OutBranchLifeAndInteriorInfoVO;
import com.cfpamf.ms.mallshop.api.StoreIsolateWhiteListFeignClient;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.msgpush.facade.enums.ReceiverTypeEnum;
import com.cfpamf.msgpush.facade.request.templateMessage.BatchBizMessageSubReq;
import com.cfpamf.msgpush.facade.request.templateMessage.BatchBizMessageTemplateReq;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Sets;
import com.slodon.bbc.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zml
 * @CreateTime: 2023/8/2 11:27
 */
@Service
@Slf4j
public class PerformanceServiceImpl implements IPerformanceService {


    @Value("#{'${self-lift-order.auto-delivery-store-list}'.split(',')}")
    private List<Long> autoDeliveryStoreIdList;

    @Autowired
    OrderPerformanceStoreConfig orderPerformanceStoreConfig;
    @Resource
    private ERPIntegration erpIntegration;

    @Resource
    private MessagePushIntegration messagePushIntegration;

    @Resource
    private OrderProductModel orderProductModel;

    @Resource
    private OrderProductMapper orderProductMapper;

    @Resource
    private OrderExtendModel orderExtendModel;

    @Resource
    private OrderModel orderModel;

    @Resource
    private IOrderExtendService orderExtendService;

    @Resource
    private OrderReceiveCodeConfig orderReceiveCodeConfig;

    @Resource
    private OrderLogModel orderLogModel;

    @Resource
    private OrderReceiveTimeDelay orderReceiveTimeDelay;

    @Resource
    private IOrderLogisticService orderLogisticService;
    @Autowired
    private IOrderPerformanceBelongsService orderPerformanceBelongsService;

    @Autowired
    private IOrderService orderService;

    @Autowired
    private IOrderProductService orderProductService;

    @Autowired
    private OrderPromotionDetailMapper orderPromotionDetailMapper;

    @Resource
    private TemplateMessageFacade templateMessageFacade;

    @Autowired
    private HrmsIntegration hrmsIntegration;

    @Autowired
    private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;


    /**
     * ERP履约渠道--商品发货检查
     *
     * @param orderPODb
     * @param productPOList
     * @param orderExtendPO
     */
    @Override
    public void erpOutBoundCheck(OrderPO orderPODb, List<OrderProductPO> productPOList, OrderExtendPO orderExtendPO, OrderDeliveryReq deliveryReq) {

        List<ErpStockProductDTO> productDetail = new ArrayList<>();
        productPOList.stream().forEach(productPO -> {
            ErpStockProductDTO erpStockProductDTO = new ErpStockProductDTO();
            int operNumber = getDeliveryNum(productPO.getOrderProductId(), deliveryReq.getOrderProductDetail());
            erpStockProductDTO.setOperNumber(operNumber);
            erpStockProductDTO.setSkuId(productPO.getChannelSkuId());
            erpStockProductDTO.setSkuUnit(productPO.getChannelSkuUnit());
            productDetail.add(erpStockProductDTO);

        });
        if (CollectionUtils.isNotEmpty(productDetail)) {
            boolean isSelfLiftFlag = Objects.equals(orderPODb.getOrderPattern(), OrderPatternEnum.SELF_LIFT.getValue());
            int isSelfLift = isSelfLiftFlag ? 1 : 2;
            String depotCode = isSelfLiftFlag ? orderExtendPO.getWarehouseCode() : deliveryReq.getDeliveryWarehouse();
            if (orderPerformanceStoreConfig.getDeliveryCheckFlag() && !orderPerformanceBelongsService.verifyBoundBelongerByOrderSn(orderPODb.getOrderSn())) {
                throw new BusinessException("订单业绩归属未绑定或未生效，请联系管理员");
            }
            OrderPerformanceBelongsPO performanceBelongsPO = orderPerformanceBelongsService.getEntityByOrderSn(orderPODb.getOrderSn());
            if (!orderPerformanceStoreConfig.getDeliveryCheckFlag()) {
                if (Objects.isNull(performanceBelongsPO)) {
                    performanceBelongsPO = new OrderPerformanceBelongsPO();
                }
            }
            erpIntegration.stockOutPreCheck(orderPODb.getOrderSn(), orderPODb.getStoreId(), isSelfLift, orderExtendPO.getBranch(), depotCode, productDetail, performanceBelongsPO,orderExtendPO.getActualWarehouseCode(),deliveryReq);
        }

    }


    @Override
    public boolean outbounding(String orderSn) {
        //存在出库中商品不允许售后
        List<OrderProductPO> productPOList = orderProductModel.getOrderProductListByOrderSn(orderSn);
        BizAssertUtil.notEmpty(productPOList, String.format("%s该订单无商品信息，请检查", orderSn));

        List<Integer> productDeliveryStateList = productPOList.stream().map(x -> x.getDeliveryState().getValue()).collect(Collectors.toList());

        return CollectionUtils.isNotEmpty(productDeliveryStateList) && productDeliveryStateList.contains(OrderProductDeliveryEnum.OUTBOUND.getValue());

    }

    @Override
    public ErpPerformanceStock buildErpOutBoundParam(List<Long> orderProductIds,
                                                     OrderDeliveryReq deliveryReq,
                                                     OrderPO orderPODb,
                                                     Vendor vendor) {

        ErpPerformanceStock erpPerformanceStock = new ErpPerformanceStock();
        OrderExtendPO extendPO = orderExtendModel.getOrderExtendByOrderSn(orderPODb.getOrderSn());
        erpPerformanceStock.setActivityType(orderPODb.getOrderType());

        //订单活动
        OrderPromotionDetailExample example = new OrderPromotionDetailExample();
        example.setOrderSn(orderPODb.getOrderSn());
        example.setPromotionType(PromotionConst.PROMOTION_TYPE_402);
        List<OrderPromotionDetailPO> orderPromotionDetailPOList = orderPromotionDetailMapper.listByExample(example);
        erpPerformanceStock.setUseCoupon(!CollectionUtils.isEmpty(orderPromotionDetailPOList));
        erpPerformanceStock.setActivityCode(orderPODb.getPromotionId());
        // 订单类型
        erpPerformanceStock.setOrderType(orderPODb.getOrderType());
        erpPerformanceStock.setOrderPattern(orderPODb.getOrderPattern());
        erpPerformanceStock.setOrderChannel(orderPODb.getChannel());
//        if (CollectionUtils.isNotEmpty(orderPromotionDetailPOList)) {
//            erpPerformanceStock.setActivityCode(orderPODb.getPromotionId());
//        }
        Integer performanceMode = 2;
        if (OrderPatternEnum.SELF_LIFT.getValue().equals(orderPODb.getOrderPattern())) {
            performanceMode = 1;
            erpPerformanceStock.setDepotCode(extendPO.getWarehouseCode());
            erpPerformanceStock.setActualDepotCode(extendPO.getActualWarehouseCode());
        } else {
            erpPerformanceStock.setDepotCode(deliveryReq.getDeliveryWarehouse());
        }

        erpPerformanceStock.setChannel(1);
        erpPerformanceStock.setBusinessNo(deliveryReq.getOrderSn());
        erpPerformanceStock.setBusinessType(1);
        erpPerformanceStock.setType(2);
        erpPerformanceStock.setStoreId(orderPODb.getStoreId());
        erpPerformanceStock.setPerformanceMode(performanceMode);

        erpPerformanceStock.setBranch(extendPO.getBranch());

        OrderPerformanceBelongsPO performanceBelongsEntity = orderPerformanceBelongsService.getEntityByOrderSn(orderPODb.getOrderSn());
        if (!orderPerformanceStoreConfig.getDeliveryCheckFlag()) {
            if (Objects.isNull(performanceBelongsEntity)) {
                performanceBelongsEntity = new OrderPerformanceBelongsPO();
            }
        }
        erpPerformanceStock.setPerformanceType(performanceBelongsEntity.getPerformanceType());
        erpPerformanceStock.setBelongerEmployeeNo(performanceBelongsEntity.getBelongerEmployeeNo());
        erpPerformanceStock.setEmployeeBranchCode(performanceBelongsEntity.getEmployeeBranchCode());
        List<PerformanceSkuItemDTO> performanceSkuItemList = new ArrayList<>();
        List<OrderProductPO> orderProductPOList = orderProductMapper.selectBatchIds(orderProductIds);
        orderProductPOList.forEach(x -> {
            PerformanceSkuItemDTO performanceSkuItemDTO = new PerformanceSkuItemDTO();
            performanceSkuItemDTO.setSkuId(x.getChannelSkuId());
            performanceSkuItemDTO.setSkuUnit(x.getChannelSkuUnit());
            //performanceSkuItemDTO.setOperNumber(x.getProductNum() - x.getReturnNumber());
            performanceSkuItemDTO.setOperNumber(getDeliveryNum(x.getOrderProductId(), deliveryReq.getOrderProductDetail()));
            PerformanceSkuItemDTO.PerformanceSkuItemExtraInfo performanceSkuItemExtraInfo = new PerformanceSkuItemDTO.PerformanceSkuItemExtraInfo();
            performanceSkuItemExtraInfo.setProductId(x.getProductId());
            performanceSkuItemExtraInfo.setPurchaseSubCode(x.getPurchaseSubCode());
            performanceSkuItemExtraInfo.setMoneyAmount(x.getMoneyAmount());
            performanceSkuItemExtraInfo.setProductNum(x.getProductNum());
            performanceSkuItemExtraInfo.setDiscountAmount(x.getActivityDiscountAmount());
            performanceSkuItemExtraInfo.setGoodsAmount(x.getGoodsAmountTotal());
            performanceSkuItemExtraInfo.setTaxPrice(x.getTaxPrice());
            performanceSkuItemExtraInfo.setActivityProductType(x.getIsGift());
            performanceSkuItemDTO.setExtraInfo(performanceSkuItemExtraInfo);
            performanceSkuItemList.add(performanceSkuItemDTO);
        });
        erpPerformanceStock.setPerformanceSkus(performanceSkuItemList);
        PerformanceStockExtraInfo performanceStockExtraInfo = new PerformanceStockExtraInfo();
        performanceStockExtraInfo.setBusinessAmount(orderPODb.getOrderAmount());
        performanceStockExtraInfo.setBusinessPayTime(orderPODb.getPayTime());
        erpPerformanceStock.setExtraInfo(performanceStockExtraInfo);
        PerformanceStockDeliverInfo performanceStockDeliverInfo = new PerformanceStockDeliverInfo();
        performanceStockDeliverInfo.setDeliverType(deliveryReq.getDeliverType());
        performanceStockDeliverInfo.setExpressId(deliveryReq.getExpressId());
        performanceStockDeliverInfo.setExpressName(deliveryReq.getExpressName());
        performanceStockDeliverInfo.setExpressCompanyCode(deliveryReq.getExpressCompanyCode());
        performanceStockDeliverInfo.setExpressNumber(deliveryReq.getExpressNumber());
        performanceStockDeliverInfo.setDeliverName(deliveryReq.getDeliverName());
        performanceStockDeliverInfo.setDeliverMobile(deliveryReq.getDeliverMobile());
        performanceStockDeliverInfo.setSenderName(vendor.getVendorName());
        performanceStockDeliverInfo.setConsigneeName(extendPO.getReceiverName());
        performanceStockDeliverInfo.setConsigneeMobile(extendPO.getReceiverMobile());
        performanceStockDeliverInfo.setAllowNoLogistics(deliveryReq.getAllowNoLogistics());
        erpPerformanceStock.setDeliverInfo(performanceStockDeliverInfo);

        erpPerformanceStock.setExternalOrderNo(deliveryReq.getExternalOrderNo());
        erpPerformanceStock.setOutboundOrderNo(deliveryReq.getOutboundOrderNo());
        erpPerformanceStock.setActualDepotId(deliveryReq.getActualDepotId());
        log.info("buildErpOutBoundParam result:{}",erpPerformanceStock);
        return erpPerformanceStock;
    }

    private int getDeliveryNum(Long orderProductId, List<OrderProductDeliverDTO> orderProductDeliverDTOList) {
        for (OrderProductDeliverDTO orderProductDeliverDTO : orderProductDeliverDTOList) {
            if (Objects.equals(orderProductDeliverDTO.getOrderProductId(), orderProductId)) {
                return orderProductDeliverDTO.getDeliveryNum();
            }
        }
        log.error("未匹配到发货商品，发货数量为0，请检查!");
        return 0;
    }

    @Override
    public void sendSmsReceiveCodeForDelivery(String orderSn) {
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
        BizAssertUtil.notNull(orderPO, "查下不到订单该订单，请检查" + orderSn);

        //指定店铺需要发送订单签收码
        if (!isReceiveCodeStore(orderPO.getStoreId())) {
            return;
        }
        OrderExtendPO extendPO = orderExtendModel.getOrderExtendByOrderSn(orderSn);
        if (StringUtils.isNotEmpty(extendPO.getReceiveCode())) {
            return;
        }
        //生成签收码
        getOrderReceiveCode(orderSn);

        //无物流发货
        List<OrderLogisticPO> logisticPOList = orderLogisticService.getOrderLogisticByOrderSn(orderPO.getOrderSn(), null);
        //白名单店铺及无物流发货方式，延迟签收时间为45天
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(logisticPOList)) {
            long deliverType = logisticPOList.stream().filter(x -> x.getDeliverType().equals(OrderConst.DELIVER_TYPE_1)).count();
            if (deliverType == 0) {
                return;
            }
            //发送签收码短信
            sendSmsDeliveryCode(orderSn, orderPO.getUserMobile());
        }
    }

    @Override
    public String sendSmsReceiveCode(String orderSn, String userMobile) {
        String receiveCode = getOrderReceiveCode(orderSn);
        BizAssertUtil.isTrue(StringUtils.isEmpty(receiveCode), "订单签收短信验证码为空，请检查！");
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("receiveCode", receiveCode);
        paramMap.put("orderSn", orderSn);
        messagePushIntegration.sendSms(OrderConst.ORDER_RECEIVE_BIZ_TYPE, ReceiverTypeEnum.CUSTMER_MOBILE, userMobile, paramMap);
        return receiveCode;
    }


    @Override
    public String sendSmsDeliveryCode(String orderSn, String userMobile) {
        String receiveCode = getOrderReceiveCode(orderSn);
        BizAssertUtil.isTrue(StringUtils.isEmpty(receiveCode), "订单签收短信验证码为空，请检查！");
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("receiveCode", receiveCode);
        paramMap.put("orderSn", orderSn);
        messagePushIntegration.sendSms(OrderConst.ORDER_DELIVERY_BIZ_TYPE, ReceiverTypeEnum.CUSTMER_MOBILE, userMobile, paramMap);
        return receiveCode;
    }

    @Override
    public String sendSmsReceiveCodeAndInsertLog(String orderSn, UserDTO userDTO, OrderCreateChannel channel) {
        // 订单信息
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
        BizAssertUtil.notNull(orderPO, "查下不到订单该订单，请检查" + orderSn);
        // 获取签收码
        String receiveCode = sendSmsReceiveCode(orderSn, orderPO.getUserMobile());

        //记录轨迹
        orderLogModel.insertOrderLog(userDTO.getUserRole(), Long.valueOf(userDTO.getUserId()), userDTO.getUserName(), orderPO.getOrderSn(), orderPO.getOrderState(),
                orderPO.getOrderState(), LoanStatusEnum.valueOf(orderPO.getLoanPayState()).getValue(), "获取签收码", channel);
        return receiveCode;

    }


    @Override
    public String getOrderReceiveCode(String orderSn) {
        String receiveCode = null;
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
        BizAssertUtil.notNull(orderPO, "查下不到订单该订单，请检查" + orderSn);
        OrderExtendPO extendPO = orderExtendModel.getOrderExtendByOrderSn(orderSn);
        if (StringUtils.isNotEmpty(extendPO.getReceiveCode())) {
            return extendPO.getReceiveCode();
        }
        int random = (int) ((Math.random() * 9 + 1) * 1000);
        receiveCode = String.valueOf(random);
        log.info("getOrderReceiveCode receiveCode = {}", random);
        //更新签收码
        orderExtendService.updateOrderReceiveCode(orderSn, receiveCode);

        return receiveCode;
    }

    @Override
    public boolean isReceiveCodeStore(Long storeId) {
        return storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.RECEIVE_CODE,storeId);
//        return ObjectUtils.isNotEmpty(orderReceiveCodeConfig.getStoreId())
//                && orderReceiveCodeConfig.getStoreId().contains(String.valueOf(storeId));
    }

    @Override
    public boolean isExtendReceiveStore(Long storeId) {
        return ObjectUtils.isNotEmpty(orderReceiveTimeDelay.getStoreId())
                && orderReceiveTimeDelay.getStoreId().contains(String.valueOf(storeId));
    }


    @Override
    public List<String> getReceiveCodeStoreList() {
        List<Long> storeIdList = storeIsolateWhiteListFeignClient.whiteListStoreIds(WhiteListEnum.RECEIVE_CODE);
        if (CollectionUtils.isEmpty(storeIdList)){
            return Lists.newArrayList();
        }
        return storeIdList.stream().map(String::valueOf).collect(Collectors.toList());
//        List<String> storeIdList = new ArrayList<>();
//        if (ObjectUtils.isNotEmpty(orderReceiveCodeConfig.getStoreId())) {
//            storeIdList = Arrays.asList(orderReceiveCodeConfig.getStoreId().split(","));
//        }
//        return storeIdList;
    }

    /**
     * 自提订单 自动发货
     */
    @Override
    public boolean selfLiftOrderAutoDelivery(String orderSn) {

        OrderPO orderPO = orderService.getByOrderSn(orderSn);

        if (orderPO == null){
            return false;
        }

        // 待发货
        if (OrderStatusEnum.WAIT_DELIVER != OrderStatusEnum.valueOf(orderPO.getOrderState())){
            return false;
        }

        // 自提
        if (OrderPatternEnum.SELF_LIFT != OrderPatternEnum.valueOf(orderPO.getOrderPattern())){
            return false;
        }

        // 白名单
        if (!autoDeliveryStoreIdList.contains(orderPO.getStoreId())){
            return false;
        }

        // 查询订单商品ID
        LambdaQueryWrapper<OrderProductPO> orderProductQuery = new LambdaQueryWrapper<>();
        orderProductQuery.eq(OrderProductPO::getOrderSn, orderSn)
                .select(OrderProductPO::getOrderProductId);
        List<OrderProductPO> orderProductPOS = orderProductService.list(orderProductQuery);

        // 自提订单物流信息取收货联系人，既自提点联系人
        LambdaQueryWrapper<OrderExtendPO> orderExtendQuery = new LambdaQueryWrapper<>();
        orderExtendQuery.eq(OrderExtendPO::getOrderSn, orderSn)
                .select(OrderExtendPO::getExtendId, OrderExtendPO::getReceiverName, OrderExtendPO::getReceiverMobile);
        OrderExtendPO orderExtendPO = orderExtendService.getOne(orderExtendQuery);

        OrderDeliveryReq deliveryReq = new OrderDeliveryReq();
        deliveryReq.setOrderSn(orderSn);
        deliveryReq.setOrderProductIds(orderProductPOS.stream()
                .map(OrderProductPO::getOrderProductId).collect(Collectors.toList()));
        deliveryReq.setDeliverType(1);
        deliveryReq.setDeliverName(orderExtendPO.getReceiverName());
        deliveryReq.setDeliverMobile(orderExtendPO.getReceiverMobile());
        deliveryReq.setDistribution(0);
        deliveryReq.setAllowNoLogistics(true);
        deliveryReq.setChannel(OrderCreateChannel.WEB);

        Vendor vendor = new Vendor();
        vendor.setVendorId(OrderConst.LOG_USER_ID);
        vendor.setVendorName(OrderConst.LOG_USER_NAME);

        try {
            orderService.deliveryV2(deliveryReq, vendor);
        } catch (Exception ex) {
            log.warn("酒水自提订单业绩归属绑定后自动发货出现未知异常,orderSn:{},原因", orderSn, ex);
        }
        return true;
    }

    @Override
    public int updateProductForOutbound(Set<Long> orderProductIds) {
        // 更新商品行发货状态为出库中
        LambdaUpdateWrapper<OrderProductPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(OrderProductPO::getDeliveryState, OrderProductDeliveryEnum.OUTBOUND.getValue())
                .eq(OrderProductPO::getDeliveryState, OrderProductDeliveryEnum.WAIT_DELIVERY.getValue())
                .in(OrderProductPO::getOrderProductId, orderProductIds);
        return orderProductMapper.update(null, updateWrapper);
    }

    @Override
    public int outboundToWaitDelivery(String orderSn, List<Long> productList) {
        // 更新商品行发货状态为出库中
        LambdaUpdateWrapper<OrderProductPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(OrderProductPO::getDeliveryState, OrderProductDeliveryEnum.WAIT_DELIVERY.getValue())
                .eq(OrderProductPO::getDeliveryState, OrderProductDeliveryEnum.OUTBOUND.getValue())
                .eq(OrderProductPO::getOrderSn, orderSn)
                .in(OrderProductPO::getProductId, productList);
        return orderProductMapper.update(null, updateWrapper);
    }


    /**
     * 库存不足钉钉提醒
     * 钉钉提醒业绩归属人对应工号的钉钉
     * 【{organization}】您关注的自提订单库存不足发货失败，请关注。可用库存：{availableCount}箱、需求数量：{orderCount}箱
     * */
    @Override
    public void outOfStackDingdingRemind(String orderSn, String skuName,String spuName, String requestStockNum, String currentStockNum) {
        OrderPerformanceBelongsPO performanceBelongsPO = orderPerformanceBelongsService.getEntityByOrderSn(orderSn);
        if(performanceBelongsPO == null) {
            return;
        }
        if(StringUtils.isBlank(performanceBelongsPO.getBelongerEmployeeNo())) {
            return;
        }

        BatchBizMessageTemplateReq msgSendReq = new BatchBizMessageTemplateReq();
        HashMap<String, String> bizParams = new HashMap<>(5);
        bizParams.put("organization", performanceBelongsPO.getEmployeeBranchName());
        bizParams.put("availableCount", String.valueOf(currentStockNum));
        bizParams.put("orderCount", String.valueOf(requestStockNum));
        bizParams.put("orderSn", orderSn);
        bizParams.put("spuName", spuName);
        bizParams.put("skuName", skuName);
        /*bizParams.put("unit", unit);*/

        List<BatchBizMessageSubReq> bizMessageSubReqList = new ArrayList<>();

        if (!performanceBelongsPO.getBelongerEmployeeNo().equals(PerformanceBelongsConst.DEFAULT_EMPLOYEE_CODE)){
            BatchBizMessageSubReq itemBizMsgReq = new BatchBizMessageSubReq();
            itemBizMsgReq.setReceiver(performanceBelongsPO.getBelongerEmployeeNo());
            itemBizMsgReq.setBizParams(bizParams);
            bizMessageSubReqList.add(itemBizMsgReq);
        }
        // 获取生服对接人和内务工号
        String branchCode = performanceBelongsPO.getEmployeeBranchCode();
        try {
            Map<String, OutBranchLifeAndInteriorInfoVO> hrInfoMap = this.queryBranchLifeAndInteriorList(Collections.singletonList(branchCode));
            if (hrInfoMap.containsKey(branchCode)){
                OutBranchLifeAndInteriorInfoVO outBranchLifeAndInteriorInfoVO = hrInfoMap.get(branchCode);
                List<String> interiorEmployeeCodeList = outBranchLifeAndInteriorInfoVO.getInteriorEmployeeCodeList();
                Set<String> sendEmployeeCode = Sets.newHashSet();
                if (!CollectionUtils.isEmpty(interiorEmployeeCodeList)){
                    sendEmployeeCode.addAll(interiorEmployeeCodeList);
                }
                List<String> lifeEmployeeCodeList = outBranchLifeAndInteriorInfoVO.getLifeEmployeeCodeList();
                if (!CollectionUtils.isEmpty(lifeEmployeeCodeList)){
                    sendEmployeeCode.addAll(lifeEmployeeCodeList);
                }
                log.info("outOfStackDingdingRemind,sendEmployeeCode:{}",sendEmployeeCode);
                if (!CollectionUtils.isEmpty(sendEmployeeCode)){
                    for (String employeeCode : sendEmployeeCode) {
                        BatchBizMessageSubReq temp = new BatchBizMessageSubReq();
                        temp.setReceiver(employeeCode);
                        temp.setBizParams(bizParams);
                        bizMessageSubReqList.add(temp);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("发送钉钉缺货提醒给分支内务、生服对接人出现异常,异常原因:{}",e.getMessage());
        }

        if (CollectionUtils.isNotEmpty(bizMessageSubReqList)) {
            msgSendReq.setBizType(OrderConst.SELF_LIFT_ORDER_DELIVERY_DINGDING_REMIND);
            msgSendReq.setReceiverTypeEnum(ReceiverTypeEnum.EMPLOYEE);
            msgSendReq.setBizMessageTemplateList(bizMessageSubReqList);
            templateMessageFacade.bizTemplateBatchSend(msgSendReq);
        }

    }

    /**
     * 线下订单生成签收码
     *
     * @param orderPO 订单信息
     */
    @Override
    public void createReceiveCodeForOffline(OrderPO orderPO) {
        Integer orderType = orderPO.getOrderType();
        log.info("createReceiveCode start,orderPo:{}",orderPO);
        if (!OrderTypeEnum.ORDER_TYPE_6.getValue().equals(orderType)){
            // 只处理线下订单
            return;
        }
        String receiveCode = null;
        OrderExtendPO extendPO = orderExtendModel.getOrderExtendByOrderSn(orderPO.getOrderSn());
        if (StringUtils.isNotEmpty(extendPO.getReceiveCode())) {
            log.info("createReceiveCodeForOffline fail,receive code already exist");
            return;
        }
        int random = (int) ((Math.random() * 9 + 1) * 1000);
        receiveCode = String.valueOf(random);
        log.info("createReceiveCode receiveCode = {}", random);
        //更新签收码
        orderExtendService.updateOrderReceiveCode(orderPO.getOrderSn(), receiveCode);
        log.info("createReceiveCode finish");
    }

    /**
     * 根据分支编码查询对应的生服对接人以及分支内务
     *
     * @param branchCodeList 分支编码列表
     * @return 分支对接人、分支内务信息
     */
    @Override
    public Map<String, OutBranchLifeAndInteriorInfoVO> queryBranchLifeAndInteriorList(List<String> branchCodeList) {
        return hrmsIntegration.queryBranchLifeAndInteriorList(branchCodeList);
    }
}
