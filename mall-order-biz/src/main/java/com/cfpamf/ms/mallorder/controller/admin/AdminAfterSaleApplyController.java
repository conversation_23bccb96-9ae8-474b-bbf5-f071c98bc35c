package com.cfpamf.ms.mallorder.controller.admin;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallorder.service.IOrderAfterService;
import com.cfpamf.ms.mallorder.service.IOrderProductService;
import com.cfpamf.ms.mallorder.vo.AfsCountVO;
import com.cfpamf.ms.mallorder.vo.AfsOrderProductVOV2;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR> 2022/12/29.
 */
@Api(tags = "admin-售后申请")
@RestController
@RequestMapping("admin/after/sale/apply")
@Slf4j
public class AdminAfterSaleApplyController {

    @Resource
    private IOrderProductService orderProductService;
    @Resource
    private IOrderAfterService orderAfterService;


    @ApiOperation("售后订单货品信息列表")
    @ApiImplicitParams(@ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query"))
    @GetMapping("getOrderProductList")
    public JsonResult<List<AfsOrderProductVOV2>> getAfsOrderProductList(HttpServletRequest request, String orderSn) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        log.info("admin apply after sale, getOrderProductList, admin info is : {}", JSONObject.toJSONString(admin));
        return SldResponse.success(orderProductService.getAfsOrderProductList(orderSn));
    }

    @ApiOperation(value = "计算售后退款信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "afsOrderProductInfos", value = "退换的订单货品列表，格式为：id1-num1,id2-num2...num为空时表示此订单货品全部退换",
                    required = true, paramType = "query")})
    @GetMapping("countRefundInfo")
    public JsonResult<AfsCountVO> countRefundInfo(HttpServletRequest request, String orderSn, String afsOrderProductInfos) {
        Admin admin = UserUtil.getUser(request, Admin.class);
        log.info("admin apply after sale, countRefundInfo, admin info is : {}", JSONObject.toJSONString(admin));
        return SldResponse.success(orderAfterService.refundCountMoneyInfo(orderSn, afsOrderProductInfos, null));
    }

}
