package com.cfpamf.ms.mallorder.v2.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类OperationUserVO.java的实现描述：操作对象
 *
 * <AUTHOR> 14:02
 */
@NoArgsConstructor
@Data
public class OperationUserVO implements java.io.Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 3434744061678515662L;

    public OperationUserVO(Integer operationRole, Integer returnBy, Integer operationUserId, String operationUserName,
        String operationRemark) {
        super();
        this.operationRole = operationRole;
        this.returnBy = returnBy;
        this.operationUserId = operationUserId;
        this.operationUserName = operationUserName;
        this.operationRemark = operationRemark;
    }

    public OperationUserVO(Integer operationRole, Integer returnBy, Long operationUserId, String operationUserName,
        String operationRemark) {
        super();
        this.operationRole = operationRole;
        this.returnBy = returnBy;
        this.operationUserId = (operationUserId != null) ? operationUserId.intValue() : null;
        this.operationUserName = operationUserName;
        this.operationRemark = operationRemark;
    }

    /**
     * 操作者角色：1-系统管理员，2-商户，3-会员
     */
    private Integer operationRole;

    /**
     * 退款发起者：0-系统 1-客户 2-商家 3-平台
     */
    public Integer returnBy;
    /**
     * 操作者id
     */
    private Integer operationUserId;

    /**
     * 操作者手机号
     */
    private String operationUserMobile;

    /**
     * 操作者名称
     */
    private String operationUserName;

    /**
     * 操作备注
     */
    private String operationRemark;

}
