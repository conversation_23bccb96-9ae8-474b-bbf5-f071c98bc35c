package com.cfpamf.ms.mallorder.dto;

import com.cfpamf.ms.mallorder.enums.OrderPlaceUserRole;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 提交订单请求放入mq队列的dto
 */
@Data
public class OrderSubmitMqConsumerDTO implements Serializable {
    private static final long serialVersionUID = -1259632721554196812L;
    /**
     * 提交订单前端传参
     */
    private OrderSubmitParamDTO paramDTO;
    /**
     * 会员id
     */
    private Integer memberId;
    /**
     * 用户号
     */
    private String userNo;
    /**
     * 支付单号，入队列前统一生成，用于查询订单是否已经生成
     */
    private String paySn;
    /**
     * 订单预处理信息（是否计算优惠、是否描述）
     */
    private PreOrderDTO preOrderDTO;

    /**
     * 区域编码
     */
    private String areaCode;

    private List<OrderSkuInfoDTO> skuInfoList;

    private OrderTypeEnum orderType;

    /**
     * 渠道订单提交参数
     */
    private ChannelOrderSubmitDTO channelOrderSubmitDTO;
    
    /**
     * 线下补录订单
     */
    private OrderOfflineParamDTO orderOfflineParamDTO;

    /**
     * 拼单满赠订单下单参数
     */
    private GroupOrderProductSubmitDTO groupOrderProductSubmitDTO;

    /**
     * 批量订单（非购物车）
     */
    private OrderParamDTO orderParamDTO;
    /**
     * 是否需要模拟购物车
     */
    private boolean simulationShoppingCart;
    
    @ApiModelProperty(value = "金融规则编号")
    private String financeRuleCode;
    
	@NotBlank(message = "下单用户角色不能为空")
	@ApiModelProperty(value = "下单用户角色:本人：SELF，客户经理（BAPP客户经理代客下单传入）：CUSTOMER_MANAGER", required = true)
	private OrderPlaceUserRole orderPlaceUserRole;
	
	private OrderPlaceUserDTO orderPlaceUserDTO;

    @ApiModelProperty(value = "渠道订单号")
    private String channelOrderSn;
}
