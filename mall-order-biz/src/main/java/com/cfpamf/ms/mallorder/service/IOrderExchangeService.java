package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.dto.OrderExchangeAuditDTO;
import com.cfpamf.ms.mallorder.dto.UserDTO;
import com.cfpamf.ms.mallorder.po.OrderExchangeDetailPO;
import com.cfpamf.ms.mallorder.po.OrderExchangePO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.req.BappOrderReturnRequest;
import com.cfpamf.ms.mallorder.req.ExchangeApplyListPcReq;
import com.cfpamf.ms.mallorder.req.ExchangeApplyListReq;
import com.cfpamf.ms.mallorder.req.exchange.OrderExchangeRequest;
import com.cfpamf.ms.mallorder.vo.OrderExchangeDetailVO;
import com.cfpamf.ms.mallorder.vo.OrderExchangeReturnListVO;
import com.cfpamf.ms.mallorder.vo.OrderProductListVO;
import com.cfpamf.ms.mallorder.vo.OrderReturnVOV2;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;

import java.math.BigDecimal;
import java.util.List;

public interface IOrderExchangeService extends IService<OrderExchangePO> {

    OrderExchangeDetailVO applyExchange(OrderExchangeRequest orderExchangeRequest) throws Exception;

    OrderExchangePO saveExchangeApplyOrder(String exchangeSn, Long storeId, Integer memberId
            , UserDTO userDTO, Integer buyerConfirmFlag, String exchangeReason) throws Exception;

    OrderPO createExchangeOrder(OrderPO orderPO, OrderExchangeDetailVO orderExchangeDetailVO, OrderExchangeRequest orderExchangeRequest);

    OrderExchangeDetailVO orderExchangeTrial(OrderExchangeRequest orderExchangeRequest);

    void dealProductExchangeFlag(List<OrderProductListVO> orderProductList);

    void exchangeOrderAudit(OrderExchangeAuditDTO orderExchangeAuditDTO, UserDTO userDTO) throws Exception;
    void exchangeOrderAuditDeal(OrderExchangeAuditDTO orderExchangeAuditDTO, UserDTO userDTO) throws Exception;

    OrderExchangeDetailPO saveExchangeOrderDetail(OrderExchangeDetailVO orderExchangeDetailVO, String operatorName);

    void exchangeOrderAutoAuditJob() throws Exception;

    void dealExchangeOrderFinish(String orderSn);

    void updateExchangeApplyStatus(String afsSn);

    void dealCloseExchangeOrder(OrderExchangeDetailPO exchangeDetailPO, UserDTO userDTO, int returnBy, String content) throws Exception;

    void dealAgreeExchangeOrder(OrderExchangeDetailPO exchangeDetailPO, UserDTO userDTO);

    List<OrderExchangeReturnListVO> getOrderExchangeReturnList(ExchangeApplyListReq exchangeApplyListReq, PagerInfo pager);

    List<OrderExchangeReturnListVO> getExchangeApplyList(ExchangeApplyListPcReq exchangeApplyListReq, PagerInfo pager);

    void exchangeOrderAutoCancelJob() throws Exception;


    void updateOrderAmount(String orderSn, OrderExchangeDetailVO orderExchangeDetailVO);

    void updateOrderProductAmount(OrderExchangeDetailVO orderExchangeDetailVO);

    void updatePayAmount(String paySn, BigDecimal orderAmount);

    void updateExtendAmount(String orderSn, OrderExchangeDetailVO orderExchangeDetailVO);

    void updateOrderToExchange(String orderSn,int exchangeFlag);



    void cancelPaidOrder(OrderExchangeDetailPO exchangeDetailPO,UserDTO userDTO);

    PageVO<OrderExchangeReturnListVO> getBappOrderExchangeList(BappOrderReturnRequest request);
}
