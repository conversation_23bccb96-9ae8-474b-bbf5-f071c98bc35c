package com.cfpamf.ms.mallorder.common.mq;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallorder.po.CommonMqEvent;
import com.cfpamf.ms.mallorder.service.ICommonMqEventService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.ContentTypeDelegatingMessageConverter;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * @Date 2020/7/16 11:00
 **/
@Configuration
@Data
@Slf4j
public class RabbitMqConfig {

    public static final String EXCHANGE_ORDER_CHANGE = "mall.order.exchange.order.change.event";

    /**
     * 订单发货Exchange
     * */
    public static final String EXCHANGE_ORDER_DELIVERY = "mall.order.exchange.order.delivery";

    /**
     * ERP履约渠道出库Exchange
     * */
    public static final String EXCHANGE_ERP_DELIVERY = "erp.performance.stock.exchange";

    /**
     * ERP履约渠道出库结果Exchange
     */
    public static final String EXCHANGE_ERP_DELIVERY_RESULT_NOTICE = "erp.performance.stock.notice.exchange";

    /**
     * 放款结果通知队列
     */
    public static final String QUEUE_TRADE_RESULT_MALL = "core.trade.queue.result.mallOrder";
    /**
     * 放款结果通知交换机
     */
    public static final String EXCHANGE_TRADE_RESULT_MALL = "core.trade.exchange.result";
    /**
     * 放款路由key
     */
    public static final String ROUTING_TRADE_RESULT_MALL = "core.trade.routing.result.mallOrder";
    /**
     * 退汇结果通知交换机
     */
    public static final String EXCHANGE_TRADE_REFUND_MALL = "core.trade.exchange.refund";

    /**
     * 订单关联费用金额变化交换机
     */
    public static final String  ORDER_RELATION_AMOUNT_UPDATE_FANOUT_EXCHANGE = "order.relation.amount.update.fanout.exchange";

    public static final String EXCHANGE_ORDER_REFUND_CHANGE = "mall.order.exchange.order.refund.change.event";

    /**
     * 用呗退款结果通知队列
     */
    public static final String QUEUE_LOAN_REFUND_MALL_ORDER = "liquidate.loan.refund.queue.mallOrder";
    /**
     * 用呗退款通知交换机
     */
    public static final String EXCHANGE_LOAN_REFUND_MALL_ORDER = "mall.liquidate.exchange.subsidy.refund.change.event";
    
    
    public static final String EXCHANGE_ORDER_OPERATION_EVENT = "mall.order.exchange.order.operation.event";
    
    /**
     * 订单变化订单操作代客下单，代客户确认消息PUSH队列
     */
    public static final String QUEUE_VALET_ORDER_CUSTOMER_CONFIRM_MESSAGE_PUSH = "mall.order.valet.order.customer.confirm.message.push.queue";

    /**
     * 订单状态通知交换机——绑定队列
     * */
    public static final String EXCHANGE_ORDER_STATUS_CHANGE_QUEUE = "mall.order.change.queue.mall.order";

    /**
     * ERP出库结果通知MQ——绑定队列
     * */
    public static final String EXCHANGE_ERP_DELIVERY_QUEUE = "erp.performance.out.stock.queue";

    /**
     * 售后单状态通知交换机——绑定队列
     * */
    public static final String EXCHANGE_ORDER_REFUND_STATUS_CHANGE_QUEUE = "mall.order.refund.change.queue.mall.order";

    /**
     * 绩效服务 站长喜报队列
     */
    public static final String PERFORMANCE_GOOD_NEWS_QUEUE_NAME = "agric.performance.goodNews.queue";

    /**
     * 绩效服务 站长喜报交换机
     */
    public static final String PERFORMANCE_GOOD_NEWS_EXCHANGE_NAME = "agric.performance.goodNews.exchange";

    /**
     * 酒水PK动态业绩推送交换机
     */
    public static final String SCRM_EXCHANGE_PK_BUSINESS_MESSAGE_EXCHANGE_NAME = "scrm.exchange.pk.businessMessage";
    /**
     * 酒水PK动态业绩推送routeKey
     */
    public static final String SCRM_QUEUE_PK_LIQUOR_MESSAGE_KEY = "scrm.queue.pk.liquor-message.key";


    @Autowired
    private RabbitProperties rabbitProperties;
    @Autowired
    private ICommonMqEventService mqEventService;
    
    /**
     * 创建交换机
     *
     * @return the exchange
     */
    @Bean("mallOrderExchangeOrderOperationEvent")
    public FanoutExchange deadLetterExchange() {
        return (FanoutExchange)ExchangeBuilder.fanoutExchange(EXCHANGE_ORDER_OPERATION_EVENT).build();
    }
    
    @Bean("valetOrderCustomerConfirmMessagePushQueue")
    public Queue valetOrderCustomerConfirmMessagePushQueue() {
        return QueueBuilder.durable(QUEUE_VALET_ORDER_CUSTOMER_CONFIRM_MESSAGE_PUSH).build();// 队列持久
    }
    
    /**
     * valetOrderCustomerConfirmMessagePushQueue业务队列绑定mallOrderExchangeOrderOperationEvent业务交换机
     *
     * @return
     */
    @Bean("valetOrderCustomerUnConfirmBindOrderExchangeOrderOperationExchange")
    public Binding valetOrderCustomerUnConfirmBindOrderExchangeOrderOperationExchange(Queue valetOrderCustomerConfirmMessagePushQueue) {
        return BindingBuilder.bind(valetOrderCustomerConfirmMessagePushQueue)
            .to((FanoutExchange)ExchangeBuilder.fanoutExchange(EXCHANGE_ORDER_OPERATION_EVENT).build());
    }
    
    /**
     * @param amqpAdmin
     * @return java.lang.String
     * @description : 放款绑定
     */
    @Bean
    public String channelBindingTradeResult(AmqpAdmin amqpAdmin) {
        amqpAdmin.declareExchange(
                new TopicExchange(EXCHANGE_TRADE_RESULT_MALL, true, false));
        amqpAdmin.declareQueue(
                new Queue(QUEUE_TRADE_RESULT_MALL, true));
        amqpAdmin.declareBinding(
                new Binding(QUEUE_TRADE_RESULT_MALL,
                        Binding.DestinationType.QUEUE,
                        EXCHANGE_TRADE_RESULT_MALL, ROUTING_TRADE_RESULT_MALL, null));

        return QUEUE_TRADE_RESULT_MALL;
    }

    /**
     * @param amqpAdmin
     * @return java.lang.String
     * @description : 退汇绑定
     */

    @Bean
    public String channelBindingLoanRefund(AmqpAdmin amqpAdmin) {
        amqpAdmin.declareExchange(
                new FanoutExchange(EXCHANGE_LOAN_REFUND_MALL_ORDER, true, false));
        amqpAdmin.declareQueue(
                new Queue(QUEUE_LOAN_REFUND_MALL_ORDER, true));
        amqpAdmin.declareBinding(
                new Binding(QUEUE_LOAN_REFUND_MALL_ORDER,
                        Binding.DestinationType.QUEUE,
                        EXCHANGE_LOAN_REFUND_MALL_ORDER, "", null));

        return QUEUE_LOAN_REFUND_MALL_ORDER;
    }

    /**
     * 注册交换器-订单变更消息
     *
     * @return
     */
    @Bean("fanoutExchangeOrderSubmit")
    public FanoutExchange fanoutExchangeOrderSubmit() {
        return ExchangeBuilder.fanoutExchange(EXCHANGE_ORDER_CHANGE).durable(true).build();
    }

    /**
     * 注册交换器-订单变更消息
     * @return
     */
    @Bean("fanoutExchangeOrderRefundSubmit")
    public FanoutExchange fanoutExchangeOrderRefundSubmit() {
        return ExchangeBuilder.fanoutExchange(EXCHANGE_ORDER_REFUND_CHANGE).durable(true).build();
    }


    @Bean
    public FanoutExchange exchangeToOrderRelationAmountUpdate() {
        return new FanoutExchange(ORDER_RELATION_AMOUNT_UPDATE_FANOUT_EXCHANGE, true, false);
    }

    /**
     * 创建监听订单状态变更队列
     * @return
     */
    @Bean("orderStateChangeQueue")
    public Queue orderStateChangeQueue(){
        return new Queue(EXCHANGE_ORDER_STATUS_CHANGE_QUEUE,true);
    }


    /**
     * 绑定订单状态交换机
     * @return
     */
    @Bean
    public Binding orderStateChangeQueueBinding(){
        return BindingBuilder.bind(orderStateChangeQueue()).to((FanoutExchange)ExchangeBuilder.fanoutExchange(EXCHANGE_ORDER_CHANGE).build());
    }


    /**
     * 创建监听售后订单状态变更队列
     * @return
     */
    @Bean("orderRefundStateChangeQueue")
    public Queue orderRefundStateChangeQueue(){
        return new Queue(EXCHANGE_ORDER_REFUND_STATUS_CHANGE_QUEUE,true);
    }

    /**
     * 绑定订单状态交换机
     * @return
     */
    @Bean
    public Binding orderRefundStateChangeQueueBinding(){
        return BindingBuilder.bind(orderRefundStateChangeQueue()).to((FanoutExchange)ExchangeBuilder.fanoutExchange(EXCHANGE_ORDER_REFUND_CHANGE).build());
    }

    /**
     * 注册交换器-订单发货消息
     *
     * @return
     */
    @Bean("fanoutExchangeOrderDelivery")
    public FanoutExchange fanoutExchangeOrderDelivery() {
        return ExchangeBuilder.fanoutExchange(EXCHANGE_ORDER_DELIVERY).durable(true).build();
    }

    /**
     * 注册交换器-ERP订单出库消息
     *
     * @return
     */
    @Bean("fanoutExchangeErpDelivery")
    public FanoutExchange fanoutExchangeErpDelivery() {
        return ExchangeBuilder.fanoutExchange(EXCHANGE_ERP_DELIVERY).durable(true).build();
    }

    /**
     * 创建监听-ERP订单出库结果队列
     * @return
     */
    @Bean("erpDeliveryQueue")
    public Queue erpDeliveryQueue(){
        return new Queue(EXCHANGE_ERP_DELIVERY_QUEUE,true);
    }

    /**
     * ERP出库结果queue和exchange绑定
     * @return
     */
    @Bean
    public Binding erpDeliveryQueueBinding(){
        return BindingBuilder.bind(erpDeliveryQueue()).to((FanoutExchange)ExchangeBuilder.fanoutExchange(EXCHANGE_ERP_DELIVERY_RESULT_NOTICE).build());
    }



    /**
     * MQ发送方配置
     *
     * @param connectionFactory
     * @return
     */
    @Bean(name = "mallOrderServiceRabbitTemplate")
    @Primary
    public RabbitTemplate rabbitTemplate(@Qualifier("mallOrderConnectionFactory") ConnectionFactory connectionFactory) {
        // 若使用return-callback, 必须设置publisherReturns为true
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        // 使用return-callback, 必须设置mandatory为true
        rabbitTemplate.setMandatory(true);
        rabbitTemplate.setMessageConverter(producerJackson2MessageConverter());


        /**
         * 如果消息没有到exchange, 则confirm回调, ack=false;
         * 如果消息到达exchange,   则confirm回调, ack=true
         */
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {

            log.info("======= received ack:{}", ack);

            if (correlationData != null && StringUtils.isNotEmpty(correlationData.getId())) {
                log.info("======= correlationData :{}", correlationData.getId());

                Long id = Long.valueOf(correlationData.getId());

                CommonMqEvent mqEvent = mqEventService.getById(id);

                String orderSubmitMsgBodyStr = JSONObject.toJSONString(mqEvent);

                log.info("======= mq消息体:{}", orderSubmitMsgBodyStr);

                if (ack) {
                    // 成功：删除数据库记录
                    mqEventService.removeById(id);
                } else {
                    log.error("======= 消息推送exchange失败, 请检查MQ是否异常, 消息ID: {}", correlationData.getId());
                    // 失败：发送次数+1
                    mqEvent.setSendTimes(mqEvent.getSendTimes() + 1);
                    mqEventService.updateById(mqEvent);
                }
            }
        });


        /**
         * 如果exchange到queue成功, 则不回调return;
         * 如果exchange到queue失败, 则回调return(需设置mandatory=true, 否则不会回调, 消息就丢了)
         */
        rabbitTemplate.setReturnCallback((message, replyCode, replyText, exchange, routingKey) -> {
            log.info("======= 消息推送MQ队列失败, 请检查路由键是否准确, 交换机:{}, 路由键:{}", exchange, routingKey);
        });

        return rabbitTemplate;
    }

    @Bean
    @Primary
    public ConnectionFactory mallOrderConnectionFactory() {
        return this.buildConnectionFactory(rabbitProperties.getHost(), rabbitProperties.getPort(),
                rabbitProperties.getUsername(), rabbitProperties.getPassword(), "/");
    }

    private ConnectionFactory buildConnectionFactory(String host, int port, String username, String password,
                                                     String virtualHost) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setVirtualHost(virtualHost);
        connectionFactory.setPublisherReturns(true);
        connectionFactory.setPublisherConfirmType(CachingConnectionFactory.ConfirmType.CORRELATED);
        return connectionFactory;
    }

    /**
     * 消费方配置
     *
     * @param connectionFactory
     * @return
     */
    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(@Qualifier("mallOrderConnectionFactory") ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(contentTypeDelegatingMessageConverter());
        // 设置应答模式为手动
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        return factory;
    }

    @Bean
    public MessageConverter contentTypeDelegatingMessageConverter() {
        return new ContentTypeDelegatingMessageConverter(new Jackson2JsonMessageConverter());
    }

    @Bean
    public Jackson2JsonMessageConverter producerJackson2MessageConverter() {
        return new Jackson2JsonMessageConverter();
    }

}