package com.cfpamf.ms.mallorder.scheduler;

import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.v2.common.enums.OrderSnType;
import com.cfpamf.ms.mallorder.v2.config.CommonConfig;
import com.cfpamf.ms.mallorder.v2.domain.vo.OperationUserVO;
import com.cfpamf.ms.mallorder.v2.domain.vo.OrderCancelVO;
import com.cfpamf.ms.mallorder.v2.service.OrderCancelService;
import com.cfpamf.ms.mallorder.v2.service.OrderCancelSupportService;
import com.slodon.bbc.core.constant.PromotionConst;
import com.slodon.bbc.core.util.AssertUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.extern.slf4j.Slf4j;

/**
 * 系统自动取消没有付款的秒杀订单 cron:0 0/5 * * * ?
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/4 15:22
 */
@Component
@Slf4j
public class OrderCancelSeckillJob {

    @Autowired
    private OrderModel orderModel;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private OrderCancelService orderCancelService;

    @Autowired
    private OrderCancelSupportService seckillOrderCancelSupportService;
    
    @Autowired
    private CommonConfig commonConfig;

    @XxlJob(value = "OrderCancelSeckillJob")
    public ReturnT<String> execute(String s) {

        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {0}", LocalDateTime.now());

        log.info("jobSystemCancelOrder() start");
        try {
            if (commonConfig.isOpenV2()) {//TODO 
                log.info("开启V2流程Job取消秒杀订单，jobSystemCancelSeckillOrder()");
                boolean jobResult = this.jobSystemCancelSeckillOrder();
                AssertUtil.isTrue(!jobResult, "[jobSystemCancelSeckillOrder] 定时任务系统自动取消秒杀订单失败");
                XxlJobHelper.log("finish job at local time: {0}  cost:{1}", LocalDateTime.now(),
                    (System.currentTimeMillis() - startTime) / 1000);
                return ReturnT.SUCCESS;
            }
            boolean jobResult = orderModel.jobSystemCancelSeckillOrder();
            AssertUtil.isTrue(!jobResult, "[jobSystemCancelSeckillOrder] 定时任务系统自动取消没有付款秒杀订单时失败");
        } catch (Exception e) {
            log.error("jobSystemCancelOrder()", e);
        }

        XxlJobHelper.log("finish job at local time: {0}  cost:{1}", LocalDateTime.now(),
            (System.currentTimeMillis() - startTime) / 1000);

        return ReturnT.SUCCESS;
    }

    /**
     * 系统自动取消没有付款的秒杀订单
     *
     * @return
     */
    public boolean jobSystemCancelSeckillOrder() {
        // 买家几分钟未支付订单，订单取消
        String value = stringRedisTemplate.opsForValue().get("seckill_order_cancle");
        int limitMinute = value == null ? 5 : Integer.parseInt(value);

        // 获取当前时间limitHour小时之前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -limitMinute);

        Date cancelTime = calendar.getTime();

        // 获取超时未付款的订单，此处查询的都是待付款状态的父订单
        OrderExample example = new OrderExample();
        example.setCreateTimeBefore(cancelTime);
        example.setOrderState(OrderConst.ORDER_STATE_10);
        example.setOrderType(PromotionConst.PROMOTION_TYPE_104);// 秒杀订单
        example.setOrderBy("parent_sn");// 按照父订单号分组
        List<OrderPO> parentOrderPOList = orderMapper.listByExample(example);

        if (CollectionUtils.isEmpty(parentOrderPOList)) {
            log.warn("系统自动取消没有付款的秒杀订单，parentOrderPOList为空，不存在相关记录无需取消操作");
            return true;
        }
        // 父订单号去重
        Set<String> orderParentSns = parentOrderPOList.stream().map(OrderPO::getParentSn).collect(Collectors.toSet());
        orderParentSns.forEach(parentSn -> {
            try {
                log.info("开启V2流程取消订单，job系统自动取消没有付款的秒杀订单。parentSn:{}", parentSn);
                orderCancelService.cancelOrder(seckillOrderCancelSupportService,
                    new OrderCancelVO(OrderSnType.PARENT_ORDER_SN, parentSn, null, "system"),
                    new OperationUserVO(OrderConst.LOG_ROLE_ADMIN,OrderConst.RETURN_BY_0, 0, "system", "系统自动取消订单"));
            } catch (Exception e) {
                log.error("开启V2流程取消订单，job系统自动取消没有付款的秒杀订单。parentSn:{}", parentSn, e);
            }
        });
        return true;
    }
}
