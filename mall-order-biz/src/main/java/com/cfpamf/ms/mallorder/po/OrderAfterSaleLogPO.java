package com.cfpamf.ms.mallorder.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 售后服务操作日志表
 */
@Data
@TableName("bz_order_after_sale_log")
public class OrderAfterSaleLogPO implements Serializable {
    private static final long serialVersionUID = 7770524984078252518L;
    @ApiModelProperty("日志id")
    @TableId(type = IdType.AUTO)
    private Integer logId;

    @ApiModelProperty("操作人角色(1-系统管理员，2-商户，3-会员, 4- 客户经理, 5- 乡信站长）")
    private Integer logRole;

    @ApiModelProperty("操作人id")
    private Long logUserId;

    @ApiModelProperty("操作人名称")
    private String logUserName;

    @ApiModelProperty("售后单号")
    private String afsSn;

    @ApiModelProperty("售后服务端类型：1-退货退款单，2-换货单，3-仅退款单")
    private Integer afsType;

    @ApiModelProperty("状态，与退换货表状态相同")
    private String state;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "是否可用:1-是 0-否")
    private Integer enabledFlag;

    @ApiModelProperty(value = "发起售后渠道：H5-浏览器H5，APP-乡助APP，WE_CHAT-微信浏览器，MINI_PRO-小程序， XXAPP -乡信APP，BAPP-BAPP")
    @TableField("channel")
    private String channel;
}