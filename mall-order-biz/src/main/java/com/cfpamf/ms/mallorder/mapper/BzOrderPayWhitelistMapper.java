package com.cfpamf.ms.mallorder.mapper;

import com.cfpamf.ms.mallorder.po.BzOrderPayWhitelistPO;
import com.cfpamf.framework.autoconfigure.mybatis.MyBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 支付黑名单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-19
 */
public interface BzOrderPayWhitelistMapper extends MyBaseMapper<BzOrderPayWhitelistPO> {

    /**
     * 根据店铺id查询支付黑名单
     * @param storeId
     * @return
     */
    List<BzOrderPayWhitelistPO> getPayMethodByStoreId(@Param("storeId") Long storeId);
}
