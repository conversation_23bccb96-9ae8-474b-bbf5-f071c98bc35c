package com.cfpamf.ms.mallorder.mapper;

import com.cfpamf.ms.mallorder.po.TaskQueuePO;
import com.cfpamf.framework.autoconfigure.mybatis.MyBaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 定时任务推送队列表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
public interface TaskQueueMapper extends MyBaseMapper<TaskQueuePO> {

    int modifyTaskQueue(@Param("bizId")Long bizId, @Param("bizType")Integer bizType, @Param("excludeId")Long excludeId);

}
