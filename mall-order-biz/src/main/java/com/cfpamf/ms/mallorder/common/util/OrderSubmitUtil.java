package com.cfpamf.ms.mallorder.common.util;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.enums.GoodsTypeEnum;
import com.cfpamf.ms.mallgoods.facade.vo.*;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.constant.CartConst;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderAddressDTO;
import com.cfpamf.ms.mallorder.dto.OrderSkuInfoDTO;
import com.cfpamf.ms.mallorder.dto.OrderSubmitDTO;
import com.cfpamf.ms.mallorder.dto.OrderSubmitParamDTO;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.integration.erp.vo.ProductVO;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.integration.facade.BmsUserFacade;
import com.cfpamf.ms.mallorder.integration.shop.ShopIntegration;
import com.cfpamf.ms.mallorder.model.CartModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.Cart;
import com.cfpamf.ms.mallorder.po.CartPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.request.CartExample;
import com.cfpamf.ms.mallorder.request.OrderProductExample;
import com.cfpamf.ms.mallorder.service.impl.OrderCreateHelper;
import com.cfpamf.ms.mallpromotion.api.LadderGroupOrderExtendFeignClient;
import com.cfpamf.ms.mallpromotion.api.PresellOrderExtendFeignClient;
import com.cfpamf.ms.mallpromotion.api.PromotionCommonFeignClient;
import com.cfpamf.ms.mallpromotion.request.LadderGroupOrderExtend;
import com.cfpamf.ms.mallpromotion.request.PresellOrderExtendExample;
import com.cfpamf.ms.mallpromotion.vo.LadderGroupOrderExtendVO;
import com.cfpamf.ms.mallpromotion.vo.PresellOrderExtendVO;
import com.cfpamf.ms.mallshop.api.PurchaserFeign;
import com.cfpamf.ms.mallshop.vo.CheckAuthParam;
import com.cfpamf.ms.mallshop.vo.CheckMemberAuthVo;
import com.cfpamf.ms.mallshop.vo.FrontStoreRegionVo;
import com.slodon.bbc.core.constant.PromotionConst;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
//import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 提交订单构造工具
 */
@Slf4j
@Component
public class OrderSubmitUtil {

    @Autowired
    OrderCreateHelper orderCreateHelper;
    @Resource
    private CartModel cartModel;
    @Resource
    private OrderProductModel orderProductModel;
    @Resource
    private ProductFeignClient productFeignClient;
    @Resource
    private PromotionCommonFeignClient promotionCommonFeignClient;
    @Resource
    private PresellOrderExtendFeignClient presellOrderExtendFeignClient;
    @Resource
    private LadderGroupOrderExtendFeignClient ladderGroupOrderExtendFeignClient;
    @Autowired
    private BmsUserFacade bmsUserFacade;
    @Autowired
    private MemberFeignClient memberFeignClient;
    @Autowired
    private OrderLocalUtils orderLocalUtils;
    @Autowired
    private OrderSubmitAttributesUtils orderSubmitAttributesUtils;
    @Autowired
    private PromotionUtils promotionUtils;
    @Autowired
    private PurchaserFeign purchaserFeign;
    @Autowired
    private ShopIntegration shopIntegration;

    @Autowired
    private BmsIntegration bmsIntegration;
    @Autowired
    private CustomerIntegration customerIntegration;

    @Autowired
    private CartUtils cartUtils;

    /**
     * 根据前端参数获取后台操作的dto
     *
     * @param dto         前端参数
     * @param memberId    会员id
     * @param addressDTO  下单地址
     * @param refreshCart 是否刷新购物车，true==刷新，检测购物车时不需要刷新
     * @param isSubmit    是否提交订单，检查、确认接口为否，不需要下单校验
     * @return
     */
    public OrderSubmitDTO getOrderSubmitDTO(OrderSubmitParamDTO dto, Integer memberId, Integer productType,
                                            OrderAddressDTO addressDTO, boolean refreshCart, boolean isSubmit) {
        List<CartPO> cartPOList = new ArrayList<>();
        // 非购物车购买单品活动
        GoodsPromotion singlePromotion = null;

        /**
         * 获取购买商品cartPOList
         */
        if (dto.getIsCart()) {
            /**
             * 购物车结算
             */
            if (refreshCart) {
                cartPOList = cartModel.refreshCart(memberId, productType, addressDTO, true);
            } else {
                CartExample cartExample = new CartExample();
                cartExample.setMemberId(memberId);
                cartExample.setProductType(productType);
                cartExample.setIsChecked(CartConst.IS_CHECKED_YES);
                cartPOList = cartModel.getCartList(cartExample, null);
            }
            BizAssertUtil.notEmpty(cartPOList, "购物车被修改，请返回购物车重新选购商品下单！");

        } else {
            /**
             * 非购物车下单，立即购买或活动下单
             */
            // 参数校验
            BizAssertUtil.isTrue(StringUtil.isNullOrZero(dto.getProductId()), "请选择要购买的商品");
            BizAssertUtil.isTrue(StringUtil.isNullOrZero(dto.getNumber()), "请选择要购买的数量");
            BizAssertUtil.isTrue(dto.getNumber().compareTo(0) < 0, "购买数量不能小于0");

            // 获取店铺areaCod
            if (Objects.nonNull(addressDTO)) {
                Member member = orderSubmitAttributesUtils.getMemberByMemberId(memberId);
                FrontStoreRegionVo storeRegionVo = orderSubmitAttributesUtils.getStoreAreaCode(dto.getStoreId(), addressDTO, member);
//                areaCode = orderCreateHelper.getStoreAreaCodeByAddress(cartPO.getStoreId().toString(), addressDTO, memberId);
                String areaCode = null;
                if (Objects.nonNull(storeRegionVo) && !CollectionUtils.isEmpty(storeRegionVo.getRegionVOList())) {
                    areaCode = storeRegionVo.getRegionVOList().get(0).getCode();
                }
//                String areaCode = orderCreateHelper.getStoreAreaCodeByAddress(dto.getStoreId(), addressDTO, memberId);
                dto.setAreaCode(areaCode);
            }

            // 获取商品信息
            ProductPriceVO productPriceByProductId = orderSubmitAttributesUtils.getProductPriceByProductId(
                    dto.getProductId(), dto.getAreaCode(), dto.getFinanceRuleCode());
            if (Objects.isNull(productPriceByProductId) || Objects.isNull(productPriceByProductId.getProduct())){
                throw new BusinessException("商品信息异常，请返回重新下单");
            }
            // 增加调用erp获取物料信息并放入缓存
            String skuMaterialCode = productPriceByProductId.getProduct().getSkuMaterialCode();
            if (StringUtils.isNotBlank(skuMaterialCode)){
                Map<String, ProductVO> materialBatch = orderSubmitAttributesUtils.getMaterialBatch(Collections.singletonList(skuMaterialCode));
                log.info("getMaterialBatch result:{}",materialBatch);
            }

            /**
             * 刷新单独购买商品cartPO
             */
            CartPO cartPO = cartModel.refreshAloneBuy(memberId, dto.getNumber(), dto.getAreaCode(), productPriceByProductId);

            // 查询是否有单品活动
            singlePromotion = cartModel.getSinglePromotion(dto.getProductId(), memberId);
            if (singlePromotion != null && !dto.getIsAloneBuy()) {
                BigDecimal promotionPrice = orderLocalUtils.getPromotionPrice(dto, productPriceByProductId, singlePromotion);
                cartPO.setProductPrice(promotionPrice);
            }

            cartPOList = Collections.singletonList(cartPO);
        }


        List<Cart> collect = cartPOList.stream().map(x -> {
            Cart cart = new Cart();
            BeanUtils.copyProperties(x, cart);
            // 获取商品类型
            ProductPriceVO productPriceVO = orderSubmitAttributesUtils.getProductPriceByProductId(
                    x.getProductId(), x.getAreaCode(), x.getFinanceRuleCode());
            cart.setIsVirtualGoods(productPriceVO.getGoods().getIsVirtualGoods());
            List<CombinationChildGoodsVO> combinationChildGoodsVOList = productPriceVO.getCombinationChildGoodsVOList();
            if (CollectionUtil.isNotEmpty(combinationChildGoodsVOList)){
                List<OrderSubmitDTO.CombinationChildGoodsVO> childProductVOList = new ArrayList<>(combinationChildGoodsVOList.size());
                for (CombinationChildGoodsVO vo : combinationChildGoodsVOList) {
                    OrderSubmitDTO.CombinationChildGoodsVO childGoodsVO = new OrderSubmitDTO.CombinationChildGoodsVO();
                    BeanUtils.copyProperties(vo,childGoodsVO);
                    childProductVOList.add(childGoodsVO);
                }
                cart.setChildProductVOList(childProductVOList);
                cart.setIsCombination(1);
            }
            // 获取自提标识
            ProductExtend productExtend = productPriceVO.getProductExtend();
            cart.setIsSelfLift(productExtend == null ? 0 : productExtend.getIsSelfLift());
            // 获取厂商模式标识
            cart.setMfrCoopFlag(productPriceVO.getMfrCoopFlag() == null ? 0 : productPriceVO.getMfrCoopFlag());
            cart.setFundsBorrowable(productPriceVO.getGoods().getFundsBorrowable());
            // 起订量
            cart.setMinimumOrderQuantity(productPriceVO.getProduct().getMinimumPurchaseAmount());
            cart.setIncludedService(productPriceVO.getGoods().getIncludedService());
            // 商品运费计算模式
            cart.setFreightModel(productPriceVO.getFreightModel());
            return cart;
        }).collect(Collectors.toList());

        /**
         * 下单校验cartPOList
         */
        if (isSubmit) {
            submitCheck(collect);
        }

        //构造计算优惠dto
        OrderSubmitDTO orderSubmitDTO = new OrderSubmitDTO(collect, dto);
        if (singlePromotion != null && !dto.getIsAloneBuy()) {
            // dto活动参数赋值
            promotionUtils.promotionParamSet(singlePromotion, dto, orderSubmitDTO);
        }

        return orderSubmitDTO;
    }

	private void submitCheck(List<Cart> cartList) {
		log.info("submitCheck with CartList:{}", JSONObject.toJSONString(cartList));
		StringBuilder cantSale = new StringBuilder();
		StringBuilder stateInvalid = new StringBuilder();
		StringBuilder stateLock = new StringBuilder();
		StringBuilder virtualGoods = new StringBuilder();
		StringBuilder miniBuyNum = new StringBuilder();
		int purchaseNum = 0;
		int mrfNum = 0;
		int fundsBorrowableNum = 0;
		int selfNum = 0;
		Set<Long> orderInfoNum = new HashSet<>();
		List<Long> goodsIdList = new ArrayList<>(cartList.size());
		for (Cart cart : cartList) {
			orderInfoNum.add(cart.getStoreId());
			goodsIdList.add(cart.getGoodsId());
			// 区域编码校验
			if (StringUtils.isEmpty(cart.getAreaCode())) {
				cantSale.append("【").append(cart.getGoodsName()).append("】");
			}
			// 商品有效性判断
			if (cart.getProductState() == CartConst.STATTE_INVALID) {
				stateInvalid.append("【").append(cart.getGoodsName()).append("】");
			}
			// 商品缺货判断
			if (cart.getProductState() == CartConst.STATTE_LACK) {
				ProductPriceVO productPriceVO = orderSubmitAttributesUtils.getProductPriceByProductId(
						cart.getProductId(), cart.getAreaCode(), cart.getFinanceRuleCode());
				stateLock.append("商品").append("【").append(cart.getGoodsName()).append("】")
						.append("库存不足，该地区剩余库存为").append(productPriceVO.getEffectiveStock()).append("；");
			}
			// 商品购买数量判断
			if (cart.getBuyNum().compareTo(0) <= 0) {
				throw new BusinessException("商品" + cart.getGoodsName() + "购买数量不能小于等于0");
			}
			// 内购商品校验员工是否为内部员工
			if (cart.getIsVirtualGoods() == 10) {
				virtualGoods.append("【").append(cart.getGoodsName()).append("】");
			}
			// 商品拦截 提示优化
			ProductPriceVO productPriceVO = orderSubmitAttributesUtils.getProductPriceByProductId(
					cart.getProductId(), cart.getAreaCode(), cart.getFinanceRuleCode());
			if (!productPriceVO.getJudgeFlag() && productPriceVO.getProductReasonTypeEnum() != null) {
				throw new BusinessException("商品" + cart.getGoodsName() + "暂不可售，原因："
						+ productPriceVO.getProductReasonTypeEnum().getDesc());
			}
			//判断厂商合作商品
			if (OrderConst.ENABLED_FLAG_Y.equals(cart.getMfrCoopFlag())) {
				mrfNum++;
			}
			//现款现货商品
			if (NumberUtils.INTEGER_ONE.equals(cart.getFundsBorrowable())) {
				fundsBorrowableNum++;
			}
			//判断自提商品
			if (OrderConst.ENABLED_FLAG_Y.equals(cart.getIsSelfLift())) {
				selfNum++;
			}
			//起订量判断
			if (Objects.nonNull(cart.getMinimumOrderQuantity()) &&
					cart.getMinimumOrderQuantity() > cart.getBuyNum()) {
				miniBuyNum.append(cart.getGoodsName()).append("起订量为")
						.append(cart.getMinimumOrderQuantity()).append("；");
			}
			// 采购订单
			if (GoodsTypeEnum.PURCHASE.getCode().equals(cart.getProductType())) {
				purchaseNum++;
			}
		}
		if (cantSale.length() > 0) {
			throw new BusinessException("商品" + cantSale + "该地区暂不售卖～！");
		}
		if (stateInvalid.length() > 0) {
			throw new BusinessException("商品" + stateInvalid + "已失效不可购买～！");
		}
		if (stateLock.length() > 0) {
			throw new BusinessException(stateLock.toString());
		}
		if (miniBuyNum.length() > 0) {
			throw new BusinessException("很抱歉，未达到起订量，" + miniBuyNum);
		}
		if (virtualGoods.length() > 0) {
			Integer memberId = cartList.get(0).getMemberId();
            Member member = orderSubmitAttributesUtils.getMemberByMemberId(memberId);
			Boolean isInter = ExternalApiUtil.callResultApi(() -> bmsUserFacade.isInter(member.getMemberMobile()), member.getMemberMobile(),
					"/user/isInter", "根据手机号判断是否是内部员工");
			if (!isInter) {
				throw new BusinessException("抱歉，您不符合" + virtualGoods + "下单条件");
			}
		}
		if (cartList.size() > 1 && mrfNum >= 1) {
			throw new BusinessException("厂家合作的商品只能单个下单，请检查后重新下单～！");
		}
		if (cartList.size() > 1 && fundsBorrowableNum >= 1) {
			throw new BusinessException("现款现货的商品仅允许单独购买，请检查后重新下单～！");
		}
		if (orderInfoNum.size() > 1 && selfNum >= 1) {
			throw new BusinessException("跨店铺自提商品不允许同时下单，请检查后重新下单～！");
		}
		if (purchaseNum > 0) {
			if (purchaseNum < cartList.size()) {
				throw new BusinessException("采购商品不允许与普通商品一起购买，请检查后重新下单～！");
			} else {
				Integer memberId = cartList.get(0).getMemberId();
                Member member = orderSubmitAttributesUtils.getMemberByMemberId(memberId);
                CheckAuthParam param = new CheckAuthParam();
				param.setMember(member);
				param.setGoodsIdList(goodsIdList);
				JsonResult<CheckMemberAuthVo> result = ExternalApiUtil.callJsonResultApi(() -> purchaserFeign.checkMemberAuth(param), param,
						"/purchaser/checkMemberAuthr", "校验用户能否下单采购商品");
				if (Objects.isNull(result.getData()) || !result.getData().getCheckSuccess()) {
					throw new BusinessException(ErrorCodeEnum.U.ACCESS_DENIED.getCode(), result.getData().getErrorMsg());
				}
			}
		}

        boolean noRuleCode = false;
        boolean haveRuleCode = false;

        // 要么全部为有贴息标签、要么全部没有贴息标签
        Map<Long, String> ruleMap = new HashMap<>();
        // 同一店铺自提商品不能与非自提商品同时下单
        Map<Long, Integer> selfMap = new HashMap<>();
        for (Cart cart : cartList) {
            if (!StringUtil.isBlank(cart.getFinanceRuleCode())) {
                noRuleCode = true;
            } else {
                haveRuleCode = true;
            }
            if (ruleMap.containsKey(cart.getStoreId())) {
                String financeRuleCode = ruleMap.get(cart.getStoreId());
                if (!cart.getFinanceRuleCode().equals(financeRuleCode)) {
                    throw new BusinessException("不同分期规则商品不能同时下单");
                }
            }
            ruleMap.put(cart.getStoreId(), cart.getFinanceRuleCode());

            if (selfMap.containsKey(cart.getStoreId())) {
                Integer selfLift = selfMap.get(cart.getStoreId());
                if (!cart.getIsSelfLift().equals(selfLift)) {
                    throw new BusinessException("同一店铺自提商品不能与非自提商品同时下单，请检查后重新下单～");
                }
            }
            selfMap.put(cart.getStoreId(), cart.getIsSelfLift());

        }
        if (noRuleCode && haveRuleCode) {
            throw new BusinessException("分期商品与非分期商品不能同时下单");
        }
    }

    public OrderSubmitDTO getOrderSubmitDTOV2(List<CartPO> cartPOList, OrderSubmitParamDTO dto, boolean isSubmit) {

        List<Cart> collect = cartPOList.stream().map(x -> {
            Cart cart = new Cart();
            BeanUtils.copyProperties(x, cart);
            // 获取商品类型
            ProductPriceVO productPriceVO = orderSubmitAttributesUtils.getProductPriceByProductId(
                    x.getProductId(), x.getAreaCode(), x.getFinanceRuleCode());
            cart.setIsVirtualGoods(productPriceVO.getGoods().getIsVirtualGoods());
            List<CombinationChildGoodsVO> combinationChildGoodsVOList = productPriceVO.getCombinationChildGoodsVOList();
            if (CollectionUtil.isNotEmpty(combinationChildGoodsVOList)){
                List<OrderSubmitDTO.CombinationChildGoodsVO> childProductVOList = new ArrayList<>(combinationChildGoodsVOList.size());
                for (CombinationChildGoodsVO vo : combinationChildGoodsVOList) {
                    OrderSubmitDTO.CombinationChildGoodsVO childGoodsVO = new OrderSubmitDTO.CombinationChildGoodsVO();
                    BeanUtils.copyProperties(vo,childGoodsVO);
                    childProductVOList.add(childGoodsVO);
                }
                cart.setChildProductVOList(childProductVOList);
                cart.setIsCombination(1);
            }
            // 获取自提标识
            ProductExtend productExtend = productPriceVO.getProductExtend();
            cart.setIsSelfLift(productExtend == null ? 0 : productExtend.getIsSelfLift());
            // 获取厂商模式标识
            cart.setMfrCoopFlag(productPriceVO.getMfrCoopFlag() == null ? 0 : productPriceVO.getMfrCoopFlag());
            cart.setFundsBorrowable(productPriceVO.getGoods().getFundsBorrowable());
            // 起订量
            cart.setMinimumOrderQuantity(productPriceVO.getProduct().getMinimumPurchaseAmount());
            cart.setIncludedService(productPriceVO.getGoods().getIncludedService());
            return cart;
        }).collect(Collectors.toList());

        // 下单校验
        if (Boolean.TRUE.equals(isSubmit)) {
            submitCheck(collect);
        }

        return new OrderSubmitDTO(collect, dto);
    }

    public OrderSubmitDTO getOrderSubmitDTOV3(List<CartPO> cartPOList, OrderSubmitParamDTO dto, boolean isSubmit, boolean isCart) {

        OrderSubmitDTO orderSubmitDTO = getOrderSubmitDTOV2(cartPOList, dto, isSubmit);

        // 非购物车单品下单，查单品活动优惠
        if (cartPOList.size() == 1 && !isCart) {
            CartPO cartPO = cartPOList.get(0);
            ProductPriceVO productPriceVO = orderSubmitAttributesUtils.getProductPriceByProductId(
                    cartPO.getProductId(), cartPO.getAreaCode(), cartPO.getFinanceRuleCode());
            // 查询是否有单品活动
            GoodsPromotion singlePromotion = cartModel.getSinglePromotion(dto.getProductId(), cartPO.getMemberId());
            if (singlePromotion != null) {
                // 活动价格设置
                BigDecimal promotionPrice = orderLocalUtils.getPromotionPrice(dto, productPriceVO, singlePromotion);
                cartPO.setProductPrice(promotionPrice);
                // dto活动参数赋值
                promotionUtils.promotionParamSet(singlePromotion, dto, orderSubmitDTO);
            }
        }

        return orderSubmitDTO;
    }

    /**
     * 根据前端参数获取后台操作的dto
     *
     * @param dto      前端参数
     * @param memberId 会员id
     * @param orderPO  订单
     * @return
     */
    public OrderSubmitDTO getOrderSubmitDTO(OrderSubmitParamDTO dto, Integer memberId, OrderPO orderPO) {
        List<CartPO> cartPOList;
        Integer singlePromotionType = null;//需要单独提交订单的单品活动类型
        Integer singlePromotionId = null;//需要单独提交订单的单品活动id

        //查询订单货品
        OrderProductExample orderProductExample = new OrderProductExample();
        orderProductExample.setOrderSn(dto.getOrderSn());
        List<OrderProductPO> orderProductPOList = orderProductModel.getOrderProductList(orderProductExample, null);
        AssertUtil.notEmpty(orderProductPOList, "订单货品不存在");
        OrderProductPO orderProductPO = orderProductPOList.get(0);
        Product product = productFeignClient.getProductByProductId(orderProductPO.getProductId());
        CartPO cartPO = new CartPO();
        cartPO.setMemberId(memberId);
        cartPO.setStoreId(orderProductPO.getStoreId());
        cartPO.setStoreName(orderProductPO.getStoreName());
        cartPO.setGoodsId(orderProductPO.getGoodsId());
        cartPO.setGoodsName(orderProductPO.getGoodsName());
        cartPO.setBuyNum(orderProductPO.getProductNum());
        cartPO.setProductId(orderProductPO.getProductId());
        if (orderPO.getOrderType() == PromotionConst.PROMOTION_TYPE_103) {
            PresellOrderExtendExample example = new PresellOrderExtendExample();
            example.setOrderSn(dto.getOrderSn());
            List<PresellOrderExtendVO> orderExtendList = presellOrderExtendFeignClient.getPresellOrderExtendList(example);
            AssertUtil.notEmpty(orderExtendList, "获取预售订单扩展信息为空");
            PresellOrderExtendVO orderExtend = orderExtendList.get(0);
            cartPO.setProductPrice(orderExtend.getPresellPrice());

            singlePromotionType = PromotionConst.PROMOTION_TYPE_103;
            singlePromotionId = orderExtend.getPresellId();
        } else if (orderPO.getOrderType() == PromotionConst.PROMOTION_TYPE_105) {
            LadderGroupOrderExtend example = new LadderGroupOrderExtend();
            example.setOrderSn(dto.getOrderSn());
            List<LadderGroupOrderExtendVO> orderExtendList = ladderGroupOrderExtendFeignClient.getLadderGroupOrderExtendList(example);
            AssertUtil.notEmpty(orderExtendList, "获取阶梯团订单扩展信息为空");
            LadderGroupOrderExtendVO orderExtend = orderExtendList.get(0);
            cartPO.setProductPrice(orderExtend.getRemainAmount());

            singlePromotionType = PromotionConst.PROMOTION_TYPE_105;
            singlePromotionId = orderExtend.getGroupId();
        }
        cartPO.setProductImage(orderProductPO.getProductImage());
        cartPO.setSpecValues(orderProductPO.getSpecValues());
        cartPO.setIsChecked(CartConst.IS_CHECKED_YES);
        //获取单条购物车可参与的最优惠的活动
        cartModel.getBestPromotion(product, cartPO);
        cartPOList = Collections.singletonList(cartPO);

        log.debug("构造的购物车信息=================================================================" + cartPOList);

        List<Cart> collect = cartPOList.stream().map(x -> {
            Cart cart = new Cart();
            BeanUtils.copyProperties(x, cart);
            return cart;
        }).collect(Collectors.toList());

        //构造计算优惠dto
        OrderSubmitDTO orderSubmitDTO = new OrderSubmitDTO(collect, dto);
        if (singlePromotionType != null) {
            //单品活动下单
            for (OrderSubmitDTO.OrderInfo orderInfo : orderSubmitDTO.getOrderInfoList()) {
                orderInfo.setPromotionType(singlePromotionType);
                orderInfo.setPromotionId(singlePromotionId);
                if (promotionCommonFeignClient.specialOrder(singlePromotionType)) {
                    //需要订单扩展信息
                    orderInfo.setOrderType(singlePromotionType);
                }
                for (OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo : orderInfo.getOrderProductInfoList()) {
                    orderProductInfo.setSinglePromotionType(singlePromotionType);
                }
            }
        }
        return orderSubmitDTO;
    }
    private CartPO getCartPO(GoodsPromotion singlePromotion,OrderSubmitParamDTO dto, Integer memberId, OrderAddressDTO addressDTO,  Long productId,Integer num) {
        // 参数校验
        BizAssertUtil.isTrue(StringUtil.isNullOrZero(productId), "请选择要购买的商品");
        BizAssertUtil.isTrue(StringUtil.isNullOrZero(num), "请选择要购买的数量");
        BizAssertUtil.isTrue(num.compareTo(0) < 0, "购买数量不能小于0");
        // 获取店铺areaCod
        if (Objects.nonNull(addressDTO)) {
            String areaCode = orderCreateHelper.getStoreAreaCodeByAddress(dto.getStoreId(), addressDTO, memberId);
            dto.setAreaCode(areaCode);
        }
        // 获取商品信息
        ProductPriceVO productPriceByProductId = orderSubmitAttributesUtils.getProductPriceByProductId(
                productId, dto.getAreaCode(), dto.getFinanceRuleCode());

        /**
         * 刷新单独购买商品cartPO
         */
        CartPO cartPO = cartModel.refreshAloneBuy(memberId,num, dto.getAreaCode(), productPriceByProductId);

        // 查询是否有单品活动
        singlePromotion = cartModel.getSinglePromotion(productId, memberId);
        if (singlePromotion != null && !dto.getIsAloneBuy()) {
            BigDecimal promotionPrice = orderLocalUtils.getPromotionPrice(dto, productPriceByProductId, singlePromotion);
            cartPO.setProductPrice(promotionPrice);
        }
        return cartPO;
    }

    public List<OrderSkuInfoDTO> getOrderSkuInfoDTOS(OrderSubmitParamDTO dto) {
        BizAssertUtil.notEmpty(dto.getOrderProductInfoList(),"抱歉，您购买的分享订单商品信息不能为空");
        List<OrderSubmitParamDTO.OrderProductInfo> orderProductInfoList = dto.getOrderProductInfoList();
        List<OrderSkuInfoDTO> skuInfoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(orderProductInfoList)){
            for (OrderSubmitParamDTO.OrderProductInfo orderProductInfo : orderProductInfoList) {
                OrderSkuInfoDTO orderSkuInfoDTO = new OrderSkuInfoDTO();
                orderSkuInfoDTO.setProductId(orderProductInfo.getProductId());
                orderSkuInfoDTO.setNumber(orderProductInfo.getNumber());
                orderSkuInfoDTO.setAreaCode(orderProductInfo.getAreaCode());
                orderSkuInfoDTO.setFinanceRuleCode(orderProductInfo.getFinanceRuleCode());
                orderSkuInfoDTO.setStoreId(orderProductInfo.getStoreId());
                skuInfoList.add(orderSkuInfoDTO);
            }
        }
        return skuInfoList;
    }


    /**
     * 根据前端参数获取后台操作的dto
     *
     * @param dto         前端参数
     * @param memberId    会员id
     * @param addressDTO  下单地址
     * @param isSubmit    是否提交订单，检查、确认接口为否，不需要下单校验
     * @return
     */
    public OrderSubmitDTO getOrderSubmitDTOV4(OrderSubmitParamDTO dto, Integer memberId, OrderAddressDTO addressDTO,  boolean isSubmit) {

        /**
         * 非购物车下单，多个商品
         */
        BizAssertUtil.notEmpty(dto.getMultiProductInfoList(), "请选择需要购买的商品！");
        BizAssertUtil.isTrue(dto.getMultiProductInfoList().stream().anyMatch(temp -> Objects.isNull(temp.getProductId())), "请选择要购买的商品！");
        BizAssertUtil.isTrue(dto.getMultiProductInfoList().stream().anyMatch(temp -> Objects.isNull(temp.getNumber())), "请选择要购买的数量");
        BizAssertUtil.isTrue(dto.getMultiProductInfoList().stream().anyMatch(temp -> temp.getNumber() <= 0), "购买数量不能小于等于0");

        // 获取店铺areaCod
        if (Objects.nonNull(addressDTO)) {
            String areaCode = orderCreateHelper.getStoreAreaCodeByAddress(dto.getStoreId(), addressDTO, memberId);
            dto.setAreaCode(areaCode);
        }
        //获取商品信息
        List<Long> productIdList = dto.getMultiProductInfoList().stream().map(OrderSubmitParamDTO.MultiProductInfo:: getProductId).collect(Collectors.toList());
        Map<Long, ProductPriceVO> productBatchMap = orderSubmitAttributesUtils.getProductBatch(productIdList, dto.getAreaCode(), dto.getFinanceRuleCode());

        List<Cart> cartList = dto.getMultiProductInfoList().stream().map(productInfo -> {
            Cart cart = new Cart();

            ProductPriceVO productPriceVo = productBatchMap.get(productInfo.getProductId());
            // 构造cartPO
            CartPO cartPo = cartModel.assembleCartPO(memberId, productInfo.getNumber(), dto.getAreaCode(), productPriceVo, false);

            /**
             * 单品商品刷新：
             * 有效性判断，置为失效
             */
            if (cartUtils.isCartInvalid(cartPo, productPriceVo)) {
                cartPo.setProductState(CartConst.STATTE_INVALID);
                cartPo.setIsChecked(CartConst.IS_CHECKED_NO);
            }

            /**
             * 商品库存判断，置为缺货
             */
            Integer totalStock = productPriceVo.getEffectiveStock();
            if (cartPo.getBuyNum().compareTo(totalStock) > 0) {
                // sku库存不足时且未失效，置为缺货状态
                cartPo.setProductState(CartConst.STATTE_LACK);
                cartPo.setIsChecked(CartConst.IS_CHECKED_NO);
            }
            BeanUtils.copyProperties(cartPo, cart);

            // 获取商品类型
            ProductPriceVO productPriceVO = orderSubmitAttributesUtils.getProductPriceByProductId(
                    cartPo.getProductId(), cartPo.getAreaCode(), cartPo.getFinanceRuleCode());
            cart.setIsVirtualGoods(productPriceVO.getGoods().getIsVirtualGoods());
            List<CombinationChildGoodsVO> combinationChildGoodsVOList = productPriceVO.getCombinationChildGoodsVOList();
            if (CollectionUtil.isNotEmpty(combinationChildGoodsVOList)){
                List<OrderSubmitDTO.CombinationChildGoodsVO> childProductVOList = new ArrayList<>(combinationChildGoodsVOList.size());
                for (CombinationChildGoodsVO vo : combinationChildGoodsVOList) {
                    OrderSubmitDTO.CombinationChildGoodsVO childGoodsVO = new OrderSubmitDTO.CombinationChildGoodsVO();
                    BeanUtils.copyProperties(vo,childGoodsVO);
                    childProductVOList.add(childGoodsVO);
                }
                cart.setChildProductVOList(childProductVOList);
                cart.setIsCombination(1);
            }
            // 获取自提标识
            ProductExtend productExtend = productPriceVO.getProductExtend();
            cart.setIsSelfLift(productExtend == null ? 0 : productExtend.getIsSelfLift());
            // 获取厂商模式标识
            cart.setMfrCoopFlag(productPriceVO.getMfrCoopFlag() == null ? 0 : productPriceVO.getMfrCoopFlag());
            cart.setFundsBorrowable(productPriceVO.getGoods().getFundsBorrowable());
            // 起订量
            cart.setMinimumOrderQuantity(productPriceVO.getProduct().getMinimumPurchaseAmount());
            cart.setIncludedService(productPriceVO.getGoods().getIncludedService());
            // 商品运费计算模式
            cart.setFreightModel(productPriceVO.getFreightModel());


            return cart;
        }).collect(Collectors.toList());

        /**
         * 下单校验cartPOList
         */
        if (isSubmit) {
            submitCheck(cartList);
        }

        //构造计算优惠dto

        return new OrderSubmitDTO(cartList, dto);
    }

}
