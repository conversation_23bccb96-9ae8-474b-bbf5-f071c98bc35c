package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallgoods.facade.vo.StockCutVO;
import com.cfpamf.ms.mallorder.common.enums.BizTypeEnum;
import com.cfpamf.ms.mallorder.po.OrderSnapshotPO;

import java.util.List;

/**
 * <p>
 * 订单快照记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
public interface IOrderSnapshotService extends IService<OrderSnapshotPO> {

	Boolean saveOrderSnapshot(String bizSn, String subBizSn, BizTypeEnum bizType, String remark, Object snapshot, String operator);

	/**
	 * 保存订单商品库存批次信息
	 */
	Boolean saveOrderProductStockBatch(String bizSn, String subBizSn, BizTypeEnum bizType, List<StockCutVO> snapshot, String operator);

	Boolean refreshOrderSnapshot();

	/**
	 * 组装实体
	 *
	 * @return 快照实体
	 */
	List<OrderSnapshotPO> buildOrderProductStockVo(String bizSn, String subBizSn, BizTypeEnum bizType, List<StockCutVO> snapshot, String operator);

	/**
	 * 异步批量保存快照
	 *
	 * @param snapshotPOS 快照
	 */
    void saveBatchAsync(List<OrderSnapshotPO> snapshotPOS);
}
