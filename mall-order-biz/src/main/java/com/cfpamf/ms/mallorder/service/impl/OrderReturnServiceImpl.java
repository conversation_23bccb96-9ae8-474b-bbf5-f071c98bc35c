package com.cfpamf.ms.mallorder.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.dts.biz.api.channel.SkyCraneFeignClient;
import com.cfpamf.dts.biz.request.channel.skycrane.RefundedRequest;
import com.cfpamf.framework.autoconfigure.common.exception.MSBizNormalException;
import com.cfpamf.mallpayment.facade.vo.PaymentRefundNotifyVO;
import com.cfpamf.ms.customer.facade.vo.UserInfoVo;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.constant.OrdersAfsConst;
import com.cfpamf.ms.mallorder.common.constant.SentenceConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.constant.ExchangeOrderConst;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OperatorDTO;
import com.cfpamf.ms.mallorder.dto.OrderAfterDTO;
import com.cfpamf.ms.mallorder.dto.refund.RevokeRefundDTO;
import com.cfpamf.ms.mallorder.enums.CustomerConfirmStatusEnum;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderLogMapper;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.mapper.OrderReturnMapper;
import com.cfpamf.ms.mallorder.mapper.OrderReturnTrackMapper;
import com.cfpamf.ms.mallorder.model.*;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.BappOrderReturnRequest;
import com.cfpamf.ms.mallorder.req.base.RevokeRefundBaseRequest;
import com.cfpamf.ms.mallorder.req.exportreq.OrderRefundExportRequest;
import com.cfpamf.ms.mallorder.request.OrderProductExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.common.lock.Lock;
import com.cfpamf.ms.mallorder.v2.domain.vo.OperationUserVO;
import com.cfpamf.ms.mallorder.v2.domain.vo.RefundInfoExtraInfoVO;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.v2.service.OrderRefundRecordService;
import com.cfpamf.ms.mallorder.v2.strategy.LoanRefundStrategy;
import com.cfpamf.ms.mallorder.v2.strategy.context.LoanRefundStrategyContext;
import com.cfpamf.ms.mallorder.validation.OrderCancelValidation;
import com.cfpamf.ms.mallorder.validation.OrderReturnValidation;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallorder.vo.exportvo.OrderRefundExportVO;
import com.cfpamf.ms.mallorder.vo.refund.RestoreLimitRefundVO;
import com.cfpamf.ms.mallshop.api.VendorFeignClient;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.smartid.client.utils.sharding.SeqEnum;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import com.google.common.collect.Lists;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.StringUtil;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 订单正向操作 service
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/30 17:02
 */
@Slf4j
@Service
public class OrderReturnServiceImpl extends ServiceImpl<OrderReturnMapper, OrderReturnPO>
    implements IOrderReturnService {

    @Value("${spring.application.name}")
    private String appName;

    @Autowired
    private IOrderProductService orderProductService;
    @Autowired
    private IOrderAfterService orderAfterService;
    @Resource
    private IOrderReturnService orderReturnService;
    @Autowired
    private IOrderPlacingService orderPlacingService;
    @Autowired
    private OrderPresellService orderPresellService;
    @Autowired
    private IOrderInfoService orderInfoService;
    @Autowired
    private IOrderReturnTrackService orderReturnTrackService;
    @Autowired
    private IOrderAfterSaleLogService orderAfterSaleLogService;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderLogMapper orderLogMapper;
    @Resource
    private OrderReturnTrackMapper orderReturnTrackMapper;
    @Resource
    private OrderModel orderModel;
    @Resource
    private OrderReturnModel orderReturnModel;
    @Resource
    private OrderProductModel orderProductModel;
    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;
    @Resource
    private OrderLogModel orderLogModel;
    @Resource
    private OrderProductExtendModel orderProductExtendModel;
    @Resource
    private OrderReturnMapper orderReturnMapper;
    @Resource
    private VendorFeignClient vendorFeignClient;
    @Resource
    private ShardingId shardingId;
    @Resource
    private OrderCreateHelper orderCreateHelper;

    @Autowired
    private OrderRefundRecordService orderRefundRecordService;

    @Autowired
    private OrderCancelValidation orderCancelValidation;

    @Autowired
    private LoanRefundStrategyContext loanRefundStrategyContext;

    @Resource
    private SkyCraneFeignClient skyCraneFeignClient;

    @Resource
    private IOrderService orderService;

    @Resource
    private OrderReturnValidation orderReturnValidation;

    @Resource
    private IPerformanceService performanceService;

    @Resource
    private EmployeeService employeeService;

    @Resource
    private OrderLocalUtils orderLocalUtils;

    @Resource
    private IBzOrderProductCombinationService orderProductCombinationService;

    @Resource
    private CustomerIntegration customerIntegration;

    @Autowired
    private Lock lock;


    @Override
    public boolean isLastReturn(String afsSn) {

        OrderReturnPO orderReturnPO = this.getByAfsSn(afsSn);

        // 查出【平台审批完成】和【退款完成】的退订单
        LambdaQueryWrapper<OrderReturnPO> orderReturnListQuery = new LambdaQueryWrapper();
        orderReturnListQuery.eq(OrderReturnPO::getOrderSn, orderReturnPO.getOrderSn());
        orderReturnListQuery.eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        orderReturnListQuery.in(OrderReturnPO::getState,
                Arrays.asList(OrderReturnStatus.PLATFORM_AGREE.getValue(), OrderReturnStatus.REFUND_SUCCESS.getValue()));

        List<OrderReturnPO> orderReturnPOS = orderReturnMapper.selectList(orderReturnListQuery);
        List<String> afsSnList = orderReturnPOS.stream().map(OrderReturnPO::getAfsSn).collect(Collectors.toList());
        afsSnList.add(afsSn);

        LambdaQueryWrapper<OrderAfterPO> orderAfterQuery = new LambdaQueryWrapper();
        orderAfterQuery.eq(OrderAfterPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        orderAfterQuery.in(OrderAfterPO::getAfsSn, afsSnList);
        List<OrderAfterPO> orderAfterPOS = orderAfterService.list(orderAfterQuery);

        LambdaQueryWrapper<OrderProductPO> orderProductQuery = new LambdaQueryWrapper();
        orderProductQuery.eq(OrderProductPO::getOrderSn, orderReturnPO.getOrderSn());
        orderProductQuery.eq(OrderProductPO::getIsGift, OrderConst.IS_GIFT_NO);
        List<OrderProductPO> orderProductPOS = orderProductService.list(orderProductQuery);

        if (orderAfterPOS.size() < orderProductPOS.size()) {
            return false;
        }

        // 商品明细已退数量  key->orderProductId  value->汇总数量
        Map<String, Integer> aftNumMap = new HashMap<>();
        for (OrderAfterPO orderAfterPO : orderAfterPOS) {
            String orderProductId = String.valueOf(orderAfterPO.getOrderProductId());
            if (aftNumMap.containsKey(orderProductId)) {
                aftNumMap.put(orderProductId, aftNumMap.get(orderProductId) + orderAfterPO.getAfsNum());
            } else {
                aftNumMap.put(orderProductId, orderAfterPO.getAfsNum());
            }
        }

        for (OrderProductPO orderProductPO : orderProductPOS) {
            Integer aftNum = aftNumMap.get(String.valueOf(orderProductPO.getOrderProductId()));
            if (aftNum != null && aftNum < orderProductPO.getProductNum()) {
                return false;
            }
        }

        return true;
    }

    @Override
    public boolean isAllReturnedFinish(String orderSn) {

        // 查出【平台审批完成】和【退款完成】的退订单
        LambdaQueryWrapper<OrderReturnPO> orderReturnListQuery = new LambdaQueryWrapper();
        orderReturnListQuery.eq(OrderReturnPO::getOrderSn, orderSn);
        orderReturnListQuery.in(OrderReturnPO::getState,
            new Integer[] {OrderReturnStatus.PLATFORM_AGREE.getValue(), OrderReturnStatus.REFUND_SUCCESS.getValue()});

        List<OrderReturnPO> orderReturnPOS = this.list(orderReturnListQuery);
        if (CollectionUtils.isEmpty(orderReturnPOS)) {
            return false;
        }

        List<String> aftSns = new ArrayList<>(orderReturnPOS.size());
        orderReturnPOS.forEach(o -> aftSns.add(o.getAfsSn()));

        // 退订完成明细
        LambdaQueryWrapper<OrderAfterPO> orderAfterQuery = new LambdaQueryWrapper();
        orderAfterQuery.in(OrderAfterPO::getAfsSn, aftSns);
        List<OrderAfterPO> orderAfterPOS = orderAfterService.list(orderAfterQuery);

        // 购买商品记录
        LambdaQueryWrapper<OrderProductPO> orderProductQuery = new LambdaQueryWrapper();
        orderProductQuery.eq(OrderProductPO::getOrderSn, orderSn);
        orderProductQuery.eq(OrderProductPO::getIsGift, OrderConst.IS_GIFT_NO);
        List<OrderProductPO> orderProductPOS = orderProductService.list(orderProductQuery);

        if (orderAfterPOS.size() < orderProductPOS.size()) {
            return false;
        }

        // 商品明细已退数量 key->orderProductId value->汇总数量
        Map<String, Integer> aftNumMap = new HashMap<>();
        for (OrderAfterPO orderAfterPO : orderAfterPOS) {
            String orderProductId = String.valueOf(orderAfterPO.getOrderProductId());
            if (aftNumMap.containsKey(orderProductId)) {
                aftNumMap.put(orderProductId, aftNumMap.get(orderProductId) + orderAfterPO.getAfsNum());
            } else {
                aftNumMap.put(orderProductId, orderAfterPO.getAfsNum());
            }
        }

        for (OrderProductPO orderProductPO : orderProductPOS) {
            Integer aftNum = aftNumMap.get(String.valueOf(orderProductPO.getOrderProductId()));
            if (aftNum == null) {
                return false;
            }
            if (aftNum < orderProductPO.getProductNum()) {
                return false;
            }
        }

        return true;
    }

    /**
     * 判断该订单商品行是否全部退完
     *
     * @param orderProductId    订单商品id
     * @return                  true/false
     */
    public boolean isOrderProductAllReturn(Long orderProductId) {
        // 查询该订单商品
        LambdaQueryWrapper<OrderProductPO> orderProductWrapper = Wrappers.lambdaQuery();
        orderProductWrapper.eq(OrderProductPO::getOrderProductId, orderProductId);
        orderProductWrapper.eq(OrderProductPO::getIsGift, OrderConst.IS_GIFT_NO);
        OrderProductPO orderProductPO = orderProductService.getOne(orderProductWrapper);

        // 查询该商品行下的所有退款单
        LambdaQueryWrapper<OrderAfterPO> orderAfterQueryWrapper = Wrappers.lambdaQuery();
        orderAfterQueryWrapper.eq(OrderAfterPO::getOrderProductId, orderProductId);
        List<OrderAfterPO> orderAfterPOs = orderAfterService.list(orderAfterQueryWrapper);

        // 查询平台审批通过/退款成功的退款单
        LambdaQueryWrapper<OrderReturnPO> orderReturnQueryWrapper = Wrappers.lambdaQuery();
        orderReturnQueryWrapper.in(OrderReturnPO::getAfsSn, orderAfterPOs.stream().map(OrderAfterPO::getAfsSn).collect(Collectors.toList()))
                .in(OrderReturnPO::getState, Arrays.asList(OrderReturnStatus.PLATFORM_AGREE.getValue(), OrderReturnStatus.REFUND_SUCCESS.getValue()));
        List<OrderReturnPO> orderReturnPOs = this.list(orderReturnQueryWrapper);

        // 数量比较：已退数量 = 商品行数量
        int alreadyReturnNum = orderReturnPOs.stream().mapToInt(OrderReturnPO::getReturnNum).sum();

        return alreadyReturnNum == orderProductPO.getProductNum();
    }

    @Override
    public BigDecimal sumReturnedExpressFee(String orderSn) {
        BigDecimal result = orderReturnMapper.sumReturnedExpressFee(orderSn);
        if (result == null) {
            result = BigDecimal.ZERO;
        }
        return result;
    }

    @Override
    public OrderReturnPO getByAfsSn(String afsSn) {
        LambdaQueryWrapper<OrderReturnPO> orderReturnQuery = new LambdaQueryWrapper<>();
        orderReturnQuery.eq(OrderReturnPO::getAfsSn, afsSn);
        orderReturnQuery.eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return this.getOne(orderReturnQuery);
    }

    @Override
    public boolean hasDuringRefund(String orderSn) {
        LambdaQueryWrapper<OrderReturnPO> orderReturnQuery = new LambdaQueryWrapper<>();
        orderReturnQuery.eq(OrderReturnPO::getOrderSn, orderSn);
        orderReturnQuery.in(OrderReturnPO::getState, OrderReturnStatus.duringRefundStatus());
        orderReturnQuery.eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return super.count(orderReturnQuery) > 0;
    }

    @Override
    public boolean hasDuringRefundNoExchange(String orderSn) {
        LambdaQueryWrapper<OrderReturnPO> orderReturnQuery = new LambdaQueryWrapper<>();
        orderReturnQuery.eq(OrderReturnPO::getOrderSn, orderSn);
        orderReturnQuery.in(OrderReturnPO::getState, OrderReturnStatus.duringRefundStatus());
        orderReturnQuery.eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        orderReturnQuery.in(OrderReturnPO::getReturnType, Arrays.asList(ReturnTypeEnum.REFUND.getValue(), ReturnTypeEnum.RETURN_AND_REFUND.getValue()));
        return super.count(orderReturnQuery) > 0;
    }


    @Override
    public OrderReturnInfoForDbcVO orderAfterSaleDetailInfoFordbc(String orderSn, List<String> afsSns) {

        OrderReturnInfoForDbcVO returnInfoVO = new OrderReturnInfoForDbcVO();

        BigDecimal allActualReturnMoneyAmount = new BigDecimal(0);

        StringJoiner sfsSnsString = new StringJoiner(",");

        // 查询订单信息
        OrderPO orderPO = orderModel.getOrderByOrderSnLambda(orderSn);

        if (Objects.nonNull(orderPO)) {
            returnInfoVO.setOrderSn(orderSn);
            returnInfoVO.setOrderState(orderPO.getOrderState());
            returnInfoVO.setAllRefundFlag(
                this.isAllReturn(orderSn) ? OrderConst.IS_ALL_RETURN_YES : OrderConst.IS_ALL_RETURN_NO);
        }

        List<OrderReturnInfoForDbcVO.OrderAfterSaleInfoForDbcVO> afterSales = new ArrayList<>(afsSns.size());
        for (String afsSn : afsSns) {
            OrderReturnInfoForDbcVO.OrderAfterSaleInfoForDbcVO afterSaleVO =
                new OrderReturnInfoForDbcVO.OrderAfterSaleInfoForDbcVO();

            sfsSnsString.add(afsSn);
            // 查询退款商品信息
            OrderAfterPO orderAfterPO = orderAfterServiceModel.getOrderAfterByAfsSn(afsSn);

            afterSaleVO.setReturnId(orderAfterPO.getAfsId());
            afterSaleVO.setAfsSn(orderAfterPO.getAfsSn());
            afterSaleVO.setOrderSn(orderAfterPO.getOrderSn());
            afterSaleVO.setAfsNum(orderAfterPO.getAfsNum());
            afterSaleVO.setProductLastRefund(orderAfterPO.getProductLastRefund());
            // 查询订单商品明细信息
            LambdaQueryWrapper<OrderProductPO> orderProductQueryWrapper = Wrappers.lambdaQuery(OrderProductPO.class);
            orderProductQueryWrapper.eq(OrderProductPO::getOrderProductId, orderAfterPO.getOrderProductId())
                .eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            OrderProductPO orderProductPO = orderProductService.getOne(orderProductQueryWrapper);

            afterSaleVO.setProductId(orderProductPO.getProductId());
            afterSaleVO.setGoodsId(orderProductPO.getGoodsId());
            afterSaleVO.setPromotionId(String.valueOf(orderProductPO.getProductId()));

            OrderReturnPO orderReturnPO = orderReturnModel.getOrderReturnByOrderSn(afsSn);

            afterSaleVO.setReturnType(orderReturnPO.getReturnType());
            afterSaleVO.setActualReturnMoneyAmount(orderReturnPO.getActualReturnMoneyAmount());
            afterSaleVO.setCompleteTime(orderReturnPO.getCompleteTime());
            afterSaleVO.setState(orderReturnPO.getState());
            afterSaleVO.setProductDeliveryState(orderAfterPO.getProductDeliveryState());
            afterSales.add(afterSaleVO);

            // 查询金额信息
            if (Objects.nonNull(orderReturnPO)) {
                allActualReturnMoneyAmount = allActualReturnMoneyAmount.add(orderReturnPO.getActualReturnMoneyAmount());
                allActualReturnMoneyAmount = allActualReturnMoneyAmount.add(orderReturnPO.getReturnExpressAmount());
                returnInfoVO.setCompleteTime(orderReturnPO.getCompleteTime());
            }
        }

        returnInfoVO.setActualReturnMoneyAmount(allActualReturnMoneyAmount);
        returnInfoVO.setAfsSn(sfsSnsString.toString());

        returnInfoVO.setOrderAfterSaleInfo(afterSales);

        return returnInfoVO;
    }

    @Override
    public OrderReturnInfoForDbcVO orderAfterSaleDetailInfoForDbcWithOrderSn(String orderSn) {

        OrderReturnInfoForDbcVO returnInfoVO = new OrderReturnInfoForDbcVO();

        BigDecimal allActualReturnMoneyAmount = BigDecimal.ZERO;

        // 查询订单信息
        OrderPO orderPO = orderModel.getOrderByOrderSnLambda(orderSn);
        if (Objects.isNull(orderPO)) {
            throw new MSBizNormalException(String.valueOf(ErrorCodeEnum.S.DATA_NOT_FOUND.getCode()),
                    String.format("订单号%s不存在", orderSn));
        }
        returnInfoVO.setOrderSn(orderSn);
        returnInfoVO.setOrderState(orderPO.getOrderState());
        returnInfoVO.setAllRefundFlag(
                this.isAllReturn(orderSn) ? OrderConst.IS_ALL_RETURN_YES : OrderConst.IS_ALL_RETURN_NO);

        List<OrderReturnPO> orderReturnEntityList = this.getByOrderSn(orderSn);
        if (CollectionUtils.isEmpty(orderReturnEntityList)) {
            returnInfoVO.setActualReturnMoneyAmount(allActualReturnMoneyAmount);
            returnInfoVO.setOrderAfterSaleInfo(Collections.emptyList());
            return returnInfoVO;
        }

        List<OrderReturnInfoForDbcVO.OrderAfterSaleInfoForDbcVO> afterSales = new ArrayList<>(orderReturnEntityList.size());
        for (OrderReturnPO orderReturnPO : orderReturnEntityList) {
            if (!OrderReturnStatus.REFUND_SUCCESS.getValue().equals(orderReturnPO.getState())) {
                continue;
            }

            OrderReturnInfoForDbcVO.OrderAfterSaleInfoForDbcVO afterSaleVO =
                    new OrderReturnInfoForDbcVO.OrderAfterSaleInfoForDbcVO();
            // 查询退款商品信息
            OrderAfterPO orderAfterPO = orderAfterServiceModel.getOrderAfterByAfsSn(orderReturnPO.getAfsSn());

            afterSaleVO.setReturnId(orderAfterPO.getAfsId());
            afterSaleVO.setAfsSn(orderAfterPO.getAfsSn());
            afterSaleVO.setOrderSn(orderAfterPO.getOrderSn());
            afterSaleVO.setAfsNum(orderAfterPO.getAfsNum());
            afterSaleVO.setProductLastRefund(orderAfterPO.getProductLastRefund());
            // 查询订单商品明细信息
            LambdaQueryWrapper<OrderProductPO> orderProductQueryWrapper = Wrappers.lambdaQuery(OrderProductPO.class);
            orderProductQueryWrapper.eq(OrderProductPO::getOrderProductId, orderAfterPO.getOrderProductId())
                    .eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
            OrderProductPO orderProductPO = orderProductService.getOne(orderProductQueryWrapper);

            afterSaleVO.setProductId(orderProductPO.getProductId());
            afterSaleVO.setGoodsId(orderProductPO.getGoodsId());
            afterSaleVO.setPromotionId(String.valueOf(orderProductPO.getProductId()));

            afterSaleVO.setReturnType(orderReturnPO.getReturnType());
            afterSaleVO.setActualReturnMoneyAmount(orderReturnPO.getActualReturnMoneyAmount());
            afterSaleVO.setCompleteTime(orderReturnPO.getCompleteTime());
            afterSaleVO.setState(orderReturnPO.getState());
            afterSaleVO.setProductDeliveryState(orderAfterPO.getProductDeliveryState());
            afterSales.add(afterSaleVO);

            allActualReturnMoneyAmount = allActualReturnMoneyAmount.add(orderReturnPO.getActualReturnMoneyAmount());
            allActualReturnMoneyAmount = allActualReturnMoneyAmount.add(orderReturnPO.getReturnExpressAmount());
            returnInfoVO.setCompleteTime(orderReturnPO.getCompleteTime());
        }

        returnInfoVO.setActualReturnMoneyAmount(allActualReturnMoneyAmount);

        returnInfoVO.setOrderAfterSaleInfo(afterSales);

        return returnInfoVO;
    }

    @Override
    public OrderReturnInfoVO getOrderReturnDetailInfo(String afsSn) {
        OrderReturnInfoVO orderReturnInfoVO = new OrderReturnInfoVO();

        // 查询退款金额信息
        OrderReturnPO orderReturnPO = orderReturnModel.getOrderReturnByOrderSn(afsSn);

        orderReturnInfoVO.setReturnType(orderReturnPO.getReturnType());

        // 查询订单信息
        OrderPO orderPO = orderModel.getOrderByOrderSnLambda(orderReturnPO.getOrderSn());
        orderReturnInfoVO.setOrderSn(orderPO.getOrderSn());
        orderReturnInfoVO.setOrderStatus(orderPO.getOrderState());
        orderReturnInfoVO.setStoreId(orderPO.getStoreId());
        orderReturnInfoVO.setIsSelf(orderPO.getStoreIsSelf());
        if (orderReturnPO.getRefundType().equals(RefundType.ASSIST_PAYMENT.getValue())
            || orderReturnPO.getRefundType().equals(RefundType.RESTORE_LIMIT.getValue())) {
            orderReturnInfoVO
                .setLendSuccess(LoanStatusEnum.LENDING_SUCCESS.getValue().equals(orderPO.getLoanPayState()));
        }

        // 查询订单日志信息
        LambdaQueryWrapper<OrderLogPO> orderLogQueryWrapper = Wrappers.lambdaQuery(OrderLogPO.class);
        orderLogQueryWrapper.eq(OrderLogPO::getOrderSn, orderPO.getOrderSn())
            .eq(OrderLogPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y).orderByDesc(OrderLogPO::getCreateTime)
            .last("limit 1");
        OrderLogPO orderLogPO = orderLogMapper.selectOne(orderLogQueryWrapper);
        orderReturnInfoVO.setOrderPreStatus(orderLogPO.getOrderPreState());

        // 查询退款商品信息
        OrderAfterPO orderAfterPO = orderAfterServiceModel.getOrderAfterByAfsSn(afsSn);
        log.info("orderAfterPO.getGiftReturnOrderProductId:{}", orderAfterPO.getGiftReturnOrderProductId());
        if (StringUtils.isNotEmpty(orderAfterPO.getGiftReturnOrderProductId())) {
            List<String> giftReturnOrderProductId = Arrays.asList(orderAfterPO.getGiftReturnOrderProductId().split(","));
            List<OrderProductPO> orderProductPOS = orderProductService.lambdaQuery().in(OrderProductPO::getOrderProductId, giftReturnOrderProductId).list();
            log.info("getGiftReturnOrderProductId:{}", JSON.toJSONString(orderProductPOS));
            List<GiftOrderReturnProductInfoVO> giftOrderReturnProductInfoVOS = orderProductPOS.stream().map(x -> {
                GiftOrderReturnProductInfoVO infoVO = new GiftOrderReturnProductInfoVO();
                infoVO.setAfsSn(orderAfterPO.getAfsSn());
                infoVO.setAfsNum(x.getProductNum());
                infoVO.setProductId(x.getProductId());
                infoVO.setCompleteTime(orderReturnPO.getCompleteTime());
                infoVO.setReturnType(orderReturnPO.getReturnType());
                infoVO.setSkuMaterialCode(x.getSkuMaterialCode());
                infoVO.setChannelSource(x.getChannelSource());
                infoVO.setChannelSkuUnit(x.getChannelSkuUnit());
                infoVO.setChannelSkuId(x.getChannelSkuId());
                return infoVO;
            }).collect(Collectors.toList());
            orderReturnInfoVO.setGiftOrderReturnProductInfoVOList(giftOrderReturnProductInfoVOS);
        }
        // 查询原商品信息
        OrderProductPO orderProductPO = orderProductModel.getOrderProductById(orderAfterPO.getOrderProductId());
        // 查询原商品扩展信息
        LambdaQueryWrapper<OrderProductExtendPO> queryWrapper = Wrappers.lambdaQuery(OrderProductExtendPO.class);
        queryWrapper.eq(OrderProductExtendPO::getOrderProductId, orderProductPO.getOrderProductId())
            .eq(OrderProductExtendPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        List<OrderProductExtendPO> orderProductExtendPOs =
            orderProductExtendModel.getOneOrderProductExtendPOByQueryWrapper(queryWrapper);

        OrderReturnProductInfoVO returnProductInfoVO = new OrderReturnProductInfoVO();
        if (CollectionUtil.isNotEmpty(orderProductExtendPOs)) {
            StringJoiner promotionIdJoin = new StringJoiner(",");
            orderProductExtendPOs.forEach(extend -> {
                promotionIdJoin.add(extend.getPromotionId());
            });
            returnProductInfoVO.setPromotionId(promotionIdJoin.toString());
        }
        returnProductInfoVO.setAfsSn(orderReturnPO.getAfsSn());
        returnProductInfoVO.setState(orderReturnPO.getState());
        returnProductInfoVO.setReturnAmount(orderReturnPO.getReturnMoneyAmount());
        returnProductInfoVO.setActualReturnMoneyAmount(orderReturnPO.getActualReturnMoneyAmount());
        returnProductInfoVO.setRefundPunishAmount(orderReturnPO.getRefundPunishAmount());
        returnProductInfoVO.setAfsNum(orderReturnPO.getReturnNum());
        returnProductInfoVO.setProductId(orderProductPO.getProductId());
        returnProductInfoVO.setDistributeParentProductId(orderProductPO.getDistributeParent());
        returnProductInfoVO.setGoodsId(orderAfterPO.getGoodsId());
        returnProductInfoVO.setGoodsState(orderAfterPO.getGoodsState());
        returnProductInfoVO.setProductShowPrice(orderProductPO.getProductShowPrice());
        returnProductInfoVO.setReturnExpressAmount(orderReturnPO.getReturnExpressAmount());
        returnProductInfoVO.setSkuMaterialCode(orderProductPO.getSkuMaterialCode());
        returnProductInfoVO.setChannelSource(orderProductPO.getChannelSource());
        returnProductInfoVO.setChannelSkuUnit(orderProductPO.getChannelSkuUnit());
        returnProductInfoVO.setChannelSkuId(orderProductPO.getChannelSkuId());
        returnProductInfoVO.setChannelNewSkuId(orderProductPO.getChannelNewSkuId());
        returnProductInfoVO.setStoreAfsAddress(orderAfterPO.getStoreAfsAddress());

        // 计算优惠金额
        BigDecimal discountAmount = orderProductPO.getStoreActivityAmount()
            .add(orderProductPO.getPlatformActivityAmount()).add(orderProductPO.getStoreVoucherAmount())
            .add(orderProductPO.getPlatformVoucherAmount()).add(orderProductPO.getIntegralCashAmount())
            .divide(new BigDecimal(orderProductPO.getProductNum()), 2, BigDecimal.ROUND_HALF_DOWN)
            .multiply(new BigDecimal(orderReturnPO.getReturnNum()));

        returnProductInfoVO.setDiscountAmount(discountAmount);
        returnProductInfoVO.setCustomerAssumeAmount(orderReturnPO.getCustomerAssumeAmount());
        returnProductInfoVO.setOtherCompensationAmount(orderReturnPO.getOtherCompensationAmount());
        returnProductInfoVO.setCompleteTime(orderReturnPO.getCompleteTime());
        returnProductInfoVO.setReturnType(orderReturnPO.getReturnType());

        orderReturnInfoVO.setOrderReturnProductInfo(returnProductInfoVO);

        return orderReturnInfoVO;
    }

    /**
     * 判断该订单是否整单退
     *
     * @param orderSn
     *            订单号
     * @return 是否整单退:1-是 0-否
     */
    private boolean isAllReturn(String orderSn) {

        List<OrderProductPO> orderProductPOs = orderProductModel.getOrderProductListByOrderSn(orderSn);

        for (OrderProductPO orderProductPO : orderProductPOs) {
            if (!orderProductPO.getProductNum().equals(orderProductPO.getReturnNumber())) {
                return false;
            }
        }

        return true;
    }

    /**
     *  支持商家取消和用户取消
     *
     * 订单取消: 1、定时任务自动取消依旧走该旧方法，注意维护（无测试跟进测试，适当时候再处理）：
     * （com.cfpamf.ms.mallorder.model.OrderModel#cancelOrder(java.util.List, java.lang.String, java.lang.String,
     * java.lang.Integer, java.lang.Long, java.lang.String, java.lang.String, java.lang.Integer)） 2、客户取消：不涉及退款，
     * 3、商家取消，对已支付订单涉及退款（已发货商家不可以取消）
     */
    @GlobalTransactional
    @Override
    public void cancelOrder(List<OrderPO> orderPOList, String cancelReason, String cancelRemark, Integer optRole,
        Long optUserId, String optUserName, String optRemark, Integer returnBy, String channel) {
        orderPOList.forEach(orderDb -> {
            BizAssertUtil.isTrue(ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == orderDb.getExchangeFlag(), "换货订单不允许需取消订单");
            LambdaQueryWrapper<OrderReturnPO> orderReturnQuery = Wrappers.lambdaQuery(OrderReturnPO.class);
            orderReturnQuery.eq(OrderReturnPO::getOrderSn, orderDb.getOrderSn())
                .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .notIn(OrderReturnPO::getState, OrderReturnStatus.endStatus());
            int orderReturnCount = orderReturnService.count(orderReturnQuery);
            BizAssertUtil.isTrue(orderReturnCount > 0, "订单已经存在退款记录, 不允许取消：" + orderDb.getOrderSn());
            //存在出库中商品不允许售后
            BizAssertUtil.isTrue(performanceService.outbounding(orderDb.getOrderSn()), "该订单包含出库中的商品, 不允许取消：" + orderDb.getOrderSn());
            if (orderDb.getOrderState() == OrderConst.ORDER_STATE_10
                    && orderDb.getOrderType() != com.cfpamf.ms.mallpromotion.constant.PromotionConst.PROMOTION_TYPE_107) {
                if (returnBy == OrderConst.RETURN_BY_1 && CustomerConfirmStatusEnum.isDraftOrUnconfirmed(orderDb.getCustomerConfirmStatus())) {
                    log.info("客户取消订单，客户确认状态为拒绝。orderSn:{}", orderDb.getOrderSn());
                    orderService.setCustomerConfirmRefuseStatus(orderDb.getOrderSn(), optRole, optUserId, optUserName);
                } else if (CustomerConfirmStatusEnum.isDraftOrUnconfirmed(orderDb.getCustomerConfirmStatus())) {
                    log.info("非客户取消订单，且为未确认的订单，客户确认状态为关闭。orderSn:{}", orderDb.getOrderSn());
                    orderService.setCustomerConfirmClosedStatus(orderDb.getOrderSn(), optRole, optUserId, optUserName);
                }

                BizAssertUtil.isTrue(!orderCancelValidation.dealOrderBeforeCancel(orderDb), "订单暂不支持取消，请稍后~");
                this.orderCancelWithoutRefund(orderDb, cancelReason, cancelRemark, optUserId, optUserName);
                if (returnBy == OrderConst.RETURN_BY_2) {
                    orderReturnModel.handleReturnPlatformCoupon(orderDb, null);
                }
            } else if (orderDb.getOrderState() == OrderConst.ORDER_STATE_15
                    && orderDb.getPaymentCode().equals(PayMethodEnum.BANK_TRANSFER.getValue())){
                BizAssertUtil.isTrue(!orderCancelValidation.dealOrderBeforeCancel(orderDb), "订单暂不支持取消，请稍后~");
                this.orderCancelWithoutRefund(orderDb, cancelReason, cancelRemark, optUserId, optUserName);
                if (returnBy == OrderConst.RETURN_BY_2) {
                    orderReturnModel.handleReturnPlatformCoupon(orderDb, null);
                }
            } else if ((orderDb.getOrderState() == OrderConst.ORDER_STATE_20 || OrderTypeEnum.isOfflineAll(orderDb.getOrderType()))
                    && orderDb.getOrderType() != com.cfpamf.ms.mallpromotion.constant.PromotionConst.PROMOTION_TYPE_107) {
                this.orderCancelWithRefund(orderDb, cancelReason, returnBy, optUserId, channel);
            } else if (orderDb.getOrderType() == com.cfpamf.ms.mallpromotion.constant.PromotionConst.PROMOTION_TYPE_107) {
                BizAssertUtil.isTrue(!orderCancelValidation.dealOrderBeforeCancel(orderDb), "订单暂不支持取消，请稍后~");
                orderPresellService.cancelPresellOrder(orderDb, new OperationUserVO(optRole, returnBy, optUserId, optUserName, optRemark));
            }

            // 2.记录订单日志
            orderLogModel.insertOrderLog(optRole, optUserId, optUserName, orderDb.getOrderSn(), orderDb.getOrderState(),
                    OrderConst.ORDER_STATE_0, LoanStatusEnum.DEFAULT.getValue(), optRemark, OrderCreateChannel.getEnumByValue(channel));
        });

        // 5.客户取消，订单未付款，返还使用的平台优惠券，记录优惠券日志
        if (returnBy == OrderConst.RETURN_BY_1 || returnBy == OrderConst.RETURN_BY_4) {
            orderModel.orderCancelReturnPlatformCoupon(orderPOList);
        }
    }

    /**
     * 订单取消：不涉及退款
     */
    @Override
    public void orderCancelWithoutRefund(OrderPO orderDb, String cancelReason, String cancelRemark, Long optUserId,
        String optUserName) {
        //Andy.2022.05.30
        if (orderDb.getOrderType() == com.cfpamf.ms.mallpromotion.constant.PromotionConst.PROMOTION_TYPE_107
                && !orderDb.getOrderState().equals(OrderStatusEnum.WAIT_PAY_DEPOSIT.getValue())) {
        	log.warn("已付定金的预付订单不可取消，orderState:{} orderSn:{}",orderDb.getOrderState(),orderDb.getOrderSn());
            throw new BusinessException("已付定金的预付订单不可取消");
        }

        log.info("【orderCancelWithoutRefund】orderSn:{} , xid:{}",orderDb.getOrderSn(),RootContext.getXID());

        // 1.修改订单状态，只有未付款的才能直接设置为已取消
        OrderPO updateOrderPO = new OrderPO();
        updateOrderPO.setOrderId(orderDb.getOrderId());
        updateOrderPO.setOrderState(OrderStatusEnum.CANCELED.getValue());
        updateOrderPO.setRefuseReason(cancelReason);
        updateOrderPO.setRefuseRemark(cancelRemark);
        boolean result = orderPlacingService.updateById(updateOrderPO);
        AssertUtil.isTrue(!result, "取消订单失败");

        // 更新商品的已退数量
        orderProductService.updateProductReturnNumAfterCancel(orderDb.getOrderSn());

        // 3.返还订单使用的积分，记录积分日志
        orderModel.orderCancelReturnIntegral(orderDb, optUserId, optUserName);

        // 4.返还使用的店铺优惠券，记录优惠券日志
        orderModel.orderCancelReturnStoreCoupon(orderDb);

        // 5.增加货品库存，增加商品库存
        orderModel.orderCancelAddGoodsStock(orderDb.getOrderSn(), orderDb.getOrderType(), orderDb.getAreaCode(),
            orderDb.getFinanceRuleCode(), orderDb);

        // 取消不涉及退款，发送取消通知
        orderCreateHelper.addOrderChangeEvent(orderDb, OrderEventEnum.CANCEL, new Date());
    }

    /**
     * 订单取消：涉及退款
     */
    private void orderCancelWithRefund(OrderPO orderDb, String cancelReason, Integer returnBy, Long optUserId,
        String channel) {
        orderCancelInsertAfterServiceAndReturn(orderDb, cancelReason, returnBy, optUserId, channel);
    }

    /**
     * 生成售后和商品退款记录
     *
     * @param orderDb
     *            订单信息
     * @param cancelReason
     *            取消理由
     * @param returnBy
     *            退款发起者
     */
    private void orderCancelInsertAfterServiceAndReturn(OrderPO orderDb, String cancelReason, Integer returnBy,
        Long optUserId, String channel) {
        BizAssertUtil.isTrue(OrderPatternEnum.COUPON_CENTRE.getValue().equals(orderDb.getOrderPattern()),
                "抱歉，卡券订单不允许进行退款");

        // 构造退款单信息
        OrderAfterDTO orderAfterDTO = new OrderAfterDTO();
        orderAfterDTO.setOrderSn(orderDb.getOrderSn());
        orderAfterDTO.setAfsType(OrdersAfsConst.AFS_TYPE_REFUND);
        orderAfterDTO.setGoodsState(OrdersAfsConst.GOODS_STATE_NO);
        orderAfterDTO.setApplyReasonContent(cancelReason);
        orderAfterDTO.setAfsDescription(cancelReason);
        orderAfterDTO.setFinalReturnAmount(orderDb.getPayAmount());
        orderAfterDTO.setReturnBy(returnBy);
        log.warn("取消订单涉及退款，请注意！:{}", orderDb.getOrderSn());
        // 查询订单货品
        OrderProductExample orderProductExample = new OrderProductExample();
        orderProductExample.setOrderSn(orderDb.getOrderSn());
        List<OrderProductPO> orderProductList = orderProductModel.getOrderProductList(orderProductExample, null);
        List<OrderAfterDTO.AfterProduct> afterProductList = orderProductList.stream().filter(x->x.getIsGift().equals(OrderConst.IS_GIFT_NO))
                .map((orderProduct -> {
            OrderAfterDTO.AfterProduct afterProduct = new OrderAfterDTO.AfterProduct();
            afterProduct.setOrderProductId(orderProduct.getOrderProductId());
            afterProduct.setAfsNum(orderProduct.getProductNum());
            afterProduct.setReturnAmount(orderProduct.getMoneyAmount());
            return afterProduct;
        })).collect(Collectors.toCollection(LinkedList::new));
        orderAfterDTO.setProductList(afterProductList);

        List<AfsProductVO> afsProductVOS = orderAfterServiceModel.dealAfsProduct(orderDb, orderAfterDTO);

        afsProductVOS.forEach(afsProductVO -> {
            BizAssertUtil.isTrue(!orderReturnValidation.refundProductCheck(afsProductVO.getOrderProductId()),
                    "商品存在退款中的退款单，不允许再次发起退款~");
            //商品的退货退款数量，不能超过发货数量
            if(OrdersAfsConst.AFS_TYPE_RETURN == orderAfterDTO.getAfsType()) {
                BizAssertUtil.isTrue(orderReturnValidation.validReturnNumGtDeliverNum(afsProductVO.getOrderProductId(), afsProductVO.getAfsNum()),
                        "商品的退货退款数量，不能超过发货数量~");
            }
            String afsSn = shardingId.next(SeqEnum.RNO, orderDb.getMemberId().toString()) + "";
            // 1.插入售后申请表， 退款明细
            orderAfterServiceModel.insertAfterService(afsSn, afsProductVO, orderAfterDTO, orderDb);
            // 2.根据售后类型插入退货表或者换货表，保存售后日志 退款主档
            OrderReturnPO orderReturnPO = orderAfterServiceModel.insertReturnOrder(afsSn, orderAfterDTO, afsProductVO, orderDb);
            // 3.更新订单货品中的退换货数量
            orderProductService.addReturnNumber(afsProductVO.getOrderProductId(), afsProductVO.getAfsNum());
            // 处理订单商品行退款状态
            orderProductService.dealOrderProductReturnStatus(CommonConst.PRODUCT_RETURN_APPLY,
                    afsProductVO.getOrderProductId());
            // 计算扣罚金额，实际退款金额需要减去扣罚金额 并更新
            if (orderReturnValidation.refundPunishAmountSupport(orderReturnPO)) {
                orderAfterServiceModel.recalculateRefundAmount(orderDb, orderReturnPO);
            }
            // 锁订单
            orderInfoService.dealOrderLock(orderDb.getOrderSn(), 1);
            // 4.记录退款轨迹轨迹（商家取消）
            Date now = new Date();
            Vendor vendor = vendorFeignClient.getVendorByVendorId(optUserId);
            OrderReturnTrackPO orderReturnTrackPO = OrderReturnTrackPO.builder().afsSn(afsSn)
                .operateType(OrderReturnOperateTypeEnum.STORE_CANCEL.getValue())
                .operator(vendor.getStore().getStoreName() + "-" + vendor.getVendorMobile()).operateTime(now)
                .operateResult(AuditResultEnum.AUDIT_PASS.getValue()).operateRemark(cancelReason).channel(channel)
                .build();
            orderReturnTrackMapper.insert(orderReturnTrackPO);

            //售后申请MQ
            orderCreateHelper.addOrderReturnEvent(orderDb, orderDb.getXzCardAmount(), orderReturnPO,
                    OrderEventEnum.REFUND_APPLY, now,null);
        });
    }

    @Override
    public List<OrderRefundExportVO> adminOrderRefundExport(OrderRefundExportRequest exportRequest) {
        returnStateHandle(exportRequest);
        List<OrderRefundExportVO> orderRefundExportList = orderReturnMapper.orderRefundExport(exportRequest);
        BizAssertUtil.notEmpty(orderRefundExportList, "导出信息为空");
        this.refundExportVoBindBillSn(orderRefundExportList);
        return orderRefundExportList;
    }

    /**
     * 退款状态处理
     *
     * @param exportRequest 导出请求
     */
    private void returnStateHandle(OrderRefundExportRequest exportRequest) {
        if (!StringUtil.isEmpty(exportRequest.getType()) && "audit".equals(exportRequest.getType())) {
            if (!StringUtil.isNullOrZero(exportRequest.getState())) {
                if (exportRequest.getState().equals(OrdersAfsConst.RETURN_STATE_203)) {
                    // 待处理
                    exportRequest.setStateIn(OrdersAfsConst.RETURN_STATE_200 + "," + OrdersAfsConst.RETURN_STATE_203);
                    exportRequest.setState(null);
                } else {
                    // 已完成
                    exportRequest.setState(OrdersAfsConst.RETURN_STATE_300);
                }
            } else {
                exportRequest.setStateIn(OrdersAfsConst.RETURN_STATE_200 + "," + OrdersAfsConst.RETURN_STATE_203 + ","
                    + OrdersAfsConst.RETURN_STATE_300);
                exportRequest.setState(null);
            }
        } else {
            if (!StringUtil.isNullOrZero(exportRequest.getState())) {
                if (exportRequest.getState().equals(OrdersAfsConst.RETURN_STATE_100)) {
                    exportRequest.setStateIn(OrdersAfsConst.RETURN_STATE_100 + "," + OrdersAfsConst.RETURN_STATE_101);
                    exportRequest.setState(null);
                } else if (exportRequest.getState().equals(OrdersAfsConst.RETURN_STATE_203)) {
                    exportRequest.setStateIn(OrdersAfsConst.RETURN_STATE_200 + "," + OrdersAfsConst.RETURN_STATE_203);
                    exportRequest.setState(null);
                }
            }
        }

        if (OrderReturnStatus.STORE_AGREE_RETURN.getValue().equals(exportRequest.getState())) {
            exportRequest.setState(null);
            exportRequest.setStateIn(OrderReturnStatus.STORE_AGREE_RETURN.getValue() + "," + OrderReturnStatus.SELF_PICKUP_POINT_RETURN_APPLY.getValue());
        }

    }

    @Override
    public List<OrderRefundExportVO> sellerOrderRefundExport(OrderRefundExportRequest exportRequest) {
        returnStateHandle(exportRequest);
        List<OrderRefundExportVO> orderRefundExportList = orderReturnMapper.orderRefundExport(exportRequest);
        BizAssertUtil.notEmpty(orderRefundExportList, "导出信息为空");
        this.refundExportVoBindBillSn(orderRefundExportList);
        return orderRefundExportList;
    }

    /**
     * 退款导出视图补充结算单号
     *
     * @param orderRefundExportList 导出的退款信息
     */
    private void refundExportVoBindBillSn(List<OrderRefundExportVO> orderRefundExportList) {
        List<String> bizLists = orderRefundExportList.stream().map(OrderRefundExportVO::getRefundNum)
                .collect(Collectors.toList());
        Map<String, String> biz2BillSn = this.getBiz2BillSn(bizLists);
        for (OrderRefundExportVO refundExportVo : orderRefundExportList) {
            refundExportVo.setBillSn(biz2BillSn.get(refundExportVo.getRefundNum()));
        }
    }

    /**
     * 查询业务单号对应结算单号信息
     *
     * @param bizSnList 业务单号集合
     * @return 业务单号对应结算单号：{key:业务单号,value:结算单号}
     */
    private Map<String, String> getBiz2BillSn(List<String> bizSnList) {
        Map<String, String> biz2Bill = new HashMap<>(bizSnList.size());
        List<List<String>> bizSnLists = Lists.partition(bizSnList, 1000);
        List<CompletableFuture> futureList = new ArrayList<>();
        try {
            for (List<String> bizSnSubList : bizSnLists) {
                CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                    Map<String, String> partMap = orderModel.orderBillList(bizSnSubList);
                    if (MapUtils.isNotEmpty(partMap)) {
                        biz2Bill.putAll(partMap);
                    }
                });
                futureList.add(completableFuture);
            }
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).get();
        } catch (Exception e) {
            log.error("退款单导出，查询结算单号异常，结算单号丢失", e);
        }
        return biz2Bill;
    }

    @Override
    public int getAfsCountInPlatformAuditOvertime(Integer overtime) {
        return orderReturnMapper.getAfsCountInPlatformAuditOvertime(overtime);
    }

    @Override
    public int getAfsCountInStoreAuditOvertime(Long storeId, Integer recommendStoreId) {
        if (recommendStoreId == null || recommendStoreId == 0) {
            return orderReturnMapper.getAfsCountInStoreAuditOvertime(3, storeId, null);
        }
        return orderReturnMapper.getAfsCountInStoreAuditOvertime(3, null, storeId);
    }

    /**
     * 根据支付方式的不同处理退款结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void dealRefundByPaymethod(RefundInfoExtraInfoVO refundInfoExtraInfoVO,PaymentRefundNotifyVO refundVO) {
        log.info("退款回调，orderOn:{}",refundVO.getOrderOn());
        List<OrderReturnPO> orderReturnPOList=null;
        if(ObjectUtils.isNotEmpty(refundInfoExtraInfoVO) && ObjectUtils.isNotEmpty(refundInfoExtraInfoVO.getRefundNo())){
            log.info("退款回调，预付 orderOn:{} refundInfoExtraInfoVO:{}",refundVO.getOrderOn(),refundInfoExtraInfoVO);
            OrderRefundRecordPO data = orderRefundRecordService.queryNoCloseRefundOrderByRefundNo(refundInfoExtraInfoVO.getRefundNo());
            if (ObjectUtils.isEmpty(data)) {
                log.error("退款回调时，该退订单不存在.refundNo:{} orderOn:{}",refundInfoExtraInfoVO.getRefundNo(),refundVO.getOrderOn());
                throw new MallException("退款回调时，该退订单不存在!");
            }else {
                log.info("退款回调，预付 orderOn:{} AfsSn：{}",refundVO.getOrderOn(),data.getAfsSn());
                OrderReturnPO orderReturnPO= orderReturnService.getByAfsSn(data.getAfsSn());
                if(ObjectUtils.isNotEmpty(orderReturnPO)) {
                    orderReturnPOList=new ArrayList<>();
                    orderReturnPOList.add(orderReturnPO);
                }
            }
        }else {
            LambdaQueryWrapper<OrderReturnPO> orderReturnQuery = new LambdaQueryWrapper<>();
            orderReturnQuery.eq(OrderReturnPO::getAfsSn, refundVO.getRefundOn())
                .or(wrapper -> wrapper.eq(OrderReturnPO::getOrderSn, refundVO.getRefundOn()).eq(OrderReturnPO::getState,
                    OrderReturnStatus.PLATFORM_AGREE.getValue()));
            // 恢复额度传订单号、可能存在多笔退订单
            orderReturnPOList = orderReturnService.list(orderReturnQuery);
        }

        if (CollectionUtils.isEmpty(orderReturnPOList)) {
            log.error("退款回调时，该退订单不存在.orderOn:{}",refundVO.getOrderOn());
            throw new MallException("退款回调时，该退订单不存在!");
        }

        for (OrderReturnPO returnPO : orderReturnPOList) {
            if (refundVO.getRefundStatus() == 1) {
                // 退款回调成功时处理乡助卡金额
                orderReturnModel.refundSuccess(refundInfoExtraInfoVO,returnPO.getAfsSn());
            } else
            // 失败
            if (refundVO.getRefundStatus() == 3) {
                orderReturnModel.refundFail(returnPO.getAfsSn(), null);
            }
        }
    }

    @Override
    public List<String> getAllRestoreLimitOrders(String orderSn) {
        LambdaQueryWrapper<OrderReturnPO> queryWrapper = Wrappers.lambdaQuery(OrderReturnPO.class);
        queryWrapper.select(OrderReturnPO::getAfsSn).eq(OrderReturnPO::getOrderSn, orderSn).notIn(
            OrderReturnPO::getState, Arrays.asList(OrderReturnStatus.STORE_REFUSED.getValue(),
                        OrderReturnStatus.PLATFORM_REFUSED.getValue(), OrderReturnStatus.REVOKE_REFUND.getValue()));
        List<OrderReturnPO> orderReturns = orderReturnMapper.selectList(queryWrapper);
        BizAssertUtil.notEmpty(orderReturns, "恢复额度退款查询该批次所有未关闭退款单号为空");
        return orderReturns.stream().map(OrderReturnPO::getAfsSn).collect(Collectors.toList());
    }

    public List<RestoreLimitRefundVO> getAllRestoreLimitRefundOrderInfos(String orderSn) {
        // 1、查询恢复额度该订单下所有的售后单
        List<OrderReturnPO> orderReturnPOs = this.lambdaQuery().eq(OrderReturnPO::getOrderSn, orderSn)
                .in(OrderReturnPO::getState, OrderReturnStatus.duringRefundStatus())
                .eq(OrderReturnPO::getEnabledFlag, CommonEnum.YES.getCode())
                .list();
        if (CollectionUtils.isEmpty(orderReturnPOs)) {
            return null;
        }

        // 2、构建返回 VO 对象
        List<RestoreLimitRefundVO> restoreLimitRefundVOs = new ArrayList<>();
        for (OrderReturnPO orderReturnPO : orderReturnPOs) {
            // 查询对应的售后信息
            OrderAfterPO orderAfterPO = orderAfterService.getByAfsSn(orderReturnPO.getAfsSn());
            // 查询对应的商品信息
            OrderProductPO orderProductPO = orderProductService.selectOneByOrderProductId(orderAfterPO.getOrderProductId());
            RestoreLimitRefundVO restoreLimitRefundVO = new RestoreLimitRefundVO(orderReturnPO.getAfsSn(), orderReturnPO.getState(),
                    orderProductPO.getGoodsName(), orderProductPO.getProductImage(), orderReturnPO.getReturnNum());
            restoreLimitRefundVOs.add(restoreLimitRefundVO);
        }

        return restoreLimitRefundVOs;
    }

    @GlobalTransactional
    public void cancelSpellOrders(List<String> orderSns) {
        // 查询订单列表
        LambdaQueryWrapper<OrderPO> orderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orderLambdaQueryWrapper.in(OrderPO::getOrderSn, orderSns)
            .notIn(OrderPO::getOrderState, Arrays.asList(OrderConst.ORDER_STATE_0, OrderConst.ORDER_STATE_50))
            .eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        List<OrderPO> orderPOList = orderMapper.selectList(orderLambdaQueryWrapper);

        // 单独处理单个订单，避免某个订单取消异常导致后续订单无法继续取消
        orderPOList.forEach(order -> {
            try {
                orderModel.cancelOrder(Collections.singletonList(order),
                    SentenceConst.SYSTEM_AUTO_CANCEL_SPELL_FAIL_ORDER, null, OrderConst.LOG_ROLE_VENDOR,
                    OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME, SentenceConst.SYSTEM_AUTO_CANCEL_SPELL_FAIL_ORDER,
                    OrderConst.RETURN_BY_0);
                // 营销模块处理拼团取消的订单
//                spellFeignClient.cancelOrder(order.getOrderSn());
            } catch (Exception e) {
                log.warn("拼团订单自动取消异常，订单信息：{}", order.toString(), e);
            }
        });
    }

    @Override
    public List<String> getApprovedMerchantOrderReturnList(String orderSn) {
        if (StringUtils.isEmpty(orderSn)) {
            return null;
        }
        LambdaQueryWrapper<OrderReturnPO> queryWrapper = Wrappers.lambdaQuery(OrderReturnPO.class);
        queryWrapper.select(OrderReturnPO::getAfsSn).eq(OrderReturnPO::getOrderSn, orderSn).eq(OrderReturnPO::getState,
            OrdersAfsConst.RETURN_STATE_200);
        List<OrderReturnPO> orderReturns = orderReturnMapper.selectList(queryWrapper);
        BizAssertUtil.notEmpty(orderReturns, "恢复额度退款查询该批次所有未关闭退款单号为空");
        return orderReturns.stream().map(OrderReturnPO::getAfsSn).collect(Collectors.toList());
    }


    /**
     * 获取云中鹤退款结果
     *
     * @param orderSn
     * @param afsSn
     * @return
     */
    @Override
    public Integer getYzhIsRefunded(String orderSn,String afsSn,String productId){
        log.info("getSupplyIsRefunded 获取供应商是否已退款结果参数,orderSn:{},afsSn:{}", orderSn, afsSn);
        Integer refoundState = null;
        RefundedRequest refundedRequest = new RefundedRequest();
        refundedRequest.setOrderSn(orderSn);
        refundedRequest.setAfsSn(afsSn);
        refundedRequest.setProductId(productId);
        JsonResult<Integer> jsonResult = skyCraneFeignClient.isRefunded(refundedRequest);
        log.info("getSupplyIsRefunded 获取供应商是否已退款结果：{}", JSONObject.toJSONString(jsonResult));
        BizAssertUtil.isTrue(jsonResult == null|| jsonResult.getState().intValue() != 200, "调用DTS获取云中鹤退款接口报错");
        return jsonResult.getData();
    }

    /**
     * Bapp客户经理获取管护关系用户售后列表(不包含换货订单)
     * @param request
     * @return
     */
    @Override
    public PageVO<OrderReturnVOV2> getBappOrderReturnList(BappOrderReturnRequest request) {
        AuthBranchAndUserVO vo = employeeService.getBranchAndUserList(request.getJobNumber(), request.getOrgCode());
        log.info("客户经理查看换货单列表入参:{}, AuthBranchAndUserVO:{}", JSONObject.toJSONString(request), JSONObject.toJSONString(vo));
        request.setBranchList(vo.getBranchList());
        request.setUserList(vo.getUserList());
        request.setStartRow(request.getPageSize() * (request.getNumber() - 1));
        // 如果分支权限与用户权限都没有，直接返回空集合
        if (CollectionUtils.isEmpty(request.getBranchList()) && CollectionUtils.isEmpty(request.getUserList())) {
            return new PageVO<>();
        }
        int count = orderReturnMapper.getBappOrderReturnCount(request);
        if (count == 0) {
            log.info("Bapp售后单查询数量为0");
            return new PageVO<>();
        }
        List<OrderReturnVOV2> orderReturnList = orderReturnMapper.getBappOrderReturnList(request);
        // 根据用户编码批量从客户中心获取用户信息
        List<String> userNoList = orderReturnList.stream().map(OrderReturnVOV2::getUserNo).collect(Collectors.toList());

        Map<String, UserInfoVo> userInfoVoMap = customerIntegration.queryUserInfoByUserNoList(userNoList, false);
        orderReturnList.forEach(orderReturnVOV2 -> {
            log.info("orderReturnVOV2:{}", JSON.toJSONString(orderReturnVOV2));
            List<OrderProductListVO> orderGiftProductListVOS = orderLocalUtils.getOrderGiftProductListVOS(
                    orderReturnVOV2.getOrderType(), orderReturnVOV2.getOrderSn(),
                    orderReturnVOV2.getGiftGroup(), orderReturnVOV2.getGiftReturnOrderProductId());
            orderReturnVOV2.setOrderGiftProductList(orderGiftProductListVOS);

            if (OrderConst.ORDER_TYPE_13 == orderReturnVOV2.getOrderType()) {
                LambdaQueryWrapper<BzOrderProductCombinationPO> queryOrder = Wrappers.lambdaQuery();
                queryOrder.eq(BzOrderProductCombinationPO::getOrderSn, orderReturnVOV2.getOrderSn())
                        .select(BzOrderProductCombinationPO::getMainProductId);
                BzOrderProductCombinationPO productCombinationPO = orderProductCombinationService.getOne(queryOrder);
                if (ObjectUtil.isNotNull(productCombinationPO) && orderReturnVOV2.getProductId().equals(productCombinationPO.getMainProductId())) {
                    orderReturnVOV2.setIsMain(1);
                }
            }

            // 因为订单保存的会员姓名均为手机号码(网关原因), 根据用户编码转换成用户名称
            UserInfoVo userInfoVo = userInfoVoMap.getOrDefault(orderReturnVOV2.getUserNo(), null);
            if (Objects.nonNull(userInfoVo)) {
                if (Objects.nonNull(userInfoVo.getCustInfoVo())) {
                    orderReturnVOV2.setMemberName(userInfoVo.getCustInfoVo().getCustName());
                } else if (!org.springframework.util.StringUtils.isEmpty(userInfoVo.getUserName())) {
                    orderReturnVOV2.setMemberName(userInfoVo.getUserName());
                }
            }
        });
        PagerInfo pagerInfo = new PagerInfo(request.getPageSize(), request.getNumber());
        pagerInfo.setRowsCount(count);
        return new PageVO<>(orderReturnList, pagerInfo);
    }

    @Override
    @Transactional
    public void revokeRefund(RevokeRefundBaseRequest revokeRequest, OrderRefundRevokingPartyEnum revokingParty, OperatorDTO operator) {
        // 增加分布式锁
        String lockKey = String.format("MALL_ORDER_REVOKE_REFUND_%s", revokeRequest.getAfsSn());
        lock.tryLockExecuteFunction(lockKey, 0, 30, TimeUnit.SECONDS, () -> {
            OrderReturnPO orderReturnPO = orderReturnService.getByAfsSn(revokeRequest.getAfsSn());
            OrderAfterPO orderAfterPO = orderAfterService.getByAfsSn(revokeRequest.getAfsSn());
            OrderPO orderPO = orderPlacingService.getByOrderSn(orderReturnPO.getOrderSn());
            BizAssertUtil.notNull(orderReturnPO, String.format("退款单%s查询为空，请检查~", revokeRequest.getAfsSn()));

            BizAssertUtil.isTrue(OrdersAfsConst.JD_INTERCEPT_STATE_INTERCEPTING == orderReturnPO.getJdInterceptStatus()
                            || OrdersAfsConst.JD_INTERCEPT_STATE_INTERCEPTED_SUCCESS == orderReturnPO.getJdInterceptStatus(),
                    "该售后单已对接第三方快递拦截，暂不支持撤销");

            // 退款失败过的订单不支持撤销
            BizAssertUtil.isTrue(orderReturnPO.getRefundFailTimes() > NumberUtils.INTEGER_ZERO, "退款失败过的订单不支持撤销");
            ReturnByEnum orderReturnStatus = ReturnByEnum.getByValue(orderReturnPO.getReturnBy());
            // 校验
            String errorMsg = OrderReturnValidation.validRevoke(orderReturnStatus, orderReturnPO.getReturnType(), orderReturnPO.getState());
            BizAssertUtil.isTrue(StringUtils.isNotBlank(errorMsg),errorMsg);
//            BizAssertUtil.isTrue(!OrderReturnValidation.validSupportRevoke(orderReturnPO.getReturnType(), orderReturnPO.getState()),
//                    "退款单当前状态不可撤销");

            // 更新售后单信息
            if (RefundType.RESTORE_LIMIT.getValue().equals(orderReturnPO.getRefundType())) {
                // 对于恢复额度的售后单撤销，需要将订单下的所以售后单撤销
                List<OrderReturnPO> restoreLimitRefunds = lambdaQuery().eq(OrderReturnPO::getOrderSn, orderPO.getOrderSn())
                        .in(OrderReturnPO::getState, OrderReturnStatus.duringRefundStatus())
                        .list();
                for (OrderReturnPO tempReturnPO : restoreLimitRefunds) {
                    // 对于退货退款，存在售后单客户已发货，禁止撤销。异常回滚
                    if (ReturnTypeEnum.RETURN_AND_REFUND.getValue().equals(tempReturnPO.getReturnType())) {
                        if (!OrderReturnStatus.RETURN_APPLY.getValue().equals(tempReturnPO.getState())
                                && !OrderReturnStatus.STORE_AGREE_RETURN.getValue().equals(tempReturnPO.getState())) {
                            throw new BusinessException("该订单存在退货退款单用户已发货，无法撤销售后");
                        }
                    }
                    OrderAfterPO tempRestoreLimitOrderAfterPO = orderAfterService.getByAfsSn(tempReturnPO.getAfsSn());
                    RevokeRefundDTO revokeRefundDTO = new RevokeRefundDTO(tempRestoreLimitOrderAfterPO.getAfsSn(), revokeRequest,
                            revokingParty, tempReturnPO, tempRestoreLimitOrderAfterPO, orderPO, operator);
                    this.doRevokeRefund(revokeRefundDTO);
                }
            } else {
                RevokeRefundDTO revokeRefundDTO = new RevokeRefundDTO(orderReturnPO.getAfsSn(), revokeRequest, revokingParty,
                        orderReturnPO, orderAfterPO, orderPO, operator);
                this.doRevokeRefund(revokeRefundDTO);
            }
            return true;
        });
    }

    /**
     * 执行撤销售后
     */
    private boolean doRevokeRefund(RevokeRefundDTO revokeRefundDTO) {
        // 读取参数
        String afsSn = revokeRefundDTO.getAfsSn();
        RevokeRefundBaseRequest revokeRequest = revokeRefundDTO.getRevokeRefundBaseRequest();
        OrderRefundRevokingPartyEnum revokingParty = revokeRefundDTO.getRevokingParty();
        OperatorDTO operator = revokeRefundDTO.getOperatorDTO();
        OrderPO orderPO = revokeRefundDTO.getOrderPO();
        OrderReturnPO orderReturnPO = revokeRefundDTO.getOrderReturnPO();
        OrderAfterPO orderAfterPO = revokeRefundDTO.getOrderAfterPO();

        // 更新退款单信息
        int returnUpdateCount = orderReturnMapper.revokeRefundUpdate(afsSn);
        AssertUtil.isTrue(returnUpdateCount == 0, String.format("更新退款单%s信息失败", revokeRequest.getAfsSn()));

        // 更新售后单信息
        boolean afterUpdateResult = orderAfterService.lambdaUpdate()
                .eq(OrderAfterPO::getAfsSn, afsSn)
                .eq(OrderAfterPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .set(OrderAfterPO::getRevokeParty, revokingParty.getValue())
                .set(OrderAfterPO::getRevokeReason, revokeRequest.getRevokeReason())
                .set(OrderAfterPO::getRevokeRemark, revokeRequest.getRemark())
                .set(OrderAfterPO::getRefundTerminationTime, new Date()).update();
        AssertUtil.isTrue(!afterUpdateResult, String.format("更新售后单%s信息失败", afsSn));

        // 恢复订单商品行已退数量
        boolean deductReturnNumResult = orderProductService.deductReturnNumber(orderAfterPO.getOrderProductId(), orderAfterPO.getAfsNum());
        AssertUtil.isTrue(!deductReturnNumResult, String.format("恢复订单商品行已退数量失败,退款单号:%s", afsSn));

        // 释放订单锁定状态
        boolean lockStateUpdateResult = orderInfoService.updateOrderLockStates(orderReturnPO.getOrderSn(), -1);
        AssertUtil.isTrue(!lockStateUpdateResult, String.format("撤销退款，修改订单锁定状态失败,退款单号:%s", afsSn));

        // 维护商品行的退款状态
        boolean productReturnStatusResult = orderProductService.dealOrderProductReturnStatus(CommonConst.PRODUCT_RETURN_REJECT,
                orderAfterPO.getOrderProductId());
        AssertUtil.isTrue(!productReturnStatusResult, String.format("撤销退款，维护商品行的退款状态失败,退款单号:%s", afsSn));

        // 针对特殊订单特殊处理
        LoanRefundStrategy loanRefundStrategy = loanRefundStrategyContext.getStrategy(orderPO.getOrderType());
        if (ObjectUtils.isNotEmpty(loanRefundStrategy)) {
            log.info("revokeRefund ，afsSn:{}", afsSn);
            loanRefundStrategy.refundRefuse(afsSn);
        }

        // 记录轨迹
        boolean saveResult = orderReturnTrackService.insertOrderReturnTrack(afsSn, OrderReturnOperateTypeEnum.REVOKE_REFUND, operator,
                AuditResultEnum.AUDIT_PASS.getValue(), revokingParty.getDesc() + OrderReturnOperateTypeEnum.REVOKE_REFUND.getDesc(),
                revokeRequest.getChannel());
        AssertUtil.isTrue(!saveResult, String.format("保存退款单%s轨迹失败", afsSn));

        // 记录退款日志
        Integer roleId = operator.getRoleId() == null ? 0 : operator.getRoleId();
        int operatorId = operator.getUserId() == null ? 0 : operator.getUserId();
        String userName = StringUtils.isAllBlank(operator.getUserName(), operator.getMobile()) ? "" : operator.getUserName() + "-" + operator.getMobile();
        String channel = revokeRequest.getChannel() == null ? "WEB" : revokeRequest.getChannel();
        boolean logSaveResult = orderAfterSaleLogService.insertOrderAfterSaleLog(roleId, (long) operatorId, userName,
                afsSn, orderReturnPO.getRefundType(), String.valueOf(orderReturnPO.getState()),
                revokingParty.getDesc() + OrderReturnOperateTypeEnum.REVOKE_REFUND.getDesc(), channel);
        AssertUtil.isTrue(!logSaveResult, String.format("保存退款单%s日志失败", afsSn));

        // 发送退款单撤销的消息
        orderCreateHelper.addOrderReturnEvent(orderPO, orderPO.getXzCardAmount(), orderReturnPO,
                OrderEventEnum.REVOKE_REFUND, new Date(), null);

        return true;
    }


    /**
     * 重置退款扣罚金额
     *
     * @param orderReturnPo         退款单
     * @param refundPunishAmount    重新传入的退款扣罚金额
     */
    public boolean resetRefundPunishAmount(OrderReturnPO orderReturnPo, BigDecimal refundPunishAmount) {
        log.info("重置退款扣罚金额处理, resetRefundPunishAmount afsSn: {}, originalAmount: {}, newAmount: {}",
                orderReturnPo.getAfsSn(), orderReturnPo.getRefundPunishAmount(), refundPunishAmount);

        if (Objects.isNull(refundPunishAmount)) {
            return Boolean.TRUE;
        }

        // 退款扣罚金额设置校验
        BizAssertUtil.isTrue((!orderReturnValidation.refundPunishAmountSupport(orderReturnPo) && refundPunishAmount.compareTo(BigDecimal.ZERO) > 0),
                String.format("退款扣罚%f元，非指定商家，不允许退款扣除", refundPunishAmount));

        // 扣罚金额不能大于退款申请金额（商品退款金额+运费）,组合退款不能大于贷款类的退款金额
        BizAssertUtil.isTrue(!orderReturnValidation.refundPunishAmountCheck(orderReturnPo, refundPunishAmount),
                "扣罚金额不能大于贷款类的退款金额");

        // 没扣罚金额变更，不做重置处理
        if (orderReturnPo.getRefundPunishAmount().compareTo(refundPunishAmount) == 0) {
            return Boolean.TRUE;
        }

        return this.lambdaUpdate().set(OrderReturnPO::getRefundPunishAmount, refundPunishAmount)
                .eq(OrderReturnPO::getAfsSn, orderReturnPo.getAfsSn())
                .eq(OrderReturnPO::getRefundPunishAmount, orderReturnPo.getRefundPunishAmount())
                .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .update();
    }

    @Override
    public OrderRefundMoneyDetail refundMoneyDetail(String afsSn) {
        OrderRefundMoneyDetail refundMoneyDetail = new OrderRefundMoneyDetail();
        OrderReturnPO orderReturnPO = this.getByAfsSn(afsSn);

        // 统一的信息设置
        refundMoneyDetail.setAfsSn(afsSn);
        refundMoneyDetail.setOrderSn(orderReturnPO.getOrderSn());
        refundMoneyDetail.setRefundEndTime(orderReturnPO.getRefundEndTime());
        refundMoneyDetail.setXzCardAmount(orderReturnPO.getXzCardAmount());
        refundMoneyDetail.setXzCardExpressFeeAmount(orderReturnPO.getXzCardExpressFeeAmount());
        if (StringUtils.isNotBlank(orderReturnPO.getReturnVoucherCode())) {
            refundMoneyDetail.setReturnCouponNum(orderReturnPO.getReturnVoucherCode().split(",").length);
        }

        // 组合退款的情况下，查出各类金额的信息
        if (RefundType.COMBINATION_REFUND.getValue().equals(orderReturnPO.getRefundType())) {
            List<OrderRefundRecordPO> orderRefundRecordPOs = orderRefundRecordService.queryRefundOrderByAfsSn(afsSn);
            if (CollectionUtils.isNotEmpty(orderRefundRecordPOs)) {
                for (OrderRefundRecordPO orderRefundRecordPO : orderRefundRecordPOs) {
                    if (RefundType.isLoanPayRefundType(RefundType.value(orderRefundRecordPO.getRefundType()))) {
                        refundMoneyDetail.setLoanRefundAmount(refundMoneyDetail.getLoanRefundAmount().add(orderRefundRecordPO.getActualAmount()));
                    } else {
                        refundMoneyDetail.setCashRefundAmount(refundMoneyDetail.getCashRefundAmount().add(orderRefundRecordPO.getActualAmount()));
                    }
                }
            }
        } else {
            // 非组合退款的情况下，直接按类型设置
            if (RefundType.isLoanPayRefundType(RefundType.value(orderReturnPO.getRefundType()))) {
                refundMoneyDetail.setLoanRefundAmount(orderReturnPO.getActualReturnMoneyAmount().add(orderReturnPO.getReturnExpressAmount()));
            } else {
                refundMoneyDetail.setCashRefundAmount(orderReturnPO.getActualReturnMoneyAmount().add(orderReturnPO.getReturnExpressAmount()));
            }
        }
        return refundMoneyDetail;
    }

    @Override
    public List<OrderReturnPO> getPlatformAutoAuditOrder() {
        return orderReturnMapper.getPlatformAutoAuditOrder();
    }

    @Override
    public Map<String, BigDecimal> getSumAmountByProductId(Long orderProductId) {
        return orderReturnMapper.getSumAmountByProductId(orderProductId);
    }

    @Override
    public List<OrderReturnVOV2> listOrderReturnByProductId(Long orderProductId) {
        return orderReturnMapper.listOrderReturnByProductId(orderProductId);
    }

    /**
     * 根据订单编号查询售后单信息
     *
     * @param orderSn 订单编号
     * @return 售后单信息
     */
    @Override
    public List<OrderReturnPO> getByOrderSn(String orderSn) {
        LambdaQueryWrapper<OrderReturnPO> orderReturnQuery = new LambdaQueryWrapper<>();
        orderReturnQuery.eq(OrderReturnPO::getOrderSn, orderSn);
        orderReturnQuery.eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return this.list(orderReturnQuery);
    }

    /**
     * 是否有在途的换货单
     *
     * @param orderSn 订单编号
     * @return 是否有在途换货单
     */
    @Override
    public boolean hasDuringExchangeRefund(String orderSn) {
        LambdaQueryWrapper<OrderReturnPO> orderReturnQuery = new LambdaQueryWrapper<>();
        orderReturnQuery.eq(OrderReturnPO::getOrderSn, orderSn);
        orderReturnQuery.in(OrderReturnPO::getState, OrderReturnStatus.duringRefundStatus());
        orderReturnQuery.eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        orderReturnQuery.eq(OrderReturnPO::getReturnType, OrdersAfsConst.RETURN_TYPE_3);
        return super.count(orderReturnQuery) > 0;
    }
}
