package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.mallgoods.facade.enums.InterestWayEnum;
import com.cfpamf.ms.mallgoods.facade.vo.ProductFinanceRuleLabel;
import com.cfpamf.ms.mallgoods.facade.vo.ProductPriceVO;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.common.util.OrderSubmitAttributesUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderExtendFinanceMapper;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.OrderExtendFinancePO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.IOrderExtendFinanceService;
import com.slodon.bbc.core.exception.MallException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 订单金融信息扩展表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-22
 */
@Service
@Slf4j
public class OrderExtendFinanceServiceImpl extends BaseRepoServiceImpl<OrderExtendFinanceMapper, OrderExtendFinancePO> implements IOrderExtendFinanceService {
	@Autowired
	private OrderLocalUtils orderLocalUtils;
	@Autowired
	private OrderModel orderModel;
	@Autowired
	private OrderSubmitAttributesUtils orderSubmitAttributesUtils;
	@Autowired
	private BmsIntegration bmsIntegration;

	@Override
	public OrderExtendFinancePO getByOrderSn(String orderSn) {
		LambdaQueryWrapper<OrderExtendFinancePO> query = new LambdaQueryWrapper<>();
		query.eq(OrderExtendFinancePO::getOrderSn, orderSn);
		query.last("limit 1");

		OrderExtendFinancePO orderExtendFinancePO = null;
		try {
			orderExtendFinancePO = getOne(query);
		} catch (Exception e) {
			log.warn("查询订单金融规则信息失败，orderSn：{}", orderSn);
		}

		return orderExtendFinancePO;
	}

	@Override
	public void insertOrderExtendFinance(Long productId, String areaCode, String financeRuleCode,String orderSn) {
		ProductPriceVO finance = orderSubmitAttributesUtils.getProductPriceByProductId(productId, areaCode, financeRuleCode);
		log.info("贷款支付，查询金融规则信息：{}", finance);
		List<DictionaryItemVO> dictionary = bmsIntegration.listDictionaryItemsByTypeCode(CommonConst.AUTO_RECEIVE_DAYS, CommonConst.MALL_SYSTEM_MANAGE_ID);
		Integer autoReceiveDays = null;
		if (!CollectionUtils.isEmpty(dictionary)){
			DictionaryItemVO dictionaryItemVO = dictionary.get(0);
			autoReceiveDays = Integer.valueOf(dictionaryItemVO.getItemCode());
		}
		OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
		// 保存金融规则信息
		if (!StringUtils.isEmpty(financeRuleCode) && Objects.nonNull(finance)) {
			orderModel.saveOrderExtendFinance(finance, orderPO,autoReceiveDays);
		}
	}

	/**
	 * 获取orderExtendFinance实体
	 *
	 * @param productId       货品id
	 * @param areaCode        地区编码
	 * @param financeRuleCode 金融规则
	 * @param orderPo         订单编号
	 * @param autoReceiveDays
	 * @return 实体
	 */
	@Override
	public OrderExtendFinancePO getOrderExtendFinance(Long productId, String areaCode, String financeRuleCode, OrderPO orderPo, Integer autoReceiveDays) {
		ProductPriceVO finance = orderSubmitAttributesUtils.getProductPriceByProductId(productId, areaCode, financeRuleCode);
		log.info("贷款支付，查询金融规则信息：{}", finance);

		ProductFinanceRuleLabel productFinanceRuleLabel = finance.getProductFinanceRuleLabel();
		if (Objects.isNull(productFinanceRuleLabel)) {
			return null;
		}
		OrderExtendFinancePO financePO = new OrderExtendFinancePO();
		if (productFinanceRuleLabel.getInterestWay().equals(InterestWayEnum.PLAN_LOAN_DATE.getCode())
				&& productFinanceRuleLabel.getPlanLoanDate() == null) {
			throw new MallException("下单异常，请联系管理员处理！", "保存贷款类型时计划放款日为空：" + orderPo,
					ErrorCodeEnum.C.RESULT_INVALID.getCode());
		}
		financePO.setPlanLoanDate(productFinanceRuleLabel.getPlanLoanDate());
		financePO.setOrderSn(orderPo.getOrderSn());
		financePO.setDeliverMethod(productFinanceRuleLabel.getDeliverMethod());
		financePO.setAutoDeliverTime(productFinanceRuleLabel.getAutoDeliverTime());
		financePO.setAutoReceiveDays(productFinanceRuleLabel.getAutoReceiveDays());

		if (!Objects.isNull(autoReceiveDays)){
			log.info("贷款类支付，设置自动收货天数,days:{}",autoReceiveDays);
			financePO.setAutoReceiveDays(autoReceiveDays);
		}else{
			log.info("贷款类支付，设置自动收货天数失败，bms未配置字典");
		}

		financePO.setInterestWay(productFinanceRuleLabel.getInterestWay());
		financePO.setCouponBatch(productFinanceRuleLabel.getCouponBatch());
		financePO.setInterestStartType(productFinanceRuleLabel.getInterestStartType());
		financePO.setInterestStartDays(productFinanceRuleLabel.getInterestStartDays());
		financePO.setEmployeeInterestStartDate(productFinanceRuleLabel.getEmployeeInterestStartDate());
		financePO.setFinanceRuleCode(productFinanceRuleLabel.getFinanceRuleCode());
		financePO.setPlanDiscountType(productFinanceRuleLabel.getPlanDiscountType());
		financePO.setPlanDiscountStoreRate(productFinanceRuleLabel.getPlanDiscountStoreRate());
		financePO.setPlanInterestStartDays(productFinanceRuleLabel.getPlanInterestStartDays());
		financePO.setPlanDiscountUpperLimit(productFinanceRuleLabel.getPlanDiscountUpperLimit());
		return financePO;
	}

	@Override
	public void updateAutoReceiveDays(String orderSn, Integer autoReceiveDays) {
		OrderExtendFinancePO orderExtendFinancePO = this.getByOrderSn(orderSn);
		if(Objects.isNull(orderExtendFinancePO)) {
			return;
		}
		if(Objects.isNull(autoReceiveDays) || autoReceiveDays == 0) {
			return;
		}
		if(orderExtendFinancePO.getAutoReceiveDays() > autoReceiveDays) {
			return;
		}
		LambdaUpdateWrapper<OrderExtendFinancePO> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.set(OrderExtendFinancePO::getAutoReceiveDays, autoReceiveDays)
						.eq(OrderExtendFinancePO::getOrderSn, orderSn);
		this.update(updateWrapper);
	}
}
