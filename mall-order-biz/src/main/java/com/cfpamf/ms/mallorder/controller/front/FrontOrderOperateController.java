package com.cfpamf.ms.mallorder.controller.front;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.framework.autoconfigure.ZhnxServiceException;
import com.cfpamf.framework.autoconfigure.redis.lock.DistributeLock;
import com.cfpamf.ms.mallgoods.facade.api.GoodsFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.ProductFeignClient;
import com.cfpamf.ms.mallgoods.facade.vo.Goods;
import com.cfpamf.ms.mallgoods.facade.vo.GoodsPromotion;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallmember.request.MemberExample;
import com.cfpamf.ms.mallorder.api.feign.OrderFacade;
import com.cfpamf.ms.mallorder.builder.OrderBuilder;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.constant.OrderProductConst;
import com.cfpamf.ms.mallorder.common.constant.SentenceConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.util.*;
import com.cfpamf.ms.mallorder.constant.ExchangeOrderConst;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.CustomerConfirmStatusEnum;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.enums.OrderPerformanceModeEnum;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.cashier.CashierIntegration;
import com.cfpamf.ms.mallorder.integration.facade.OrderQueryFacade;
import com.cfpamf.ms.mallorder.integration.facade.dto.ErpDepotVO;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.model.BankTransferModel;
import com.cfpamf.ms.mallorder.model.CartModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.OrderDeliveryReq;
import com.cfpamf.ms.mallorder.req.front.PurchaseOrderAddressUpdateRequest;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.service.impl.OrderCreateHelper;
import com.cfpamf.ms.mallorder.v2.service.IOrderConfirmService;
import com.cfpamf.ms.mallorder.vo.ChannelOrderSubmitVO;
import com.cfpamf.ms.mallorder.vo.OrderSubmitCheckVO;
import com.cfpamf.ms.mallorder.vo.OrderSubmitPageVO;
import com.cfpamf.ms.mallorder.vo.OrderSubmitVO;
import com.cfpamf.ms.mallpromotion.api.*;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.cfpamf.ms.mallpromotion.request.LadderGroupGoods;
import com.cfpamf.ms.mallpromotion.request.LadderGroupOrderExtend;
import com.cfpamf.ms.mallpromotion.request.PresellGoodsExample;
import com.cfpamf.ms.mallpromotion.request.PresellOrderExtendExample;
import com.cfpamf.ms.mallpromotion.vo.*;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallsystem.api.ReasonFeignClient;
import com.cfpamf.ms.mallsystem.vo.Reason;
import com.cfpamf.smartid.client.utils.sharding.SeqEnum;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import com.google.common.collect.Lists;
import com.slodon.bbc.core.constant.*;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.StringUtil;
import com.slodon.bbc.core.util.UserUtil;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 订单操作controller
 */
@RestController
@RequestMapping("front/orderOperate")
@Api(tags = "front-订单操作")
@Slf4j
public class FrontOrderOperateController implements OrderFacade {

    @Resource
    private CartModel cartModel;
    @Resource
    private OrderModel orderModel;
    @Resource
    private MemberFeignClient memberFeignClient;
    @Resource
    private PromotionCommonFeignClient promotionCommonFeignClient;
    @Resource
    private ReasonFeignClient reasonFeignClient;
    @Resource
    private GoodsFeignClient goodsFeignClient;
    @Resource
    private PresellFeignClient presellFeignClient;
    @Resource
    private PresellGoodsFeignClient presellGoodsFeignClient;
    @Resource
    private PresellOrderExtendFeignClient presellOrderExtendFeignClient;
    @Resource
    private LadderGroupFeignClient ladderGroupFeignClient;
    @Resource
    private LadderGroupGoodsFeignClient ladderGroupGoodsFeignClient;
    @Resource
    private LadderGroupOrderExtendFeignClient ladderGroupOrderExtendFeignClient;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private OrderSubmitUtil orderSubmitUtil;
    @Resource
    private OrderLocalUtils orderLocalUtils;
    @Autowired
    private PromotionUtils promotionUtils;
    @Autowired
    private IOrderPlacingService orderPlacingService;
    @Resource
    private IOrderService orderService;
    @Resource
    private IOrderReturnService orderReturnService;
    @Autowired
    private ShardingId shardingId;
    @Autowired
    private ValidUtils validUtils;
    @Autowired
    private OrderCreateHelper orderCreateHelper;
    @Autowired
    private OrderQueryFacade orderQueryFacade;
    @Autowired
    private BankTransferModel bankTransferModel;
    @Autowired
    private IOrderProductService iOrderProductService;
    @Resource
    private OrderMapper orderMapper;
    @Autowired
    private IOrderExtendService orderExtendService;
    @Autowired
    private IOrderConfirmService orderConfirmService;
    @Autowired
    private DistributeLock distributeLock;
    @Autowired
    private IOrderPayService iOrderPayService;
    @Autowired
    private CashierIntegration cashierIntegration;
    @Autowired
    private PrivilegeService privilegeService;


    @Resource
    private IOrderExchangeService orderExchangeService;

    @Resource
    private IOrderExternalPerformanceService orderExternalPerformanceService;
    @Autowired
    private ProductFeignClient productFeignClient;

    /**
     * 渠道订单 外部接口调用、无购物车
     * 代客下单
     * @param dto
     * @return
     */
    @PostMapping("/batch/submit")
    @ApiOperation("批量订单提交接口 200-下单成功")
    public JsonResult<OrderSubmitVO> batchSubmitOrder(@Valid @RequestBody OrderParamDTO dto) {
        // 创建订单
        OrderSubmitVO vo = orderPlacingService.createBatchOrder(dto);
        return SldResponse.success(vo);
    }

    /**
     * 更新客户确认状态接口
     * 初始状态：草稿
     * 支付人是站长：草稿--》无需确认
     * 支付人不是站长 && 订单阅读确认：草稿--》待确认 (消息推送)
     * 支付人不是站长 && 不是订单阅读确认：草稿--》无需确认
     * @param
     * @return
     */
    @PostMapping("/valetOrder/customerConfirmStatus/update")
    @ApiOperation("更新客户确认状态接口")
    public JsonResult<String> valetOrderStatusUpdate(@Valid @RequestBody ValetOrderParamDTO dto) {
        dataValid(dto);
        OrderPO orderPO = orderModel.getOrderByOrderSn(dto.getOrderSn());
        BizAssertUtil.isTrue(Objects.isNull(orderPO),"查询不到该订单:" + dto.getOrderSn());
        if(dto.getIsStationMasterPay()){
            orderService.setCustomerNoNeedConfirmStatus(dto.getOrderSn());
        } else {
            if(OrderConst.LOAN_CONFIRM_METHOD_READ.equals(orderPO.getLoanConfirmMethod())) {
                orderService.setCustomerUnConfirmStatus(dto.getOrderSn());
            } else if (OrderConst.LOAN_CONFIRM_METHOD_FACE_DETECTION.equals(orderPO.getLoanConfirmMethod())){
                orderService.setCustomerNoNeedConfirmStatus(dto.getOrderSn());
            }
        }

        return SldResponse.success("");
    }

    private void dataValid(ValetOrderParamDTO dto) {
        BizAssertUtil.isTrue(!("XXAPP".equals(dto.getChannel()) || "XX_MINI_PRO".equals(dto.getChannel())),
                "只允许乡信渠道更改客户确认状态");

        BizAssertUtil.isTrue((CustomerConfirmStatusEnum.NO_NEED_CONFIRM.getValue() != dto.getCustomerConfirmStatus()
                        && CustomerConfirmStatusEnum.UNCONFIRMED.getValue() != dto.getCustomerConfirmStatus()),
                "只允许更改客户确认状态为待确认或无需确认");
    }

    /**
     * 渠道订单 外部接口调用、无购物车
     *
     * @param dto
     * @return
     */
    @Override
    @PostMapping("submitChannelOrder")
    @ApiOperation("渠道订单提交接口 200-下单成功, 6601003-库存不足")
    public JsonResult<ChannelOrderSubmitVO> submitChannelOrder(@RequestBody ChannelOrderSubmitDTO dto) {
        if (dto.getOutBizSource() == null || dto.getOutBizId() == null) {
            throw new MallException("渠道订单号不能为空");
        }
        OrderPayPO orderPayPO = iOrderPayService.lambdaQuery()
                .eq(OrderPayPO::getOutBizId, dto.getOutBizId())
                .eq(OrderPayPO::getOutBizSource, dto.getOutBizSource())
                .last("limit 1")
                .one();
        if (Objects.nonNull(orderPayPO)) {
            LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.select(OrderPO::getOrderSn);
            queryWrapper.eq(OrderPO::getPaySn, orderPayPO.getPaySn());
            List<OrderPO> orderPOList = orderPlacingService.list(queryWrapper);

            ChannelOrderSubmitVO vo = new ChannelOrderSubmitVO();
            vo.setPaySn(String.valueOf(orderPayPO.getPaySn()));
            vo.setOrderSnList(orderPOList.stream().map(OrderPO::getOrderSn).collect(Collectors.toList()));
            return SldResponse.success(vo);
        }

        /**
         * 查询用户信息
         */
        Member member = memberFeignClient.getOrRegisterByCappUserNo(dto.getUserNo());
        if (member == null) {
            throw new MallException("用户信息不存在 userNo:" + dto.getUserNo(), ErrorCodeEnum.U.INVALID_BIZ_DATA.getCode());
        }

        /**
         * 校验订单类型
         */
        if (dto.getSkuInfoList().size() > 1 && dto.getOrderType() != OrderTypeEnum.NORMAL) {
            throw new MallException("活动订单不支持多商品下单", ErrorCodeEnum.U.INVALID_BIZ_DATA.getCode());
        }

        /**
         * 收货地址校验
         */
        if (dto.getAddressId() != null) {
            OrderAddressDTO addressDTO = orderCreateHelper.buildOrderAddressById(dto.getAddressId());
            dto.setAddress(addressDTO);
        } else if (!ValidUtils.isAddressValid(dto.getAddress())) {
            throw new BusinessException(ErrorCodeEnum.U.INVALID_BIZ_DATA.getCode(), "收货地址缺失");
        }

        /**
         * 查询活动商品信息
         */
        PreOrderDTO preOrderDTO = new PreOrderDTO();
        if (dto.getOrderType() == OrderTypeEnum.SECONDS_KILL) {
            // 立即购买，查否订单类型、是否计算优惠
            OrderSkuInfoDTO skuInfoDTO = dto.getSkuInfoList().get(0);
            GoodsPromotion singlePromotion = cartModel.getSinglePromotion(skuInfoDTO.getProductId(), member.getMemberId());

            if (singlePromotion != null) {
                JsonResult<PreOrderDTO> result = promotionCommonFeignClient.preOrderSubmit(
                        singlePromotion.getPromotionType(), singlePromotion.getPromotionId(), skuInfoDTO.getProductId(),
                        skuInfoDTO.getNumber(), member.getMemberId());
                if (result.getState() != ResponseConst.STATE_SUCCESS) {
                    throw new MallException(result.getMsg());
                }
                preOrderDTO = result.getData();
            }
        }

        OrderSubmitParamDTO dto1 = orderCreateHelper.buildOrderSubmit(dto, true);

        long pno = shardingId.next(SeqEnum.PNO, member.getMemberId().toString());

        // 构造入mq对象
        OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        consumerDTO.setParamDTO(dto1);
        consumerDTO.setMemberId(member.getMemberId());
        consumerDTO.setPaySn(pno + "");
        consumerDTO.setPreOrderDTO(preOrderDTO);
        consumerDTO.setAreaCode(dto1.getAreaCode());
        consumerDTO.setSkuInfoList(dto.getSkuInfoList());
        consumerDTO.setOrderType(dto.getOrderType());
        consumerDTO.setChannelOrderSubmitDTO(dto);
        // 将表标识放入redis，key=标识前缀+paySn，mq处理成功或失败后删除标识
        stringRedisTemplate.opsForValue().set(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + pno, "");

        // 创建订单
        orderPlacingService.createOrder(consumerDTO);

        if (dto.getOrderType() == OrderTypeEnum.SECONDS_KILL) {
            log.debug("秒杀订单---------------");
        } else {
            log.debug("普通订单--------------");
        }

        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper<>();
        orderQuery.select(OrderPO::getOrderSn);
        orderQuery.eq(OrderPO::getPaySn, consumerDTO.getPaySn());
        List<OrderPO> orderPOList = orderPlacingService.list(orderQuery);

        ChannelOrderSubmitVO vo = new ChannelOrderSubmitVO();
        vo.setPaySn(String.valueOf(pno));
        vo.setOrderSnList(orderPOList.stream().map(OrderPO::getOrderSn).collect(Collectors.toList()));

        return SldResponse.success(vo);
    }

    /**
     * 判断用户是否为新人（未曾下单）,true为新人，false则反
     *
     * @param userNo
     * @param userMobile
     * @return
     */
    @Override
    @GetMapping("/checkIsNewUser")
    @ApiOperation("判断用户是否为新人（未曾下单）,true为新人，false则反")
    public JsonResult<Boolean> checkIsNewUser(@ApiParam("用户编号") @RequestParam(value = "userNo") String userNo,
                                              @ApiParam("用户手机号") @RequestParam(value = "userMobile") String userMobile) {

        Result<Boolean> result = orderQueryFacade.checkIsNewUser(userNo, userMobile);

        if (result == null || result.getData() == null || !result.isSuccess()) {
            throw new BusinessException("未查询到明确结果，请重试");
        }

        return SldResponse.success(result.getData());
    }

    @PostMapping("check")
    @ApiOperation(value = "检测购物车信息")
    @ApiImplicitParams({@ApiImplicitParam(name = WebConst.USER_HEADER, value = "客户信息 由网关处理", paramType = "header",
            dataType = "String")})
    public JsonResult<OrderSubmitCheckVO> check(HttpServletRequest request, @RequestBody OrderSubmitParamDTO dto) {
        Member member = UserUtil.getUser(request, Member.class);

        // 构造计算优惠dto
        OrderSubmitDTO orderSubmitDTO = orderSubmitUtil.getOrderSubmitDTO(dto, member.getMemberId(),
                dto.getProductType(), null, false, false);
        log.info("front/orderOperate/check obtain orderSubmitDTO:{}", JSONObject.toJSONString(orderSubmitDTO));

        OrderSubmitCheckVO vo = cartModel.checkCart(orderSubmitDTO);

        if (vo.getState().equals(OrderSubmitCheckVO.STATE_0)) {
            // 检测通过
            return SldResponse.success();
        } else {
            // 检测不通过
            return SldResponse.failSpecial(vo);
        }

    }

    @PostMapping("confirm")
    @ApiOperation(value = "确认订单接口（去结算），获取提交订单页数据(注：调用此接口之前需先调用[检测购物车信息]接口，state=200才可以调用此接口)", tags = "CORE")
    public JsonResult<OrderSubmitPageVO> confirm(HttpServletRequest request, @RequestBody OrderSubmitParamDTO dto) {
        Member member = UserUtil.getUser(request, Member.class);
        BizAssertUtil.isTrue(!orderLocalUtils.checkMemberValid(member),
                String.format("用户已失效，用户号码：%s", member.getMemberMobile()));
        // 构造返回数据
        OrderSubmitPageVO vo = null;
        OrderAddressDTO orderAddressDTO = null;
        if (!StringUtil.isNullOrZero(dto.getAddressId())) {
            orderAddressDTO = orderCreateHelper.buildOrderAddressById(dto.getAddressId());
        } else if (Objects.nonNull(dto.getPointId())) {
            orderAddressDTO = orderCreateHelper.buildOrderAddressByPointId(dto.getPointId());
        }

        // 构造计算优惠dto
        OrderSubmitDTO orderSubmitDTO = orderSubmitUtil.getOrderSubmitDTO(dto, member.getMemberId(),
                dto.getProductType(), orderAddressDTO, true, false);
        log.info("构造计算优惠dto:{}", JSONObject.toJSONString(orderSubmitDTO));

        // 计算运费
        List<BigDecimal> expressFeeList = new ArrayList<>();
        for (OrderSubmitDTO.OrderInfo orderInfo : orderSubmitDTO.getOrderInfoList()) {
            // 传了地址信息，计算运费
            BigDecimal expressFee = orderLocalUtils.getOrderExpressFee(orderAddressDTO == null ? null :
                    orderAddressDTO.getCityCode(), orderInfo);
            expressFeeList.add(expressFee);
        }

        Result<List<OrderSubmitDTO.OrderInfo.OrderProductInfo>> fundsBorrowVerify = IFundsBorrowBizService.verifyCartOrder(orderSubmitDTO.getOrderInfoList());
        if (!fundsBorrowVerify.isSuccess()) {
            throw new BusinessException(fundsBorrowVerify.getMessage());
        }

        GoodsPromotion singlePromotion = null;
        if (!dto.getIsCart()) {
            // 立即购买，查询是否为预售
            singlePromotion = cartModel.getSinglePromotion(dto.getProductId(), member.getMemberId());
        }
        if (singlePromotion != null && singlePromotion.getPromotionType().equals(PromotionConst.PROMOTION_TYPE_103)) {
            // 预售确认
            vo = preSaleConfirm(dto, orderSubmitDTO, singlePromotion, expressFeeList);
        } else if (singlePromotion != null
                && singlePromotion.getPromotionType().equals(PromotionConst.PROMOTION_TYPE_105)) {
            // 阶梯团确认
            vo = ladderGroupConfirm(dto, orderSubmitDTO, singlePromotion, expressFeeList);
        } else {
            // 调用活动模块计算优惠
            JsonResult<OrderSubmitDTO> result =
                    promotionCommonFeignClient.orderSubmitCalculationDiscountV2(orderSubmitDTO, dto.getSource());
            if (result.getState() != 200) {
                throw new MallException(result.getMsg());
            }
            orderSubmitDTO = result.getData();
            log.info("调用活动模块计算优惠:{}", JSONObject.toJSONString(orderSubmitDTO));

            // 处理已计算好的运费
            orderService.dealExpress(orderSubmitDTO, expressFeeList);
            // 计算乡助卡优惠金额
            orderService.calculateCard(orderSubmitDTO, dto.getCardCodeList(), member.getUserNo());

            // 是否可以开增值税发票
            boolean isVatInvoice = true;
            Map<Long, Goods> goodsMap = new HashMap<>();// 保存已查询的商品，减少查库次数
            for (OrderSubmitDTO.OrderInfo orderInfo : orderSubmitDTO.getOrderInfoList()) {
                Set<Integer> performanceModeList = new HashSet<>();

                for (OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo : orderInfo.getOrderProductInfoList()) {
                    int performanceMode = OrderPerformanceModeEnum.PERFORMANCE_MODE_COMMON.getValue();
                    Goods goods;
                    if (goodsMap.containsKey(orderProductInfo.getGoodsId())) {
                        goods = goodsMap.get(orderProductInfo.getGoodsId());
                    } else {
                        goods = goodsFeignClient.getGoodsByGoodsId(orderProductInfo.getGoodsId());
                    }
                    if (goods.getIncludedService() != null && OrderProductConst.GOODS_SERVICE_TYPE_HOME.equals(goods.getIncludedService())) {
                        performanceMode = OrderPerformanceModeEnum.PERFORMANCE_MODE_HOME_SERVICE.getValue();
                    }
                    if (goods.getIsVirtualGoods() != null && OrderProductConst.VIRTUAL_GOODS_TYPE_9.equals(goods.getIsVirtualGoods())) {
                        orderProductInfo.setIsVirtualGoods(OrderProductConst.VIRTUAL_GOODS_TYPE_9);
                        performanceMode = OrderPerformanceModeEnum.PERFORMANCE_MODE_SUPPLIER.getValue();
                    }

                    if (goods.getIsVatInvoice() != null && goods.getIsVatInvoice() == GoodsConst.IS_VAT_INVOICE_NO) {
                        // 商品不允许开增值税发票,跳出循环
                        isVatInvoice = false;
                        break;
                    }
                    orderProductInfo.setPerformanceMode(performanceMode);
                    goodsMap.put(goods.getGoodsId(), goods);
                    performanceModeList.add(performanceMode);
                }
                if (!isVatInvoice) {
                    // 有一个商品不允许开增值税发票,跳出循环
                    break;
                }
                orderInfo.setPerformanceModes(performanceModeList.toString());
            }

            vo = new OrderSubmitPageVO(orderSubmitDTO);
            if (singlePromotion != null) {
                vo.setPromotionType(singlePromotion.getPromotionType());
            }
            for (OrderSubmitPageVO.StoreGroupVO storeGroupVO : vo.getStoreGroupList()) {
                for (OrderSubmitPageVO.StoreGroupVO.ProductVO productVO : storeGroupVO.getAllProductList()) {
                    if (productVO.getIsGift().equals(OrderConst.IS_GIFT_NO)) {
                        continue;
                    }
                    Product product = productFeignClient.getProductByProductId(productVO.getProductId());
                    productVO.setSpecValues(product.getSpecValues());
                }
            }
            vo.setIsVatInvoice(isVatInvoice);

            orderConfirmService.getPromotionDetail(dto, vo);
        }
        return SldResponse.success(vo);
    }

    @PostMapping("confirmPurchaseOrder")
    @ApiOperation("订单确认下单")
    @ApiImplicitParams({@ApiImplicitParam(name = WebConst.USER_HEADER, value = "客户信息 由网关处理", paramType = "header",
            dataType = "String")})
    public JsonResult<OrderSubmitPageVO> confirmPurchaseOrder(HttpServletRequest request,
                                                              @RequestBody OrderSubmitParamDTO dto) {
        Member member = UserUtil.getUser(request, Member.class);
        BizAssertUtil.isTrue(!orderLocalUtils.checkMemberValid(member),
                String.format("用户已失效，用户号码：%s", member.getMemberMobile()));
        if (CommonConst.PURCHASE_ORDER_HEADER.equals(request.getHeader(CommonConst.ORDER_PATTERN_HEADER))) {
            dto.setOrderPattern(OrderPatternEnum.PURCHASE_CENTRE.getValue());
        }
        return SldResponse.success(orderPlacingService.confirmPurchaseOrder(dto, member));
    }

    @PostMapping("checkPurchaseOrder")
    @ApiOperation("检测购物车信息")
    @ApiImplicitParams({@ApiImplicitParam(name = WebConst.USER_HEADER, value = "客户信息 由网关处理", paramType = "header",
            dataType = "String")})
    public JsonResult<OrderSubmitCheckVO> checkPurchaseOrder(HttpServletRequest request,
                                                             @RequestBody OrderSubmitParamDTO dto) {
        if (CommonConst.PURCHASE_ORDER_HEADER.equals(request.getHeader(CommonConst.ORDER_PATTERN_HEADER))) {
            dto.setOrderPattern(OrderPatternEnum.PURCHASE_CENTRE.getValue());
        }
        // vendor 转的 member，当前只会有 vendor和member的共同信息
        Member user = UserUtil.getUser(request, Member.class);
        log.info("checkPurchaseOrder, the member info is : {}", JSONObject.toJSONString(user));
        MemberExample memberExample = new MemberExample();
        memberExample.setUserNo(user.getUserNo());
        List<Member> memberList = memberFeignClient.getMemberList(memberExample);
        AssertUtil.notEmpty(memberList, "查询对应用户信息为空，请检查用户信息");
        AssertUtil.isTrue(memberList.size() > 1, "根据用户编码查询对应用户信息结果超过一个，请检查用户信息");
        log.info("checkPurchaseOrder, user info is : {}", JSONObject.toJSONString(memberList.get(0)));
        log.info("checkPurchaseOrder, dto is : {}", JSONObject.toJSONString(dto));
        OrderSubmitCheckVO checkResult = orderPlacingService.checkPurchaseOrder(dto, memberList.get(0));
        return checkResult.getState().equals(OrderSubmitCheckVO.STATE_0) ? SldResponse.success()
                : SldResponse.failSpecial(checkResult);
    }

    @PostMapping("submitPurchaseOrder")
    @ApiOperation("提交采购订单接口")
    @ApiImplicitParams({@ApiImplicitParam(name = WebConst.USER_HEADER, value = "客户信息 由网关处理", paramType = "header",
            dataType = "String")})
    @ApiResponses(@ApiResponse(code = 200, message = "data.paySn:支付单号;data.needPay:是否需要支付，true==是"))
    public JsonResult submitPurchaseOrder(HttpServletRequest request, @RequestBody OrderSubmitParamDTO dto) {
        if (CommonConst.PURCHASE_ORDER_HEADER.equals(request.getHeader(CommonConst.ORDER_PATTERN_HEADER))) {
            dto.setOrderPattern(OrderPatternEnum.PURCHASE_CENTRE.getValue());
        }
        log.info("checkPurchaseOrder userinfo: {}", JSONObject.toJSONString(UserUtil.getUser(request, Member.class)));
        return SldResponse
                .success(orderPlacingService.submitPurchaseOrder(dto, UserUtil.getUser(request, Member.class)));
    }

    @PostMapping("submitCouponOrder")
    @ApiOperation("提交卡券包订单接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = WebConst.USER_HEADER, value = "客户信息 由网关处理", paramType = "header", dataType = "String")
    })
    @ApiResponses(@ApiResponse(code = 200, message = "data.paySn:支付单号;data.needPay:是否需要支付，true==是"))
    public JsonResult submitCouponOrder(HttpServletRequest request, @RequestBody OrderSubmitParamDTO dto) {
        dto.setOrderPattern(OrderPatternEnum.COUPON_CENTRE.getValue());

        Member member = UserUtil.getUser(request, Member.class);
        if (Objects.isNull(member) || Objects.isNull(member.getMemberId())){
            BizAssertUtil.notNull(dto.getMemberId(),"用户信息为空，请检查");
            member = memberFeignClient.getMemberByMemberId(dto.getMemberId());
        }
        return SldResponse.success(orderPlacingService.submitCouponOrder(dto, member, null));
    }

    @PostMapping("submitChannelCouponOrder")
    @ApiOperation("提交卡券包订单接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = WebConst.USER_HEADER, value = "客户信息 由网关处理", paramType = "header", dataType = "String")
    })
    @ApiResponses(@ApiResponse(code = 200, message = "data.paySn:支付单号;data.needPay:是否需要支付，true==是"))
    public JsonResult submitChannelCouponOrder(@RequestBody @Valid ChannelCouponOrderSubmitDTO dto) {

        /**
         * 判断渠道订单是否已存在
         */
        OrderPayPO orderPayPO = iOrderPayService.lambdaQuery()
                .eq(OrderPayPO::getOutBizId, dto.getOutBizId())
                .eq(OrderPayPO::getOutBizSource, dto.getOutBizSource())
                .last("limit 1")
                .one();

        if (Objects.nonNull(orderPayPO)) {
            LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.select(OrderPO::getOrderSn);
            queryWrapper.eq(OrderPO::getPaySn, orderPayPO.getPaySn());
            List<OrderPO> orderPOList = orderService.list(queryWrapper);

            ChannelOrderSubmitVO vo = new ChannelOrderSubmitVO();
            vo.setPaySn(String.valueOf(orderPayPO.getPaySn()));
            vo.setOrderSnList(orderPOList.stream().map(OrderPO::getOrderSn).collect(Collectors.toList()));
            return SldResponse.success(vo);
        }

        /**
         * 当前只支持单商品下单
         */
        if (dto.getSkuInfoList().size() > 1){
            throw new MallException("当前只支持单商品下单", ErrorCodeEnum.U.INVALID_BIZ_DATA.getCode());
        }

        /**
         * 查询用户信息
         */
        Member member = memberFeignClient.getMemberByMemberId(dto.getMemberId());
        if (member == null) {
            throw new MallException("用户信息不存在 memberId:" + dto.getMemberId(), ErrorCodeEnum.U.INVALID_BIZ_DATA.getCode());
        }

        OrderSubmitParamDTO dto1 = new OrderSubmitParamDTO();
        dto1.setMemberId(dto.getMemberId());
        dto1.setOrderPattern(OrderPatternEnum.COUPON_CENTRE.getValue());
        dto1.setOrderType(OrderTypeEnum.NORMAL);
        dto1.setChannel(dto.getChannel());
        dto1.setAreaCode(dto.getAreaCode());
        dto1.setShareCode(dto.getShareCode());
        dto1.setProductId(dto.getSkuInfoList().get(0).getProductId());
        dto1.setNumber(dto.getSkuInfoList().get(0).getNumber());
        dto1.setSource(1);
        dto1.setOrderFrom(dto.getOrderFrom());
        dto1.setProductType(1);
        dto1.setChannelOrder(true);
        dto1.setIsCart(false);
        dto1.setIsAloneBuy(false);
        dto1.setBankTransferable(false);

        /**
         * 店铺纬度信息:优惠、发票、留言
         */
        List<OrderSubmitParamDTO.StoreInfo> storeInfoList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(dto.getStoreInfoList())){
            dto.getStoreInfoList().forEach(o ->{
                OrderSubmitParamDTO.StoreInfo storeInfo = new OrderSubmitParamDTO.StoreInfo();
                BeanUtils.copyProperties(o, storeInfo);
                storeInfoList.add(storeInfo);
            });
        }
        dto1.setStoreInfoList(storeInfoList);

        return SldResponse.success(orderPlacingService.submitCouponOrder(dto1, member, dto));
    }

    /**
     * 预售确认
     *
     * @param dto
     * @param orderSubmitDTO
     * @param singlePromotion
     * @return
     */
    public OrderSubmitPageVO preSaleConfirm(OrderSubmitParamDTO dto, OrderSubmitDTO orderSubmitDTO,
                                            GoodsPromotion singlePromotion, List<BigDecimal> expressFeeList) {
        // 构造返回数据
        OrderSubmitPageVO vo = new OrderSubmitPageVO(orderSubmitDTO);
        vo.setTotalAmount(new BigDecimal(vo.getTotalAmount()).subtract(expressFeeList.get(0)).toString());
        vo.setPromotionType(PromotionConst.PROMOTION_TYPE_103);
        // 查询预售活动
        PreSellVO presell = presellFeignClient.getPresellByPresellId(singlePromotion.getPromotionId());
        // 查询预售商品
        PresellGoodsExample example = new PresellGoodsExample();
        example.setPresellId(singlePromotion.getPromotionId());
        example.setProductId(dto.getProductId());
        List<PresellGoodsVO> presellGoodsList = presellGoodsFeignClient.getPresellGoodsList(example);
        AssertUtil.notEmpty(presellGoodsList, "预售商品不存在");
        PresellGoodsVO presellGoods = presellGoodsList.get(0);

        OrderSubmitPageVO.PresellInfo presellInfo = new OrderSubmitPageVO.PresellInfo();
        presellInfo.setPresellId(presell.getPresellId());
        presellInfo.setType(presell.getType());
        presellInfo.setPresellState(OrderConst.ORDER_SUB_STATE_101);
        presellInfo.setDeliverTime(presell.getDeliverTime());
        presellInfo.setProductId(presellGoods.getProductId());
        presellInfo.setPresellPrice(presellGoods.getPresellPrice().toString());
        presellInfo.setFirstMoney(presellGoods.getFirstMoney().multiply(new BigDecimal(dto.getNumber())).toString());
        presellInfo.setSecondMoney(presellGoods.getSecondMoney().multiply(new BigDecimal(dto.getNumber())).toString());
        if (!StringUtil.isNullOrZero(presellGoods.getFirstExpand())) {
            presellInfo
                    .setFirstExpand(presellGoods.getFirstExpand().multiply(new BigDecimal(dto.getNumber())).toString());
            presellInfo.setFinalDiscount(presellGoods.getFirstExpand().multiply(new BigDecimal(dto.getNumber()))
                    .subtract(presellGoods.getFirstMoney().multiply(new BigDecimal(dto.getNumber()))).toString());
        }
        presellInfo.setRemainStartTime(presell.getRemainStartTime());
        vo.setPresellInfo(presellInfo);
        Goods goods = goodsFeignClient.getGoodsByGoodsId(presellGoods.getGoodsId());
        vo.setIsVatInvoice(goods.getIsVatInvoice() == null || goods.getIsVatInvoice() == GoodsConst.IS_VAT_INVOICE_YES);
        return vo;
    }

    /**
     * 阶梯团确认
     *
     * @param dto
     * @param orderSubmitDTO
     * @param singlePromotion
     * @return
     */
    public OrderSubmitPageVO ladderGroupConfirm(OrderSubmitParamDTO dto, OrderSubmitDTO orderSubmitDTO,
                                                GoodsPromotion singlePromotion, List<BigDecimal> expressFeeList) {
        // 构造返回数据
        OrderSubmitPageVO vo = new OrderSubmitPageVO(orderSubmitDTO);
        vo.setTotalAmount(new BigDecimal(vo.getTotalAmount()).subtract(expressFeeList.get(0)).toString());
        vo.setPromotionType(PromotionConst.PROMOTION_TYPE_105);
        // 查询阶梯团活动
        LadderGroupVO ladderGroup = ladderGroupFeignClient.getLadderGroupByGroupId(singlePromotion.getPromotionId());
        // 查询阶梯团商品
        LadderGroupGoods example = new LadderGroupGoods();
        example.setGroupId(singlePromotion.getPromotionId());
        example.setProductId(dto.getProductId());
        List<LadderGroupGoodsVO> groupGoodsList = ladderGroupGoodsFeignClient.getLadderGroupGoodsList(example);
        AssertUtil.notEmpty(groupGoodsList, "阶梯团商品不存在");
        LadderGroupGoodsVO groupGoods = groupGoodsList.get(0);

        OrderSubmitPageVO.LadderGroupInfo ladderGroupInfo = new OrderSubmitPageVO.LadderGroupInfo();
        ladderGroupInfo.setGroupId(ladderGroup.getGroupId());
        ladderGroupInfo.setLadderGroupState(OrderConst.ORDER_SUB_STATE_101);
        ladderGroupInfo.setProductId(groupGoods.getProductId());
        ladderGroupInfo.setProductPrice(groupGoods.getProductPrice().toString());
        ladderGroupInfo
                .setAdvanceDeposit(groupGoods.getAdvanceDeposit().multiply(new BigDecimal(dto.getNumber())).toString());
        ladderGroupInfo.setRemainAmount((groupGoods.getProductPrice().subtract(groupGoods.getAdvanceDeposit()))
                .multiply(new BigDecimal(dto.getNumber())).toString());
        ladderGroupInfo.setRemainStartTime(ladderGroup.getEndTime());
        vo.setLadderGroupInfo(ladderGroupInfo);
        Goods goods = goodsFeignClient.getGoodsByGoodsId(groupGoods.getGoodsId());
        vo.setIsVatInvoice(goods.getIsVatInvoice() == null || goods.getIsVatInvoice() == GoodsConst.IS_VAT_INVOICE_YES);
        return vo;
    }

    @GetMapping("checkVerifyCode")
    @ApiOperation("验证码校验")
    public JsonResult checkVerifyCode(HttpServletRequest request,
                                      @RequestParam(value = "verifyCode") String verifyCode) {
        Member member = UserUtil.getUser(request, Member.class);
        if (Objects.isNull(member)) {
            return SldResponse.fail();
        }
        validUtils.checkVerifyCode(verifyCode, member, 1);
        return SldResponse.success("校验通过");
    }

    @PostMapping("submit")
    @ApiOperation(value = "提交订单接口(注：调用此接口之前需先调用[检测购物车信息]接口，state=200才可以调用此接口)", tags = "CORE")
    @ApiImplicitParams({@ApiImplicitParam(name = WebConst.USER_HEADER, value = "客户信息 由网关处理", paramType = "header",
            dataType = "String")})
    @ApiResponses(@ApiResponse(code = 200, message = "data.paySn:支付单号;data.needPay:是否需要支付，true==是"))
    public JsonResult<Map<String, String>> submit(HttpServletRequest request, @RequestBody OrderSubmitParamDTO dto) {
        Member member = UserUtil.getUser(request, Member.class);

        if (member == null) {
            log.warn("MEMBER INFO FROM GATEWAY: null");
        } else {
            log.warn("MEMBER INFO FROM GATEWAY: " + JSON.toJSONString(member));
        }
        log.info("会员信息:{}", member);

        // 零元订单验证码校验
        validUtils.checkVerifyCode(dto.getVerifyCode(), member, 0);

        OrderAddressDTO orderAddressDTO;
        if (!StringUtil.isNullOrZero(dto.getAddressId())) {
            orderAddressDTO = orderCreateHelper.buildOrderAddressById(dto.getAddressId());
        } else if (Objects.nonNull(dto.getPointId())) {
            orderAddressDTO = orderCreateHelper.buildOrderAddressByPointId(dto.getPointId());
        } else {
            throw new BusinessException(ErrorCodeEnum.U.INVALID_BIZ_DATA.getCode(), "订单收货地址缺失");
        }
        dto.setOrderAddress(orderAddressDTO);

        PreOrderDTO preOrderDTO = new PreOrderDTO();
        if (!dto.getIsCart()) {
            // 立即购买，查否订单类型、是否计算优惠
            GoodsPromotion singlePromotion = promotionUtils.getSinglePromotion(dto.getProductId());

            if (singlePromotion != null && !dto.getIsAloneBuy()) {
                // 活动预校验
                preOrderDTO = promotionUtils.preCheckSubmit(singlePromotion, dto.getProductId(), dto.getNumber(), member.getMemberId());
            }
        }

        String pno = dto.getPno();
        if (StringUtils.isEmpty(pno)) {
            pno = String.valueOf(shardingId.next(SeqEnum.PNO, member.getMemberId().toString()));
        }

        // 构造入mq对象
        OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        consumerDTO.setParamDTO(dto);
        consumerDTO.setMemberId(member.getMemberId());
        consumerDTO.setUserNo(member.getUserNo());
        consumerDTO.setPaySn(pno);
        consumerDTO.setPreOrderDTO(preOrderDTO);
        consumerDTO.setAreaCode(dto.getAreaCode());
        // 将表标识放入redis，key=标识前缀+paySn，mq处理成功或失败后删除标识
        stringRedisTemplate.opsForValue().set(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + pno, "");

        // 创建订单
        orderPlacingService.createOrder(consumerDTO);
        OrderExample orderExample = new OrderExample();
        orderExample.setPaySn(consumerDTO.getPaySn());
        List<OrderPO> orderPOList = orderModel.getOrderList(orderExample, null);
        List<String> orderSnList = orderPOList.stream().map(OrderPO::getOrderSn).collect(Collectors.toList());

        // 入队列
        // rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_ORDER_SUBMIT, consumerDTO);

        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("paySn", pno + "");
        dataMap.put("orderList", JSONObject.toJSONString(orderSnList));

        if (preOrderDTO != null && preOrderDTO.getOrderType().equals(PromotionConst.PROMOTION_TYPE_104)) {
            log.debug("秒杀订单---------------");
        } else {
            log.debug("普通订单--------------");
        }
        return SldResponse.success(dataMap);
    }

    @GetMapping("pushToCashier")
    @ApiOperation(value = "收银台推数", tags = "CORE")
    public JsonResult<String> testPush(@RequestParam(value = "paySn") @ApiParam("支付单号") String paySn,
                                       @RequestParam(value = "returnPage") @ApiParam("支付回跳页面") String returnPage) {
        cashierIntegration.pushToCashier(paySn, returnPage);
        return SldResponse.success("推数成功");
    }

    @PostMapping("cancel")
    @ApiOperation(value = "取消订单接口",tags = "CORE")
    @ApiImplicitParams({@ApiImplicitParam(name = "parentSn", value = "父订单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "reasonId", value = "取消原因", required = true, paramType = "query"),
            @ApiImplicitParam(name = "channel",
                    value = "下单操作渠道：H5-浏览器H5；APP-乡助APP；WE_CHAT-微信浏览器；" + "MINI_PRO-小程序；OMS-运管物资", paramType = "query")})
    public JsonResult<String> cancelOrder(HttpServletRequest request, String parentSn, Integer reasonId, String channel)
            throws Exception {
        Member member = UserUtil.getUser(request, Member.class);
        // 订单信息
        LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery(OrderPO.class);
        queryWrapper.eq(OrderPO::getParentSn, parentSn);
        queryWrapper.eq(OrderPO::getMemberId, member.getMemberId());
        queryWrapper.and(
                wrapper -> wrapper.in(OrderPO::getOrderState,
                                Arrays.asList(OrderStatusEnum.WAIT_PAY_DEPOSIT.getValue(), OrderStatusEnum.WAIT_PAY.getValue()))
                        .or(
                                wrapper2 -> wrapper2.eq(OrderPO::getOrderState, OrderStatusEnum.DEAL_PAY.getValue())
                                        .eq(OrderPO::getPaymentCode, PayMethodEnum.BANK_TRANSFER.getValue()))
        );
        List<OrderPO> orderPOList = orderService.list(queryWrapper);

        BizAssertUtil.notEmpty(orderPOList, "请选择要取消的订单");

        // 查询取消原因
        String reasonContent = "用户主动取消";
        if (Objects.nonNull(reasonId)) {
            Reason reason = reasonFeignClient.getReasonByReasonId(reasonId);
            BizAssertUtil.notNull(reason, "取消原因不存在");
            reasonContent = reason.getContent();
        }


        String lockName = "FrontOrderOperateController:cancelOrder:" + parentSn;
        try {
            String finalReasonContent = reasonContent;
            distributeLock.lockAndProcess(lockName, 0, 30, TimeUnit.SECONDS, () -> {
                int optRole = OrderConst.LOG_ROLE_MEMBER;
                Long optUserId = Long.valueOf(member.getMemberId());
                String optUserName = member.getMemberName();
                String optRemark = "会员取消订单";
                int returnBy = OrderConst.RETURN_BY_1;
                BizUserInfoDTO bizUserInfoDTO = OrderBuilder.getUser(request, BizUserInfoDTO.class);//BAPP 操作
                if (Objects.nonNull(bizUserInfoDTO)) {
                    optRole = OrderConst.LOG_ROLE_CUSTOMER_MANAGER;
                    optUserId = Long.valueOf(bizUserInfoDTO.getEmployeeId());
                    optUserName = bizUserInfoDTO.getUserName();
                    optRemark = "客户经理取消订单";
                    returnBy = OrderConst.RETURN_BY_4;
                } else {
                    //获取站长信息
                    Member stationMasterMember = OrderBuilder.getStationMasterUser(request);
                    if(Objects.nonNull(stationMasterMember)) {
                        optRole = OrderConst.LOG_ROLE_STATIONMASTER;
                        optUserId = Long.valueOf(stationMasterMember.getMemberId());
                        optUserName = stationMasterMember.getMemberName();
                        optRemark = "站长取消订单";
                        returnBy = OrderConst.RETURN_BY_5;
                    }
                }
                orderReturnService.cancelOrder(orderPOList, finalReasonContent, null, optRole,
                        optUserId, optUserName, optRemark, returnBy, channel);
                return null;
            });
        } catch (ZhnxServiceException e) {
            throw new BusinessException("订单正在取消中,请不要频繁点击");
        }

        return SldResponse.success("订单取消成功");
    }

    @PostMapping("cancelBankTransfer")
    @ApiOperation("取消银行卡汇款转账订单接口")
    @ApiImplicitParams({@ApiImplicitParam(name = "paySn", value = "支付单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "reasonId", value = "取消原因", required = true, paramType = "query"),
            @ApiImplicitParam(name = "channel",
                    value = "下单操作渠道：H5-浏览器H5；APP-乡助APP；WE_CHAT-微信浏览器；" + "MINI_PRO-小程序；OMS-运管物资", paramType = "query")})
    public JsonResult<String> cancelBankTransfer(HttpServletRequest request, String paySn, Integer reasonId, String channel) {
        Member member = UserUtil.getUser(request, Member.class);
        // 订单信息
        BzBankTransferPO bankTransferEntity = bankTransferModel.queryByPaySn(paySn);
        AssertUtil.notNull(bankTransferEntity, String.format("未找到指定的银行卡汇款记录,paySn:%s", paySn));

        BizAssertUtil.isTrue(!bankTransferEntity.getMemberId().equals(member.getMemberId()), "您不能操作他人的订单");
        if (!TransferStatusEnum.WAIT_TRANSFER.getValue().equals(bankTransferEntity.getTransferState())
                && !TransferStatusEnum.DEAL_TRANSFER.getValue().equals(bankTransferEntity.getTransferState())) {
            throw new BusinessException(String.format("当前银行卡汇款订单不允许执行取消操作,paySn:%s", paySn));
        }
        String reasonContent = "用户主动取消";
        // 查询取消原因
        if (Objects.nonNull(reasonId)) {
            Reason reason = reasonFeignClient.getReasonByReasonId(reasonId);
            BizAssertUtil.notNull(reason, "取消原因不存在");
            reasonContent = reason.getContent();
        }

        OrderExample orderExample = new OrderExample();
        orderExample.setPaySn(bankTransferEntity.getPaySn());
        List<OrderPO> orderPOList = orderModel.getOrderList(orderExample, null);

        String lockName = "FrontOrderOperateController:cancelBankTransfer:" + paySn;
        String finalReasonContent = reasonContent;
        try {
            distributeLock.lockAndProcess(lockName, 0, 30, TimeUnit.SECONDS, () -> {
                bankTransferModel.cancelLargePayment(bankTransferEntity);
                orderReturnService.cancelOrder(orderPOList, finalReasonContent, null, OrderConst.LOG_ROLE_MEMBER,
                        Long.valueOf(member.getMemberId()), member.getMemberName(), "会员取消订单", OrderConst.RETURN_BY_1, channel);
                return null;
            });
        } catch (ZhnxServiceException ex) {
            throw new BusinessException("订单正在取消中,请不要频繁点击");
        }
        return SldResponse.success("订单取消成功");
    }

    @ApiOperation(value = "BAPP、站长端获取仓库信息")
    @GetMapping("getDepotList")
    public JsonResult<List<ErpDepotVO>> getDepotList(HttpServletRequest request,@Param("orderSn") String orderSn,@Param("skuIdList") String skuIdList) {
        // 获取商户信息
        BizAssertUtil.notEmpty(skuIdList, "skuIdList不能为空");
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
        BizAssertUtil.notNull(orderPO, "查询不到改订单号，" + orderSn);
        return SldResponse.success(orderExternalPerformanceService.getDepotListWithResult(orderPO.getStoreId(), Arrays.asList(skuIdList.split(","))));
    }


    @ApiOperation(value = "BAPP、站长端发货", tags = "CORE")
    @PostMapping("deliver")
    public JsonResult deliver(HttpServletRequest request, @RequestBody @NotNull @Valid OrderDeliveryReq deliveryReq) {
        // 获取客户经理信息
        BizUserInfoDTO bizUserInfoDTO = OrderBuilder.getUser(request, BizUserInfoDTO.class);
        Member stationMasterMember = OrderBuilder.getStationMasterUser(request);
        Vendor vendor = new Vendor();
        if (bizUserInfoDTO != null) {
            log.info("客户经理发货，客户经理信息：{}", JSONObject.toJSONString(bizUserInfoDTO));
            vendor.setVendorId(Long.valueOf(bizUserInfoDTO.getEmployeeId()));
            vendor.setVendorName(bizUserInfoDTO.getJobNumber() + "-" + bizUserInfoDTO.getUserName());
            deliveryReq.setManagerDeliver(true);
        } else if (stationMasterMember != null) {
            log.info("站长发货，站长信息：{}", JSONObject.toJSONString(bizUserInfoDTO));
            vendor.setVendorId(Long.valueOf(stationMasterMember.getMemberId()));
            vendor.setVendorName(stationMasterMember.getMemberName());
            deliveryReq.setStationMasterDeliver(true);
        } else {
            throw new BusinessException("获取客户经理或站长信息为空,不可操作发货");
        }
        orderService.deliveryV2(deliveryReq, vendor);
        return SldResponse.success("订单发货成功");
    }

    @PostMapping("receive")
    @ApiOperation(value = "确认收货接口")
    public JsonResult<String> receive(HttpServletRequest request, @RequestBody @NotNull @Valid OrderReceiveDTO orderReceiveDTO) {
        // 获取客户信息
        Member member = UserUtil.getUser(request, Member.class);
        // 获取客户经理信息
        BizUserInfoDTO bizUserInfoDTO = OrderBuilder.getUser(request, BizUserInfoDTO.class);
        log.info("RECEIVE FROM GATEWAY，member info: {}；manage info:{}",
                JSON.toJSONString(member), JSON.toJSONString(bizUserInfoDTO));

        // 订单信息
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderReceiveDTO.getOrderSn());

        log.info("RECEIVE MEMBER ID FROM DB: {}", orderPO.getMemberId());

        BizAssertUtil.isTrue(!orderPO.getMemberId().equals(member.getMemberId()), "不能操作他人的订单");
        BizAssertUtil.isTrue(orderPO.getLockState() > 0, "售后中的订单无法操作");
        BizAssertUtil.isTrue(!orderPO.getOrderState().equals(OrderConst.ORDER_STATE_30), "未到收货状态");
        BizAssertUtil.isTrue(ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == orderPO.getExchangeFlag(), "换货后订单不允许用户签收");

        // 组装操作人信息
        UserDTO userDTO;
        String optRemark;
        if (OrderCreateChannel.BAPP == orderReceiveDTO.getChannel()) {
            BizAssertUtil.notNull(bizUserInfoDTO, "获取客户经理信息为空，不可操作签收！");
            BizAssertUtil.isTrue(!privilegeService.storeReceivePrivilege(orderPO.getOrderSn()),"该店铺不允许客户经理操作签收");
            userDTO = new UserDTO(Long.valueOf(bizUserInfoDTO.getEmployeeId()), bizUserInfoDTO.getUserName(), OrderConst.LOG_ROLE_CUSTOMER_MANAGER);
            optRemark = "客户经理签收";
        } else if (OrderCreateChannel.XXAPP == orderReceiveDTO.getChannel() || OrderCreateChannel.XX_MINI_PRO == orderReceiveDTO.getChannel()) {
            //获取站长信息
            Member stationMasterMember = OrderBuilder.getStationMasterUser(request);
            BizAssertUtil.notNull(stationMasterMember, "获取站长信息为空，不可操作签收！");
            BizAssertUtil.isTrue(!privilegeService.storeReceivePrivilege(orderPO.getOrderSn()),"该店铺不允许站长操作签收");
            userDTO = new UserDTO(stationMasterMember);
            optRemark = "站长确认收货";
        } else {
            userDTO = new UserDTO(member);
            optRemark = "会员确认收货";
        }

        // 确认收货
        orderModel.receiveOrder(orderPO, userDTO.getUserRole(), userDTO.getUserId(),
                userDTO.getUserName(), optRemark, orderReceiveDTO.getChannel());

        return SldResponse.success("确认收货成功");
    }

    @ApiOperation("删除订单")
    @ApiImplicitParams({@ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query")})
    @PostMapping("delete")
    public JsonResult deleteOrder(HttpServletRequest request, String orderSn) {
        Member member = UserUtil.getUser(request, Member.class);
        log.info("member :{}", JSONObject.toJSONString(member));

        // 订单信息
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);
        BizAssertUtil.isTrue(!orderPO.getMemberId().equals(member.getMemberId()), "您无权删除他人订单");
        BizAssertUtil.isTrue(!orderPO.getOrderState().equals(OrderConst.ORDER_STATE_0)
                && !orderPO.getOrderState().equals(OrderConst.ORDER_STATE_50), "订单状态不允许删除");

        // 修改订单状态
        OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(orderPO.getOrderId());
        orderPO1.setDeleteState(OrderConst.DELETE_STATE_1);
        orderService.updateById(orderPO1);

        // 取消不涉及退款，发送取消通知
        orderCreateHelper.addOrderChangeEvent(orderPO, OrderEventEnum.DELETE, new Date());

        return SldResponse.success("删除成功");
    }

    @ApiOperation("修改地址")
    @ApiImplicitParams({@ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "addressId", value = "地址id", required = true, paramType = "query")})
    @PostMapping("updateAddress")
    public JsonResult updateAddress(HttpServletRequest request, String orderSn, Integer addressId) {
        Member member = UserUtil.getUser(request, Member.class);
        orderService.updateAddress(orderSn, addressId, member);
        return SldResponse.success("修改成功");
    }

    @ApiOperation("修改采购订单地址")
    @PostMapping("updatePurchaseOrderAddress")
    public JsonResult updatePurchaseOrderAddress(HttpServletRequest request,
                                                 @RequestBody @Valid PurchaseOrderAddressUpdateRequest address) {
        Member member = UserUtil.getUser(request, Member.class);
        log.info("【修改采购订单地址】用户信息为【{}】", member.toString());
        orderService.updatePurchaseOrderAddress(address, member);
        return SldResponse.success("修改成功");
    }

//    @ApiOperation("修改银行卡转账汇款的付款信息")
//    @ApiImplicitParams({@ApiImplicitParam(name = "paySn", value = "支付单号", required = true, paramType = "query"),
//        @ApiImplicitParam(name = "paymentAccount", value = "付款账号", required = true, paramType = "query"),
//        @ApiImplicitParam(name = "paymentName", value = "付款人姓名", required = true, paramType = "query")})
//    @PostMapping("updateBankTransferPayment")
//    public JsonResult updateBankTransferPayment(HttpServletRequest request,
//        @RequestBody @NotNull @Valid BankTransferOrderRequest payRequest) {
//        Member member = UserUtil.getUser(request, Member.class);
//        BzBankTransferPO entity = new BzBankTransferPO();
//        entity.setPaySn(payRequest.getPaySn());
//        entity.setMemberId(member.getMemberId());
//        entity.setPaymentAccount(payRequest.getPaymentAccount());
//        entity.setPaymentName(payRequest.getPaymentName());
//        return bankTransferModel.updateBankTransferByPaySn(entity);
//    }

    @ApiOperation("定金预售和阶梯团订单尾款确认")
    @PostMapping("balanceConfirm")
    public JsonResult<OrderSubmitPageVO> balanceConfirm(HttpServletRequest request,
                                                        @RequestBody OrderSubmitParamDTO dto) {
        Member member = UserUtil.getUser(request, Member.class);

        OrderPO orderPO = orderModel.getOrderByOrderSn(dto.getOrderSn());

        // 构造计算优惠dto
        OrderSubmitDTO orderSubmitDTO = orderSubmitUtil.getOrderSubmitDTO(dto, member.getMemberId(), orderPO);

        // orderSubmitDTO = promotionCommonFeignClient.orderSubmitCalculationDiscount(orderSubmitDTO, dto.getSource());

        // 计算运费
        List<BigDecimal> expressFeeList = new ArrayList<>();
        BigDecimal expressFee = orderPO.getExpressFee();
        expressFeeList.add(expressFee);

        // 构造返回数据
        OrderSubmitPageVO vo = new OrderSubmitPageVO(orderSubmitDTO);
        if (orderPO.getOrderType() == PromotionConst.PROMOTION_TYPE_103) {
            vo.setPromotionType(PromotionConst.PROMOTION_TYPE_103);
            vo = preSaleBalanceConfirm(vo, orderPO.getOrderSn());
        } else if (orderPO.getOrderType() == PromotionConst.PROMOTION_TYPE_105) {
            vo.setPromotionType(PromotionConst.PROMOTION_TYPE_105);
            vo = ladderGroupBalanceConfirm(vo, orderPO.getOrderSn());
        }
        return SldResponse.success(vo);
    }

    /**
     * 预售尾款确认
     *
     * @param vo
     * @param orderSn
     * @return
     */
    public OrderSubmitPageVO preSaleBalanceConfirm(OrderSubmitPageVO vo, String orderSn) {
        // 查询预售订单扩展信息
        PresellOrderExtendExample example = new PresellOrderExtendExample();
        example.setOrderSn(orderSn);
        List<PresellOrderExtendVO> orderExtendList = presellOrderExtendFeignClient.getPresellOrderExtendList(example);
        AssertUtil.notEmpty(orderExtendList, "获取预售订单扩展信息为空");
        PresellOrderExtendVO orderExtend = orderExtendList.get(0);

        OrderSubmitPageVO.PresellInfo presellInfo = new OrderSubmitPageVO.PresellInfo();
        presellInfo.setPresellId(orderExtend.getPresellId());
        if (orderExtend.getIsAllPay() == OrderConst.IS_ALL_PAY_1) {
            presellInfo.setType(PreSellConst.PRE_SELL_TYPE_2);
        } else {
            presellInfo.setType(PreSellConst.PRE_SELL_TYPE_1);
        }
        presellInfo.setPresellState(orderExtend.getOrderSubState());
        presellInfo.setDeliverTime(orderExtend.getDeliverTime());
        presellInfo.setProductId(orderExtend.getProductId());
        presellInfo.setPresellPrice(
                StringUtil.isNullOrZero(orderExtend.getPresellPrice()) ? null : orderExtend.getPresellPrice().toString());
        presellInfo.setFirstMoney(
                orderExtend.getDepositAmount().multiply(new BigDecimal(orderExtend.getProductNum())).toString());
        if (!StringUtil.isNullOrZero(orderExtend.getFirstExpand())) {
            presellInfo.setFirstExpand(
                    orderExtend.getFirstExpand().multiply(new BigDecimal(orderExtend.getProductNum())).toString());
            presellInfo.setFinalDiscount((orderExtend.getFirstExpand().subtract(orderExtend.getDepositAmount()))
                    .multiply(new BigDecimal(orderExtend.getProductNum())).toString());
        }
        presellInfo.setSecondMoney(StringUtil.isNullOrZero(orderExtend.getRemainAmount()) ? null
                : orderExtend.getRemainAmount().multiply(new BigDecimal(orderExtend.getProductNum())).toString());
        vo.setPresellInfo(presellInfo);
        Goods goods = goodsFeignClient.getGoodsByGoodsId(orderExtend.getGoodsId());
        vo.setIsVatInvoice(goods.getIsVatInvoice() == null || goods.getIsVatInvoice() == GoodsConst.IS_VAT_INVOICE_YES);

        if (!StringUtil.isNullOrZero(orderExtend.getFirstExpand())) {
            vo.setTotalDiscount(
                    orderExtend.getFirstExpand().multiply(new BigDecimal(orderExtend.getProductNum())).toString());
        } else {
            vo.setTotalDiscount(
                    orderExtend.getDepositAmount().multiply(new BigDecimal(orderExtend.getProductNum())).toString());
        }
        vo.setTotalAmount(
                new BigDecimal(vo.getTotalAmount()).subtract(new BigDecimal(vo.getTotalDiscount())).toString());
        return vo;
    }

    /**
     * 阶梯团尾款确认
     *
     * @param vo
     * @param orderSn
     * @return
     */
    public OrderSubmitPageVO ladderGroupBalanceConfirm(OrderSubmitPageVO vo, String orderSn) {
        // 查询阶梯团订单扩展信息
        LadderGroupOrderExtend example = new LadderGroupOrderExtend();
        example.setOrderSn(orderSn);
        List<LadderGroupOrderExtendVO> orderExtendList =
                ladderGroupOrderExtendFeignClient.getLadderGroupOrderExtendList(example);
        AssertUtil.notEmpty(orderExtendList, "获取阶梯团订单扩展信息为空");
        LadderGroupOrderExtendVO orderExtend = orderExtendList.get(0);

        OrderSubmitPageVO.LadderGroupInfo ladderGroupInfo = new OrderSubmitPageVO.LadderGroupInfo();
        ladderGroupInfo.setGroupId(orderExtend.getGroupId());
        ladderGroupInfo.setLadderGroupState(orderExtend.getOrderSubState());
        ladderGroupInfo.setProductId(orderExtend.getProductId());
        ladderGroupInfo.setProductPrice(orderExtend.getProductPrice().toString());
        ladderGroupInfo.setAdvanceDeposit(
                orderExtend.getAdvanceDeposit().multiply(new BigDecimal(orderExtend.getProductNum())).toString());
        ladderGroupInfo.setRemainAmount(
                orderExtend.getRemainAmount().multiply(new BigDecimal(orderExtend.getProductNum())).toString());
        if (orderExtend.getOrderSubState() != LadderGroupConst.ORDER_SUB_STATE_1) {
            if (!StringUtils.isEmpty(vo.getTotalDiscount())
                    && !StringUtil.isNullOrZero(new BigDecimal(vo.getTotalDiscount()))) {
                ladderGroupInfo.setRealRemainAmount(new BigDecimal(ladderGroupInfo.getRemainAmount())
                        .subtract(new BigDecimal(vo.getTotalDiscount())).toString());
            } else {
                ladderGroupInfo.setRealRemainAmount(ladderGroupInfo.getRemainAmount());
            }
        }
        ladderGroupInfo.setRemainStartTime(orderExtend.getRemainStartTime());
        vo.setLadderGroupInfo(ladderGroupInfo);
        Goods goods = goodsFeignClient.getGoodsByGoodsId(orderExtend.getGoodsId());
        vo.setIsVatInvoice(goods.getIsVatInvoice() == null || goods.getIsVatInvoice() == GoodsConst.IS_VAT_INVOICE_YES);
        return vo;
    }

    @ApiOperation("尾款提交订单接口")
    @ApiResponses(@ApiResponse(code = 200, message = "data.paySn:支付单号"))
    @PostMapping("balanceSubmit")
    public JsonResult balanceSubmit(HttpServletRequest request, @RequestBody OrderSubmitParamDTO dto) {

        if (dto.getAddressId() == null || dto.getAddressId() == 0) {
            throw new BusinessException("请选择收货地址");
        }

        AssertUtil.notEmpty(dto.getOrderSn(), "订单号不能为空");

        Member member = UserUtil.getUser(request, Member.class);

        OrderPO orderPO = orderModel.getOrderByOrderSn(dto.getOrderSn());

        // 构造计算优惠dto
        OrderSubmitDTO orderSubmitDTO = orderSubmitUtil.getOrderSubmitDTO(dto, member.getMemberId(), orderPO);

        if (orderPO.getOrderType() == PromotionConst.PROMOTION_TYPE_103) {
            // 查询预售订单扩展信息
            PresellOrderExtendExample example = new PresellOrderExtendExample();
            example.setOrderSn(dto.getOrderSn());
            List<PresellOrderExtendVO> orderExtendList =
                    presellOrderExtendFeignClient.getPresellOrderExtendList(example);
            AssertUtil.notEmpty(orderExtendList, "获取预售订单扩展信息为空");
            PresellOrderExtendVO orderExtend = orderExtendList.get(0);
            if (!StringUtil.isNullOrZero(orderExtend.getFirstExpand())) {
                orderSubmitDTO.setTotalDiscount(
                        orderExtend.getFirstExpand().multiply(new BigDecimal(orderExtend.getProductNum())));
            } else {
                orderSubmitDTO.setTotalDiscount(
                        orderExtend.getDepositAmount().multiply(new BigDecimal(orderExtend.getProductNum())));
            }
            orderSubmitDTO.setTotalAmount(orderSubmitDTO.getTotalAmount().subtract(orderSubmitDTO.getTotalDiscount()));
        } else if (orderPO.getOrderType() == PromotionConst.PROMOTION_TYPE_105) {
            // 查询阶梯团订单扩展信息
            LadderGroupOrderExtend example = new LadderGroupOrderExtend();
            example.setOrderSn(dto.getOrderSn());
            List<LadderGroupOrderExtendVO> orderExtendList =
                    ladderGroupOrderExtendFeignClient.getLadderGroupOrderExtendList(example);
            AssertUtil.notEmpty(orderExtendList, "获取阶梯团订单扩展信息为空");
            LadderGroupOrderExtendVO orderExtend = orderExtendList.get(0);
            orderSubmitDTO.setTotalDiscount(
                    orderExtend.getAdvanceDeposit().multiply(new BigDecimal(orderExtend.getProductNum())));
            orderSubmitDTO.setTotalAmount(orderSubmitDTO.getTotalAmount().subtract(orderSubmitDTO.getTotalDiscount()));
        }
        // orderSubmitDTO = promotionCommonFeignClient.orderSubmitCalculationDiscount(orderSubmitDTO, dto.getSource());

        orderModel.submitBalanceOrder(orderSubmitDTO, dto, member, orderPO, orderPO.getAreaCode());

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("paySn", orderPO.getPaySn());
        dataMap.put("orderList", orderPO.getOrderSn());
        return SldResponse.success(dataMap);
    }

    @ApiOperation("订单延期自动收货")
    @ApiImplicitParams({@ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query")})
    @GetMapping("extendOrderAutoReceiveTime")
    public JsonResult<String> extendOrderAutoReceiveTime(String orderSn) {
        orderService.extendOrderAutoReceiveTime(orderSn);
        return SldResponse.success(SentenceConst.EXTEND_AUTO_RECEIVE_SUCCESS);
    }

    @ApiOperation("供应商刷数")
    @GetMapping("dealSupplier")
    public JsonResult<Boolean> dealSupplier(@ApiParam(value = "开始时间 yyyy-MM-dd", required = true) @RequestParam("startTime") String startTime,
                                            @ApiParam(value = "结束时间 yyyy-MM-dd", required = true) @RequestParam("endTime") String endTime
    ) {
        //查询商品行
        List<OrderProductPO> productPOS = iOrderProductService.lambdaQuery().eq(OrderProductPO::getSupplierName, "").between(OrderProductPO::getCreateTime, startTime, endTime).list();
        //获取去重后goodsId
        List<Long> list = productPOS.stream().map(OrderProductPO::getGoodsId).distinct().collect(Collectors.toList());
        // 查询商品信息
        List<Goods> listByGoodsIds = goodsFeignClient.getGoodsListByGoodsIds(list);
        // 需要更新的数据
        List<OrderProductPO> needUpdate = new ArrayList<>();

        for (OrderProductPO productPO : productPOS) {
            listByGoodsIds.stream().filter(x -> x.getGoodsId().equals(productPO.getGoodsId())).findFirst().ifPresent(y -> {
                        productPO.setSupplierName(y.getSupplierName());
                        needUpdate.add(productPO);
                    }
            );
        }
        // 批量更新
        iOrderProductService.updateBatchById(needUpdate);
        return SldResponse.success(true);
    }

    @Override
    @GetMapping("getOrderShareCode")
    @ApiOperation("获取订单分享码")
    public JsonResult<String> getOrderShareCode(@RequestParam(value = "orderSn") @NotNull String orderSn) {
        LambdaQueryWrapper<OrderExtendPO> query = new LambdaQueryWrapper<>();
        query.eq(OrderExtendPO::getOrderSn, orderSn)
                .eq(OrderExtendPO::getEnabledFlag, 1)
                .select(OrderExtendPO::getShareCode, OrderExtendPO::getOrderSn);
        OrderExtendPO extendPO = orderExtendService.getOne(query);
        String shareCode = "";
        if (Objects.nonNull(extendPO)) {
            shareCode = extendPO.getShareCode();
        }

        return SldResponse.success(shareCode);
    }

    @Override
    @GetMapping("getOrderDiscount")
    @ApiOperation("获取订单优惠明细")
    public JsonResult<String> getOrderDiscount(@RequestParam(value = "orderSn") @NotNull String orderSn) {
        LambdaQueryWrapper<OrderPO> query = new LambdaQueryWrapper<>();
        query.eq(OrderPO::getOrderSn, orderSn)
                .eq(OrderPO::getEnabledFlag, 1)
                .select(OrderPO::getActivityDiscountDetail, OrderPO::getOrderSn);
        OrderPO orderPO = orderService.getOne(query);
        String discountDetail = "";
        if (Objects.nonNull(orderPO)) {
            discountDetail = orderPO.getActivityDiscountDetail();
        }

        return SldResponse.success(discountDetail);
    }


    @ApiOperation(value = "客户确认", notes = "接口data=ture标识成功，data=false标识失败")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单号", paramType = "query", required = true)
    })
    @GetMapping("/customer/confirm")
    public JsonResult<Void> customerConfirm(String orderSn) {
        boolean status = orderService.setCustomerConfirmStatus(orderSn);
        BizAssertUtil.isTrue(!status, "抱歉，当前客户确认操作失败，请查看是否已经操作了客户确认！");
        return SldResponse.success();
    }

    @ApiOperation(value = "换货审批")
    @PostMapping("/exchangeOrder/audit")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "exchangeSn", value = "换货单号", paramType = "query", required = true)
    })
    public JsonResult<Void> exchangeOrderAudit(HttpServletRequest request, @RequestBody OrderExchangeAuditDTO orderExchangeAuditDTO) throws Exception {
        Member member = UserUtil.getUser(request, Member.class);
        UserDTO userDTO = new UserDTO(member);
        orderExchangeService.exchangeOrderAudit(orderExchangeAuditDTO, userDTO);
        return SldResponse.success();
    }


}
