package com.cfpamf.ms.mallorder.integration.facade;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.loan.facade.request.external.mall.CdmallPreconditionCheckRequest;
import com.cfpamf.ms.loan.facade.request.external.mall.ContractPreviewRequest;
import com.cfpamf.ms.loan.facade.request.external.mall.GetMallContractCodesRequest;
import com.cfpamf.ms.loan.facade.vo.external.mall.CdmallOrderVo;
import com.cfpamf.ms.loan.facade.vo.external.mall.CdmallPreconditionCheckVo;
import com.cfpamf.ms.loan.facade.vo.external.mall.MallContractCodesVo;
import com.cfpamf.ms.loan.facade.vo.external.mall.MallContractContentVO;
import com.cfpamf.ms.mallorder.common.base.ResultT;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: zengzhen
 * @Date: 2020-01-06 16:34
 * @Version 1.0
 * @Desc
 */
@FeignClient(value = "ms-service-loan", url = "${ms-service-loan.url}")
public interface CdMallOrderQueryFacade {


    /**
     * 获取电商订单信息
     *
     * @param orderBatchId
     * @return
     */
    @GetMapping(value = "/loan/externalBusiness/getCdmallOrder")
    Result<CdmallOrderVo> getCdmallOrder(@RequestParam(name = "orderBatchId") String orderBatchId);

    /**
     * 电商用呗前置条件判断
     * - 杨浩
     * @param request
     * @return
     */
    @PostMapping(value = "/loan/externalBusiness/cdmallPreconditionCheck")
    Result<CdmallPreconditionCheckVo> cdmallPreconditionCheck(@RequestBody CdmallPreconditionCheckRequest request) throws Exception;

    /**
     * 预览合同
     *
     * @param contractPreviewRequest
     */
    @GetMapping(value = "/loan/externalBusiness/contractPreview/V3")
    ResultT<MallContractContentVO> contractPreviewV3(@RequestBody ContractPreviewRequest contractPreviewRequest);
    /**
     * 获取用呗合同
     * @param request
     * @return
     */
    @PostMapping("/loan/externalBusiness/getMallPreviewContractCodes")
    ResultT<MallContractCodesVo> getMallPreviewContractCodes(@RequestBody GetMallContractCodesRequest request);
}
