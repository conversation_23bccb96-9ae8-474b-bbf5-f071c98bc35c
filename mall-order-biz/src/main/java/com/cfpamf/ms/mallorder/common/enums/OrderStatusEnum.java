package com.cfpamf.ms.mallorder.common.enums;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 订单状态定义
 *
 * <AUTHOR>
 * @date 2021/6/19 17:16
 * @return
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum OrderStatusEnum implements IEnum<Integer> {
    UN_KNOW(-1, "未知"),
    CANCELED(0, "已取消"),
    WAIT_PAY_DEPOSIT(5, "待支付订金"),
    WAIT_PAY(10, "待付款"),
    DEAL_PAY(15, "付款中"),
    WAIT_DELIVER(20, "待发货"),
    PART_DELIVERED(25, "部分发货"),
    WAIT_RECEIPT(30, "待签收"),
    TRADE_SUCCESS(40, "交易成功"),
    TRADE_CLOSE(50, "交易关闭");

    Integer value;
    String desc;

    public static boolean isClosed(int value) {
        return CANCELED.getValue() == value || TRADE_CLOSE.getValue() == value;
    }

    public static OrderStatusEnum valueOf(int value) {
        for (OrderStatusEnum ps : OrderStatusEnum.values()) {
            if (value == ps.value) {
                return ps;
            }
        }
        return UN_KNOW;
    }

    /**
     * 交易完成
     *
     * @param value
     * @return
     */
    public static boolean isTradeSuccess(Integer value) {
        if (TRADE_SUCCESS.value == value) {
            return true;
        }
        return false;
    }

    /**
     * 交易关闭
     *
     * @param value
     * @return
     */
    public static boolean isTradeClose(Integer value) {
        if (TRADE_CLOSE.value == value) {
            return true;
        }
        return false;
    }

    public static boolean isPaid(int state) {
        return state == WAIT_DELIVER.getValue() || state == PART_DELIVERED.getValue() || state == WAIT_RECEIPT.getValue() || state == TRADE_SUCCESS.getValue();
    }

    /**
     * 已支付订单状态列表
     *
     * @return List<Integer>
     */
    public static List<Integer> paidStatus() {
        return Arrays.asList(
                WAIT_DELIVER.getValue(),
                PART_DELIVERED.getValue(),
                WAIT_RECEIPT.getValue(),
                TRADE_SUCCESS.getValue()
        );
    }

    public static boolean isAllowedReturn(int state, int orderType) {
        return (state == WAIT_PAY.getValue() && orderType == PromotionConst.PROMOTION_TYPE_107) || state == WAIT_DELIVER.getValue() || state == PART_DELIVERED.getValue() || state == WAIT_RECEIPT.getValue() || state == TRADE_SUCCESS.getValue();
    }

    // 只处理非预付的条件判断,预付有专门的条件判断，其他条件的按原有的逻辑走
    public static boolean isAllowedReturnV2(int state,int orderType) {
        return orderType == PromotionConst.PROMOTION_TYPE_107 || state == WAIT_DELIVER.getValue() || state == PART_DELIVERED.getValue() || state == WAIT_RECEIPT.getValue() || state == TRADE_SUCCESS.getValue();
    }

    public static boolean isPresellDeposit(int state, int orderType) {
        return state == WAIT_PAY.getValue() && orderType == PromotionConst.PROMOTION_TYPE_107;
    }

    public static boolean isPresellDeposit(OrderPO orderPo) {
        return orderPo.getOrderState().equals(DEAL_PAY.getValue())
                && orderPo.getOrderType() == PromotionConst.PROMOTION_TYPE_107
                && orderPo.getLoanPayState().equals(LoanStatusEnum.APPLY_FAIL.getValue());
    }

    @Override
    public Integer getValue() {
        return this.value;
    }

    public boolean isTrue(int state) {
        return this.getValue() == state;
    }

    /**
     * 是否待发货
     *
     * @param value
     * @return
     */
    public static boolean isWaitDelivery(int value) {
        return WAIT_DELIVER.getValue() == value || PART_DELIVERED.getValue() == value;
    }

    public static boolean isWaitPay(int value) {
        return WAIT_PAY_DEPOSIT.getValue() == value || WAIT_PAY.getValue() == value;
    }

    public static String parseDesc(Integer value) {
        return Arrays.stream(OrderStatusEnum.values()).filter(x -> x.getValue().equals(value)).findFirst()
                .orElseThrow(() -> new MSException(String.valueOf(ErrorCodeEnum.U.DATA_NOT_EXISTS.getCode()), String.format("订单状态status转换成desc失败,status:%s", value)))
                .getDesc();
    }

    /**
     * 申请售后时是否需要退还货品
     *
     * @param value 订单状态
     * @return 是否需要退还
     */
    public static boolean isNeedReturnGoods(Integer value) {
        return PART_DELIVERED.getValue().equals(value) || WAIT_RECEIPT.getValue().equals(value) || TRADE_SUCCESS.getValue().equals(value);
    }

}
