package com.cfpamf.ms.mallorder.integration.cashier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 收银台-支付订单收款商家信息表
 * </p>
 * <AUTHOR>
 */
@Data
public class OrderMerchant {

    @ApiModelProperty("引荐商")
    private String rcmdMerchant;

    @ApiModelProperty("店铺id")
    private String merchantId;

    @ApiModelProperty("商家虚拟账id")
    private String virtualAcctNo;

    @ApiModelProperty(" 店铺名")
    private String merchantName;

    @ApiModelProperty("店铺类型")
    private Integer merchantType;

    @ApiModelProperty("商家联系方式")
    private String merchantMobile;

    @ApiModelProperty("是否自营店铺 1 是 0 否")
    private Integer ownStore;

    @ApiModelProperty("店铺注册证件号")
    private String orgCode;

    @ApiModelProperty("商家印章编码")
    private String companySealCode;

    @ApiModelProperty("法人姓名")
    private String corporateName;

    @ApiModelProperty("入驻时间")
    private LocalDateTime joinDate;

    @ApiModelProperty("失效时间")
    private LocalDateTime endDate;
}
