package com.cfpamf.ms.mallorder.vo.exportvo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.cfpamf.ms.mallorder.common.annotation.PoiExportProperty;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> 2021/10/18.
 *
 * 退款导出
 */
@Data
public class OrderRefundExportVO {

    @ApiModelProperty("退款编号")
    @PoiExportProperty(name = "退款编号", order = 1)
    @ExcelProperty(value = "退款编号",index = 1)
    private String refundNum;

    @PoiExportProperty(name = "订单编号", order = 2)
    @ExcelProperty(value = "订单编号")
    private String orderSn;

    @PoiExportProperty(name = "结算单号", order = 3)
    @ExcelProperty(value = "结算单号")
    private String billSn;

    @PoiExportProperty(name = "支付方式", order = 4)
    @ExcelProperty(value = "支付方式")
    private String payMethod;

    @PoiExportProperty(name = "退款申请时间", order = 5)
    @ExcelProperty(value = "退款申请时间")
    private String refundStartTime;

    @PoiExportProperty(name = "平台审核时间", order = 5)
    @ExcelProperty(value = "平台审核时间")
    private Date platformAuditTime;

    @PoiExportProperty(name = "客户编号", order = 6)
    @ExcelProperty(value = "客户编号")
    private String customerNo;

    @PoiExportProperty(name = "客户姓名", order = 7)
    @ExcelProperty(value = "客户姓名")
    private String customerName;

    @PoiExportProperty(name = "会员名", order = 8)
    @ExcelProperty(value = "会员名")
    private String memberName;

    @PoiExportProperty(name = "客户经理", order = 9)
    @ExcelProperty(value = "客户经理")
    private String customerManager;

    @PoiExportProperty(name = "所属分支", order = 10)
    @ExcelProperty(value = "所属分支")
    private String branchName;

    @PoiExportProperty(name = "引荐商户", order = 11)
    @ExcelProperty(value = "引荐商户")
    private String recommendStoreName;

    @ApiModelProperty("店铺id")
    @PoiExportProperty(name = "店铺id", order = 12)
    @ExcelProperty(value = "店铺id")
    private Long storeId;

    @PoiExportProperty(name = "店铺名称", order = 13)
    @ExcelProperty(value = "店铺名称")
    private String storeName;

    @PoiExportProperty(name = "商品编号", order = 14)
    @ExcelProperty(value = "商品编号")
    private String productId;

    @PoiExportProperty(name = "商品名称", order = 15)
    @ExcelProperty(value = "商品名称")
    private String productName;

    @PoiExportProperty(name = "购买数", order = 16)
    @ExcelProperty(value = "购买数")
    private Integer buyNum;

    @PoiExportProperty(name = "物料编码", order = 16)
    @ExcelProperty(value = "物料编码")
    @ApiModelProperty(value = "sku物料编码")
    private String skuMaterialCode;

    @PoiExportProperty(name = "物料名称", order = 16)
    @ExcelProperty(value = "物料名称")
    @ApiModelProperty(value = "sku物料名称")
    private String skuMaterialName;

    @ApiModelProperty(value = "新 渠道商品Sku ID")
    @PoiExportProperty(name = "物料规格编码", order = 17)
    @ExcelProperty(value = "物料规格编码")
    private String channelNewSkuId;

    @PoiExportProperty(name = "实付金额", order = 17)
    @ExcelProperty(value = "实付金额")
    private BigDecimal productAmount;

    @PoiExportProperty(name = "退货数", order = 18)
    @ExcelProperty(value = "退货数")
    private Integer returnNum;

    @PoiExportProperty(name = "退款申请金额", order = 19)
    @ExcelProperty(value = "退款申请金额")
    private BigDecimal refundApplySumAmount;

    @PoiExportProperty(name = "退款金额", order = 20)
    @ExcelProperty(value = "退款金额")
    private BigDecimal productActualReturnAmount;

    @PoiExportProperty(name = "商品退款金额(元)", order = 21)
    @ExcelProperty(value = "商品退款金额(元)")
    private BigDecimal productReturnAmount;

    @PoiExportProperty(name = "运费", order = 22)
    @ExcelProperty(value = "运费")
    private BigDecimal returnExpressAmount;

    @PoiExportProperty(name = "其他赔偿", order = 23)
    @ExcelProperty(value = "其他赔偿")
    private BigDecimal otherCompensationAmount;

    @PoiExportProperty(name = "客户承担", order = 24)
    @ExcelProperty(value = "客户承担")
    private BigDecimal customerAssumeAmount;

    @PoiExportProperty(name = "退款扣罚", order = 25)
    @ExcelProperty(value = "退款扣罚")
    private BigDecimal refundPunishAmount;

    @PoiExportProperty(name = "利息承担", order = 26)
    @ExcelProperty(value = "利息承担")
    private String interestPayer;

    @PoiExportProperty(name = "下单间隔天数", order = 27)
    @ExcelProperty(value = "下单间隔天数")
    private Integer orderCreateInterval;

    private Integer state;

    @PoiExportProperty(name = "售后状态", order = 28)
    @ExcelProperty(value = "售后状态")
    private String stateDesc;

    private Integer returnType;

    @PoiExportProperty(name = "退款方式", order = 29)
    @ExcelProperty(value = "退款方式")
    private String returnTypeDesc;

    private Integer refundType;

    @PoiExportProperty(name = "退款方式", order = 30)
    @ExcelProperty(value = "退款方式")
    private String refundTypeDesc;

    @PoiExportProperty(name = "退款时间", order = 31)
    @ExcelProperty(value = "退款时间")
    private Date refundEndTime;

    @PoiExportProperty(name = "退款账户", order = 32)
    @ExcelProperty(value = "退款账户")
    private String refundAccount;

    @PoiExportProperty(name = "是否冲正", order = 33)
    @ExcelProperty(value = "是否冲正")
    private String isReverse;

    @PoiExportProperty(name = "冲正日期", order = 34)
    @ExcelProperty(value = "冲正日期")
    private Date reverseTime;

    @PoiExportProperty(name = "订单创建时间", order = 35)
    @ExcelProperty(value = "订单创建时间")
    private Date orderCreateTime;

    private Date orderPayTime;

    @PoiExportProperty(name = "订单完成时间", order = 36)
    @ExcelProperty(value = "订单完成时间")
    private Date orderFinishTime;

    @PoiExportProperty(name = "乡助卡退款金额", order = 37)
    @ExcelProperty(value = "乡助卡退款金额")
    private BigDecimal xzCardAmount;

    @PoiExportProperty(name = "所属区域", order = 38)
    @ExcelProperty(value = "所属区域")
    private String areaName;

    @PoiExportProperty(name = "片区名称", order = 38)
    @ExcelProperty(value = "片区名称")
    private String zoneName;

    @PoiExportProperty(name = "片区编号", order = 38)
    @ExcelProperty(value = "片区编号")
    private String zoneCode;

    private Integer orderPattern;

    @PoiExportProperty(name = "订单模式", order = 39)
    @ExcelProperty(value = "订单模式")
    private String orderPatternDesc;

    @PoiExportProperty(name = "发货地址", order = 40)
    @ExcelProperty(value = "发货地址")
    private String deliverPlace;

    @PoiExportProperty(name = "预估运费", order = 41)
    @ExcelProperty(value = "预估运费")
    private BigDecimal estimateExpressFee;

    private Integer deliveryRequirements;

    @PoiExportProperty(name = "预估发货时间", order = 42)
    @ExcelProperty(value = "预估发货时间")
    private String purchaseOrderDeliveryTime;

    @PoiExportProperty(name = "发货时间", order = 43)
    @ExcelProperty(value = "发货时间")
    private Date deliverTime;

    private Integer productDeliveryState;

    @PoiExportProperty(name = "发货时间", order = 43)
    @ExcelProperty(value = "发货状态")
    private String productDeliveryStateDesc;

    /**
     * 退款申请时间
     */
    private Date applyTime;

    public String getReturnTypeDesc() {
        return ReturnTypeEnum.getDesc(this.returnType);
    }

    public String getRefundTypeDesc() {
        return RefundType.getDesc(this.refundType);
    }

    public String getStateDesc() {
        return OrderReturnStatus.getShowDesc(this.state);
    }

    public String getOrderPatternDesc() {
        return Objects.isNull(this.orderPattern) ? "" : OrderPatternEnum.valueOf(this.orderPattern).getDesc();
    }

    public String getPurchaseOrderDeliveryTime() {
        if (Objects.isNull(this.orderPattern) || !this.orderPattern.equals(OrderPatternEnum.PURCHASE_CENTRE.getValue())) {
            return "";
        }
        if (OrderDeliveryRequirementsEnum.ANYTIME.getValue().equals(this.deliveryRequirements)) {
            return OrderDeliveryRequirementsEnum.ANYTIME.getDesc();
        } else if (OrderDeliveryRequirementsEnum.TEN_TO_TWENTY.getValue().equals(this.deliveryRequirements)) {
            String startTime = DateUtil.getDateString(DateUtil.addDays(this.orderCreateTime, 10), DateUtil.FORMAT_TIME);
            String endTime = DateUtil.getDateString(DateUtil.addDays(this.orderCreateTime, 20), DateUtil.FORMAT_TIME);
            return String.format("%s-%s", startTime, endTime);
        }
        return "";
    }

    public Integer getOrderCreateInterval() {
        if (this.orderPayTime == null || this.applyTime == null) {
            return null;
        }
        return DateUtil.getDaysDiffBetweenTwoDateWithoutTime(this.orderPayTime, this.applyTime);
    }

    public String getProductDeliveryStateDesc() {
        if (Objects.isNull(this.productDeliveryState)) {
            return "";
        }
        OrderProductDeliveryEnum productDelivery = OrderProductDeliveryEnum.valueOf(this.productDeliveryState);
        return Objects.isNull(productDelivery) ? "" : productDelivery.getDesc();
    }
}
