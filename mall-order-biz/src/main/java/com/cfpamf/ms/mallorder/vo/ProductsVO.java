package com.cfpamf.ms.mallorder.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Create 2022-01-12 10:42
 * @Description :商品详情
 */
@Data
public class ProductsVO {

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "售价")
    private BigDecimal sellingPrice;

    @ApiModelProperty(value = "购买数量")
    private Integer qty;

}
