package com.cfpamf.ms.mallorder.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.customer.facade.vo.user.UserBaseInfoVo;
import com.cfpamf.ms.mallmember.api.MemberAddressFeignClient;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallmember.po.MemberAddress;
import com.cfpamf.ms.mallorder.common.config.EmployeeTradeConfig;
import com.cfpamf.ms.mallorder.common.config.ShareOrderConfig;
import com.cfpamf.ms.mallorder.common.constant.OrderProductConst;
import com.cfpamf.ms.mallorder.common.enums.CustomerTypeEnum;
import com.cfpamf.ms.mallorder.common.enums.transaction.TransactionLogTypeEnum;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.mq.RabbitMQUtils;
import com.cfpamf.ms.mallorder.common.mq.RabbitMqConfig;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.cfpamf.ms.mallorder.common.util.OrderSubmitAttributesUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.*;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.integration.facade.BmsUserFacade;
import com.cfpamf.ms.mallorder.integration.shop.ShopIntegration;
import com.cfpamf.ms.mallorder.integration.system.TradeDocumentIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.model.OrderExtendModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.service.ICommonMqEventService;
import com.cfpamf.ms.mallorder.vo.CustInfoVo;
import com.cfpamf.ms.mallorder.vo.OrderSubmitPageVO;
import com.cfpamf.ms.mallshop.api.PurchaserFeign;
import com.cfpamf.ms.mallshop.api.StoreRegionFeignClient;
import com.cfpamf.ms.mallshop.enums.EmployeeWhiteListEnum;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.cfpamf.ms.mallshop.vo.FrontStoreRegionRequestVO;
import com.cfpamf.ms.mallshop.vo.FrontStoreRegionVo;
import com.cfpamf.ms.mallshop.vo.SelfLiftingPointVo;
import com.cfpamf.ms.mallsystem.request.OrderSubmitRuleMatchDTO;
import com.cfpamf.ms.mallsystem.vo.OrderDataPushHitObject;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import io.seata.spring.annotation.GlobalTransactional;
import io.seata.tm.api.GlobalTransaction;
import io.seata.tm.api.GlobalTransactionContext;
import io.seata.tm.api.transaction.Propagation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * 订单创建工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-03
 */
@Component
@Slf4j
public class OrderCreateHelper {


    @Autowired
    private ICommonMqEventService commonMqEventService;

    @Resource
    private OrderModel orderModel;

    @Resource
    private OrderProductModel orderProductModel;

    @Autowired
    private OrderExtendModel orderExtendModel;

    @Autowired
    private RabbitMQUtils rabbitMQUtils;

    @Resource
    private MemberAddressFeignClient memberAddressFeignClient;

    @Autowired
    private StoreRegionFeignClient storeRegionFeignClient;
    @Autowired
    private MemberFeignClient memberFeignClient;
    @Autowired
    private ShopIntegration shopIntegration;
    @Autowired
    private UserInfoComponent userInfoComponent;
	@Autowired
	private PurchaserFeign purchaserFeign;
	@Autowired
	private TradeDocumentIntegration tradeDocumentIntegration;

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private EmployeeTradeConfig employeeTradeConfig;
    @Autowired
    private CustomerIntegration customerIntegration;
    @Autowired
    private BmsUserFacade bmsUserFacade;

    @Autowired
    private ShareOrderConfig shareOrderConfig;

    @Autowired
    private OrderSubmitAttributesUtils orderSubmitAttributesUtils;

    /**
     * 根据店铺ID和收货地址获取店铺areaCode
     *
     * @param storeId
     * @param addressDTO
     * @return
     */
    public String getStoreAreaCodeByAddress(String storeId, OrderAddressDTO addressDTO, Integer memberId) {
//        Member member = memberFeignClient.getMemberByMemberId(memberId);
//        FrontStoreRegionVo accurateAreaByGps = getStoreRegionByAddress(storeId, addressDTO, member);
        Member member = orderSubmitAttributesUtils.getMemberByMemberId(memberId);
        FrontStoreRegionVo accurateAreaByGps = orderSubmitAttributesUtils.getStoreAreaCode(storeId,addressDTO,member);

        if (Objects.nonNull(accurateAreaByGps) && !CollectionUtils.isEmpty(accurateAreaByGps.getRegionVOList())) {
            return accurateAreaByGps.getRegionVOList().get(0).getCode();
        }
        return null;
    }

    public String getStoreAreaCodeByAddress(String storeId, OrderAddressDTO addressDTO, Member member) {
        FrontStoreRegionVo accurateAreaByGps = getStoreRegionByAddress(storeId, addressDTO, member);

        if (Objects.nonNull(accurateAreaByGps) && !CollectionUtils.isEmpty(accurateAreaByGps.getRegionVOList())) {
            return accurateAreaByGps.getRegionVOList().get(0).getCode();
        }
        return null;
    }

    public FrontStoreRegionVo getStoreRegionByAddress(String storeId, OrderAddressDTO addressDTO, Member member) {
        FrontStoreRegionRequestVO regionRequestVO = new FrontStoreRegionRequestVO();
        regionRequestVO.setProvince(addressDTO.getProvince());
        regionRequestVO.setCity(addressDTO.getCity());
        regionRequestVO.setCounty(addressDTO.getDistrict());
        regionRequestVO.setTown(addressDTO.getTown());
        regionRequestVO.setStoreId(storeId);
        regionRequestVO.setUser(member);
        return storeRegionFeignClient.getAccurateAreaByGps(regionRequestVO);
    }

    public OrderAddressDTO buildOrderAddressById(Integer addressId) {
        if (addressId == null || addressId == 0) {
            throw new BusinessException("请选择订单收货地址");
        }
        OrderAddressDTO orderAddress = new OrderAddressDTO();
        //收货地址
        MemberAddress memberAddress = orderSubmitAttributesUtils.getMemberAddressByAddressId(addressId);
        if (memberAddress == null) {
            throw new MallException("收货地址不存在 addressId:" + addressId,
                    ErrorCodeEnum.U.DATA_NOT_EXISTS.getCode());
        }
        if (StringUtils.isEmpty(memberAddress.getProvinceName()) || StringUtils.isEmpty(memberAddress.getCityName())
                || StringUtils.isEmpty(memberAddress.getDistrictName()) || StringUtils.isEmpty(memberAddress.getDetailAddress())) {
            throw new BusinessException("收货地址有误，请重新维护收货地址～");
        }
        orderAddress.setReceiverName(memberAddress.getMemberName());
        orderAddress.setReceiverMobile(memberAddress.getTelMobile());
        orderAddress.setProvince(memberAddress.getProvinceName());
        orderAddress.setCity(memberAddress.getCityName());
        orderAddress.setCityCode(memberAddress.getCityCode());
        orderAddress.setProvinceCode(memberAddress.getProvinceCode());
        orderAddress.setDistrictCode(memberAddress.getDistrictCode());
        orderAddress.setTownCode(memberAddress.getTownCode());
        orderAddress.setDistrict(memberAddress.getDistrictName());
        orderAddress.setTown(memberAddress.getTownName());
        orderAddress.setDetailAddress(memberAddress.getDetailAddress());
        BizAssertUtil.isTrue(org.apache.commons.lang.StringUtils.isEmpty(orderAddress.getReceiverName()), "收件人姓名不能为空");
        return orderAddress;
    }

    /**
     * 查询自提点地址
     * @param pointId
     * @return
     */
    public OrderAddressDTO buildOrderAddressByPointId(Long pointId) {
        if (pointId == null || pointId == 0) {
            throw new BusinessException("请选择自提点");
        }
        OrderAddressDTO orderAddress = new OrderAddressDTO();

        // 自提地址
        SelfLiftingPointVo selfLiftingPoint = shopIntegration.getSelfLiftingPoint(pointId);
        if (selfLiftingPoint.getOpenFlag() != 1){
            throw new BusinessException("自提点已停用，请更换自提点后下单～");
        }
        if (StringUtils.isEmpty(selfLiftingPoint.getProvinceName()) || StringUtils.isEmpty(selfLiftingPoint.getCityName())
                || StringUtils.isEmpty(selfLiftingPoint.getAreaName()) || StringUtils.isEmpty(selfLiftingPoint.getAddress())) {
            throw new BusinessException("自提地址有误，请重新维护自提地址～");
        }
        orderAddress.setReceiverName(selfLiftingPoint.getContactName());
        orderAddress.setReceiverMobile(selfLiftingPoint.getContactPhone());
        orderAddress.setProvince(selfLiftingPoint.getProvinceName());
        orderAddress.setCity(selfLiftingPoint.getCityName());
        orderAddress.setCityCode(selfLiftingPoint.getCityCode());
        orderAddress.setDistrict(selfLiftingPoint.getAreaName());
        orderAddress.setTown("");
        orderAddress.setDetailAddress(selfLiftingPoint.getAddress());
        orderAddress.setPointId(selfLiftingPoint.getPointId());
        orderAddress.setPointName(selfLiftingPoint.getPointName());
        orderAddress.setWarehouseCode(selfLiftingPoint.getWarehouseCode());
        orderAddress.setSelfLiftingBranchCode(selfLiftingPoint.getBranchCode());
        orderAddress.setActualWarehouseCode(selfLiftingPoint.getActualWarehouseCode());
        return orderAddress;
    }

    /**
     * 是否可参与拼单规则：true-可以参与拼单 false-不可以参与拼单
     * 规则：店铺在白名单 & 非自提模式 & 客户有管护分支
     *
     * @return
     */
    public Boolean isInGroupBuyingRules(String performanceModes, Long storeId, UserBaseInfo userBaseInfo) {
        List<Integer> performanceModeList = JSONObject.parseArray(performanceModes, Integer.class);
        boolean isSelfLifting = performanceModeList
                .contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_SELF.getValue());
        if (isSelfLifting) {
            return false;
        }
        Boolean storeOnList = shopIntegration.isStoreOnList(WhiteListEnum.SPELLING_FULL_GIFT, storeId);
        if (!storeOnList) {
            return false;
        }
        return Objects.nonNull(userBaseInfo) && org.apache.commons.lang.StringUtils.isNotBlank(userBaseInfo.getBranchCode());
    }

    /**
     * 是否可开具自动发票
     * @param storeId storeId
     * @return
     */
    public Boolean isInvoiceAutoStoreRules(Long storeId) {
        return shopIntegration.isStoreOnList(WhiteListEnum.INVOICE_WHITE_LIST, storeId);
    }


    /**
     * 拼单标签校验
     */
    public void groupBuyingTagCheck(String userNo, Integer groupBuyingTag, OrderSubmitDTO orderSubmitDTO) {
        // 不参与拼单，直接返回
        if (OrderGroupBuyingEnum.WAITING != OrderGroupBuyingEnum.valueOf(groupBuyingTag)) {
            return;
        }

        if (orderSubmitDTO.getOrderInfoList().size() > 1) {
            throw new BusinessException("参与拼单不支持多店铺同时下单，请分店铺进行购买！");
        }

        // 查询是否可拼单
        OrderSubmitDTO.OrderInfo orderInfo = orderSubmitDTO.getOrderInfoList().get(0);
        UserBaseInfo userBaseInfo = orderSubmitAttributesUtils.getUserBaseInfo(userNo);
        Boolean inGroupBuyingRules = this.isInGroupBuyingRules(orderInfo.getPerformanceModes(), orderInfo.getStoreId(), userBaseInfo);
        if (!inGroupBuyingRules) {
            throw new BusinessException("该订单不允许参与拼单，请确认订单重新下单");
        }

        // 拼单参数设置
        for (OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo : orderInfo.getOrderProductInfoList()) {
            orderProductInfo.setGroupBuyingTag(OrderGroupBuyingEnum.WAITING.getValue());
        }
    }

	/**
	 * 内部员工下单校验
	 * 	1、补录订单 & 赠品订单 允许下单
	 * 	2、内部员工 & 在白名单内 允许下单
	 * 	3、内部员工 & 不在白名单内 & 订单命中规则引擎设置 不允许下单
	 * @param orderInfo
	 * @param orderPO
	 * @param orderExtendPO
	 */
	public void employeeTradeCheck(OrderSubmitDTO.OrderInfo orderInfo, OrderPO orderPO, OrderExtendPO orderExtendPO) {
		// 特殊订单不校验
		if (OrderTypeEnum.isOfflineAll(orderPO.getOrderType())
				|| OrderTypeEnum.GROUP_BUYING_GIFT == OrderTypeEnum.getValue(orderInfo.getOrderType())
                || OrderTypeEnum.REBATE_GIFT == OrderTypeEnum.getValue(orderInfo.getOrderType())) {
			return;
		}

		// 非内部员工，不校验
		if (!OrderUserIdentityEnum.EMPLOYEE.getValue().equals(orderPO.getUserIdentity())) {
			return;
		}

		// 员工白名单查询，在白名单内，通过
		JsonResult<Boolean> isInWhiteList = ExternalApiUtil.callJsonResultApi(() ->
						purchaserFeign.checkEmployeeIsInWhiteList(EmployeeWhiteListEnum.ALL_ORDER.getType(), orderExtendPO.getUserCode()),
				orderExtendPO.getUserCode(), "/checkEmployeeIsInWhiteList", "检查员工是否在白名单内");
		log.info("orderTradeCheck checkEmployeeIsInWhiteList result:{}",isInWhiteList.getData());
        if (isInWhiteList.getData()) {
            return;
        }


        // 按业务、员工汇总金额
        String startTime = employeeTradeConfig.getStartTime();
        if (StringUtils.isEmpty(startTime)) {
            // 获取当前年份1月1日 - 2024/03/29 修改
            LocalDate firstDayOfYear = LocalDate.now().withMonth(1).withDayOfMonth(1);
            // 转换为java.util.Date
            Date date = Date.from(firstDayOfYear.atStartOfDay(ZoneId.systemDefault()).toInstant());
            startTime = DateUtil.format(date,"yyyy-MM-dd HH:mm:ss");
        }
        BigDecimal employeeTotalAmount = orderMapper.employeeTotalAmount(orderPO.getRecommendStoreId(), orderPO.getUserMobile(), startTime);
        if (Objects.isNull(employeeTotalAmount)) {
            employeeTotalAmount = BigDecimal.ZERO;
        }
        log.info("orderTradeCheck orderSubmitRuleMatch userMobile:{},userCode:{},recommendStore:{},recommendStoreId:{},startTime:{},totalAmount:{}",
                orderPO.getUserMobile(), orderExtendPO.getUserCode(), orderPO.getRecommendStoreName(), orderPO.getRecommendStoreId(), startTime, employeeTotalAmount);
        // 加上本次下单金额
        employeeTotalAmount = employeeTotalAmount.add(orderPO.getOrderAmount().add(orderPO.getPlatformActivityAmount()).add(orderPO.getPlatformVoucherAmount()));

        // 改为单个子订单只处理一次
        // 订单匹配规则引擎
        List<OrderDataPushHitObject> hitObjects = new ArrayList<>();
        OrderSubmitRuleMatchDTO queryDTO = new OrderSubmitRuleMatchDTO();
        queryDTO.setStoreId(orderPO.getStoreId());
        queryDTO.setRecommendStoreId(orderPO.getRecommendStoreId());
        queryDTO.setOrderAmount(orderPO.getOrderAmount());
        queryDTO.setOrderPattern(orderPO.getOrderPattern());
        queryDTO.setChannel(orderPO.getChannel());
//        queryDTO.setIsVirtualGoods(orderProductInfo.getIsVirtualGoods());
        queryDTO.setPerformanceModes(orderPO.getPerformanceModes());
        queryDTO.setTotalAmount(employeeTotalAmount);
        List<OrderDataPushHitObject> hits = tradeDocumentIntegration.orderSubmitRuleMatch(queryDTO);
        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(hits)) {
            hitObjects.addAll(hits);
        }
//        for (OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo : orderInfo.getOrderProductInfoList()) {
//		}

		// 订单在限制规则内，提示不能下单
		if (!org.apache.commons.collections4.CollectionUtils.isEmpty(hitObjects)) {
			log.info("orderTradeCheck orderSubmitRuleMatch result:{}", JSONObject.toJSONString(hitObjects));
            String reason = hitObjects.get(0).getReason();
            if (StringUtils.isEmpty(reason)){
                reason = "内部员工暂不允许下单";
            }
            throw new BusinessException(ErrorCodeEnum.U.ACCESS_DENIED.getCode(), reason);
		}

	}

    public OrderSubmitParamDTO buildOrderSubmit(ChannelOrderSubmitDTO dto, boolean isChannelOrder) {
        OrderSubmitParamDTO dto1 = new OrderSubmitParamDTO();
        dto1.setChannel(dto.getChannel());
        dto1.setUsrNo(dto.getUserNo());
        dto1.setIsCart(false);
        dto1.setIsAloneBuy(false);
        dto1.setSource(3);
        dto1.setOrderFrom(1);
        dto1.setOrderAddress(dto.getAddress());
        dto1.setChannelOrder(isChannelOrder);
        return dto1;
    }

    public OrderSubmitParamDTO buildOrderSubmit(OrderOfflineParamDTO dto, String userNo, CustInfoVo custInfoVo) {
        OrderSubmitParamDTO dto1 = new OrderSubmitParamDTO();
        dto1.setChannel(dto.getChannel());
        dto1.setUsrNo(userNo);
        dto1.setIsCart(false);
        dto1.setIsAloneBuy(false);
        dto1.setSource(3);
        dto1.setOrderFrom(1);
        dto1.setOrderAddress(dto.getAddress());
        dto1.setChannelOrder(false);
        dto1.setOrderType(dto.getOrderType());
        dto1.setOrderPattern(dto.getOrderPattern());
//        dto.setAreaCode(employeeVO.getRegionCode());
//        dto.setAreaName(employeeVO.getRegionName());
        if (CustomerTypeEnum.ENTERPRISE_CUSTOMER.getCode().equals(dto.getCustomerType())) {
            // 经销员工作为客户经理，分支信息从前端销售分支获取
            dto.setBranchCode(dto.getSellBranchCode());
            dto.setManager(custInfoVo.getUserCode());
            dto.setManagerName(custInfoVo.getUserName());
        } else {
            dto.setBranchCode(custInfoVo.getBranchCode());
            dto.setBranchName(custInfoVo.getBranchName());
            dto.setManager(custInfoVo.getUserCode());
            dto.setManagerName(custInfoVo.getUserName());
        }
        return dto1;
    }



    public OrderSubmitMqConsumerDTO buildOrderSubmit(OrderSubmitParamDTO orderSubmitParam,OrderOfflineParamDTO dto,Member member,Long paySn) {
     	PreOrderDTO preOrderDTO=new PreOrderDTO();
    	preOrderDTO.setIsCalculateDiscount(false);
    	preOrderDTO.setOrderType(dto.getOrderType().getValue());
    	OrderSubmitMqConsumerDTO orderDTO = new OrderSubmitMqConsumerDTO();
    	orderDTO.setParamDTO(orderSubmitParam);
    	orderDTO.setMemberId(member.getMemberId());
    	orderDTO.setPaySn(paySn + "");
    	orderDTO.setPreOrderDTO(preOrderDTO);
    	orderDTO.setAreaCode(orderSubmitParam.getAreaCode());
    	orderDTO.setSkuInfoList(dto.getSkuInfoList());
    	orderDTO.setOrderType(dto.getOrderType());
    	orderDTO.setOrderOfflineParamDTO(dto);
        return orderDTO;
    }


    public long addOrderChangeEvent(OrderPO orderPO, OrderEventEnum changeType, Date eventTime){

        // 查出完整的订单数据，避免每个入口处理
        OrderPO order = orderModel.getAllOrderByOrderSnLambda(orderPO.getOrderSn());
        OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendByOrderSnLambda(orderPO.getOrderSn());

        return addOrderChangeEventWithOrderPoAndExtend(order,orderExtendPO,changeType,eventTime);
    }


    /**
     * 订单实体肯定存在的时候调用，无须再次查询
     *
     *
     * @param order 订单实体
     * @param changeType 变化类型
     * @param eventTime 事件时间
     */
//    @Async("addOrderChangeEventWithOrderPoAndExtend")
    public long addOrderChangeEventWithOrderPoAndExtend(OrderPO order,OrderExtendPO orderExtendPO,OrderEventEnum changeType, Date eventTime){

        // 消息入库
        OrderEventNotifyDTO orderEventNotifyDTO = new OrderEventNotifyDTO();
        orderEventNotifyDTO.setPaySn(order.getPaySn());
        orderEventNotifyDTO.setOrderSn(order.getOrderSn());
        orderEventNotifyDTO.setUserNo(order.getUserNo());
        orderEventNotifyDTO.setStoreId(order.getStoreId());
        if (OrderConst.STORE_TYPE_SELF_1.equals(orderExtendPO.getJindieTransFlag())) {
            orderEventNotifyDTO.setIsSelf(OrderConst.SEND_JINDIE_YES_1);
        }
        orderEventNotifyDTO.setEventType(changeType.getCode());
        orderEventNotifyDTO.setEventTime(eventTime);
        orderEventNotifyDTO.setEventTypeDesc(changeType.getDesc());
        orderEventNotifyDTO.setChannel(order.getChannel());
        orderEventNotifyDTO.setOrderPattern(order.getOrderPattern());
        orderEventNotifyDTO.setOrderType(order.getOrderType());
        long eventId = commonMqEventService.saveEvent(orderEventNotifyDTO, RabbitMqConfig.EXCHANGE_ORDER_CHANGE);

        // 发送消息
        sendOrderChangeEvent(eventId);

        return eventId;
    }


    public OrderSubmitParamDTO buildOrderSubmit(OrderParamDTO dto,String userNo) {
        OrderSubmitParamDTO dto1 = new OrderSubmitParamDTO();
        dto1.setChannel(dto.getChannel());
        dto1.setUsrNo(userNo);
        dto1.setIsCart(false);
        dto1.setIsAloneBuy(false);
        dto1.setSource(dto.getSource());
        dto1.setOrderFrom(1);
        // 协助下单支持自提订单, 保存收货地址
        OrderAddressDTO addressDTO = Objects.nonNull(dto.getPointId()) ? buildOrderAddressByPointId(dto.getPointId()) : dto.getAddress();
        dto1.setOrderAddress(addressDTO);
        dto1.setChannelOrder(false);
        dto1.setOrderPattern(dto.getOrderPattern());
        // 封装店铺信息 优惠/发票/留言
        List<OrderSubmitParamDTO.StoreInfo> storeInfoList = new ArrayList<>(dto.getStoreInfoList().size());
        dto.getStoreInfoList().forEach(store -> {
            OrderSubmitParamDTO.StoreInfo storeInfo = new OrderSubmitParamDTO.StoreInfo();
            storeInfo.setStoreId(store.getStoreId());
            storeInfo.setInvoiceId(store.getInvoiceId());
            storeInfo.setRemark(store.getRemark());
            storeInfo.setStoreCouponCodeList(store.getStoreCouponCodeList());
            storeInfoList.add(storeInfo);
        });
        dto1.setStoreInfoList(storeInfoList);
        dto1.setPlatformCouponCodeList(dto.getPlatformCouponCodeList());
        dto1.setVerifyCode(dto.getVerifyCode());
        // 保存预售信息
        dto1.setOrderPromotionInfo(dto.getOrderPromotionInfo());
        if (Objects.equals(dto.getOrderType(), OrderTypeEnum.PRE_SELL_DEPOSIT.getValue())) {
            // 预付订单只能一个店铺一个商品的下单
            BizAssertUtil.isTrue(CollectionUtils.isEmpty(dto.getStoreInfoList()) || CollectionUtils.isEmpty(dto.getStoreInfoList().get(0).getSkuInfoList()), "下单店铺信息和商品信息不能为空");

            dto1.setNumber(dto.getStoreInfoList().get(0).getSkuInfoList().get(0).getNumber());
            dto1.setProductId(dto.getStoreInfoList().get(0).getSkuInfoList().get(0).getProductId());
            dto1.setAreaCode(dto.getStoreInfoList().get(0).getAreaCode());
            dto1.setFinanceRuleCode(dto.getStoreInfoList().get(0).getFinanceRuleCode());
        }
        dto1.setOrderType(OrderTypeEnum.getValue(dto.getOrderType()));
        // 保存自提点id
        dto1.setPointId(dto.getPointId());

        return dto1;
    }

    public OrderSubmitMqConsumerDTO buildOrderSubmit(OrderSubmitParamDTO orderSubmitParam,OrderParamDTO dto,Member member,Long paySn,OrderPlaceUserDTO orderPlaceUserDTO) {
     	PreOrderDTO preOrderDTO=new PreOrderDTO();
    	preOrderDTO.setIsCalculateDiscount(true); // 代客下单支持优惠券
    	OrderSubmitMqConsumerDTO orderDTO = new OrderSubmitMqConsumerDTO();
    	orderDTO.setOrderType(OrderTypeEnum.getValue(dto.getOrderType())); // 代客下单支持预付订单类型
    	preOrderDTO.setOrderType(orderDTO.getOrderType().getValue());
    	orderDTO.setParamDTO(orderSubmitParam);
    	orderDTO.setMemberId(member.getMemberId());
    	orderDTO.setPaySn(paySn + "");
    	orderDTO.setPreOrderDTO(preOrderDTO);
        //orderDTO.setAreaCode(orderSubmitParam.getAreaCode());
    	//orderDTO.setSkuInfoList(dto.getSkuInfoList());
    	orderDTO.setOrderParamDTO(dto);
    	orderDTO.setSimulationShoppingCart(true);
    	orderDTO.setOrderPlaceUserDTO(orderPlaceUserDTO);
    	orderDTO.setOrderPlaceUserRole(dto.getOrderPlaceUserRole());
    	//orderDTO.setFinanceRuleCode(dto.getFinanceRuleCode());
        return orderDTO;
    }


    @GlobalTransactional(propagation = Propagation.REQUIRES_NEW)
    public long addOrderChangeEventWithNewTransaction(OrderPO orderPO, OrderEventEnum changeType, Date eventTime) {
        /**
         * MQ消息发送
         */
        OrderEventNotifyDTO orderEventNotifyDTO = new OrderEventNotifyDTO();
        orderEventNotifyDTO.setPaySn(orderPO.getPaySn());
        orderEventNotifyDTO.setOrderSn(orderPO.getOrderSn());
        orderEventNotifyDTO.setUserNo(orderPO.getUserNo());
        orderEventNotifyDTO.setStoreId(orderPO.getStoreId());
        orderEventNotifyDTO.setEventType(changeType.getCode());
        orderEventNotifyDTO.setEventTime(eventTime);
        orderEventNotifyDTO.setEventTypeDesc(changeType.getDesc());
        orderEventNotifyDTO.setOrderPattern(orderPO.getOrderPattern());

        long eventId = commonMqEventService.saveEvent(orderEventNotifyDTO, RabbitMqConfig.EXCHANGE_ORDER_CHANGE);

        /**
         * 事务提交后执行
         */
        sendOrderChangeEventWithNew(eventId);

        return eventId;
    }

    /**
     * 退款信息 退款信息
     *
     * @param orderSn
     * @param xzCardAmount
     * @param orderReturnPO 退款单信息
     * @param changeType    变更类型
     * @param eventTime     事件时间
     * @return 事件ID
     */
    public long addOrderReturnEvent(OrderPO orderSn, BigDecimal xzCardAmount, OrderReturnPO orderReturnPO, OrderEventEnum changeType, Date eventTime,String applySource) {

        OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendByOrderSnLambda(orderSn.getOrderSn());

        // 存储消息
        OrderRefundEventNotifyDTO orderReturnEventNotifyDTO = new OrderRefundEventNotifyDTO();
        orderReturnEventNotifyDTO.setOrderSn(orderReturnPO.getOrderSn());
        orderReturnEventNotifyDTO.setAfsSns(Collections.singletonList(orderReturnPO.getAfsSn()));
        orderReturnEventNotifyDTO.setUserNo(orderSn.getUserNo());
        orderReturnEventNotifyDTO.setAfsSn(orderReturnPO.getAfsSn());
        orderReturnEventNotifyDTO.setStoreId(orderReturnPO.getStoreId());
        if (OrderConst.STORE_TYPE_SELF_1.equals(orderExtendPO.getJindieTransFlag())) {
            orderReturnEventNotifyDTO.setIsSelf(OrderConst.SEND_JINDIE_YES_1);
        }
        orderReturnEventNotifyDTO.setEventType(changeType.getCode());
        orderReturnEventNotifyDTO.setEventTime(eventTime);
        orderReturnEventNotifyDTO.setEventTypeDesc(changeType.getDesc());
        orderReturnEventNotifyDTO.setPaySn(orderSn.getPaySn());
        orderReturnEventNotifyDTO.setXzCardAmount(xzCardAmount);
        orderReturnEventNotifyDTO.setChannel(orderSn.getChannel());
        orderReturnEventNotifyDTO.setApplySource(applySource);
        long eventId = commonMqEventService.saveEvent(orderReturnEventNotifyDTO, RabbitMqConfig.EXCHANGE_ORDER_REFUND_CHANGE);

        // 事务提交后执行
        sendOrderChangeEvent(eventId);

        return eventId;
    }


    public long sendMqEventMessageByTransactionCommit(Object body, String exchange){
        long eventId = commonMqEventService.saveEvent(body,exchange);
        // 发送消息
        sendOrderChangeEvent(eventId);
        return eventId;
    }


    private void sendOrderChangeEvent(long eventId) {

        // SEATA 事务
        GlobalTransaction globalTransaction = GlobalTransactionContext.getCurrent();
        // SPRING 事务
        String currentTransactionName = TransactionSynchronizationManager.getCurrentTransactionName();

        try {
            if (Objects.nonNull(globalTransaction)) {
                rabbitMQUtils.sendByEventIdAfterTxCommit(eventId, globalTransaction);
            } else if (Objects.nonNull(currentTransactionName)) {
                sendChangedEventByLocalTransaction(eventId);
            } else {
                rabbitMQUtils.sendByEventId(eventId);
            }
        } catch (Exception e) {
            log.error("sendOrderChangeEvent send message error，eventId: {}", eventId);
        }
    }

    private void sendOrderChangeEventWithNew(long eventId) {
        try {
            rabbitMQUtils.sendByEventId(eventId);
        } catch (Exception e) {
            log.error("发送消息异常，eventId【{}】", eventId);
        }
    }

    public void sendChangedEventByLocalTransaction(long eventId) {
        if (!TransactionSynchronizationManager.isSynchronizationActive()) {
            return;
        }
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            /*事务提交后*/
            @Override
            public void afterCommit() {
                log.info("SPRING事务提交后================================");
                rabbitMQUtils.sendByEventId(eventId);
            }
        });
    }

    /**
     * 订单发货，消息发送
     * */
    public long addOrderDelivery(String orderSn, List<Long> orderProductIds) {

        List<Long> productIds = new ArrayList<>();
        List<OrderProductPO> orderProductList;


        if(CollectionUtils.isEmpty(orderProductIds)){
            orderProductList = orderProductModel.getOrderProductListByOrderSn(orderSn);
        } else {
            orderProductList = orderProductModel.getOrderProductListByOrderProductIds(orderProductIds);
        }

        productIds = orderProductList.stream().map(x->x.getProductId()).collect(Collectors.toList());
        OrderDeliveryNotifyDTO orderDeliveryNotifyDTO = new OrderDeliveryNotifyDTO();
        orderDeliveryNotifyDTO.setOrderSn(orderSn);
        orderDeliveryNotifyDTO.setProductIds(productIds);

        OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendByOrderSnLambda(orderSn);
        //自营店铺推送金蝶
        if (OrderConst.STORE_TYPE_SELF_1.equals(orderExtendPO.getJindieTransFlag())) {
            orderDeliveryNotifyDTO.setIsSelf(OrderConst.SEND_JINDIE_YES_1);
        }

        // 消息入库
        long eventId = commonMqEventService.saveEvent(orderDeliveryNotifyDTO, RabbitMqConfig.EXCHANGE_ORDER_DELIVERY);

        log.info("addOrderDelivery evenId = {}", eventId);
        // 发送消息
        sendOrderChangeEvent(eventId);
        log.info("addOrderDelivery sendOrderChangeEvent end");

        return eventId;
    }


    /**
     * 商品出库，消息发送
     * */
    public void productOutbound(ErpPerformanceStock erpPerformanceStock) {

        // 消息入库
        long eventId = commonMqEventService.saveEvent(erpPerformanceStock, RabbitMqConfig.EXCHANGE_ERP_DELIVERY);

        log.info("productOutbound evenId = {}", eventId);
        // 发送消息
        sendOrderChangeEvent(eventId);
        log.info("productOutbound end");
    }

    /**
     * 是否可进行员工分享
     * 可分享人：内部员工
     * 可分享业务：普通商品下单、自提订单、组合商品订单、现款现货、预付活动
     * 不可分享：内购商品、其他活动类型
     *
     * @return
     */
    public Boolean isShareAbleV2(
            Integer userIdentity, Integer promotionType, List<OrderSubmitPageVO.StoreGroupVO> storeGroupList) {

        /**
         * step1、非内部员工，不可分享
         */
        if (!OrderUserIdentityEnum.EMPLOYEE.getValue().equals(userIdentity)) {
            return false;
        }

        /**
         * step2、判断商品类型是否可分享
         */
        boolean isShareProduct = true;

        for (OrderSubmitPageVO.StoreGroupVO storeGroupVO : storeGroupList) {
            for (OrderSubmitPageVO.StoreGroupVO.ProductVO productVO : storeGroupVO.getProductList()) {
                /** 判断是否为普通实物商品
                 组合订单、自提订单、现款现货 无需判断 ; 采购订单前端根据入口判断 */
                if (!OrderProductConst.VIRTUAL_GOODS_TYPE_1.equals(productVO.getIsVirtualGoods())) {
                    isShareProduct = false;
                }
            }
        }

        /**
         * step3、判断活动类型是否可分享
         */
        boolean isSharePromotion = true;

        HashSet<Integer> promotionTypeSet = new HashSet<>();
        promotionTypeSet.add(promotionType);

        for (OrderSubmitPageVO.StoreGroupVO storeGroupVO : storeGroupList) {
            for (OrderSubmitDTO.PromotionInfo promotionInfo : storeGroupVO.getPromotionInfoList()) {
                if (promotionInfo.getPromotionType() != null) {
                    promotionTypeSet.add(promotionInfo.getPromotionType());
                }
            }
        }

        List<Integer> alow = Arrays.asList(402, 107);
        for (Integer integer : promotionTypeSet) {
            if (!alow.contains(integer)){
                isSharePromotion = false;
                break;
            }
        }

        return isShareProduct && isSharePromotion;
    }

    /**
     * 判断是否内部员工
     * @param member
     * @return
     */
    public Integer getUserIdentity(Member member) {
        String interParam;
        UserBaseInfoVo userBaseInfo = customerIntegration.userBaseInfo(member.getUserNo());
        if (Objects.nonNull(userBaseInfo)
                && Objects.nonNull(userBaseInfo.getCustInfoVo())
                && !org.apache.commons.lang.StringUtils.isEmpty(userBaseInfo.getCustInfoVo().getIdNo())) {
            // 员工已实名，用身份证查询是否内部员工
            interParam = userBaseInfo.getCustInfoVo().getIdNo();
        } else {
            // 员工未实名，用手机号查询是否内部员工
            interParam = member.getMemberMobile();
        }
        Boolean isInter = ExternalApiUtil.callResultApi(() -> bmsUserFacade.isInter(interParam), interParam,
                "/user/isInter", "判断是否是内部员工");

        return (Objects.isNull(isInter) || !isInter) ? OrderUserIdentityEnum.CUSTOMER.getValue() : OrderUserIdentityEnum.EMPLOYEE.getValue();
    }

    /**
     * 是否可进行员工分享
     * 可分享人：内部员工
     * 可分享业务：普通商品下单、自提订单、组合商品订单、现款现货、预付活动
     * 不可分享：内购商品、其他活动类型
     *
     * @return
     */
    public Boolean isShareAble(Integer userIdentity, OrderSubmitDTO orderSubmitDTO) {
        // 非内部员工，不可分享
        if (!OrderUserIdentityEnum.EMPLOYEE.getValue().equals(userIdentity)) {
            return false;
        }

        // 商品信息
        List<OrderSubmitDTO.OrderInfo.OrderProductInfo> orderProductInfoList = new ArrayList<>();
        // 活动信息
        Set<Integer> promotionTypeList = new HashSet<>();
        for (OrderSubmitDTO.OrderInfo orderInfo : orderSubmitDTO.getOrderInfoList()) {
            // 订单商品info
            orderProductInfoList.addAll(orderInfo.getOrderProductInfoList());
            // 单品活动
            if(orderInfo.getPromotionType() != null && orderInfo.getPromotionType() != 0){
                promotionTypeList.add(orderInfo.getPromotionType());
            }
            // 平台店铺活动
            for (OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo : orderInfo.getOrderProductInfoList()) {
                if (orderProductInfo.getPromotionInfoList() != null) {
                    promotionTypeList.addAll(orderProductInfo.getPromotionInfoList().stream()
                            .map(OrderSubmitDTO.PromotionInfo::getPromotionType).collect(Collectors.toList()));
                }
            }
        }

        // 判断商品信息
        boolean isShareAbleProduct = true;
        log.info("shareOrderConfig:{},OrderSubmitDTO:{}", JSONObject.toJSONString(shareOrderConfig), JSONObject.toJSONString(orderSubmitDTO));
        Set<Integer> shareableGoodsType = shareOrderConfig.getShareableGoodsType();
        Set<Integer> shareableProductType = shareOrderConfig.getShareableProductType();
        for (OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo : orderProductInfoList) {
            // 普通商品 允许分享
            if (!shareableGoodsType.contains(orderProductInfo.getIsVirtualGoods())) {
                isShareAbleProduct = false;
                break;
            }
            if (!shareableProductType.contains(orderProductInfo.getProductType())) {
                isShareAbleProduct = false;
                break;
            }
        }

        // 预付活动
        boolean isShareAblePromotion = true;
        Set<Integer> shareablePromotionType = shareOrderConfig.getShareablePromotionType();
        for (Integer integer : promotionTypeList) {
            if (!shareablePromotionType.contains(integer)) {
                isShareAblePromotion = false;
                break;
            }
        }

        return isShareAbleProduct && isShareAblePromotion;
    }

    /**
     * 发送事务回滚mq
     *
     * @param transactionId 事务id
     * @return eventId
     */
    @Transactional(rollbackFor = Exception.class,transactionManager = "masterdbTx",propagation = org.springframework.transaction.annotation.Propagation.REQUIRES_NEW)
    public long addTransactionLogEvent(String transactionId, TransactionLogTypeEnum logTypeEnum,List<String> orderSnList){
        TransactionRollbackDTO rollbackDTO = new TransactionRollbackDTO();
        rollbackDTO.setTransactionId(transactionId);
        rollbackDTO.setLogType(logTypeEnum.getCode());
        rollbackDTO.setOrderSnList(orderSnList);
        long eventId = commonMqEventService.saveEvent(rollbackDTO, RabbitMqConfig.MALL_ORDER_TRANSACTION_EXCHANGE);

        // 发送消息
        sendOrderChangeEvent(eventId);

        return eventId;
    }

}
