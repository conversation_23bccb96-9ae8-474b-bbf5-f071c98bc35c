package com.cfpamf.ms.mallorder.common.enums;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 订单状态定义
 *
 * <AUTHOR>
 * @date 2021/6/19 17:16
 * @return
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum ManageTypeEnum implements IEnum<Integer> {
    UN_KNOW(0, "未知"),
    EMPLOYEE(1, "员工本人管护"),
    CUSTOMER(2, "客户信息管护"),
    STORE(3, "店铺所属分支"),
    FIX(4, "手工维护管护");

    Integer value;
    String desc;


    public static ManageTypeEnum valueOf(int value) {
        for (ManageTypeEnum ps : ManageTypeEnum.values()) {
            if (value == ps.value) {
                return ps;
            }
        }
        return UN_KNOW;
    }

}
