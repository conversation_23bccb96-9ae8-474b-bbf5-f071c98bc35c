package com.cfpamf.ms.mallorder.common.annotation;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 说明:
 * Poi导出注解。定义导出列的表头名称，列的顺序
 *
 * <AUTHOR>
 */

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface PoiExportProperty {

    // 定义字段导出名称
    String name() default "";

    // 定义字段排序
    int order() default 0;

    // 标注该字段在指定sheet页排除
    String exclusion() default "";

}
