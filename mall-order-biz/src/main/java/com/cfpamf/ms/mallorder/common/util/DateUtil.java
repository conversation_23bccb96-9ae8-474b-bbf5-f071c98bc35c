package com.cfpamf.ms.mallorder.common.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;

/**
 * 日期通用类
 *
 * @author: lingpeng
 * @date: 2020-07-20
 */
@Slf4j
public class DateUtil {

    public static final long ONE_DAY = 24 * 60 * 60 * 1000L;
    public static final long ONE_HOUR = 60 * 60 * 1000L;

    public static final String FORMAT_DATE = "yyyy-MM-dd";
    public static final String FORMAT_DATE_YM = "yyyyMM";
    public static final String FORMAT_DATE_YMD = "yyyyMMdd";
    public static final String FORMAT_DATE_BATCHNO = "yyyyMMddHHmmssSSS";
    public static final String FORMAT_TIME_YMDHM = "yyyy-MM-dd HH:mm";
    public static final String FORMAT_TIME = "yyyy-MM-dd HH:mm:ss";
    public static final String FORMAT_TIME_HH_MM = "HH:mm";
    public static final String FORMAT_FULL = "yyyy-MM-dd HH:mm:ss.S";
    public static final String FORMAT_FULL_T = "yyyy-MM-dd'T'HH:mm:ss.SSS";

    public static final String FORMAT_FULL_Z = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";
    public static final String EXPORT_FORMAT = "yyyy-MM-dd-HH-mm-ss";
    public static final String MAX_YEAR = "9999";

    /**
     * @param srcPattern 目标日期的格式
     * @param desPattern 要转化成的格式
     * @param strDate    字符串日期
     * @e.g. convertToStringPattern(" yyyyMMdd ", " yyyy / MM / dd ", " 20110909 ")
     * @result 2011/09/09
     */
    public static String convertToStringPattern(String srcPattern, String desPattern, String strDate) {
        if (ValidUtils.isEmpty(strDate)) {
            return null;
        }
        try {
            return format(new SimpleDateFormat(srcPattern).parse(strDate), desPattern);
        } catch (ParseException e) {
            log.error(e.toString());
        }
        return null;
    }

    /**
     * 格式化日期格式为:yyyy-MM-dd
     *
     * @param date
     * @return
     */
    public static String format(Date date) {
        return new SimpleDateFormat(FORMAT_DATE).format(date);
    }

    public static String format(Date date, String pattern) {

        return new SimpleDateFormat(pattern).format(date);
    }

    public static String format(LocalDate localDate, String pattern) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(pattern);
        return df.format(localDate);
    }

    public static String batchNo() {
        return new SimpleDateFormat(FORMAT_DATE_BATCHNO).format(getNow());
    }

    /**
     * 转换字符串日期为Date类型 格式为:yyyy-MM-dd
     *
     * @param date
     * @return
     */
    public static Date parse(String date) {
        Date d = null;
        try {
            d = new SimpleDateFormat(FORMAT_DATE).parse(date);
        } catch (Exception e) {
            log.error(e.toString());
        }
        return d;
    }


    public static Date parse(String date, String partten) {
        if (date == null) {
            return null;
        }
        Date d = null;
        if (ValidUtils.isEmpty(date)) {
            return d;
        }
        try {
            d = new SimpleDateFormat(partten).parse(date);
        } catch (Exception e) {
            log.error(e.toString());
        }
        return d;
    }

    public static Date parseEn(String date, String partten) {
        if (ValidUtils.isEmpty(date)) {
            return null;
        }

        Date dt = null;
        try {
            dt = new SimpleDateFormat(partten, Locale.ENGLISH).parse(date);
        } catch (Exception e) {
            log.error(e.toString());
        }
        return dt;
    }

    /**
     * 在字符串日期上进行日期的增减 格式为:yyyy-MM-dd 可用类型：YEAR、MONTH 和 DAY_OF_MONTH
     * create("2011-08-06", Calendar.DAY_OF_MONTH, -5)。 结果：2011-08-01
     *
     * @param date   格式为:yyyy-MM-dd
     * @param field  :Calendar.YEAR(年) Calendar.MONTH（月） Calendar.DAY_OF_MONTH（日）
     * @param amount
     * @return
     */
    public static String add(String date, int field, int amount) {
        return DateUtil.add(parse(date), field, amount);
    }

    public static String add(String date, String pattern, int field, int amount) {
        return DateUtil.add(parse(date, pattern), pattern, field, amount);
    }

    public static String add(Date date, String pattern, int field, int amount) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(field, amount);
        return new SimpleDateFormat(pattern).format(c.getTime());
    }

    /**
     * 对Date类型的日期进行增减
     * 根据日历的规则，为给定的日历字段添加或减去指定的时间量。例如，要从当前日历时间减去5天,可以通过调用以下方法做到这一点：
     * create(Calendar.DAY_OF_MONTH, -5)。
     *
     * @param date
     * @param field
     * @param amount
     * @return
     */
    public static String add(Date date, int field, int amount) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        c.add(field, amount);
        return new SimpleDateFormat(FORMAT_DATE).format(c.getTime());
    }

    /**
     * 对Date类型的日期进行增减
     * 根据日历的规则，为给定的日历字段添加或减去指定的时间量。例如，要从当前日历时间减去5天,可以通过调用以下方法做到这一点：
     * create(Calendar.DAY_OF_MONTH, -5)。
     *
     * @param date
     * @param field
     * @param amount
     * @return
     */
    public static Date addDate(Date date, int field, int amount) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        c.add(field, amount);
        return c.getTime();
    }

    /**
     * 在指定日期上增加多天
     *
     * @param date
     * @param day
     * @return
     */
    public static Date addDays(Date date, int day) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        c.add(Calendar.DAY_OF_MONTH, day);
        return c.getTime();
    }

    /**
     * 在指定日期上增加月份
     *
     * @param date
     * @param month
     * @return
     */
    public static Date addMonths(Date date, int month) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        c.add(Calendar.MONTH, month);
        return c.getTime();
    }

    /**
     * 对字符串类型的日期进行设置具体日期 可用类型：YEAR、MONTH 和 DAY_OF_MONTH
     * set("2011-08-06",Calendar.YEAR, 2000)。 结果：2000-08-06
     *
     * @param date  格式为:yyyy-MM-dd
     * @param field :Calendar.YEAR(年) Calendar.MONTH（月） Calendar.DAY_OF_MONTH（日）
     * @param value
     * @return
     */
    public static String set(String date, int field, int value) {
        return DateUtil.set(parse(date), field, value);
    }

    /**
     * 对Date类型的日期进行设置具体日期
     *
     * @param date
     * @param field
     * @param value
     * @return
     */
    public static String set(Date date, int field, int value) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(field, value);
        return new SimpleDateFormat(FORMAT_DATE).format(c.getTime());
    }

    /**
     * 对Date类型的日期进行设置具体日期
     *
     * @param date
     * @param field
     * @param value
     * @return
     */
    public static Date setDate(Date date, int field, int value) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(field, value);
        return c.getTime();
    }

    /**
     * 因为月返回的是从0开始的月份 所以直接返回int类型
     *
     * @param date
     * @param field
     * @return
     */
    public static int get(Date date, int field) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(field);
    }

    /**
     * @param date  格式为:yyyy-MM-dd
     * @param field :Calendar.YEAR(年) Calendar.MONTH（月） Calendar.DAY_OF_MONTH（日）
     * @return
     * @e.g. get(" 2011 - 08 - 06 ", Calendar.YEAR)
     * @result 2011
     */
    public static int get(String date, int field) {
        return DateUtil.get(parse(date), field);
    }

    /**
     * 获取当前时间，返回时间格式
     *
     * @return
     */
    public static Date getNow() {
        return new Date(System.currentTimeMillis());
    }

    /**
     * 获取当前时间
     *
     * @return 格式为:yyyy-MM-dd HH:mm:ss
     */
    public static String getNowTime() {
        return new SimpleDateFormat(FORMAT_TIME).format(new Date());
    }

    /**
     * 获取当前时分
     *
     * @return 格式为:  HH:mm
     */
    public static String getNowHHmm(){
        return new SimpleDateFormat(FORMAT_TIME_HH_MM).format(new Date());
    }

    /**
     * 获取当前时间
     *
     * @return 格式为:yyyy-MM-dd
     */
    public static String getNowDay() {
        return new SimpleDateFormat(FORMAT_DATE).format(new Date());
    }

    /**
     * 获取当前时间上个月 年月
     *
     * @return 格式为:yyyyMM 如 202006
     */
    public static int getNowYearAndMonth() {
        Date date = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, -1);
        String value = new SimpleDateFormat(FORMAT_DATE_YM).format(c.getTime());
        return Integer.valueOf(value).intValue();
    }

    /**
     * 获取当前时间上个月 年月月初
     *
     * @return 格式为:yyyy-MM-dd 如 2020-06-01
     */
    public static String getNowYearAndMonthStr() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date date = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH);
            Date resultDate = sdf.parse(year + "-" + month + "-01");
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            return dateFormat.format(resultDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 获得昨天的日期
     *
     * @param date
     * @return
     */
    public static String getYesterday(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DATE, -1);
        return new SimpleDateFormat(FORMAT_DATE).format(c.getTime());
    }

    public static String getYesterday(Date date, String partten) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DATE, -1);
        return new SimpleDateFormat(partten).format(c.getTime());
    }

    /**
     * 获得昨天的字符串日期
     *
     * @param date 格式为:yyyy-MM-dd
     * @return
     */
    public static String getYesterday(String date) {
        return DateUtil.getYesterday(parse(date));
    }

    /**
     * 获得昨天的字符串日期
     *
     * @param date 格式为:yyyy-MM-dd
     * @return
     */
    public static String getYesterdayEnd(String date) {
        return DateUtil.getYesterday(parse(date)) + " 23:59:59";
    }

    /**
     * 获取当天日期
     *
     * @return 格式为:yyyy-MM-dd
     */
    public static String getToday() {
        return new SimpleDateFormat(FORMAT_DATE).format(new Date());
    }

    /**
     * 获取当天日期直到最后一秒
     *
     * @return 格式为:yyyy-MM-dd
     */
    public static String getTodayEnd() {
        return (new SimpleDateFormat(FORMAT_DATE).format(new Date())) + " 23:59:59";
    }

    /**
     * 获得明天的字符串日期
     *
     * @param date 格式为:yyyy-MM-dd
     * @return
     */
    public static String getTomorrow(String date) {
        return DateUtil.getTomorrow(parse(date));
    }

    public static String getDateString(Date date, String format) {
        return new SimpleDateFormat(format).format(date);
    }

    /**
     * 获得明天的日期
     *
     * @param date
     * @return
     */
    public static String getTomorrow(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DATE, 1);
        return new SimpleDateFormat(FORMAT_DATE).format(c.getTime());
    }

    /**
     * @param from        开始日期
     * @param end         结束日期
     * @param includeForm 是否包括开始日期
     * @e.g. getDatesBetweenTwoDate(" 2011 - 07 - 03 ", " 2011 - 09 - 09 ", true);
     * @content 获取两个日期中的所有日期，includeForm为true包括开始日期，为false不包括开始日期，结束日期均包括
     */
    public static List<String> getDatesBetweenTwoDate(String from, String end, boolean includeForm) throws ParseException {
        List<String> list = new ArrayList<>();

        int a = getDaysBetweenTwoDate(from, end);

        if (a != -1) {
            Calendar c = Calendar.getInstance();
            c.setTime(new SimpleDateFormat(FORMAT_DATE).parse(from));

            if (includeForm) {
                list.add(from);
            }

            for (int i = 0; i < a; i++) {
                c.add(Calendar.DAY_OF_MONTH, 1);
                list.add(new SimpleDateFormat(FORMAT_DATE).format(c.getTime()));
            }
        }

        return list;
    }

    public static String getCurrentDateTime() {
        Date day = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(day);
    }

    /**
     * 获取当月的 天数
     */
    public static Integer getCurrentMonthDay() {
        Calendar a = Calendar.getInstance();
        a.set(Calendar.DATE, 1);
        a.roll(Calendar.DATE, -1);
        int maxDate = a.get(Calendar.DATE);
        return maxDate;
    }

    /**
     * 根据 年、月 获取对应的月份 的 天数
     */
    public static Integer getDaysByYearMonth(int year, int month) {
        Calendar a = Calendar.getInstance();
        a.set(Calendar.YEAR, year);
        a.set(Calendar.MONTH, month - 1);
        a.set(Calendar.DATE, 1);
        a.roll(Calendar.DATE, -1);
        int maxDate = a.get(Calendar.DATE);
        return maxDate;
    }

    /**
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return
     * @e.g. getDaysBetweenTwoDate(" 2011 - 09 - 09 ", " 2011 - 09 - 23 ")
     * @result 4
     * @content 算两个日期直接的差额，不包括开始日期，包括结束日期
     */
    public static int getDaysBetweenTwoDate(String startDate, String endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_DATE);

        try {
            long dateTime1 = sdf.parse(startDate).getTime();

            long dateTime2 = sdf.parse(endDate).getTime();

            if (dateTime2 < dateTime1) {
                return -1;
            }

            return (int) ((dateTime2 - dateTime1) / ONE_DAY);
        } catch (ParseException e) {
            log.error(e.toString());
            return -1;
        }
    }

    /**
     * 检查输入日期是否在指定的日期之间
     *
     * @param inputDate
     * @param startDate
     * @param endDate
     * @return
     */
    public static Boolean between(Date inputDate, Date startDate, Date endDate) {
        long dateInputTime = inputDate.getTime();
        long dateStartTime = startDate.getTime();
        long dateEndTime = endDate.getTime();
        return (dateInputTime >= dateStartTime) && (dateInputTime <= dateEndTime);

    }

    /**
     * 获取一周内的所有日期
     *
     * @param date
     * @return
     * @start 从哪天开始算一周的开始，0表示从周日开始，1表示周一，2表示周二，依次类推
     * @e.g. dateToWeek(new Date (), "yyyy/MM/dd",1,7);
     * @result 2011/08/22 到 2011/08/28
     */
    public static List<String> dateToWeek(Date date, String partten, int start, int weekLen) {
        int b = date.getDay();
        if (b == 0) {
            b = 7;
        }
        Date fdate;
        List<String> list = new ArrayList<String>();
        Long fTime = date.getTime() - (b - start) * 24 * 3600000;
        for (int a = 0; a < weekLen; a++) {
            fdate = new Date();
            fdate.setTime(fTime + (a * 24 * 3600000));
            list.add(a, format(fdate, partten));
        }
        return list;
    }

    /**
     * 得到本周周一
     *
     * @return yyyy-MM-dd
     */
    public static String getMondayOfThisWeek(String date, String partten, String desPartten) {
        Calendar c = Calendar.getInstance();
        c.setTime(parse(date, partten));
        int dayOfWeek = c.get(Calendar.DAY_OF_WEEK) - 1;
        if (dayOfWeek == 0) {
            dayOfWeek = 7;
        }
        c.add(Calendar.DATE, -dayOfWeek + 1);
        SimpleDateFormat sdf = new SimpleDateFormat(desPartten);
        return sdf.format(c.getTime());
    }

    /**
     * 得到本周周日
     *
     * @return yyyy-MM-dd
     */
    public static String getSundayOfThisWeek(String date, String partten, String desPartten) {
        Calendar c = Calendar.getInstance();
        c.setTime(parse(date, partten));
        int dayOfWeek = c.get(Calendar.DAY_OF_WEEK) - 1;
        if (dayOfWeek == 0) {
            dayOfWeek = 7;
        }
        c.add(Calendar.DATE, -dayOfWeek + 7);
        SimpleDateFormat sdf = new SimpleDateFormat(desPartten);
        return sdf.format(c.getTime());
    }

    /**
     * @param startTime 小
     * @param endTime   大
     * @param format
     * @param type
     * @return -1
     * @content 计算两个日期差
     * @e.g. dateDiff(" 2011 - 08 - 27 ", " 2011 - 08 - 28 ", " yyyy - MM - dd ", " day ")
     */
    public static int dateDiff(String startTime, String endTime, String format, String type) {
        //按照传入的格式生成一个simpledateformate对象
        SimpleDateFormat sd = new SimpleDateFormat(format);
        // 一天的毫秒数
        long nd = 1000 * 24 * 60 * 60L;
        // 一小时的毫秒数
        long nh = 1000 * 60 * 60L;
        // 一分钟的毫秒数
        long nm = 1000 * 60L;
        // 一秒钟的毫秒数
        long ns = 1000L;
        long diff;
        try {
            // 获得两个时间的毫秒时间差异
            diff = sd.parse(endTime).getTime() - sd.parse(startTime).getTime();
            // 计算差多少天
            long day = diff / nd;
            // 计算差多少小时
            long hour = diff % nd / nh;
            // 计算差多少分钟
            long min = diff % nd % nh / nm;
            // 计算差多少秒
            long sec = diff / ns;
            // 输出结果
            if ("day".equals(type)) {
                return (int) day;
            } else if ("hour".equals(type)) {
                return (int) hour;
            } else if ("min".equals(type)) {
                return (int) min;
            } else if ("sec".equals(type)) {
                return (int) sec;
            }
        } catch (ParseException e) {
            log.error(e.toString());
        }
        return 0;
    }

    /**
     * 获取两个日期string的天数
     *
     * @param dateStr1
     * @param dateStr2
     * @param format
     * @return
     */
    public static int days(String dateStr1, String dateStr2, String format) {
        SimpleDateFormat sd = new SimpleDateFormat(format);
        // 一天的毫秒数
        long nd = 1000 * 24 * 60 * 60L;
        try {
            long diff = sd.parse(dateStr1).getTime() - sd.parse(dateStr2).getTime();
            diff = Math.abs(diff);
            return (int) (diff / nd);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * @param startDate
     * @param endDate
     * @return
     */
    public static int getDaysBetweenTwoDate(Date startDate, Date endDate) {
        try {
            long dateTimeOne = startDate.getTime();
            long dateTimeTwo = endDate.getTime();
            return (int) ((dateTimeTwo - dateTimeOne) / ONE_DAY);
        } catch (Exception e) {
            log.error(e.toString());
            return -1;
        }
    }

    /**
     * 计算两个时间的日期差，不包括时分秒
     *
     * @param startDate     开始时间
     * @param endDate       结束时间
     * @return              日期差
     */
    public static int getDaysDiffBetweenTwoDateWithoutTime(Date startDate, Date endDate) {
        try {
            String startFormat = format(startDate, FORMAT_DATE);
            String endFormat = format(endDate, FORMAT_DATE);
            return getDaysBetweenTwoDate(startFormat, endFormat);
        } catch (Exception e) {
            log.error("getDaysDiffBetweenTwoDateWithoutHms error, startDate: {}, endDate: {}", startDate, endDate);
        }
        return -1;
    }

    /**
     * 获取两个时间之间的小时差，向上取整
     *
     * @param startDate     开始时间
     * @param endDate       结束时间
     * @return              小时差
     */
    public static int getUpHoursBetweenTwoDate(Date startDate, Date endDate) {
        try {
            long dateTimeOne = startDate.getTime();
            long dateTimeTwo = endDate.getTime();
            return (int) Math.ceil((double) (dateTimeTwo - dateTimeOne) / ONE_HOUR);
        } catch (Exception e) {
            log.error("getHoursBetweenTwoDate error, startDate is {}, endDate is {}", startDate, endDate, e);
            return -1;
        }
    }

    /**
     * @param from
     * @param end
     * @return
     */
    public static List<String> getDatesBetweenTwoDate(Date from, Date end, boolean includeForm) {
        List<String> list = new ArrayList<String>();

        int a = getDaysBetweenTwoDate(from, end);

        if (a != -1) {
            Calendar c = Calendar.getInstance();
            c.setTime(from);

            if (includeForm) {
                list.add(new SimpleDateFormat(FORMAT_DATE).format(from));
            }

            for (int i = 0; i < a; i++) {
                c.add(Calendar.DAY_OF_MONTH, 1);
                list.add(new SimpleDateFormat(FORMAT_DATE).format(c.getTime()));
            }
        }

        return list;
    }

    public static String getGasMonthStartDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        if (c.get(Calendar.DATE) < 27) {
            c.add(Calendar.MONTH, -1);
        }
        c.set(Calendar.DATE, 27);
        return new SimpleDateFormat(FORMAT_DATE).format(c.getTime());
    }

    public static String getGasYearStartDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        if (!(c.get(Calendar.MONTH) == 11 && c.get(Calendar.DATE) > 26)) {
            c.add(Calendar.YEAR, -1);
        }
        c.set(Calendar.MONTH, 11);
        c.set(Calendar.DATE, 27);
        return new SimpleDateFormat(FORMAT_DATE).format(c.getTime());
    }

    public static String getGasMonthEndDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        if (c.get(Calendar.DATE) > 26) {
            c.add(Calendar.MONTH, 1);
        }
        c.set(Calendar.DATE, 26);
        return new SimpleDateFormat(FORMAT_DATE).format(c.getTime());
    }

    public static String getGasYearEndDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        if (c.get(Calendar.MONTH) == 11 && c.get(Calendar.DATE) > 26) {
            c.add(Calendar.YEAR, 1);
        }
        c.set(Calendar.MONTH, 11);
        c.set(Calendar.DATE, 26);
        return new SimpleDateFormat(FORMAT_DATE).format(c.getTime());
    }

    public static String getSulfurMonthStartDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        if (c.get(Calendar.DATE) != c.getActualMaximum(Calendar.DATE)) {
            c.add(Calendar.MONTH, -1);
        }
        c.set(Calendar.DATE, c.getActualMaximum(Calendar.DATE));
        return new SimpleDateFormat(FORMAT_DATE).format(c.getTime());
    }

    public static String getSulfurYearStartDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        if (!(c.get(Calendar.MONTH) == 11 && c.get(Calendar.DATE) == 31)) {
            c.add(Calendar.YEAR, -1);
        }
        c.set(Calendar.MONTH, 11);
        c.set(Calendar.DATE, 31);
        return new SimpleDateFormat(FORMAT_DATE).format(c.getTime());
    }

    public static String getSulfurMonthEndDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        if (c.get(Calendar.DATE) == c.getActualMaximum(Calendar.DATE)) {
            c.add(Calendar.MONTH, 1);
        }
        c.set(Calendar.DATE, c.getActualMaximum(Calendar.DATE) - 1);
        return new SimpleDateFormat(FORMAT_DATE).format(c.getTime());
    }

    public static String getSulfurYearEndDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        if (c.get(Calendar.MONTH) == 11 && c.get(Calendar.DATE) == 31) {
            c.add(Calendar.YEAR, 1);
        }
        c.set(Calendar.MONTH, 11);
        c.set(Calendar.DATE, 30);
        return new SimpleDateFormat(FORMAT_DATE).format(c.getTime());
    }

    /**
     * 获取日期年份
     *
     * @param date 日期
     * @return
     */
    public static String getYear(Date date) {
        return format(date).substring(0, 4);
    }

    /**
     * 功能描述：返回月
     *
     * @param date Date 日期
     * @return 返回月份
     */
    public static int getMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 功能描述：返回日
     *
     * @param date Date 日期
     * @return 返回日份
     */
    public static int getDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 功能描述：返回小时
     *
     * @param date 日期
     * @return 返回小时
     */
    public static int getHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 功能描述：返回分钟
     *
     * @param date 日期
     * @return 返回分钟
     */
    public static int getMinute(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MINUTE);
    }

    /**
     * 返回秒钟
     *
     * @param date Date 日期
     * @return 返回秒钟
     */
    public static int getSecond(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.SECOND);
    }

    /**
     * 功能描述：返回毫秒
     *
     * @param date 日期
     * @return 返回毫
     */
    public static long getMillis(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getTimeInMillis();
    }

    /**
     * 比较两个时间格式的大小
     * 结束时间大于开始时间，返回true,否则返回false
     *
     * @param beginDateStr
     * @param endDateStr
     * @return
     */
    public static Boolean isEndDateGreaterBeginDate(String beginDateStr, String endDateStr) {
        Boolean result = false;
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date beginDate;
        Date endDate;
        try {
            beginDate = df.parse(beginDateStr);
            endDate = df.parse(endDateStr);
            long beginLong = beginDate.getTime();
            long endLong = endDate.getTime();
            if (endLong > beginLong) {
                result = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 比较两个时间格式的大小 按天
     * 结束时间小于开始时间，返回true,否则返回false
     *
     * @param beginDate
     * @param endDate
     * @return
     */
    public static Boolean isEndDateLessBeginDateByDay(Date beginDate, Date endDate) {
        Boolean result = false;
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        String strBeginDate = sf.format(beginDate);
        String strEndDate = sf.format(endDate);
        try {
            Date beginDateTime = sf.parse(strBeginDate);
            Date endDateTime = sf.parse(strEndDate);
            long beginLong = beginDateTime.getTime();
            long endLong = endDateTime.getTime();
            if (endLong < beginLong) {
                result = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 比较两个时间格式的大小
     * 结束时间小于开始时间，返回true,否则返回false
     *
     * @param beginDate
     * @param endDate
     * @return
     */
    public static Boolean isEndDateLessBeginDate(Date beginDate, Date endDate) {
        Boolean result = false;
        try {
            long beginLong = beginDate.getTime();
            long endLong = endDate.getTime();
            if (endLong < beginLong) {
                result = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 获取指定日期的月初
     * yyyy-MM-dd格式
     *
     * @param dataMonth yyyyMM
     * @return yyyy-mm-dd
     */
    public static String getBeginDayOfCertainDate(String dataMonth) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        try {
            Date date = sdf.parse(dataMonth + "01");
            //日历对象
            Calendar calendar = Calendar.getInstance();
            //设置日期
            calendar.setTime(date);
            Date nextMonthDate = calendar.getTime();
            SimpleDateFormat lastMonthFormat = new SimpleDateFormat("yyyy-MM-dd");
            return lastMonthFormat.format(nextMonthDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 获取指定日期的月末
     * yyyy-MM-dd格式
     *
     * @param dataMonth yyyyMM
     * @return yyyy-mm-dd
     */
    public static String getEndDayOfCertainMonth(String dataMonth) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        try {
            Date date = sdf.parse(dataMonth + "01");
            //日历对象
            Calendar calendar = Calendar.getInstance();
            //设置日期
            calendar.setTime(date);
            //加一个月
            calendar.add(Calendar.MONTH, 1);
            //减一天
            calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - 1);
            Date lastMonthDate = calendar.getTime();
            SimpleDateFormat lastMonthFormat = new SimpleDateFormat("yyyy-MM-dd");
            return lastMonthFormat.format(lastMonthDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static Long getTimestamp(String date, String format) throws Exception {
        Date d = null;
        try {
            d = new SimpleDateFormat(format).parse(date);
        } catch (Exception e) {
            log.error(e.toString());
            throw new Exception(String.format("日期格式不正确:%s", date));
        }
        return d.getTime();
    }

    /**
     * 判断时间是不是今天
     *
     * @param date
     * @return 是返回true，不是返回false
     */
    public static boolean isToday(Date date) {
        //当前时间
        Date now = new Date();
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");

        //获取今天的日期
        String nowDay = sf.format(now);

        //对比的时间
        String day = sf.format(date);

        return day.equals(nowDay);
    }

    /**
     * Date 转 LocalDate
     *
     * @param date
     * @return
     */
    public static LocalDate date2LocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * 日期的初始值
     *
     * @param date
     * @return
     */
    public static Date toDate(Date date) {
        String dateStr = new SimpleDateFormat(FORMAT_DATE).format(date);
        return parse(dateStr);
    }

    /**
     * 获取两个日期相差的月数
     *
     * @param d1
     * @param d2
     * @return
     */
    public static int getMonthDiff(Date d1, Date d2) {
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(d1);
        c2.setTime(d2);
        int year1 = c1.get(Calendar.YEAR);
        int year2 = c2.get(Calendar.YEAR);
        int month1 = c1.get(Calendar.MONTH);
        int month2 = c2.get(Calendar.MONTH);
        int day1 = c1.get(Calendar.DAY_OF_MONTH);
        int day2 = c2.get(Calendar.DAY_OF_MONTH);

        // 获取年的差值 
        int yearInterval = year1 - year2;
        // 如果 d1的 月-日 小于 d2的 月-日 那么 yearInterval-- 这样就得到了相差的年数
        if ((month1 < month2) || (month1 == month2 && day1 < day2)) {
            yearInterval--;
        }
        // 获取月数差值
        int monthInterval = (month1 + 12) - month2;
        if (day1 < day2) {
            monthInterval--;
        }
        monthInterval %= 12;

        int monthsDiff = Math.abs(yearInterval * 12 + monthInterval);

        return monthsDiff;

    }

    /**
     * 根据Date日期 返回 XXXX年XX月XX日
     *
     * @param date
     * @return
     */
    public static String getFormatDateToStr(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        StringBuilder sbd = new StringBuilder();
        sbd.append(year).append("年").append(month).append("月").append(day).append("日");
        return sbd.toString();
    }

    /**
     * 检查指定日期是否为极限日期
     *
     * @param date
     * @return
     */
    public static Boolean isMaxDate(Date date) {
        if (ValidUtils.isEmpty(date)) {
            return false;
        }

        if (format(date).indexOf(MAX_YEAR) != -1) {
            return true;
        }

        return false;
    }

    /**
     * 传入两个日期，返回月数
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static Integer getDistanceMonth(Date startTime, Date endTime) {
        Calendar bef = Calendar.getInstance();
        Calendar aft = Calendar.getInstance();
        bef.setTime(startTime);
        aft.setTime(endTime);
        int result = aft.get(Calendar.MONTH) - bef.get(Calendar.MONTH);
        int month = (aft.get(Calendar.YEAR) - bef.get(Calendar.YEAR)) * 12;
        return Math.abs(month + result);
    }

    /**
     * 获取日期时间 年-月-日
     *
     * @return 格式为:yyyy-MM-dd 如 2020-06-01
     */
    public static Date getTargetYearAndMonthStr(String day) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date date = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1;
            Date resultDate = sdf.parse(year + "-" + month + "-" + day);
            return resultDate;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取日期时间 年-月-日
     *
     * @return 格式为:yyyy-MM-dd 如 2020-06-01
     */
    public static Date getTargetNextYearAndMonthStr(String day) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date date = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 2;
            Date resultDate = sdf.parse(year + "-" + month + "-" + day);
            return resultDate;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * 把Date类型转为LocalDateTime类型
     * @param date
     * @return
     */
    public static LocalDateTime dateToLocalDateTime(Date date){
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
        return localDateTime;
    }
    
    /**
     * 把LocalDateTime转为Date类型
     * @param localDateTime
     * @return
     */
    public static Date localDateTimeToDate( LocalDateTime localDateTime){
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);
        Date date = Date.from(zdt.toInstant());
        return date;
    }

    /**
     * 两个时间相减
     * */
    public static long dealRemainTime(LocalDateTime localDateTime,LocalDateTime localDateTimeBefore) {
        //LocalDateTime nowTime = LocalDateTime.now();
        long remainTime = ChronoUnit.SECONDS.between(localDateTimeBefore, localDateTime);
        return remainTime < 0 ? 0 : remainTime;
    }

    /**
     * 两个时间相减
     * */
    public static long dealRemainTime(Date dateTime,Date dateBefore) {
        long time1 = dateTime.getTime();
        //long time2 = System.currentTimeMillis();
        long time2 = dateBefore.getTime();
        long remainTime = (time1 - time2) / 1000;
        return remainTime < 0 ? 0 : remainTime;
    }


    /**
     * 当前日期加天数
     * */
    public static Date addDate(Date date,  long day) {
        // 得到指定日期的毫秒数
        long time = date.getTime();
        // 要加上的天数转换成毫秒数
        day = day * 24 * 60 * 60 * 1000;
        // 相加得到新的毫秒数
        time += day;
        // 将毫秒数转换成日期
        return new Date(time);
    }

    /**
     * 获取当前时间前几小时
     * @param hour
     * @return
     */
    public static Date beforeHours(long hour){
        //获取当前时间
        Date currentDate = new Date();
        //将当前时间转换为毫秒数
        long currentTimeMillis = currentDate.getTime();
        //减去一小时的毫秒数
        long oneHourMillis = TimeUnit.HOURS.toMillis(hour);
        long previousTimeMillis = currentTimeMillis - oneHourMillis;
        //将减去一小时后的毫秒数转换为日期
        Date previousDate = new Date(previousTimeMillis);
        return previousDate;
    }
}