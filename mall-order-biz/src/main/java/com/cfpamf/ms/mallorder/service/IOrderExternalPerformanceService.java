package com.cfpamf.ms.mallorder.service;


import com.cfpamf.ms.mallorder.dto.OrderPerformanceDeliveryDTO;
import com.cfpamf.ms.mallorder.dto.OrderPerformanceReceiptDTO;
import com.cfpamf.ms.mallorder.integration.facade.dto.ErpDepotVO;

import java.util.List;

public interface IOrderExternalPerformanceService {

    /**
     * 供应商发货接口对接
     * 1、根据快递单号查询快递公司
     * 2、更新订单状态
     * 3、插入发货记录
     * 4、产品发货信息记录
     * 5、下发MQ
     * 6、记录日志轨迹
     *
     * @param orderPerformanceDeliveryDTO
     */
    void extDeliver(OrderPerformanceDeliveryDTO orderPerformanceDeliveryDTO);


    boolean extReceipt(OrderPerformanceReceiptDTO orderPerformanceReceiptDTO);

    List<ErpDepotVO> getDepotListWithResult(Long storeId, List<String> skuIdList);

}
