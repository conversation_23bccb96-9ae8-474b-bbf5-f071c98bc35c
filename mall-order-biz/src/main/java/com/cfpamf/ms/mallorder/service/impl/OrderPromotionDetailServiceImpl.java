package com.cfpamf.ms.mallorder.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.mapper.OrderPromotionDetailMapper;
import com.cfpamf.ms.mallorder.po.OrderPromotionDetailPO;
import com.cfpamf.ms.mallorder.service.IOrderPromotionDetailService;
import com.slodon.bbc.core.constant.PromotionConst;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class OrderPromotionDetailServiceImpl extends ServiceImpl<OrderPromotionDetailMapper, OrderPromotionDetailPO> implements IOrderPromotionDetailService {

    @Autowired
    private OrderPromotionDetailMapper orderPromotionDetailMapper;

    /**
     * 根据订单编号获取券使用的优惠券编号列表
     *
     * @param orderSn 订单编号
     * @return 优惠券使用列表
     */
    @Override
    public List<String> listCouponNoByOrderSn(String orderSn) {

        if (ObjectUtils.isEmpty(orderSn)) {
            Collections.emptyList();
        }

        LambdaQueryWrapper<OrderPromotionDetailPO> promotionDetailQuery = Wrappers.lambdaQuery(OrderPromotionDetailPO.class);
        promotionDetailQuery.eq(OrderPromotionDetailPO::getOrderSn, orderSn);
        promotionDetailQuery.eq(OrderPromotionDetailPO::getPromotionType, PromotionConst.PROMOTION_TYPE_402);
        promotionDetailQuery.eq(OrderPromotionDetailPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        List<OrderPromotionDetailPO> promotionDetailPOS = list(promotionDetailQuery);

        if (CollectionUtils.isEmpty(promotionDetailPOS)) {
            Collections.emptyList();
        }

        List<String> result = new ArrayList<>(promotionDetailPOS.size());

        promotionDetailPOS.forEach(item -> {
            result.add(item.getPromotionId());
        });

        return result;
    }

    @Override
    public List<OrderPromotionDetailPO> getOrderPromotionDetailList(Set<String> orderSns, Integer isStore) {
        if (CollectionUtils.isEmpty(orderSns)) {
            return null;
        }
        if (ObjectUtils.isEmpty(isStore)) {
            return null;
        }
        LambdaQueryWrapper<OrderPromotionDetailPO> promotionDetailQuery = Wrappers.lambdaQuery(OrderPromotionDetailPO.class);
        promotionDetailQuery.in(OrderPromotionDetailPO::getOrderSn, orderSns);
        promotionDetailQuery.eq(OrderPromotionDetailPO::getPromotionType, PromotionConst.PROMOTION_TYPE_402);
        promotionDetailQuery.eq(OrderPromotionDetailPO::getIsStore, isStore); //店铺营销优惠、平台营销优惠
        promotionDetailQuery.eq(OrderPromotionDetailPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return list(promotionDetailQuery);
//        QueryWrapper<OrderPromotionDetailPO> queryWrapper = new QueryWrapper<>();
//        queryWrapper.in("order_sn", orderSns);
//        queryWrapper.eq("promotion_type", PromotionConst.PROMOTION_TYPE_402);
//        queryWrapper.eq("is_store", isStore);//店铺营销优惠、平台营销优惠
//        queryWrapper.eq("enabled_flag", OrderConst.ENABLED_FLAG_Y);
//        return orderPromotionDetailMapper.selectList(queryWrapper);
    }

    /**
     * 根据订单号查询对应的订单促销优惠内容
     *
     * @param orderSn 订单编号
     * @return 订单优惠内容
     */
    @Override
    public List<OrderPromotionDetailPO> getOrderPromotionDetailByOrderSn(String orderSn) {
        LambdaQueryWrapper<OrderPromotionDetailPO> promotionDetailQuery = Wrappers.lambdaQuery(OrderPromotionDetailPO.class);
        promotionDetailQuery.eq(OrderPromotionDetailPO::getOrderSn, orderSn);
        promotionDetailQuery.eq(OrderPromotionDetailPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return list(promotionDetailQuery);
//        QueryWrapper<OrderPromotionDetailPO> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("order_sn", orderSn);
//        queryWrapper.eq("enabled_flag", OrderConst.ENABLED_FLAG_Y);
//        return orderPromotionDetailMapper.selectList(queryWrapper);
    }

    /**
     * 根据订单号查询对应的订单促销优惠内容
     *
     * @param orderSnList 订单编号列表
     * @return 订单优惠内容
     */
    @Override
    public List<OrderPromotionDetailPO> getOrderPromotionDetailByOrderSnList(List<String> orderSnList) {
        LambdaQueryWrapper<OrderPromotionDetailPO> promotionDetailQuery = Wrappers.lambdaQuery(OrderPromotionDetailPO.class);
        promotionDetailQuery.in(OrderPromotionDetailPO::getOrderSn, orderSnList);
        promotionDetailQuery.eq(OrderPromotionDetailPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return list(promotionDetailQuery);
//        QueryWrapper<OrderPromotionDetailPO> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("order_sn", orderSnList);
//        queryWrapper.eq("enabled_flag", OrderConst.ENABLED_FLAG_Y);
//        return orderPromotionDetailMapper.selectList(queryWrapper);
    }
}
