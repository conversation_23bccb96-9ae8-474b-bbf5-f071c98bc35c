package com.cfpamf.ms.mallorder.controller.seller;


import com.cfpamf.ms.mall.filecenter.domain.dto.FileScenesMaterialProofDTO;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesProofVO;
import com.cfpamf.ms.mallorder.dto.OrderTradeProofQueryDTO;
import com.cfpamf.ms.mallorder.service.IOrderTradeProofService;
import com.cfpamf.ms.mallorder.vo.OrderTradeProofVO;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.SldResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 订单交易凭证表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@RestController
@Api(tags = "seller-订单交易凭证操作")
@RequestMapping("seller/orderTradeProof")
public class SellerOrderTradeProofController {

	@Autowired
	private IOrderTradeProofService orderTradeProofService;

	@ApiOperation(value = "保存订单凭证资料", tags = "CORE")
	@PostMapping("/saveFileMaterial")
	public JsonResult<Boolean> saveFileMaterial(@RequestBody @Valid @NotNull FileScenesMaterialProofDTO paramDTO) {

		return SldResponse.success(orderTradeProofService.saveScenesMaterial(paramDTO));

	}

	@ApiOperation("查询文件中心凭证资料")
	@GetMapping("/queryFileMaterial")
	public JsonResult<List<FileScenesProofVO>> queryFileMaterial(@RequestParam(value = "proofNo", required = true) String proofNo,
																 @RequestParam(value = "subProofNo", required = false) String subProofNo,
																 @RequestParam(value = "sceneNo", required = false) String sceneNo) {

		return SldResponse.success(orderTradeProofService.queryScenesMaterial(proofNo, subProofNo, sceneNo, true));
	}

	@ApiOperation("匹配订单规则资料列表")
	@PostMapping("/matchSceneMaterials")
	public JsonResult<List<OrderTradeProofVO>> matchSceneMaterials(@RequestBody @NotNull OrderTradeProofQueryDTO queryDTO) {

		return SldResponse.success(orderTradeProofService.matchSceneMaterials(queryDTO));
	}
}
