package com.cfpamf.ms.mallorder.common.util;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.mallgoods.facade.api.GoodsPromotionFeignClient;
import com.cfpamf.ms.mallgoods.facade.request.GoodsPromotionExample;
import com.cfpamf.ms.mallgoods.facade.vo.GoodsPromotion;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallorder.common.constant.SentenceConst;
import com.cfpamf.ms.mallorder.dto.OrderSubmitDTO;
import com.cfpamf.ms.mallorder.dto.OrderSubmitParamDTO;
import com.cfpamf.ms.mallorder.dto.PreOrderDTO;
import com.cfpamf.ms.mallorder.v2.manager.PromotionCommonManager;
import com.cfpamf.ms.mallpromotion.api.PromotionCommonFeignClient;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.cfpamf.ms.mallpromotion.request.ProductPromotionDetailDTO;
import com.cfpamf.ms.mallpromotion.vo.ProductPromotionDetailVO;
import com.cfpamf.ms.mallpromotion.vo.prepay.ProductPrepayVO;
import com.slodon.bbc.core.constant.RedisConst;
import com.slodon.bbc.core.constant.ResponseConst;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PromotionUtils {

	@Autowired
	private PromotionCommonFeignClient promotionCommonFeignClient;

	@Autowired
	private StringRedisTemplate stringRedisTemplate;

	@Autowired
	private PromotionCommonManager promotionCommonManager;

	@Autowired
	private GoodsPromotionFeignClient goodsPromotionFeignClient;

	/**
	 * 获取当前货品参与的单品活动
	 *
	 * @param productId
	 * @return
	 */
	public GoodsPromotion getSinglePromotion(Long productId) {
		//查询商品级别的活动
		GoodsPromotionExample example = new GoodsPromotionExample();
		example.setIsEffective(com.slodon.bbc.core.constant.PromotionConst.IS_EFFECTIVE_YES);
		example.setProductId(productId);
		example.setStartTimeBefore(DateUtil.getNow());
		example.setEndTimeAfter(DateUtil.getNow());
		List<GoodsPromotion> singlePromotionList = goodsPromotionFeignClient.getGoodsPromotionList(example);
		log.info("getSinglePromotion.singlePromotionList:{}", JSON.toJSONString(singlePromotionList));
		if (CollectionUtils.isEmpty(singlePromotionList)) {
			return null;
		}
		boolean existFullGift = singlePromotionList.stream().anyMatch(x -> x.getPromotionType() != null && x.getPromotionType().equals(PromotionConst.PROMOTION_TYPE_205));
		log.info("getSinglePromotion.existFullGift:{}", existFullGift);
		//非满赠活动，增加绑定类型为商品过滤条件
		if (!existFullGift) {
			singlePromotionList = singlePromotionList.stream().filter(x -> x.getBindType().equals(1)).collect(Collectors.toList());
		} else {
			singlePromotionList = singlePromotionList.stream().filter(x -> x.getPromotionType().equals(PromotionConst.PROMOTION_TYPE_205)).collect(Collectors.toList());
		}
		if (CollectionUtils.isEmpty(singlePromotionList)) {
			return null;
		}
		log.info("getSinglePromotion.finalSinglePromotionList:{}", JSON.toJSONString(singlePromotionList));
		GoodsPromotion goodsPromotion = singlePromotionList.get(0);
		String promotionId = "";
		if (goodsPromotion.getPromotionType() == com.slodon.bbc.core.constant.PromotionConst.PROMOTION_TYPE_104) {
			//秒杀活动传货品id
			promotionId = goodsPromotion.getProductId().toString();
		} else {
			promotionId = goodsPromotion.getPromotionId().toString();
		}

		if (promotionCommonFeignClient.isPromotionAvailable(goodsPromotion.getPromotionType(), promotionId)) {
			//活动可用
			return goodsPromotion;
		}
		//无可用的单品活动
		return null;
	}

	/**
	 * 获取当前货品可用的活动列表（店铺活动、平台活动）
	 *
	 * @param product
	 * @return
	 */
	public List<GoodsPromotion> getPromotionList(Product product) {
		List<GoodsPromotion> list = new ArrayList<>();
		//查询店铺活动
		GoodsPromotionExample example = new GoodsPromotionExample();
		example.setBindType(com.slodon.bbc.core.constant.PromotionConst.BIND_TYPE_2);
		example.setIsEffective(com.slodon.bbc.core.constant.PromotionConst.IS_EFFECTIVE_YES);
		example.setStoreId(product.getStoreId());
		example.setStartTimeBefore(DateUtil.getNow());
		example.setEndTimeAfter(DateUtil.getNow());
		List<GoodsPromotion> storeList = goodsPromotionFeignClient.getGoodsPromotionList(example);
		if (!CollectionUtils.isEmpty(storeList)) {
			storeList.forEach(goodsPromotion -> {

				if (promotionCommonFeignClient.isPromotionAvailable(goodsPromotion.getPromotionType(), goodsPromotion.getPromotionId().toString())) {
					//活动可用
					list.add(goodsPromotion);
				}
			});
		}

		//查询平台活动
		example.setStoreId(null);
		example.setBindType(com.slodon.bbc.core.constant.PromotionConst.BIND_TYPE_3);
		example.setGoodsCategoryId3(product.getCategoryId3());
		example.setStartTimeBefore(DateUtil.getNow());
		example.setEndTimeAfter(DateUtil.getNow());

		List<GoodsPromotion> platformList = goodsPromotionFeignClient.getGoodsPromotionList(example);
		if (!CollectionUtils.isEmpty(platformList)) {
			platformList.forEach(goodsPromotion -> {

				if (promotionCommonFeignClient.isPromotionAvailable(goodsPromotion.getPromotionType(), goodsPromotion.getPromotionId().toString())) {
					//活动可用
					list.add(goodsPromotion);
				}
			});
		}
		return list;
	}


	/**
	 * 根据productId获取 商品活动信息，从map中取商品或者读取数据库
	 *
	 * @return
	 */
	public List<GoodsPromotion> getPromotionListByProductId(Map<Long, List<GoodsPromotion>> goodsPromotionMap, Product product) {
		if (Objects.isNull(goodsPromotionMap)) {
			goodsPromotionMap = new HashMap<>();
		}

		if (goodsPromotionMap.containsKey(product.getProductId())) {
			return goodsPromotionMap.get(product.getProductId());
		}

		List<GoodsPromotion> promotionList = this.getPromotionList(product);
		goodsPromotionMap.put(product.getProductId(), promotionList);
		return promotionList;
	}

	/**
	 * 单品活动下单：对活动进行预校验
	 *
	 * @param singlePromotion
	 * @param productId
	 * @param number
	 * @param memberId
	 * @return
	 */
	public PreOrderDTO preCheckSubmit(GoodsPromotion singlePromotion, Long productId, Integer number, Integer memberId) {
		JsonResult<PreOrderDTO> result =
				promotionCommonFeignClient.preOrderSubmit(singlePromotion.getPromotionType(),
						singlePromotion.getPromotionId(), productId, number, memberId);
		if (result.getState() != ResponseConst.STATE_SUCCESS) {
			// 2024-03-12 告警降噪
			throw new BusinessException(result.getMsg());
		}
		PreOrderDTO preOrderDTO = result.getData();
		preOrderDTO.setProductId(productId);
		return preOrderDTO;
	}

	/**
	 * 单品活动下单失败：活动库存回退
	 *
	 * @param preOrderDTO
	 * @param memberId
	 * @param number
	 */
	public void deductionStockNumber(PreOrderDTO preOrderDTO, Integer memberId, Integer number) {
		if (preOrderDTO.getOrderType().equals(PromotionConst.PROMOTION_TYPE_102)) {
			//拼团订单保存异常，将redis中的购买数量减回去
			stringRedisTemplate.opsForValue().decrement(RedisConst.SPELL_PURCHASED_NUM_PREFIX
					+ preOrderDTO.getGoodsId() + "_" + memberId, number);
		}
		if (preOrderDTO.getOrderType().equals(PromotionConst.PROMOTION_TYPE_103)) {
			//预售订单保存异常，将redis中的购买数量减回去
			stringRedisTemplate.opsForValue().decrement(RedisConst.PRE_SELL_PURCHASED_NUM_PREFIX
					+ preOrderDTO.getGoodsId() + "_" + +memberId, number);
		}
		if (preOrderDTO.getOrderType().equals(PromotionConst.PROMOTION_TYPE_104)) {
			//秒杀订单保存异常，将redis中的库存加回去
			stringRedisTemplate.opsForValue().increment(RedisConst.REDIS_SECKILL_PRODUCT_STOCK_PREFIX
					+ preOrderDTO.getProductId(), number);
		}
		if (preOrderDTO.getOrderType().equals(PromotionConst.PROMOTION_TYPE_105)) {
			//阶梯团订单保存异常，将redis中的购买数量减回去
			stringRedisTemplate.opsForValue().decrement(RedisConst.LADDER_GROUP_PURCHASED_NUM_PREFIX
					+ preOrderDTO.getGoodsId() + "_" + +memberId, number);
		}
	}

	/**
	 * 活动类型、活动ID赋值
	 * @param singlePromotion
	 * @param submitParam
	 * @param orderSubmitDTO
	 */
	public void promotionParamSet(GoodsPromotion singlePromotion, OrderSubmitParamDTO submitParam, OrderSubmitDTO orderSubmitDTO) {
		Integer singlePromotionType = singlePromotion.getPromotionType();
		Integer singlePromotionId = singlePromotion.getPromotionId();
		//单品活动下单
		for (OrderSubmitDTO.OrderInfo orderInfo : orderSubmitDTO.getOrderInfoList()) {
			orderInfo.setPromotionType(singlePromotionType);
			orderInfo.setPromotionId(singlePromotionId);
			if (promotionCommonFeignClient.specialOrder(singlePromotionType)) {
				//需要订单扩展信息
				orderInfo.setOrderType(singlePromotionType);
			}

			if (singlePromotionType == com.cfpamf.ms.mallpromotion.constant.PromotionConst.PROMOTION_TYPE_107) {
				ProductPrepayVO productPrepayVO = prePayDeadTime(singlePromotion, submitParam);
				LocalDateTime depositDeadTime = LocalDateTime.now().plusMinutes(productPrepayVO.getOrderCancelMinutes());
				LocalDateTime balanceDeadTime = LocalDateTime.now().plusDays(productPrepayVO.getFinalPayLimitDays());

				this.buildPresellOrderInfo(orderInfo, submitParam, depositDeadTime, balanceDeadTime);
				orderSubmitDTO.setSinglePromotionType(com.cfpamf.ms.mallpromotion.constant.PromotionConst.PROMOTION_TYPE_107);
			}

			for (OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo : orderInfo.getOrderProductInfoList()) {
				orderProductInfo.setSinglePromotionType(singlePromotionType);
				//只有205（满赠订单）才在商品行传值
				if (singlePromotionType.equals(PromotionConst.PROMOTION_TYPE_205)) {
					orderProductInfo.setPromotionId(singlePromotionId);
					orderProductInfo.setPromotionType(singlePromotionType);
				}
				if (!StringUtil.isNullOrZero(submitParam.getSpellTeamId())) {
					orderProductInfo.setSpellTeamId(submitParam.getSpellTeamId());
				}
			}
		}
	}

	/**
	 * 补充预售信息
	 *
	 * @param orderInfo       传输的订单信息
	 * @param dto             前端传输的对象
	 * @param depositDeadTime 定金截止时间
	 * @param balanceDeadTime 尾款截止时间
	 */
	private void buildPresellOrderInfo(OrderSubmitDTO.OrderInfo orderInfo, OrderSubmitParamDTO dto,
									   LocalDateTime depositDeadTime, LocalDateTime balanceDeadTime) {
		orderInfo.setDepositDeadTime(depositDeadTime);
		orderInfo.setBalanceDeadTime(balanceDeadTime);
		orderInfo.setBalance(dto.getOrderPromotionInfo().getTotalRemainAmount());
		orderInfo.setDeposit(dto.getOrderPromotionInfo().getTotalDeposit());
		for (OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo : orderInfo.getOrderProductInfoList()) {
			orderProductInfo.setDeposit(dto.getOrderPromotionInfo().getSingleDeposit());
			orderProductInfo.setBalance(dto.getOrderPromotionInfo().getSingleRemainAmount());
		}
	}

	/**
	 * 预付活动截止时间
	 *
	 * @param singlePromotion
	 * @param submitParam
	 */
	public ProductPrepayVO prePayDeadTime(GoodsPromotion singlePromotion, OrderSubmitParamDTO submitParam) {
		ProductPromotionDetailDTO productPromotionDetailDTO = new ProductPromotionDetailDTO();
		productPromotionDetailDTO.setPromotionType(singlePromotion.getPromotionType());
		productPromotionDetailDTO.setPromotionId(singlePromotion.getPromotionId().toString());
		productPromotionDetailDTO.setProductId(submitParam.getProductId());
		productPromotionDetailDTO.setAreaCode(submitParam.getAreaCode());
		productPromotionDetailDTO.setSubscription(submitParam.getOrderPromotionInfo().getSingleDeposit());
		productPromotionDetailDTO.setFinanceRuleCode(submitParam.getFinanceRuleCode());
		List<ProductPromotionDetailVO> productPromotionDetailBatch = promotionCommonManager.getProductPromotionDetailBatch(Collections.singletonList(productPromotionDetailDTO));
		for (ProductPromotionDetailVO productPromotionDetailVO : productPromotionDetailBatch) {
			if (submitParam.getProductId().equals(productPromotionDetailVO.getProductId())) {
				ProductPrepayVO prepayVO = productPromotionDetailVO.getPrepayVO();

				// 校验前端传入的金额信息与后端计算的金额信息是否一致
				BigDecimal totalQuantity = new BigDecimal(submitParam.getNumber());
				BigDecimal totalDeposit = submitParam.getOrderPromotionInfo().getSingleDeposit().multiply(totalQuantity);
				BigDecimal totalRemainAmount = submitParam.getOrderPromotionInfo().getSingleRemainAmount().multiply(totalQuantity);
				if (totalDeposit.compareTo(prepayVO.getSubscription().multiply(totalQuantity)) != 0) {
					throw new BusinessException(SentenceConst.PRESELL_TOTAL_DEPOSIT_CHECK_FAIL);
				}
				if (totalRemainAmount.compareTo(prepayVO.getFinalPayment().multiply(totalQuantity)) != 0) {
					throw new BusinessException(SentenceConst.PRESELL_TOTAL_BALANCE_CHECK_FAIL);
				}
				return productPromotionDetailVO.getPrepayVO();
			}
		}
		return null;
	}

}
