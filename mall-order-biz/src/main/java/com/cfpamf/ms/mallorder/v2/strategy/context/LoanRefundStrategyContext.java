package com.cfpamf.ms.mallorder.v2.strategy.context;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.cfpamf.ms.mallorder.v2.factory.LoanRefundStrategyFactory;
import com.cfpamf.ms.mallorder.v2.strategy.LoanRefundStrategy;

@Component
public class LoanRefundStrategyContext {
    
    @Autowired
    private LoanRefundStrategyFactory loanRefundStrategyFactory;

    /**
     * 获取具体策略实例
     * 
     * @param orderType
     * @return
     */
    public LoanRefundStrategy getStrategy(Integer orderType) {
        return loanRefundStrategyFactory.getStrategyByOrderType(orderType);
    }


}
