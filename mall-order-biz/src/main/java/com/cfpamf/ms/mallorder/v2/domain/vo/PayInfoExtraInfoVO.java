package com.cfpamf.ms.mallorder.v2.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类PayInfoExtraInfoVO.java的实现描述：
 *
 * <AUTHOR> 20:48
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PayInfoExtraInfoVO implements java.io.Serializable {
    /**
    * 
    */
    private static final long serialVersionUID = -2395013627112614002L;

    /**
     * 支付方式：可以为空，组合支付增加：COMBINATION_PAY
     */
    private String payMethod;

    /**
     * 支付单号：可以为空，组合支付增加：COMBINATION_PAY，时候不为空
     */
    private String payNo;
    
    /**
     * 支付流水号
     */
    private String paySn;
    
    /**
     * 支付流水号
     */
    private String orderSn;

}
