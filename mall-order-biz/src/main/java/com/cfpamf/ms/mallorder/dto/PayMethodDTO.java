package com.cfpamf.ms.mallorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @Create 2021-08-05 13:41
 * @Description :支付方式
 */
@Data
public class PayMethodDTO {

    @ApiModelProperty(value = "主键,新增时传0，修改时传对应id")
    @NotNull(message = "主键不能为空")
    private Long id;

    @ApiModelProperty(value = "启用状态： 0-停用 1-启用")
    private Integer payMethodStatus;

    @ApiModelProperty(value = "门槛金额")
    private BigDecimal minAmount;

    @ApiModelProperty(value = "支持的订单类型 逗号分隔 eg. 1,2")
    private String supportOrderType;

    @ApiModelProperty(value = "支持的下单渠道 逗号分隔 eg. H5,APP,WE_CHAT,MINI_PRO")
    private String supportOrderChannel;

    @ApiModelProperty(value = "是否信贷支付：1-是 0-否")
    private Integer isLoanPay;

    @ApiModelProperty(value = "贷款产品编码")
    private String loanCode;

    @ApiModelProperty(value = "贷款产品")
    private String loanProduct;

    @ApiModelProperty(value = "贷款产品名称")
    private String loanProductName;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty("支付图标路径")
    private String payIconPath;

    @ApiModelProperty(value = "处理人")
    @NotEmpty(message = "处理人不能为空,取当前登录用户名")
    private String operateUserName;
}
