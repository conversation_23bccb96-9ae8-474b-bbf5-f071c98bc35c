package com.cfpamf.ms.mallorder.vo;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.slodon.bbc.core.i18n.Language;
import com.slodon.bbc.core.util.FileUrlUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ChildOrdersVO {

    @ApiModelProperty("订单id")
    private Integer orderId;

    @ApiModelProperty("订单号")
    private String orderSn;

    @ApiModelProperty("店铺id")
    private Long storeId;

    @ApiModelProperty("店铺名称")
    private String storeName;

    @ApiModelProperty("店铺logo")
    private String storeLogo;

    @ApiModelProperty("发票信息")
    private JSONObject invoiceInfo;

    @ApiModelProperty("发票状态0-未开、1-已开")
    private Integer invoiceStatus;

    @ApiModelProperty("发票状态值0-未开、1-已开")
    private String invoiceStatusValue;

    @ApiModelProperty("活动优惠总金额 （= 店铺优惠券 + 平台优惠券 + 活动优惠【店铺活动 + 平台活动】 + 积分抵扣金额）")
    private BigDecimal activityDiscountAmount;

    @ApiModelProperty("订单货品列表")
    private List<OrderProductListVO> orderProductListVOList;

    @ApiModelProperty("店铺客服电话")
    private String servicePhone;

    @ApiModelProperty("分期信息")
    private LoanInfoVo loanInfoVo;

    @ApiModelProperty(value = "金融规则编号")
    private String financeRuleCode;

    @ApiModelProperty("金融规则标签")
    private String ruleTag;

    @ApiModelProperty("乡助卡优惠金额")
    private BigDecimal xzCardAmount;

    @ApiModelProperty("乡助卡运费优惠金额")
    private BigDecimal xzCardExpressFeeAmount;

    @ApiModelProperty("是否展示保险链接")
    private Boolean showInsuranceUrl;

    public ChildOrdersVO(OrderPO orderPO, OrderExtendPO orderExtendPO, String storeAvatar) {
        orderId = orderPO.getOrderId();
        orderSn = orderPO.getOrderSn();
        storeId = orderPO.getStoreId();
        storeName = orderPO.getStoreName();
        storeLogo = FileUrlUtil.getFileUrl(storeAvatar, null);
        invoiceInfo = JSONObject.parseObject(orderExtendPO.getInvoiceInfo());
        invoiceStatus = orderExtendPO.getInvoiceStatus();
        invoiceStatusValue = getRealInvoiceStatusValue(invoiceStatus);
        activityDiscountAmount = orderPO.getActivityDiscountAmount();
        xzCardAmount = orderPO.getXzCardAmount();
        xzCardExpressFeeAmount = orderPO.getXzCardExpressFeeAmount();
    }

    public static String getRealInvoiceStatusValue(Integer invoiceStatus) {
        String value = null;
        if (StringUtils.isEmpty(invoiceStatus)) {
            return Language.translate("未知");
        }
        switch (invoiceStatus) {
            case OrderConst.INVOICE_STATE_0:
                value = "未开发票";
                break;
            case OrderConst.INVOICE_STATE_1:
                value = "已开发票";
                break;
            default:
                break;
        }
        //翻译
        value = Language.translate(value);
        return value;
    }
}
