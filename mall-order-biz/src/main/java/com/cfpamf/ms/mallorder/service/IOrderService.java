package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.loan.facade.request.external.mall.GetMallProductElementRequest;
import com.cfpamf.ms.loan.facade.request.external.mall.GetMallProductRateRequest;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.enums.OrderProductDeliveryEnum;
import com.cfpamf.ms.mallorder.common.mq.msg.PromotionDiscountMsg;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.integration.filecenter.SceneTypeEnum;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.po.OrderLogisticPO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.req.OrderDeliveryMessageReq;
import com.cfpamf.ms.mallorder.req.OrderDeliveryReq;
import com.cfpamf.ms.mallorder.req.OrderInfoModifyReq;
import com.cfpamf.ms.mallorder.req.OrderRenewalPriceReq;
import com.cfpamf.ms.mallorder.req.front.PurchaseOrderAddressUpdateRequest;
import com.cfpamf.ms.mallorder.request.req.ErpOrderDeliveryRequest;
import com.cfpamf.ms.mallorder.vo.OrderDeliveryInfoVO;
import com.cfpamf.ms.mallorder.vo.OrderFrontDeliveryVO;
import com.cfpamf.ms.mallorder.vo.OrderProductDeliveryVO;
import com.cfpamf.ms.mallorder.vo.exportvo.DeliveryFailureVO;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.slodon.bbc.core.express.TracesResult;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 订单 service 接口
 *
 * @return
 */
public interface IOrderService extends IService<OrderPO> {

    /**
     * 订单商品物流详情
     *
     * @param orderSn
     * @return
     */
    List<OrderProductDeliveryVO> deliveryDetail(String orderSn);

    Boolean deliveryV2(OrderDeliveryReq deliveryReq, Vendor vendor);

    List<OrderProductDeliverDTO> getProductDeliveryListByProductId(String orderSn, List<Long> productIds);

    Map<Long, Integer> getProductRefundCountMap(List<OrderProductPO> productPOS);

    @Deprecated
    Boolean delivery(OrderDeliveryReq deliveryReq, Vendor vendor);

    Result<Boolean> deliveryForErp(ErpOrderDeliveryRequest deliveryReq);

    /**
     * 批量发货
     *
     * @param orderSns 订单号
     * @param vendor   商户信息
     * @param channel  渠道
     * @return 返回信息
     */
    @Deprecated
    String batchDelivery(String orderSns, Vendor vendor, OrderCreateChannel channel);


    Boolean orderDelivery(OrderPO orderPODb, Vendor vendor, OrderCreateChannel channel, Integer logRole,
                                 List<Long> orderProductIds);

    String batchDeliveryV2(OrderBatchDeliveryDTO orderBatchDeliveryDTO, Vendor vendor);

    List<DeliveryFailureVO> batchReplenishDeliveryV2(List<OrderDeliveryMessageReq> deliveryMessageList, Vendor vendor);

    /**
     * 订单商品全部发货完成后处理的事项
     *
     * @param orderPO   订单信息
     */
    void orderAllDeliveryItems(OrderPO orderPO);

    void deliveryMessage(OrderPO orderPO, OrderLogisticPO logisticPO, Vendor vendor);

    /**
     * 批量导入物流信息
     *
     * @param deliveryMessageList
     * @param vendor
     * @return
     */
    List<String> batchReplenishDelivery(List<OrderDeliveryMessageReq> deliveryMessageList, Vendor vendor);

    /**
     * 获取订单物流
     *
     * @param orderSn
     * @param orderProductId
     * @return
     */
    List<TracesResult> getOrderTrace(String orderSn, Long orderProductId);

    List<OrderFrontDeliveryVO> frontDeliveryList(String orderSn, Long orderProductId);

    /**
     * 批量获取订单物流信息
     *
     * @param orderSnList
     * @param memberId
     * @return
     */
    List<OrderDeliveryInfoVO> batchOrderDeliveryInfo(List<String> orderSnList, Integer memberId);

    boolean cleanPromotion();

    /**
     * 设置订单可发货
     *
     * @param orderSns 订单号
     */
    boolean setOrdersDeliverable(List<String> orderSns);

    OrderPO getByOrderSn(String orderSn);

    /**
     * 修改地址信息
     *
     * @param orderSn   订单号
     * @param addressId 地址id
     * @param member    用户信息
     */
    void updateAddress(String orderSn, Integer addressId, Member member);

    /**
     * 设置分销佣金
     *
     * @param orderSn
     */
    @Deprecated
    Boolean updateCommissionFee4Order(String orderSn);
    
    /**
     * 同步刷新分销佣金
     *
     * @param orderSn
     */
    Boolean updateCommissionFeeV3(String orderSn);

    /**
     * 处理0元用呗订单支付状态-成功
     *
     * @param paySn
     */
    void dealZeroEnjoyPayOrder(String paySn, String payNo);

    /**
     * 处理0元用呗订单支付状态-失败
     *
     * @param paySn
     */
    void dealZeroEnjoyPayOrderFail(String paySn);

    /**
     * @param vo
     * @param cardCodeList
     * @param userNo
     * @return com.cfpamf.ms.mallorder.vo.OrderSubmitPageVO
     * @description : 计算乡助卡金额
     */
    OrderSubmitDTO calculateCard(OrderSubmitDTO vo, List<String> cardCodeList, String userNo);

    /**
     * @param orderSubmitDTO
     * @param expressFeeList
     * @return void
     * @description : 处理已计算好的运费
     */
    void dealExpress(OrderSubmitDTO orderSubmitDTO, List<BigDecimal> expressFeeList);


    /**
     * 根据支付单号查询订单
     *
     * @param paySn 支付单号
     * @return 订单集
     */
    List<OrderPO> listByPaySn(String paySn);

    /**
     * 获取商家订单的有效总额
     *
     * @param storeId 商家ID
     * @return 有效订单总金额
     */
    BigDecimal getStoreValidOrderAmountSum(String storeId);

    /**
     * 延迟自动收货时间，时间为一个周期
     *
     * @param orderSn 订单号
     */
    void extendOrderAutoReceiveTime(String orderSn);

    /**
     * 采购订单修改收货地址
     *
     * @param address 采购订单地址信息
     * @param member  当前用户
     */
    void updatePurchaseOrderAddress(PurchaseOrderAddressUpdateRequest address, Member member);

    /**
     * @param orderBatchId
     * @return com.cfpamf.common.ms.result.Result<com.cfpamf.mallpayment.facade.request.loan.GetMallProductElementRequest>
     * @description : 构建获取产品要素所需请求参数
     */
    Result<GetMallProductElementRequest> buildProductElementRequest(String orderBatchId);

    /**
     * @param orderBatchId
     * @return com.cfpamf.common.ms.result.Result<com.cfpamf.mallpayment.facade.request.loan.GetMallProductRateRequest>
     * @description :构建获取利率所需请求参数
     */
    Result<GetMallProductRateRequest> buildMallProductRateRequest(String orderBatchId);

    /**
     * 该单号通过订单查询已完结发货，或者在全域内被重复使用到不同的订单上≥3次,
     *
     * @param expressNumber 快递单号
     * @param vendor        商户
     * @return
     */
    boolean validExpressNumberReuseCount(String expressNumber, Vendor vendor);

    /**
     * 该单号通过订单查询已完结发货，或者在全域内被重复使用到不同的订单上≥3次,
     *
     * @param expressNumber 快递单号
     * @return
     */
    boolean validExpressNumberReuseCount(String expressNumber);

    /**
     * 根据userNo获取待安装的订单数
     */
    Integer getInstallOrderCountByUserNo(String userNo);

    /**
     * 到家服务历史数据处理
     */
    String homeServiceHistoryDataDeal();

    /**
     * 设置待客户确认状态（草稿--》待确认）
     *
     * @param orderSn
     * @return
     */
    boolean setCustomerUnConfirmStatus(String orderSn);


    /**
     * 设置待客户确认状态(草稿--》无需确认)
     *
     * @param orderSn
     * @return
     */
    boolean setCustomerNoNeedConfirmStatus(String orderSn);

    /**
     * 设置客户确认状态(待确认--》已确认)
     *
     * @param orderSn
     * @return
     */
    boolean setCustomerConfirmStatus(String orderSn);

    /**
     * 设置客户确认拒绝状态
     *
     * @param orderSn
     * @param logUserId
     * @param logUserName
     * @return
     */
    boolean setCustomerConfirmRefuseStatus(String orderSn, Integer optRole, Long logUserId, String logUserName);

    /**
     * 设置客户确认拒绝状态
     *
     * @param orderSn
     * @param logUserId
     * @param logUserName
     * @return
     */
    boolean setCustomerConfirmClosedStatus(String orderSn, Integer optRole, Long logUserId, String logUserName);

    Result<Long> processOrderCommission(DistributionCommissionUpdateDTO dto);

    /**
     * 处理佣金激励费
     *
     * @param dto   佣金激励费信息
     * @return      需要发送的消息的MQ的id
     */
    Long processOrderCommissionIncentive(DistributionCommissionIncentiveUpdateDTO dto);

    Result<Long> processOrderPlanDiscount(PromotionDiscountMsg dto);

    Boolean extendedAfterSales(ExtendedAfterSalesDTO extendedAfterSalesDTO, Integer logRole, Long logUserId, String logUserName);

    /**
     * 订单改价：订单运费、商品单价、商品落地价
     *
     * @param renewalPriceReq
     * @return
     */
    Boolean renewalPrice(OrderRenewalPriceReq renewalPriceReq, Long operatorId, String operatorName);

    Boolean renewalPrice(OrderRenewalPriceReq renewalPriceReq, OrderPO orderPO, OrderExtendPO orderExtendPO,
                         Map<Long, OrderProductPO> orderProductPOMap, OrderAmountDP orderAmountDP, String operatorName);

    /**
     * 更新订单状态为付款中
     *
     * @param orderSn
     * @param paymentCode
     * @return
     */

    Boolean loanStatusChange(String orderSn, String paymentCode);

    Boolean orderInfoModify(OrderInfoModifyReq orderInfoModifyReq, Vendor vendor);

    /**
     * 检查订单签收资料是否上传
     * @param orderPO
     * @return
     */
    Result<Boolean> checkOrderDeliveryMaterial(OrderPO orderPO);
    /**
     * 检查订单签收资料是否上传
     * @param orderPO
     * @return
     */
    Result<Boolean> checkOrderReceiveMaterial(OrderPO orderPO);

    SceneTypeEnum getOrderFileSceneNo(OrderPO orderPO, OrderProductDeliveryEnum deliveryEnum);

}
