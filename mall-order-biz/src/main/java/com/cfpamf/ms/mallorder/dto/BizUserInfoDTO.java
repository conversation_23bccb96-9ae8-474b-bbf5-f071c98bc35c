package com.cfpamf.ms.mallorder.dto;

import java.util.Date;

import lombok.Data;

@Data
public class BizUserInfoDTO implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2841006455754010841L;
	/**
	 * 运营平台用户id
	 */
	private Integer userId;

	/**
	 * 运营平台员工id
	 */
	private Integer employeeId;

	/**
	 * 用户对应的HR中的Id
	 */
	private Integer hrUserId;

	/**
	 * 用户手机号
	 */
	private String account;

	/**
	 * 用户姓名
	 */
	private String userName;

	/**
	 * 用户当前的主职/兼职工号
	 */
	private String jobNumber;

	/**
	 * 用户主职工号
	 */
	private String masterJobNumber;

	/**
	 * 运营平台机构id
	 */
	private Integer orgId;

	/**
	 * 机构对应的HR中的Id
	 */
	private Integer hrOrgId;

	/**
	 * 机构在HR中的Code
	 */
	private String hrOrgCode;

	/**
	 * 机构在HR中的Name(首取全名，为空则取简称)
	 */
	private String hrOrgName;

	/**
	 * 机构在HR中的hrOrgId层级树
	 */
	private String hrOrgTreePath;

	/**
	 * 当前用户的用户类别（1：平台管理员，2：平台员工用户，3：各子系统管理员）
	 */
	private Integer userType;

	/**
	 * token随机码,防止每次生成的token都一样
	 */
	private String randomCode;

	/**
	 * 是否为sso登录token
	 */
	private Boolean useSso;

	/**
	 * 灰度用户标识
	 */
	private Integer gray;

	/**
	 * AccountLoginDTO字段登入渠道，用于生成个性化token时效
	 */
	private String channel;

	/**
	 * 为哪个系统生成的token，sso改造新增字段
	 */
	private Integer systemId;

	/**
	 * 生成时间，用于最长有效期控制，sso改造新增字段
	 */
	private Date createTime;

}
