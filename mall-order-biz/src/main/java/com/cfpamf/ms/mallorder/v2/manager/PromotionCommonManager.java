package com.cfpamf.ms.mallorder.v2.manager;

import com.cfpamf.ms.mallpromotion.request.ProductPromotionDetailDTO;
import com.cfpamf.ms.mallpromotion.vo.ProductPromotionDetailVO;

import java.util.List;


public interface PromotionCommonManager {

    /**
     * 查询商品活动详情信息
     *
     * @param queryDTOList  活动商品信息
     * @return              商品活动详情
     */
    List<ProductPromotionDetailVO> getProductPromotionDetailBatch(List<ProductPromotionDetailDTO> queryDTOList);

}
