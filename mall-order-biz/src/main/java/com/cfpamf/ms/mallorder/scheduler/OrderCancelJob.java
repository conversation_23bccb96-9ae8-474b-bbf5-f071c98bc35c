package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.v2.common.enums.OrderSnType;
import com.cfpamf.ms.mallorder.v2.domain.vo.OperationUserVO;
import com.cfpamf.ms.mallorder.v2.domain.vo.OrderCancelVO;
import com.cfpamf.ms.mallorder.v2.service.OrderCancelService;
import com.cfpamf.ms.mallorder.v2.service.OrderCancelSupportService;
import com.slodon.bbc.core.util.AssertUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 未确认订单自动取消
 * <p>
 * cron: 0 30 7 * * ?
 */
@Component
@Slf4j
public class OrderCancelJob {

    @Autowired
    private OrderModel orderModel;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private OrderCancelService orderCancelService;

    @Autowired
    private OrderCancelSupportService systemOrderCancelSupportService;

    /**
     * 自动取消普通订单(非自提)
     * @param s
     * @return
     */
    @XxlJob(value = "OrderCancelJob")
    public ReturnT<String> execute(String s) {

        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {0}", LocalDateTime.now());

        log.info("jobSystemCancelOrder() start");
        try {
            boolean jobResult = orderModel.jobSystemCancelOrder();
            AssertUtil.isTrue(!jobResult, "[jobSystemCancelOrder] 定时任务系统自动取消12小时没有付款订单时失败");
        } catch (Exception e) {
            log.error("jobSystemCancelOrder()", e);
        }
        XxlJobHelper.log("finish job at local time: {0}  cost:{1}", LocalDateTime.now(),
            (System.currentTimeMillis() - startTime) / 1000);
        return ReturnT.SUCCESS;
    }

    /**
     * 自提订单自动取消 job
     * @param s
     * @return
     */
    @XxlJob(value = "OrderCancelSelfLiftJob")
    public ReturnT<String> executeOrderCancelSelfLiftJob(String s) {

        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {0}", LocalDateTime.now());

        log.info("OrderCancelSelfLiftJob() start");
        try {
            boolean jobResult = orderModel.jobSystemCancelSelfLiftOrder();
            AssertUtil.isTrue(!jobResult, "[OrderCancelSelfLiftJob] 定时任务系统自动取消12小时没有付款订单时失败");
        } catch (Exception e) {
            log.error("OrderCancelSelfLiftJob()", e);
        }
        XxlJobHelper.log("OrderCancelSelfLiftJob finish job at local time: {0}  cost:{1}", LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);
        return ReturnT.SUCCESS;
    }

    /**
     * 系统自动取消24小时没有付款订单
     *
     * @return
     */
    public boolean jobSystemCancelOrder() {
        // 获取自动取消时间限制（小时）
        String value = stringRedisTemplate.opsForValue().get("time_limit_of_auto_cancle_order");
        int limitHour = value == null ? 12 : Integer.parseInt(value);
        // 获取当前时间limitHour小时之前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, -limitHour);
        Date cancelTime = calendar.getTime();
        // 获取超时未付款的订单，此处查询的都是待付款状态的父订单
        OrderExample example = new OrderExample();
        example.setCreateTimeBefore(cancelTime);
        example.setOrderState(OrderConst.ORDER_STATE_10);
        // 普通订单和配销订单
        example.setOrderTypeIn(OrderConst.ORDER_TYPE_1 + "," + OrderConst.ORDER_TYPE_7);
        example.setOrderBy("parent_sn");// 按照父订单号分组
        List<OrderPO> parentOrderPOList = orderMapper.listByExample(example);
        if (CollectionUtils.isEmpty(parentOrderPOList)) {
            log.warn("系统自动取消24小时没有付款订单，parentOrderPOList为空，不存在相关记录无需取消操作");
            return true;
        }
        // 父订单号去重
        Set<String> orderParentSns = parentOrderPOList.stream().map(OrderPO::getParentSn).collect(Collectors.toSet());
        orderParentSns.forEach(parentSn -> {
            try {
                log.info("开启V2流程取消订单，job取消订单。parentSn:{}", parentSn);
                orderCancelService.cancelOrder(systemOrderCancelSupportService,
                    new OrderCancelVO(OrderSnType.PARENT_ORDER_SN, parentSn, null, "system"),
                    new OperationUserVO(OrderConst.LOG_ROLE_ADMIN, OrderConst.RETURN_BY_0, 0, "system", "系统自动取消订单"));
            } catch (Exception e) {
                log.error("开启V2流程取消订单，job取消订单。parentSn:{}", parentSn, e);
            }
        });
        return true;
    }
}
