package com.cfpamf.ms.mallorder.controller.front;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.mallpayment.facade.request.loan.SetlTryResultVo;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.constant.OrdersAfsConst;
import com.cfpamf.ms.mallorder.common.enums.OrderRefundRevokingPartyEnum;
import com.cfpamf.ms.mallorder.common.enums.RefundType;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.constant.DomainUrlConstant;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.ExchangeOrderReturnDetailDTO;
import com.cfpamf.ms.mallorder.dto.OperatorDTO;
import com.cfpamf.ms.mallorder.dto.UserDTO;
import com.cfpamf.ms.mallorder.model.*;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.ExchangeApplyListReq;
import com.cfpamf.ms.mallorder.req.front.FrontRevokeRefundBaseRequest;
import com.cfpamf.ms.mallorder.request.ComplainExample;
import com.cfpamf.ms.mallorder.request.ComplainTalkExample;
import com.cfpamf.ms.mallorder.request.OrderAfterSaleLogExample;
import com.cfpamf.ms.mallorder.request.OrderReturnExample;
import com.cfpamf.ms.mallorder.service.IOrderExchangeDetailService;
import com.cfpamf.ms.mallorder.service.IOrderExchangeService;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallshop.api.StoreAddressFeignClient;
import com.cfpamf.ms.mallshop.resp.StoreAddress;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallsystem.api.ExpressFeignClient;
import com.cfpamf.ms.mallsystem.vo.Express;
import com.slodon.bbc.core.constant.ComplainConst;
import com.slodon.bbc.core.controller.BaseController;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.TimeUtil;
import com.slodon.bbc.core.util.UserUtil;
import com.slodon.bbc.core.util.WebUtil;
import feign.Param;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Api(tags = "front-售后管理")
@RestController
@RequestMapping("front/after/sale")
@Slf4j
public class FrontAfterSaleController extends BaseController {

    @Resource
    private OrderReturnModel orderReturnModel;
    @Resource
    private IOrderService orderService;
    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;
    @Resource
    private OrderAfterSaleLogModel orderAfterSaleLogModel;
    @Resource
    private OrderProductModel orderProductModel;
    @Resource
    private ComplainModel complainModel;
    @Resource
    private ComplainTalkModel complainTalkModel;
    @Resource
    private StoreAddressFeignClient storeAddressFeignClient;

    @Resource
    private IOrderReturnService orderReturnService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private OrderLocalUtils orderLocalUtils;

    @Resource
    private IOrderExchangeDetailService orderExchangeDetailService;

    @Resource
    private IOrderExchangeService orderExchangeService;

    @ApiOperation(value = "售后列表（不包含换货数据）", tags = "CORE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "returnType", value = "退款方式：1-仅退款 2-退货退款", required = true, paramType = "query"),
            @ApiImplicitParam(name = "orderPattern", value = "订单模式：1-C端店铺街，2-B端采购中心", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "分页大小", defaultValue = "20", paramType = "query"),
            @ApiImplicitParam(name = "current", value = "当前页面位置", defaultValue = "1", paramType = "query")
    })
    @GetMapping("list")
    public JsonResult<PageVO<OrderReturnVOV2>> list(HttpServletRequest request, Integer returnType, Integer orderPattern) {
        Member member = UserUtil.getUser(request, Member.class);
        log.info("member信息：{}", JSON.toJSONString(member));
        Integer memberId = member.getMemberId();
        if (Objects.isNull(member.getMemberId()) || memberId == 0) {
            Vendor vendor = UserUtil.getUser(request, Vendor.class);
            log.info("vendor信息：{}", JSON.toJSONString(vendor));
            if (Objects.nonNull(vendor)) {
                Member vendorMember = orderLocalUtils.getUserByVendor(vendor);
                log.info("member信息：{}", JSON.toJSONString(member));
                memberId = vendorMember.getMemberId();
            }
        }
        PagerInfo pager = WebUtil.handlerPagerInfo(request);
        OrderReturnExample example = new OrderReturnExample();
        example.setMemberId(memberId);
        example.setReturnType(returnType);
        if(returnType == null){
            example.setReturnTypeIn(OrdersAfsConst.RETURN_TYPE_1 + "," + OrdersAfsConst.RETURN_TYPE_2);
        }
        example.setOrderPattern(orderPattern);
        List<OrderReturnVOV2> vos = orderReturnModel.getOrderReturnListWithJoin(example, pager);
        return SldResponse.success(new PageVO<>(vos, pager));
    }


    @GetMapping("countDuringRefundStatus")
    @ApiOperation("查询用户售后气泡数")
    public JsonResult<AfsOrderCountVO> countDuringRefundStatus(HttpServletRequest request) {
        Member member = UserUtil.getUser(request, Member.class);
        return SldResponse.success(orderReturnModel.countDuringRefundStatus(member.getMemberId()));
    }

    @ApiOperation("换货列表")
    @GetMapping("exchangeOrderList")
    public JsonResult<PageVO<OrderExchangeReturnListVO>> getOrderExchangeReturnList(HttpServletRequest request) {
        Member member = UserUtil.getUser(request, Member.class);
        UserDTO userDTO = new UserDTO(member);
        PagerInfo pager = WebUtil.handlerPagerInfo(request);
        ExchangeApplyListReq exchangeApplyListReq = new ExchangeApplyListReq();
        exchangeApplyListReq.setUserDTO(userDTO);
        List<OrderExchangeReturnListVO> list = orderExchangeService.getOrderExchangeReturnList(exchangeApplyListReq, pager);
        return SldResponse.success(new PageVO<>(list, pager));
    }

    @ApiOperation("换货订单详情")
    @GetMapping("exchangeOrderDetail")
    public JsonResult<OrderExchangeDetailVO> exchangeOrderDetail(HttpServletRequest request,@Param("exchangeSn") String exchangeSn) {
        Member member = UserUtil.getUser(request, Member.class);
        UserDTO userDTO = new UserDTO(member);
        OrderExchangeDetailVO orderExchangeDetailVO = orderExchangeDetailService.orderExchangeApplyDetail(exchangeSn, userDTO);
        return SldResponse.success(orderExchangeDetailVO);
    }

    @ApiOperation("商品换货详情")
    @GetMapping("orderExchangeDetail")
    public JsonResult<List<OrderExchangeDetailVO>> productOrderExchangeDetail(HttpServletRequest request,@RequestParam("orderProductId") @NotNull Long orderProductId) {
        Member member = UserUtil.getUser(request, Member.class);
        UserDTO userDTO = new UserDTO(member);
        return SldResponse.success(orderExchangeDetailService.productOrderExchangeDetail(orderProductId, userDTO));
    }

    @ApiOperation(value = "售后详情", tags = "CORE")
    @GetMapping("detail")
    public JsonResult<OrderReturnDetailVO> detail(HttpServletRequest request, String afsSn) {
        AssertUtil.notEmpty(afsSn, "退款编号不能为空");

        Member member = UserUtil.getUser(request, Member.class);
        log.info("member信息：{}", JSON.toJSONString(member));
        OrderReturnPO orderReturnPO = orderReturnModel.getOrderReturnByAfsSn(afsSn);
        AssertUtil.notNull(orderReturnPO, "获取退货信息为空，请重试！");
        AssertUtil.isTrue(!orderReturnPO.getMemberId().equals(member.getMemberId()), "您无权操作");

        //查询售后信息
        OrderAfterPO orderAfterServicePO = orderAfterServiceModel.getAfterServiceByAfsSn(orderReturnPO.getAfsSn());

        //查询订单货品
        OrderProductPO orderProductPO = orderProductModel.getOrderProductByOrderProductId(orderAfterServicePO.getOrderProductId());
        AssertUtil.notNull(orderProductPO, "获取订单货品信息为空，请重试");

        //查询售后日志
        OrderAfterSaleLogExample example = new OrderAfterSaleLogExample();
        example.setAfsSn(afsSn);
        example.setOrderBy("create_time asc");
        List<OrderAfterSaleLogPO> logList = orderAfterSaleLogModel.getOrderAfterSaleLogList(example, null);
        AssertUtil.notEmpty(logList, "获取售后记录为空，请重试");

        StoreAddress storeAddress = null;
        if (!StringUtils.isEmpty(orderAfterServicePO.getStoreAfsAddress())) {
            //storeAddress = storeAddressFeignClient.getStoreAddressByAddressId(Integer.valueOf(orderAfterServicePO.getStoreAfsAddress()));
            storeAddress = storeAddressFeignClient.getChannelStoreAddressByAddressId(Integer.valueOf(orderAfterServicePO.getStoreAfsAddress()), orderAfterServicePO.getPerformanceMode());
        }
        Result<SetlTryResultVo> tryCaculateResult = null;
        if (orderReturnPO.getRefundType().equals(RefundType.ASSIST_PAYMENT.getValue()) && ObjectUtils.isEmpty(orderReturnPO.getRefundEndTime())) {// 代还试算&还清后不试算
            try {
                tryCaculateResult = orderAfterServiceModel.getTryCaculateResult(orderReturnPO.getReturnMoneyAmount().add(orderReturnPO.getReturnExpressAmount()), afsSn);
            } catch (Exception e) {
                log.info("front端试算异常：", JSON.toJSONString(e));
            }
        }
        SetlTryResultVo setlTryResultVo = ObjectUtils.isEmpty(tryCaculateResult) ? null : tryCaculateResult.getData();
        OrderPO orderPO = orderService.getByOrderSn(orderReturnPO.getOrderSn());
        ExchangeOrderReturnDetailDTO exchangeOrderReturnDetailDTO = null;
        //如果是换货单，需要查询换货订单详情
        if (OrdersAfsConst.RETURN_TYPE_3 == orderReturnPO.getReturnType()) {
            exchangeOrderReturnDetailDTO = orderExchangeDetailService.dealExchangeOrderReturnDetail(afsSn);
        }
        OrderReturnDetailVO detailVO = new OrderReturnDetailVO(orderPO, orderReturnPO, orderAfterServicePO, orderProductPO,
                storeAddress, setlTryResultVo, member, exchangeOrderReturnDetailDTO);
        //查询商品行是否关联赠品
        detailVO.setOrderGiftProductList(orderLocalUtils.getOrderGiftProductListVOS(orderPO.getOrderType(), orderPO.getOrderSn(),
                orderProductPO.getGiftGroup(), orderAfterServicePO.getGiftReturnOrderProductId()));

        if (orderReturnPO.getState().equals(OrdersAfsConst.RETURN_STATE_100)
                || orderReturnPO.getState().equals(OrdersAfsConst.RETURN_STATE_101)) {
            //申请状态,商家处理截止时间
            int limitDay = Integer.parseInt(stringRedisTemplate.opsForValue().get("time_limit_of_afs_seller_audit"));
            detailVO.setDeadline(TimeUtil.getDayAgoDate(orderAfterServicePO.getBuyerApplyTime(), limitDay));
        } else if (orderReturnPO.getState().equals(OrdersAfsConst.RETURN_STATE_201)) {
            //商家同意退货退款，待买家发货
            int limitDay = Integer.parseInt(stringRedisTemplate.opsForValue().get("time_limit_of_afs_member_send"));
            detailVO.setDeadline(TimeUtil.getDayAgoDate(orderAfterServicePO.getStoreAuditTime(), limitDay));
        } else if (orderReturnPO.getState().equals(OrdersAfsConst.RETURN_STATE_102)) {
            //买家发货，待商家收货退款
            int limitDay = Integer.parseInt(stringRedisTemplate.opsForValue().get("time_limit_of_afs_seller_receive"));
            detailVO.setDeadline(TimeUtil.getDayAgoDate(orderAfterServicePO.getBuyerDeliverTime(), limitDay));
        }

        //根据订单类型返回相应订单类型的数据信息
        /*OrderTypeQueryStrategy orderTypeQueryStrategy = orderTypeQueryStrategyContext.getStrategy(orderPO.getOrderType());
        if(ObjectUtil.isNotEmpty(orderTypeQueryStrategy)){
            Object promotionReturnObject = orderTypeQueryStrategy.queryPromotionReturnOrderInfo(orderPO.getOrderSn());
            detailVO.setPromotionPayDetail(promotionReturnObject);
        }*/

        detailVO.setOrderState(orderPO.getOrderState());
        //拨打电话
        List<String> hiddenHosts = Arrays.asList("https://jbbcadmin.slodon.cn", "http://jbbcs-admin.slodon.cn");
        if (hiddenHosts.contains(DomainUrlConstant.SLD_ADMIN_URL)) {
            String phone = stringRedisTemplate.opsForValue().get("basic_site_phone");
            if (!StringUtils.isEmpty(phone)) {
                detailVO.setPlatformPhone(phone.replaceFirst("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));
            }
        }
        List<OrderReturnDetailVO.ReturnLogVO> returnLogList = new ArrayList<>();
        logList.forEach(orderAfterSaleLog -> {
            returnLogList.add(new OrderReturnDetailVO.ReturnLogVO(orderAfterSaleLog));
        });
        detailVO.setReturnLogList(returnLogList);
        return SldResponse.success(detailVO);
    }


    @Autowired
    ExpressFeignClient expressFeignClient;

    @ApiOperation("调用物流接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "expressId", value = "订单号", required = true, paramType = "query")
    })
    @GetMapping("order/getExpress")
    public JsonResult<Express> getOrderExpress(Integer expressId) {
        Express express = expressFeignClient.getExpressByExpressId(expressId);
        return SldResponse.success(express);
    }

    @ApiOperation(value = "用户售后发货", tags = "CORE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "afsSn", value = "售后服务单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "expressId", value = "物流公司ID", required = true, paramType = "query"),
            @ApiImplicitParam(name = "logisticsNumber", value = "快递单号", required = true, paramType = "query")
    })
    @PostMapping("deliverGoods")
    public JsonResult deliverGoods(HttpServletRequest request, String afsSn, Integer expressId, String logisticsNumber) {
        Member member = UserUtil.getUser(request, Member.class);

        log.info("FRONT REQUEST afsSn:{}  expressId:{}   logisticsNumber:{}", afsSn, expressId, logisticsNumber);
        log.info("MEMBER INFO FROM GATEWAY:{}", JSON.toJSONString(member));

        orderAfterServiceModel.memberDeliverGoods(member, afsSn, expressId, logisticsNumber);
        return SldResponse.success("发货成功");
    }

    @ApiOperation("投诉商家")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "afsSn", value = "售后服务单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "complainContent", value = "投诉内容", required = true, paramType = "query"),
            @ApiImplicitParam(name = "complainPic", value = "上传凭证,图片之间用逗号隔开", paramType = "query")
    })
    @PostMapping("complainStore")
    public JsonResult complainStore(HttpServletRequest request, String afsSn, String complainContent, String complainPic) {
        //校验
        AssertUtil.notEmpty(afsSn, "售后服务单号不能为空");
        AssertUtil.notEmpty(complainContent, "投诉内容不能为空");
        //投诉商家
        Member member = UserUtil.getUser(request, Member.class);
        Integer conplainId = complainModel.saveComplain(afsSn, complainContent, complainPic, member.getMemberId(), member.getMemberName());
        return SldResponse.success("投诉商家成功", conplainId);
    }

    @ApiOperation("投诉详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "complainId", value = "投诉id", required = true, paramType = "query")
    })
    @GetMapping("complainDetail")
    public JsonResult<ComplainDetailVO> complainDetail(HttpServletRequest request, Integer complainId) {
        AssertUtil.notNullOrZero(complainId, "投诉id不能为空");
        Member member = UserUtil.getUser(request, Member.class);

        //查询投诉信息
        ComplainPO complainPO = complainModel.getComplainByComplainId(complainId);
        AssertUtil.notNull(complainPO, "查询的投诉信息为空");
        AssertUtil.isTrue(!complainPO.getComplainMemberId().equals(member.getMemberId()), "您无权操作");
        //查询售后信息
        OrderAfterPO orderAfterServicePO = orderAfterServiceModel.getAfterServiceByAfsSn(complainPO.getAfsSn());
        AssertUtil.isTrue(!orderAfterServicePO.getMemberId().equals(member.getMemberId()), "您无权操作");
        //查询订单货品
        OrderProductPO orderProductPO = orderProductModel.getOrderProductByOrderProductId(orderAfterServicePO.getOrderProductId());
        AssertUtil.notNull(orderProductPO, "获取订单货品信息为空，请重试");
        //查询对话信息列表
        ComplainTalkExample example = new ComplainTalkExample();
        example.setComplainId(complainId);
        example.setOrderBy("complain_talk_id ASC");
        List<ComplainTalk> list = complainTalkModel.getComplainTalkList(example, null);

        ComplainDetailVO vo = new ComplainDetailVO(complainPO, orderAfterServicePO, orderProductPO);
        if (!CollectionUtils.isEmpty(list)) {
            List<ComplainDetailVO.ComplainTalkInfo> complainTalkInfoList = new ArrayList<>();
            list.forEach(complainTalk -> {
                ComplainDetailVO.ComplainTalkInfo complainTalkInfo = new ComplainDetailVO.ComplainTalkInfo(complainTalk);
                complainTalkInfoList.add(complainTalkInfo);
            });
            vo.setComplainTalkInfoList(complainTalkInfoList);
        }
        return SldResponse.success(vo);
    }

    @ApiOperation("撤销投诉")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "complainId", value = "投诉id", required = true, paramType = "query")
    })
    @PostMapping("cancelComplain")
    public JsonResult cancelComplain(HttpServletRequest request, Integer complainId) {
        AssertUtil.notNullOrZero(complainId, "投诉id不能为空");
        Member member = UserUtil.getUser(request, Member.class);
        //撤销投诉
        ComplainPO updateOne = new ComplainPO();
        updateOne.setComplainState(ComplainConst.COMPLAIN_STATE_5);

        ComplainExample example = new ComplainExample();
        example.setComplainId(complainId);
        example.setComplainMemberId(member.getMemberId());
        Integer count = complainModel.updateByExampleSelective(updateOne, example);
        return SldResponse.success("撤销投诉成功");
    }

    @ApiOperation("发送对话")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "complainId", value = "投诉id", required = true, paramType = "query"),
            @ApiImplicitParam(name = "talkContent", value = "投诉对话内容", required = true, paramType = "query")
    })
    @PostMapping("addTalk")
    public JsonResult addTalk(HttpServletRequest request, Integer complainId, String talkContent) {
        AssertUtil.notNullOrZero(complainId, "投诉id不能为空");
        AssertUtil.notEmpty(talkContent, "投诉对话内容不能为空");
        Member member = UserUtil.getUser(request, Member.class);
        int talkUserType = ComplainConst.TALK_USER_TYPE_1;
        complainTalkModel.saveComplainTalk(complainId, talkContent, Long.valueOf(member.getMemberId()), member.getMemberName(), talkUserType);
        return SldResponse.success("发送对话成功");
    }

    @ApiOperation("提交仲裁")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "complainId", value = "投诉id", required = true, paramType = "query")
    })
    @PostMapping("handle")
    public JsonResult handle(HttpServletRequest request, Integer complainId) {
        //校验
        AssertUtil.notNullOrZero(complainId, "投诉id不能为空");
        //仲裁
        Member member = UserUtil.getUser(request, Member.class);
        ComplainPO updateOne = new ComplainPO();
        updateOne.setComplainState(ComplainConst.COMPLAIN_STATE_4);

        ComplainExample example = new ComplainExample();
        example.setComplainMemberId(member.getMemberId());
        example.setComplainId(complainId);
        complainModel.updateByExampleSelective(updateOne, example);
        return SldResponse.success("提交仲裁成功");
    }

    @ApiOperation("用户端撤销退款")
    @PostMapping("frontRevokeRefund")
    public JsonResult<Boolean> adminRevokeRefund(HttpServletRequest request, @RequestBody @Valid FrontRevokeRefundBaseRequest revokeRequest) {
        Member member = UserUtil.getUser(request, Member.class);
        log.info("用户端撤销退款>>>>>>>>>>>>>>>>>>>member:{}", member.toString());
        OperatorDTO operator = new OperatorDTO(member.getMemberId(), member.getMemberName(), OrderConst.LOG_ROLE_MEMBER, member.getMemberMobile());
        orderReturnService.revokeRefund(revokeRequest, OrderRefundRevokingPartyEnum.CUSTOMER_REJECT, operator);
        return SldResponse.success();
    }

    @ApiOperation("退款金额详情")
    @GetMapping("refundMoneyDetail")
    public JsonResult<OrderRefundMoneyDetail> refundMoneyDetail(HttpServletRequest request, String afsSn) {
        Member member = UserUtil.getUser(request, Member.class);
        log.info("用户查看退款金额详情 moneyDetail >>>>>>>>>>>>>>>>>>> member : {}", JSON.toJSONString(member));
        OrderRefundMoneyDetail orderRefundMoneyDetail = orderReturnService.refundMoneyDetail(afsSn);
        log.info("用户查看退款金额详情 moneyDetail >>>>>>>>>>>>>>>>>>> moneyDetail : {}", JSON.toJSONString(orderRefundMoneyDetail));
        return SldResponse.success(orderRefundMoneyDetail);
    }

}