package com.cfpamf.ms.mallorder.req.admin;

import com.cfpamf.ms.mallorder.req.base.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class LoanListResultRequest extends PageRequest {

    @ApiModelProperty(value = "申请编号")
    private Long payNo;

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "计划放宽日开始")
    private Date deliveryTimeStart;

    @ApiModelProperty(value = "计划放宽日结束")
    private Date deliveryTimeEnd;

    @ApiModelProperty(value = "放款结果: ACTV-成功 FAIL-失败 REFUND-退汇 P-处理中", example = "FAIL")
    private String loanResult;

    @ApiModelProperty(value = "处理类型：1、放贷申请；2、重新代付")
    private Integer handleType;

}
