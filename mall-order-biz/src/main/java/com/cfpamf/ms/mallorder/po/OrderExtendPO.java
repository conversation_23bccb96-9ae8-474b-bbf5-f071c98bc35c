package com.cfpamf.ms.mallorder.po;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单信息扩展表
 */
@Data
@TableName(value = "bz_order_extend", autoResultMap = true)
public class OrderExtendPO implements Serializable {
    private static final long serialVersionUID = -8353312635429901376L;
    @ApiModelProperty("扩展id")
    @TableId(type = IdType.AUTO)
    private Integer extendId;

    @ApiModelProperty("关联的订单编号")
    private String orderSn;

    @ApiModelProperty("客户ID")
    private String customerId;

    @ApiModelProperty("客户姓名")
    private String customerName;

    @ApiModelProperty("客户经理工号")
    private String manager;

    @ApiModelProperty("客户经理姓名")
    private String managerName;

    @ApiModelProperty("站长userNo")
    private String stationMaster;

    @ApiModelProperty("站长姓名(电话)")
    private String stationMasterName;

    @ApiModelProperty("督导工号")
    private String supervisor;

    @ApiModelProperty("发货地址")
    private String deliverPlace;

    @ApiModelProperty("预估运费")
    private BigDecimal estimateExpressFee;

    @ApiModelProperty("发货要求：1：随时发货 2：10至20个自然日之间")
    private Integer deliveryRequirements;

    @ApiModelProperty("运费计算方式：1：普通方式；2：按距离计算")
    private Integer expressCalculateType;

    @ApiModelProperty("督导姓名")
    private String supervisorName;

    @ApiModelProperty("分支编号")
    private String branch;

    @ApiModelProperty("分支名称")
    private String branchName;

    @ApiModelProperty("片区编号")
    private String zoneCode;

    @ApiModelProperty("片区名称")
    private String zoneName;

    @ApiModelProperty("区域编号")
    private String areaCode;

    @ApiModelProperty("区域名称")
    private String areaName;

    @ApiModelProperty("店铺ID")
    private Long storeId;

    @ApiModelProperty("评价时间")
    private Date evaluationTime;

    @ApiModelProperty("用户订单备注")
    private String orderRemark;

    @ApiModelProperty("订单赠送积分")
    private Integer orderPointsCount;

    @ApiModelProperty("优惠券面额")
    private BigDecimal voucherPrice;

    @ApiModelProperty("优惠券编码")
    private String voucherCode;

    @ApiModelProperty("乡助卡优惠券使用列表")
    private String xzCardList;

    @ApiModelProperty("订单来源1、pc；2、H5；3、Android；4、IOS; 5-微信小程序")
    private Integer orderFrom;

    @ApiModelProperty("发货地址ID")
    private Integer deliverAddressId;

    @ApiModelProperty("收货省份编码")
    private String receiverProvinceCode;

    @ApiModelProperty("省市区组合")
    private String receiverAreaInfo;

    @ApiModelProperty("收货人详细地址")
    private String receiverAddress;

    @ApiModelProperty("收货人手机号")
    private String receiverMobile;

    @ApiModelProperty("收货城市编码")
    private String receiverCityCode;

    @ApiModelProperty("收货区县编码")
    private String receiverDistrictCode;

    @ApiModelProperty("收货县/镇编码")
    private String receiverTownCode;

    @ApiModelProperty("收货人姓名")
    private String receiverName;

    @ApiModelProperty("收货人详细地址信息")
    private String receiverInfo;

    @ApiModelProperty("收货地分支编号")
    private String receiveBranchCode;

    @ApiModelProperty("收货地分支名称")
    private String receiveBranchName;

    @ApiModelProperty("发票信息 json格式")
    private String invoiceInfo;

    @ApiModelProperty("促销信息备注")
    private String promotionInfo;

    @ApiModelProperty("是否是电子面单")
    private Integer isDzmd;

    @ApiModelProperty("发票状态0-未开、1-已开")
    private Integer invoiceStatus;

    @ApiModelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "开票时间")
    private Date invoiceTime;

    @ApiModelProperty("商家优惠券优惠金额")
    private BigDecimal storeVoucherAmount;

    @ApiModelProperty("平台优惠券优惠金额")
    private BigDecimal platformVoucherAmount;

    @ApiModelProperty("邀请人分享码")
    private String shareCode;

    @ApiModelProperty("传输金蝶 1:是 0:否 默认否")
    private Integer jindieTransFlag;

    @ApiModelProperty("店铺金蝶组织名称")
    private String storeKingdeeOrgName;
    
    @ApiModelProperty("扩展信息")
    private String extraInfo;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "是否可用:1-是 0-否")
    private Integer enabledFlag;

    @ApiModelProperty("员工工号")
    private String userCode;

    @ApiModelProperty(value = "分类名称编码")
    private String categoryCode;

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    @ApiModelProperty("采购订单-入驻类型，见PurchaseOrderSettledTypeEnum")
    private Integer settledType;

    @ApiModelProperty("采购订单-企业名称")
    private String companyName;

    @ApiModelProperty("采购订单-店铺所属分支编码")
    private String storeBranch;

    @ApiModelProperty("采购订单-店铺所属分支名称")
    private String storeBranchName;

    @ApiModelProperty("店铺所属区域编码")
    private String storeAreaCode;

    @ApiModelProperty("店铺所属区域名")
    private String storeAreaName;

    @ApiModelProperty("技术服务费标识")
    private Boolean platformServiceRateFlag;

    @ApiModelProperty("自提点仓库编码")
    private String warehouseCode;

    @ApiModelProperty("(实仓)仓库编码")
    private String  actualWarehouseCode;

    @ApiModelProperty("自提点id")
    private Long pointId;

    @ApiModelProperty("自提点名称")
    private String pointName;

    @ApiModelProperty(value = "结算方式：0-未知 1 -正常结算 2-协议结算")
    private Integer settleChannel;

    @ApiModelProperty(value = "禁用渠道列表:DisableChannelDTO")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject disableChannelList;

    @ApiModelProperty(value = "管护类型 1-员工本人管护 2-客户信息管护 3-店铺所属分支 4-手工维护管护")
    private Integer manageType;

    @ApiModelProperty(value = "客户签收单图片")
    private String signInImageUrl;

    @ApiModelProperty(value = "客户合同")
    private String customerContract;

    @ApiModelProperty(value = "金蝶库存管理标识(1-按店铺,2-按分支,3-按仓库)")
    private Integer kingdeeStockPushMode;

    @ApiModelProperty("订单签收码")
    private String receiveCode;

    @ApiModelProperty(value = "签收确认书上传状态,0-未上传,1-已上传")
    private Integer receiveMaterialStatus;

    @ApiModelProperty(value = "签收确认方式,0-未指定，1-纸质签收,2-电子签收")
    private Integer receiveConfirmDocType;

    @ApiModelProperty(value = "签收确认资料地址")
    private String receiveConfirmDocUrl;

}