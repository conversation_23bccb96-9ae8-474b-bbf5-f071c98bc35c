package com.cfpamf.ms.mallorder.v2.service.impl;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.mapper.OrderRefundRecordMapper;
import com.cfpamf.ms.mallorder.model.OrderAfterServiceModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderRefundRecordPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.v2.builder.OrderRefundRecordBuilder;
import com.cfpamf.ms.mallorder.v2.domain.bo.OrderPayRecordBO;
import com.cfpamf.ms.mallorder.v2.service.OrderPayRecordService;
import com.cfpamf.ms.mallorder.v2.service.OrderRefundRecordService;
import com.cfpamf.ms.mallorder.v2.service.RefundService;
import com.cfpamf.ms.mallorder.vo.OrderRefundRecordVO;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import com.slodon.bbc.core.util.AssertUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class OrderRefundRecordServiceImpl extends ServiceImpl<OrderRefundRecordMapper, OrderRefundRecordPO>
    implements OrderRefundRecordService {

    public static final String FULL_FORMATTER_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern(FULL_FORMATTER_PATTERN);

    @Autowired
    private ShardingId shardingId;

    @Autowired
    private OrderAfterServiceModel orderAfterServiceModel;

    @Autowired
    private RefundService refundService;

    @Autowired
    private OrderPayRecordService orderPayRecordService;
    
    @Transactional
    @Override
    public void saveBatch(OrderReturnPO orderReturnPO, OrderPO orderPODb) {
        if (!OrderTypeEnum.isPresell(orderPODb.getOrderType())) {
            log.warn("AfsSn:{}，orderType:{} 售后订单退款，退款单类型不为组合退款，无需组合退款 || 订单类型不为预售订单", orderReturnPO.getAfsSn(),orderPODb.getOrderType());
            return;
        }
//        // 如果是付款中的状态，则不进行后续操作
//        if (orderPODb.getOrderState().equals(OrderStatusEnum.DEAL_PAY.getValue())){
//            log.warn("AfsSn:{}，orderType:{} 售后订单退款，退款单类型不为组合退款，无需组合退款 || 订单状态为付款中", orderReturnPO.getAfsSn(),orderPODb.getOrderType());
//            return;
//        }
        List<OrderPayRecordBO> orderPayRecordList =
            refundService.payOrderRefundAmoutCalculate(orderPODb.getPaySn(), orderReturnPO);
        // 更新订单支付记录 可退款余额，乐观锁
        orderPayRecordService.updateBalanceBatchByIds(orderPayRecordList);
        // 注意，试算时候的 其他赔偿和客户承担 不要由于退款单上的退款类型改变为组合退款影响到记录值
        List<OrderRefundRecordPO> data = OrderRefundRecordBuilder.buildOrderRefundRecordPO(shardingId,
            orderAfterServiceModel, orderPODb, orderPayRecordList, orderReturnPO);
        boolean status = super.saveBatch(data);
        AssertUtil.isTrue(!status, "抱歉，组合支付退款明细保存失败！");
    }

    @Override
    public List<OrderRefundRecordPO> queryWaitRefundOrderByAfsSn(String afsSn) {
        if (StringUtils.isEmpty(afsSn)) {
            return null;
        }
        QueryWrapper<OrderRefundRecordPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("afs_sn", afsSn);
        //为避免退款中，状态卡住，退款申请只要失败了，所有数据会重新发起，由下游幂等。
        queryWrapper.ne("refund_status", CommonConst.REFUND_STATUS_3);// 已退款
        return super.list(queryWrapper);
    }
    
    @Override
    public List<OrderRefundRecordPO> queryRefundOrderByAfsSn(String afsSn) {
        if (StringUtils.isEmpty(afsSn)) {
            return null;
        }
        QueryWrapper<OrderRefundRecordPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("afs_sn", afsSn);
        return super.list(queryWrapper);
    }
    
    @Override
    public List<OrderRefundRecordPO> queryNoCloseRefundOrderByPayNo(String payNo) {
        if (StringUtils.isEmpty(payNo)) {
            return null;
        }
        QueryWrapper<OrderRefundRecordPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("pay_no", payNo);
        queryWrapper.ne("refund_status", CommonConst.REFUND_STATUS_0);
        return super.list(queryWrapper);
    }
    
    @Override
    public OrderRefundRecordPO queryNoCloseRefundOrderByRefundNo(String refundNo) {
        if (StringUtils.isEmpty(refundNo)) {
            return null;
        }
        QueryWrapper<OrderRefundRecordPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("refund_no", refundNo);
        queryWrapper.ne("refund_status", CommonConst.REFUND_STATUS_0);
        return super.getOne(queryWrapper);
    }
    
    @Override
    public OrderRefundRecordPO queryLoanRefundByAfsSn(String afsSn) {
        if (StringUtils.isEmpty(afsSn)) {
            return null;
        }
        QueryWrapper<OrderRefundRecordPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("afs_sn", afsSn);
        //次方法基于预售模式，2笔支付，尾款才能用贷款 只有1笔记录
        queryWrapper.in("payment_code", PayMethodEnum.getLoanPay());
        queryWrapper.last("limit 1");
        return super.getOne(queryWrapper);
    }

    @Override
    public List<OrderRefundRecordPO> queryLoanWaitRefundSByOrderSn(String orderSn) {
        if (StringUtils.isEmpty(orderSn)) {
            return null;
        }
        QueryWrapper<OrderRefundRecordPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("order_sn", orderSn);
        queryWrapper.in("payment_code", PayMethodEnum.getLoanPay());
        queryWrapper.ne("refund_status", CommonConst.REFUND_STATUS_0);
        return super.list(queryWrapper);
    }

    @Override
    public String queryLoanRefundPayNoByAfsSn(String afsSn) {
        OrderRefundRecordPO data = this.queryLoanRefundByAfsSn(afsSn);
        if (ObjectUtils.isEmpty(afsSn)) {
            return null;
        }
        return data.getPayNo();
    }

    @Override
    public Integer countLoanRefundPayNoByAfsSn(String afsSn) {
        if (StringUtils.isEmpty(afsSn)) {
            return null;
        }
        QueryWrapper<OrderRefundRecordPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("afs_sn", afsSn);
        queryWrapper.in("payment_code", PayMethodEnum.getLoanPay());
        return super.count(queryWrapper);
    }
    
    @Override
    public BigDecimal refundActualTotalAmountByAfsSn(String afsSn) {
        if (StringUtils.isEmpty(afsSn)) {
            return null;
        }
        QueryWrapper<OrderRefundRecordPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("afs_sn", afsSn);
        queryWrapper.ne("refund_status", CommonConst.REFUND_STATUS_0);
        queryWrapper.select("ifnull(sum(actual_amount),0) as refundActualAmount");     
        Map<String, Object> dataMap = this.getMap(queryWrapper);
        return  (BigDecimal)dataMap.get("refundActualAmount");
    }
    
    @Override
    @Transactional
    public boolean updateRefundTypeById(Long id, Integer refundType) {
        if (ObjectUtils.isEmpty(id)) {
            return false;
        }
        if (ObjectUtils.isEmpty(refundType)) {
            return false;
        }
        OrderRefundRecordPO entity = new OrderRefundRecordPO();
        entity.setId(id);
        entity.setRefundType(refundType);
        entity.setUpdateTime(new Date());
        QueryWrapper<OrderRefundRecordPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("id", id);
        return super.update(entity, queryWrapper);
    }

    @Override
    public Boolean existLoanRefundTypeDisaffinityByAfsSn(String afsSn, Integer refundType) {
        if (StringUtils.isEmpty(afsSn)) {
            return null;
        }
        if (ObjectUtils.isEmpty(refundType)) {
            return null;
        }
        QueryWrapper<OrderRefundRecordPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("afs_sn", afsSn);
        queryWrapper.in("payment_code", PayMethodEnum.getLoanPay());
        List<OrderRefundRecordPO> data = super.list(queryWrapper);
        for (OrderRefundRecordPO orderRefundRecordPO : data) {
            if (!orderRefundRecordPO.getRefundType().equals(refundType)) {
                return false;
            }
        }
        return true;
    }
    
    @Override
    @Transactional
    public boolean updateRefundActualAmountById(Long id, BigDecimal actualAmount, BigDecimal oldActualAmount) {
        if (ObjectUtils.isEmpty(id)) {
            return false;
        }
        if (ObjectUtils.isEmpty(actualAmount)) {
            return false;
        }
        OrderRefundRecordPO entity = new OrderRefundRecordPO();
        entity.setId(id);
        entity.setActualAmount(actualAmount);
        entity.setUpdateTime(new Date());
        QueryWrapper<OrderRefundRecordPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("id", id);
        queryWrapper.eq("actual_amount",oldActualAmount);
        return super.update(entity, queryWrapper);
    }
    
    @Override
    @Transactional
    public boolean updateRefundProceedByRefundNo(String refundNo) {
        if (ObjectUtils.isEmpty(refundNo)) {
            return false;
        }
        log.info("updateRefundProceedByRefundNo开始更新退款结果记录：{}",refundNo);
        OrderRefundRecordPO entity = new OrderRefundRecordPO();
        entity.setRefundStatus(CommonConst.REFUND_STATUS_2);
        entity.setUpdateTime(new Date());
        QueryWrapper<OrderRefundRecordPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("refund_no", refundNo);
        //待退款，可以更新为退款中
        queryWrapper.eq("refund_status", CommonConst.REFUND_STATUS_1);
        boolean status= super.update(entity, queryWrapper);
        log.info("updateRefundProceedByRefundNo开始更新退款结果记录refundNo：{} status:{}",refundNo,status);
        BizAssertUtil.isTrue(!status, "退款状态更新失败，请稍后再试！");
        return status;
    }
    
    @Override
    @Transactional
    public boolean updateRefundSuccessByRefundNo(String refundNo) {
        if (ObjectUtils.isEmpty(refundNo)) {
            return false;
        }
        log.info("updateRefundSuccessByRefundNo开始更新退款结果记录：{}",refundNo);
        OrderRefundRecordPO entity = new OrderRefundRecordPO();
        entity.setRefundNo(refundNo);
        entity.setRefundStatus(CommonConst.REFUND_STATUS_3);
        entity.setUpdateTime(new Date());
        QueryWrapper<OrderRefundRecordPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("refund_no", refundNo);
        //未退款成功，2为后期补充状态，等1的状态数据执行完结，更新条件就不需要1了
        queryWrapper.in("refund_status", CommonConst.REFUND_STATUS_1,CommonConst.REFUND_STATUS_2);
        boolean status= super.update(entity, queryWrapper);
        log.info("updateRefundSuccessByRefundNo开始更新退款结果记录refundNo：{} status:{}",refundNo,status);
        BizAssertUtil.isTrue(!status, "退款状态更新失败，请稍后再试！");
        return status;
    }
    
    @Override
    @Transactional
    public boolean updateRefundCloseByAfsSn(String afsSn) {
        if (ObjectUtils.isEmpty(afsSn)) {
            return false;
        }
        OrderRefundRecordPO entity = new OrderRefundRecordPO();
        entity.setAfsSn(afsSn);
        entity.setRefundStatus(CommonConst.REFUND_STATUS_0);
        entity.setUpdateTime(new Date());
        QueryWrapper<OrderRefundRecordPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("afs_sn", afsSn);
        //待退款
        queryWrapper.eq("refund_status", CommonConst.REFUND_STATUS_1);
        boolean status= super.update(entity, queryWrapper);
        BizAssertUtil.isTrue(!status, "退单关闭失败，请稍后再试！");
        return status;
    }
    
    @Override
    public boolean verifyRefundOrderAllFinishByAfsSn(String afsSn) {
        if (StringUtils.isEmpty(afsSn)) {
            return false;
        }
        QueryWrapper<OrderRefundRecordPO> queryWrapper = Wrappers.query();
        queryWrapper.eq("afs_sn", afsSn);
        //存在退款状态：不为退款成功的记录
        queryWrapper.ne("refund_status", CommonConst.REFUND_STATUS_3);
        int total = super.count(queryWrapper);
        log.info("verifyRefundOrderAllFinishByAfsSnafsSn：{} total:{}",afsSn,total);
        return total == 0 ? true : false;
    }

    @Override
    public List<OrderRefundRecordVO> listByAfsSn(String afsSn,Integer refundStatus) {
        LambdaQueryWrapper<OrderRefundRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderRefundRecordPO::getAfsSn,afsSn);
        if(Objects.nonNull(refundStatus)){
            queryWrapper.eq(OrderRefundRecordPO::getRefundStatus,refundStatus);
        }
        List<OrderRefundRecordPO> entityList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        List<OrderRefundRecordVO> voList = new ArrayList<>(entityList.size());
        for (OrderRefundRecordPO entity : entityList) {
            voList.add(this.entity2VO(entity));
        }
        return voList;
    }

    @Override
    public List<OrderRefundRecordVO> listByPayNo(String payNo,List<Integer> refundStatusList) {
        LambdaQueryWrapper<OrderRefundRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderRefundRecordPO::getPayNo,payNo);
        if(CollectionUtils.isNotEmpty(refundStatusList)){
            queryWrapper.in(OrderRefundRecordPO::getRefundStatus,refundStatusList);
        }
        List<OrderRefundRecordPO> entityList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        List<OrderRefundRecordVO> voList = new ArrayList<>(entityList.size());
        for (OrderRefundRecordPO entity : entityList) {
            voList.add(this.entity2VO(entity));
        }
        return voList;
    }

    @Override
    public List<OrderRefundRecordVO> listByOrderSn(String orderSn,List<Integer> refundStatusList) {
        LambdaQueryWrapper<OrderRefundRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderRefundRecordPO::getOrderSn,orderSn);
        if(CollectionUtils.isNotEmpty(refundStatusList)){
            queryWrapper.in(OrderRefundRecordPO::getRefundStatus,refundStatusList);
        }
        List<OrderRefundRecordPO> entityList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        List<OrderRefundRecordVO> voList = new ArrayList<>(entityList.size());
        for (OrderRefundRecordPO entity : entityList) {
            voList.add(this.entity2VO(entity));
        }
        return voList;
    }

    @Override
    public OrderRefundRecordVO findByRefundNo(String refundNo) {
    	if(StringUtils.isEmpty(refundNo)){
           return null;
        }
        LambdaQueryWrapper<OrderRefundRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderRefundRecordPO::getRefundNo,refundNo);
        OrderRefundRecordPO entity = this.getOne(queryWrapper);
        if(ObjectUtils.isEmpty(entity)){
            return null;
         }
        return this.entity2VO(entity);
    }

    private OrderRefundRecordVO entity2VO(OrderRefundRecordPO entity) {
        OrderRefundRecordVO vo = new OrderRefundRecordVO();
        vo.setId(entity.getId());
        vo.setRefundNo(entity.getRefundNo());
        vo.setOrderSn(entity.getOrderSn());
        vo.setAfsSn(entity.getAfsSn());
        vo.setPayNo(entity.getPayNo());
        vo.setPaySn(entity.getPaySn());
        vo.setRefundType(entity.getRefundType());
        vo.setRefundTypeValue(entity.getRefundTypeValue());
        vo.setAmount(entity.getAmount());
        vo.setActualAmount(entity.getActualAmount());
        vo.setChannelServiceFee(entity.getChannelServiceFee());
        vo.setChannelServiceRate(entity.getChannelServiceRate());
        vo.setPaymentCode(entity.getPaymentCode());
        vo.setPaymentName(entity.getPaymentName());
        vo.setRefundStatus(entity.getRefundStatus());
        if(Objects.nonNull(entity.getRefundTime())){
            vo.setRefundTime(entity.getRefundTime().format(DATETIME_FORMATTER));
        }
        return vo;
    }
}
