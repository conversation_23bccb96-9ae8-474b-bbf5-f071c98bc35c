package com.cfpamf.ms.mallorder.scheduler;

import com.cfpamf.ms.mallorder.service.IBzBankPayService;
import com.slodon.bbc.core.util.AssertUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Create 2021-09-17 14:36
 * @Description :0 0/30 * * * ? 自动付款
 */
@Component
@Slf4j
public class OrderAutoPayJob {

    @Autowired
    private IBzBankPayService iBzBankPayService;

    @XxlJob(value = "OrderAutoPayJob")
    public ReturnT<String> execute(String s) {
        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {0}", LocalDateTime.now());
        log.info("jobSystemCancelOrder() start");
        try {
            boolean jobResult = iBzBankPayService.orderAutoPayJob();
            AssertUtil.isTrue(!jobResult, "[OrderAutoPayJob] 定时任务系统自动付款失败");
        } catch (Exception e) {
            log.error("OrderAutoPayJob()", e);
        }
        XxlJobHelper.log("finish job at local time: {0}  cost:{1}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);

        return new ReturnT("SUCCESS");
    }

    @XxlJob(value = "OrderAutoPaySyncJob")
    public ReturnT<String> executeOrderAutoPaySyncJob(String s) {
        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {0}", LocalDateTime.now());
        log.info("OrderAutoPaySyncJob() start");
        try {
            boolean jobResult = iBzBankPayService.executeOrderAutoPaySyncJob();
            AssertUtil.isTrue(!jobResult, "[OrderAutoPaySyncJob] 定时任务系统自动付款失败");
        } catch (Exception e) {
            log.error("OrderAutoPaySyncJob()", e);
        }
        XxlJobHelper.log("finish job at local time: {0}  cost:{1}",
                LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);

        return new ReturnT("SUCCESS");
    }



}
