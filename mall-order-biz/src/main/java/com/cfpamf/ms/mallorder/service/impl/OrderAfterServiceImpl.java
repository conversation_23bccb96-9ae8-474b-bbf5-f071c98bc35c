package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdfinance.ms.card.facade.model.response.cardUse.CardRefundCheckResponse;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.mallpayment.facade.request.loan.SetlTryResultVo;
import com.cfpamf.mallpayment.facade.vo.CDMallRefundTryResultVO;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.constant.OrdersAfsConst;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.constant.OrderPerformanceConstant;
import com.cfpamf.ms.mallorder.constant.ProofConst;
import com.cfpamf.ms.mallorder.dto.ExchangeOrderReturnDetailDTO;
import com.cfpamf.ms.mallorder.dto.OrderAfterDTO;
import com.cfpamf.ms.mallorder.dto.UserDTO;
import com.cfpamf.ms.mallorder.enums.OrderGroupBuyingEnum;
import com.cfpamf.ms.mallorder.enums.OrderPerformanceChannelEnum;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.erp.ERPIntegration;
import com.cfpamf.ms.mallorder.integration.wms.WmsConst;
import com.cfpamf.ms.mallorder.integration.wms.WmsIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderAfterMapper;
import com.cfpamf.ms.mallorder.mapper.OrderRefundRecordMapper;
import com.cfpamf.ms.mallorder.model.*;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.admin.AdminAfterSaleListRequest;
import com.cfpamf.ms.mallorder.req.query.OrderGroupBuyingBindQuery;
import com.cfpamf.ms.mallorder.req.seller.SellerAfterSaleListRequest;
import com.cfpamf.ms.mallorder.request.OrderAfterSaleLogExample;
import com.cfpamf.ms.mallorder.request.OrderProductExample;
import com.cfpamf.ms.mallorder.request.OrderReturnExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.domain.dto.OrderReturnDetailDTO;
import com.cfpamf.ms.mallorder.validation.OrderReturnValidation;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallshop.api.StoreAddressFeignClient;
import com.cfpamf.ms.mallshop.api.StoreIsolateWhiteListFeignClient;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.cfpamf.ms.mallshop.resp.StoreAddress;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.google.common.collect.Lists;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.StringUtil;
import com.slodon.bbc.core.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单正向操作 service
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/30 17:02
 */
@Slf4j
@Service
public class OrderAfterServiceImpl extends ServiceImpl<OrderAfterMapper, OrderAfterPO> implements IOrderAfterService {

    @Resource
    private IOrderProductService orderProductService;
    @Resource
    private IOrderService orderService;

    @Resource
    private OrderModel orderModel;
    @Resource
    private OrderProductModel orderProductModel;
    @Resource
    private OrderReturnModel orderReturnModel;
    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;
    @Resource
    private OrderAfterSaleLogModel orderAfterSaleLogModel;

    @Resource
    private StoreAddressFeignClient storeAddressFeignClient;
    @Resource
    private MemberFeignClient memberFeignClient;
    @Resource
    private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private OrderRefundRecordMapper orderRefundRecordMapper;

    @Resource
    private OrderReturnValidation orderReturnValidation;

    @Resource
    private IOrderExchangeDetailService orderExchangeDetailService;

    @Resource
    private IProofService proofService;

    @Autowired
    private IOrderReturnService orderReturnService;

    @Autowired
    private IFrontAfterSaleApplyService frontAfterSaleApplyService;

    @Autowired
    private IOrderGroupBuyingBindService orderGroupBuyingBindService;

    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private OrderLocalUtils orderLocalUtils;

    @Resource
    private WmsIntegration wmsIntegration;

    @Resource
    private ERPIntegration erpIntegration;

    @Value("${refund.erp.check.storeId:283303}")
    private String storeIds;

    @Override
    public List<OrderAfterPO> listByOrderSn(String orderSn) {
        LambdaQueryWrapper<OrderAfterPO> orderProductQuery = new LambdaQueryWrapper<>();
        orderProductQuery.eq(OrderAfterPO::getOrderSn, orderSn);
        orderProductQuery.eq(OrderAfterPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return this.list(orderProductQuery);
    }

    @Override
    public boolean isAllReturnApplied(List<OrderAfterDTO.AfterProduct> afsDTOs, String orderSn) {

        List<OrderAfterPO> afsPOs = new ArrayList<>(afsDTOs.size());

        afsDTOs.forEach(o -> {
            OrderAfterPO orderAfterPO = new OrderAfterPO();
            orderAfterPO.setOrderProductId(o.getOrderProductId());
            orderAfterPO.setAfsNum(o.getAfsNum());
            afsPOs.add(orderAfterPO);
        });

        LambdaQueryWrapper<OrderProductPO> orderProductQuery = new LambdaQueryWrapper();
        orderProductQuery.eq(OrderProductPO::getOrderSn, orderSn);
        orderProductQuery.eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        orderProductQuery.eq(OrderProductPO::getIsGift, OrderConst.IS_GIFT_NO);
        List<OrderProductPO> orderProductPOS = orderProductService.list(orderProductQuery);

        return isAllReturnApplied(afsPOs, orderProductPOS);
    }

    @Override
    public boolean isAllReturnApplied(List<OrderAfterPO> afs, List<OrderProductPO> ops) {

        if (CollectionUtils.isEmpty(afs)) {
            return false;
        }

        if (CollectionUtils.isEmpty(ops)) {
            return false;
        }

     // 商品明细已退数量  key->orderProductId  value->汇总数量
        Map<String, Integer> aftNumMap = new HashMap<>();

        for (OrderAfterPO orderAfterPO : afs) {
            String orderProductId = String.valueOf(orderAfterPO.getOrderProductId());
            if (aftNumMap.containsKey(orderProductId)) {
                aftNumMap.put(orderProductId,
                        aftNumMap.get(orderProductId) + orderAfterPO.getAfsNum());
            } else {
                aftNumMap.put(orderProductId, orderAfterPO.getAfsNum());
            }
        }

        for (OrderProductPO orderProductPO : ops) {
            Integer aftNum = aftNumMap.get(String.valueOf(orderProductPO.getOrderProductId()));

            aftNum = aftNum == null ? 0 : aftNum;

            if (aftNum + orderProductPO.getReturnNumber() < orderProductPO.getProductNum()) {
                return false;
            }

            if (aftNum + orderProductPO.getReturnNumber() > orderProductPO.getProductNum()) {
                throw new BusinessException("申请退订数量超过可退订数量，请检查 ");
            }
        }

        return true;
    }

    @Override
    public PageVO<OrderReturnVOV2> adminAfterSaleList(PagerInfo pager, AdminAfterSaleListRequest adminAfterSaleListRequest) {
        OrderReturnExample example = this.convertAdminAfterSaleListRequest2OrderReturnExample(adminAfterSaleListRequest);

        // 查待平台审核的列表信息，传的 audit
        if (!StringUtil.isEmpty(adminAfterSaleListRequest.getType()) && "audit".equals(adminAfterSaleListRequest.getType())) {
            if (!StringUtil.isNullOrZero(adminAfterSaleListRequest.getState())) {
                if (adminAfterSaleListRequest.getState().equals(OrdersAfsConst.RETURN_STATE_203)) {
                    //待处理
                    example.setStateIn(OrdersAfsConst.RETURN_STATE_200 + "," + OrdersAfsConst.RETURN_STATE_203);
                } else {
                    //已完成
                    example.setState(OrdersAfsConst.RETURN_STATE_300);
                }
            } else {
                example.setStateIn(OrdersAfsConst.RETURN_STATE_200 + "," + OrdersAfsConst.RETURN_STATE_203 + "," + OrdersAfsConst.RETURN_STATE_300);
            }
            if (OrderReturnStatus.STORE_AGREE_RETURN.getValue().equals(adminAfterSaleListRequest.getState())) {
                example.setState(null);
                example.setStateIn(OrderReturnStatus.STORE_AGREE_RETURN.getValue() + "," + OrderReturnStatus.SELF_PICKUP_POINT_RETURN_APPLY.getValue());
            }
        } else if (!StringUtil.isEmpty(adminAfterSaleListRequest.getType()) && "storeAudit".equals(adminAfterSaleListRequest.getType())) {
            example.setStateIn(OrdersAfsConst.RETURN_STATE_100 + "," + OrdersAfsConst.RETURN_STATE_101 + "," + OrdersAfsConst.RETURN_STATE_102);
            // 查询指定店铺的数据，限内部人员审批待商家审批数据
            List<Long> storeIds = storeIsolateWhiteListFeignClient.whiteListStoreIds(WhiteListEnum.INNER_REFUND);
            if (CollectionUtils.isEmpty(storeIds)) {
                return new PageVO<>(Lists.newArrayList(), pager);
            }
            example.setStoreIdIn(org.apache.commons.lang3.StringUtils.join(storeIds.toArray(), ","));
            example.setState(adminAfterSaleListRequest.getState());
        } else {
            if (!StringUtil.isNullOrZero(adminAfterSaleListRequest.getState())) {
                if (adminAfterSaleListRequest.getState().equals(OrdersAfsConst.RETURN_STATE_100)) {
                    example.setStateIn(OrdersAfsConst.RETURN_STATE_100 + "," + OrdersAfsConst.RETURN_STATE_101);
                } else if (adminAfterSaleListRequest.getState().equals(OrdersAfsConst.RETURN_STATE_203)) {
                    example.setStateIn(OrdersAfsConst.RETURN_STATE_200 + "," + OrdersAfsConst.RETURN_STATE_203);
                } else {
                    example.setState(adminAfterSaleListRequest.getState());
                }
            }
        }

        List<OrderReturnVOV2> vos = orderReturnModel.getOrderReturnListWithJoin(example, pager);
        return new PageVO<>(vos, pager);
    }

    private OrderReturnExample convertAdminAfterSaleListRequest2OrderReturnExample(AdminAfterSaleListRequest searchRequest) {
        OrderReturnExample example = new OrderReturnExample();
        example.setOrderSnLike(searchRequest.getOrderSn());
        example.setAfsSnLike(searchRequest.getAfsSn());
        example.setMemberNameLike(searchRequest.getMemberName());
        example.setStoreNameLike(searchRequest.getStoreName());
        example.setReturnType(searchRequest.getReturnType());
        example.setRefundType(searchRequest.getRefundType());
        example.setRefundStartTime(searchRequest.getRefundStartTime());
        example.setRefundEndTime(searchRequest.getRefundEndTime());
        example.setPlatformAuditStartTime(searchRequest.getPlatformAuditStartTime());
        example.setPlatformAuditEndTime(searchRequest.getPlatformAuditEndTime());
        example.setChannel(searchRequest.getChannel());
        example.setCustomerId(searchRequest.getCustomerId());
        example.setCustomerName(searchRequest.getCustomerName());
        example.setGoodsName(searchRequest.getGoodsName());
        example.setRecommendStoreId(searchRequest.getRecommendStoreId());
        example.setApplyTimeAfter(searchRequest.getStartTime());
        example.setApplyTimeBefore(searchRequest.getEndTime());
        example.setOrderPattern(searchRequest.getOrderPattern());
        example.setStoreId(searchRequest.getStoreId());
        example.setManagerNameLike(searchRequest.getManagerName());
        example.setBranchCodeIn(searchRequest.getBranchCodeList());
        example.setProductDeliveryState(searchRequest.getProductDeliveryState());
        example.setManagerNameList(searchRequest.getManagerNameList());
        return example;
    }

    @Override
    public PageVO<OrderReturnVOV2> sellerAfterSaleList(Vendor vendor, PagerInfo pager, SellerAfterSaleListRequest request) {
        OrderReturnExample orderReturnExample = this.convertSellerAfterSaleListRequest2OrderReturnExample(vendor, request);
        List<OrderReturnVOV2> vos = orderReturnModel.getOrderReturnListWithJoin(orderReturnExample, pager);
        return new PageVO<>(vos, pager);
    }

    /**
     * 商家端查询退款列表入参转换
     *
     * @param searchRequest     查询请求
     * @param vendor            商家信息
     * @return  OrderReturnExample
     */
    private OrderReturnExample convertSellerAfterSaleListRequest2OrderReturnExample(Vendor vendor, SellerAfterSaleListRequest searchRequest) {
        OrderReturnExample example = new OrderReturnExample();
        //非配销查看则校验权限
        if (searchRequest.getDistribution() == null || searchRequest.getDistribution().equals(CommonEnum.NO.getCode())) {
            example.setStoreId(vendor.getStoreId());
        } else {
            example.setRecommendStoreId(vendor.getStoreId().toString());
            example.setOrderType(OrderTypeEnum.ORDER_TYPE_7.getValue());
        }
        example.setReturnType(searchRequest.getReturnType());
        example.setApplyTimeAfter(searchRequest.getStartTime());
        example.setApplyTimeBefore(searchRequest.getEndTime());
        example.setState(searchRequest.getState());
        if (OrderReturnStatus.STORE_AGREE_RETURN.getValue().equals(example.getState())) {
            example.setState(null);
            example.setStateIn(OrderReturnStatus.STORE_AGREE_RETURN.getValue() + "," + OrderReturnStatus.SELF_PICKUP_POINT_RETURN_APPLY.getValue());
        }
        example.setOrderSnLike(searchRequest.getOrderSn());
        example.setAfsSnLike(searchRequest.getAfsSn());
        example.setMemberNameLike(searchRequest.getMemberName());
        example.setRefundType(searchRequest.getRefundType());
        example.setRefundStartTime(searchRequest.getRefundStartTime());
        example.setRefundEndTime(searchRequest.getRefundEndTime());
        example.setCustomerId(searchRequest.getCustomerId());
        example.setCustomerName(searchRequest.getCustomerName());
        example.setGoodsName(searchRequest.getGoodsName());
        example.setBranchNameLike(searchRequest.getBranchName());
        example.setAreaNameLike(searchRequest.getAreaName());
        example.setStoreNameLike(searchRequest.getStoreName());
        example.setOrderPattern(searchRequest.getOrderPattern());
        example.setUserMobile(searchRequest.getUserMobile());
        example.setExchangeSn(searchRequest.getExchangeSn());
        example.setExchangeOrderState(searchRequest.getExchangeOrderState());
        example.setJdInterceptStatus(searchRequest.getJdInterceptStatus());
        example.setBranchCodeIn(searchRequest.getBranchCodeList());
        return example;
    }

    @Override
    public OrderReturnDetailVO sellerAfterSaleDetail(String afsSn, Vendor vendor, Integer distribution) {
        AssertUtil.notEmpty(afsSn, "退款编号不能为空");
        OrderReturnPO orderReturnPO = orderReturnModel.getOrderReturnByAfsSn(afsSn);
        AssertUtil.notNull(orderReturnPO, "获取退货信息为空，请重试！");
        //非配销查看则校验权限
        OrderLocalUtils.checkOrderPermissions(vendor.getStoreId(),orderReturnPO.getStoreId(),distribution);
        //查询售后信息
        OrderAfterPO orderAfterServicePO = orderAfterServiceModel.getAfterServiceByAfsSn(orderReturnPO.getAfsSn());
        //查询订单货品
        OrderProductPO orderProductPO = orderProductModel.getOrderProductByOrderProductId(orderAfterServicePO.getOrderProductId());
        AssertUtil.notNull(orderProductPO, "获取订单货品信息为空，请重试");

        //查询售后日志
        OrderAfterSaleLogExample example = new OrderAfterSaleLogExample();
        example.setAfsSn(afsSn);
        example.setOrderBy("create_time asc");
        List<OrderAfterSaleLogPO> logList = orderAfterSaleLogModel.getOrderAfterSaleLogList(example, null);
        orderAfterServiceModel.dealLogUserName(logList);
        AssertUtil.notEmpty(logList, "获取售后记录为空，请重试");
        StoreAddress storeAddress = null;
        if (!StringUtils.isEmpty(orderAfterServicePO.getStoreAfsAddress())) {
            storeAddress = storeAddressFeignClient.getStoreAddressByAddressId(Integer.valueOf(orderAfterServicePO.getStoreAfsAddress()));
            if (Objects.isNull(storeAddress)) {
                ErpDepotVo erpDepotVo = erpIntegration.getDepotById(Long.parseLong(orderAfterServicePO.getStoreAfsAddress()));
                if (Objects.nonNull(erpDepotVo)) {
                    storeAddress = new StoreAddress();
                    storeAddress.setContactName(erpDepotVo.getName());
                    storeAddress.setTelphone(erpDepotVo.getMobile());
                    storeAddress.setAreaInfo(erpDepotVo.getProvinceDesc() + erpDepotVo.getCityDesc() + erpDepotVo.getCountyDesc());
                    storeAddress.setAddress(erpDepotVo.getAddress());
                }
            }
        }
        Result<CDMallRefundTryResultVO> tryCaculateResult = null;
        // 代还试算&还清后不试算（代还退款 和 组合退款中包含代还退款）
        List<OrderRefundRecordPO> assistPaymentRefundRecords = this.getCombinationRefundContainsAssistPayment(afsSn);
        if ((orderReturnPO.getRefundType().equals(RefundType.ASSIST_PAYMENT.getValue()) || (CollectionUtils.isNotEmpty(assistPaymentRefundRecords)))
                && ObjectUtils.isEmpty(orderReturnPO.getRefundEndTime())) {
            try {
                if (CollectionUtils.isNotEmpty(assistPaymentRefundRecords)) {
                    tryCaculateResult = orderAfterServiceModel.getTryCaculateResult(assistPaymentRefundRecords.get(0).getAmount(), afsSn);
                } else {
                    tryCaculateResult = orderAfterServiceModel.getTryCaculateResult(orderReturnPO.getReturnMoneyAmount().add(orderReturnPO.getReturnExpressAmount()), afsSn);
                }
            } catch (Exception e) {
                log.error("sellerAfterSaleDetail 商家端试算异常, 退款单号 ：{}", afsSn , e);
            }
        }
        CDMallRefundTryResultVO setlTryResultVo = ObjectUtils.isEmpty(tryCaculateResult) ? null : tryCaculateResult.getData();
        Member member = memberFeignClient.getMemberByMemberId(orderReturnPO.getMemberId());
        OrderPO orderPO = orderService.getByOrderSn(orderReturnPO.getOrderSn());
        ExchangeOrderReturnDetailDTO exchangeOrderReturnDetailDTO = null;
        //如果是换货单，需要查询换货订单详情
        if(OrdersAfsConst.RETURN_TYPE_3 == orderReturnPO.getReturnType() ){
            exchangeOrderReturnDetailDTO = orderExchangeDetailService.dealExchangeOrderReturnDetail(afsSn);
        }
        OrderReturnDetailVO detailVO = new OrderReturnDetailVO(orderPO, orderReturnPO, orderAfterServicePO, orderProductPO,
                storeAddress, setlTryResultVo, member, exchangeOrderReturnDetailDTO);
        //查询商品行是否关联赠品
        detailVO.setOrderGiftProductList(orderLocalUtils.getOrderGiftProductListVOS(orderPO.getOrderType(), orderPO.getOrderSn(),
                orderProductPO.getGiftGroup(), orderAfterServicePO.getGiftReturnOrderProductId()));

        List<OrderReturnDetailVO.ReturnLogVO> returnLogList = new ArrayList<>();
        logList.forEach(orderAfterSaleLog -> {
            returnLogList.add(new OrderReturnDetailVO.ReturnLogVO(orderAfterSaleLog));
        });
        detailVO.setReturnLogList(returnLogList);

        if (orderReturnPO.getState().equals(OrdersAfsConst.RETURN_STATE_100)
                || orderReturnPO.getState().equals(OrdersAfsConst.RETURN_STATE_101)) {
            //申请状态,商家处理截止时间
            int limitDay = Integer.parseInt(stringRedisTemplate.opsForValue().get("time_limit_of_afs_seller_audit"));
            detailVO.setDeadline(TimeUtil.getDayAgoDate(orderAfterServicePO.getBuyerApplyTime(), limitDay));
        } else if (orderReturnPO.getState().equals(OrdersAfsConst.RETURN_STATE_201)) {
            //商家同意退货退款，待买家发货
            int limitDay = Integer.parseInt(stringRedisTemplate.opsForValue().get("time_limit_of_afs_member_send"));
            detailVO.setDeadline(TimeUtil.getDayAgoDate(orderAfterServicePO.getStoreAuditTime(), limitDay));
        } else if (orderReturnPO.getState().equals(OrdersAfsConst.RETURN_STATE_102)) {
            //买家发货，待商家收货退款
            int limitDay = Integer.parseInt(stringRedisTemplate.opsForValue().get("time_limit_of_afs_seller_receive"));
            detailVO.setDeadline(TimeUtil.getDayAgoDate(orderAfterServicePO.getBuyerDeliverTime(), limitDay));
        }
        //如果是组合支付，获取退款详情
        if(RefundType.COMBINATION_REFUND.getValue().equals(orderReturnPO.getRefundType())){
            getDepositDetailList(detailVO);
        }
        //查询退款设置方式
        Integer settingSwitch = Integer.parseInt(stringRedisTemplate.opsForValue().get("refund_setting_switch"));
        if (settingSwitch == OrdersAfsConst.MONEY_RETURN_TYPE_TO_BALANCE) {
            detailVO.setReturnMethod("同意退款后，退款将退回到会员的余额");
        } else {
            detailVO.setReturnMethod("同意退款后，退款将按支付方式原路退回");
        }
        // 是否展示退款扣罚提示语句
        detailVO.setIsShowRefundPunishLabel(orderReturnValidation.refundPunishAmountSupport(orderReturnPO));
        //如果是线下退款，则获取现在退款的最新凭证
        if(RefundType.OFFLINE_REFUND.getValue() == detailVO.getRefundType()){
            List<String> proofImageList = proofService.getProofImages(ProofConst.OFFLINE_REFUND_PROOF_CODE, detailVO.getAfsSn());
            detailVO.setRefundVoucherImages(proofImageList);
        }

        // 只有自提订单，且在白名单内才展示轨迹
        if (OrderPatternEnum.SELF_LIFT.getValue().equals(orderPO.getOrderPattern())
                && storeIsolateWhiteListFeignClient.isStoreOnList(WhiteListEnum.DING_DING_REFUND_WHITE_LIST, orderPO.getStoreId())) {
            detailVO.setSelfLiftReturnTrack(true);
        }

        return detailVO;
    }

    private void getDepositDetailList(OrderReturnDetailVO detailVO) {
        List<OrderReturnDetailDTO> orderReturnDetailDTOList = new ArrayList<>();
        LambdaQueryWrapper<OrderRefundRecordPO> orderRefundRecordPOLambdaQueryWrapper = Wrappers.lambdaQuery(OrderRefundRecordPO.class);
        orderRefundRecordPOLambdaQueryWrapper.eq(OrderRefundRecordPO::getAfsSn, detailVO.getAfsSn());
        List<OrderRefundRecordPO> orderRefundRecordPOList = orderRefundRecordMapper.selectList(orderRefundRecordPOLambdaQueryWrapper);
        log.info("getDepositDetailList orderRefundRecordPOList = {}", JSONObject.toJSONString(orderRefundRecordPOList));
        orderRefundRecordPOList.sort(Comparator.comparing(OrderRefundRecordPO::getId).reversed());
        orderRefundRecordPOList.forEach(x ->{
            OrderReturnDetailDTO orderReturnDetailDTO = new OrderReturnDetailDTO();
            orderReturnDetailDTO.setReturnTypeValue(detailVO.getReturnTypeValue());
            orderReturnDetailDTO.setRefundType(x.getRefundType());
            orderReturnDetailDTO.setRefundTypeValue(x.getRefundTypeValue());
            orderReturnDetailDTO.setProductReturnAmount(x.getAmount());
            orderReturnDetailDTO.setRefundApplySumAmount(x.getAmount());
            orderReturnDetailDTO.setActualReturnMoneyAmount(x.getActualAmount());
            if (RefundType.isLoanPayRefundType(RefundType.value(x.getRefundType()))) {
                orderReturnDetailDTO.setOtherCompensationAmount(detailVO.getOtherCompensationAmount());
                orderReturnDetailDTO.setCustomerAssumeAmount(detailVO.getCustomerAssumeAmount());
                orderReturnDetailDTO.setInterestPayer(detailVO.getInterestPayer());
                orderReturnDetailDTO.setRefundPunishAmount(detailVO.getRefundPunishAmount());
            }
            orderReturnDetailDTOList.add(orderReturnDetailDTO);
        });
        detailVO.setReturnDetailList(orderReturnDetailDTOList);
    }

    @Override
    public OrderReturnDetailVO adminAfterSaleDetail(String afsSn) {
        AssertUtil.notEmpty(afsSn, "退款编号不能为空");

        OrderReturnPO orderReturnPO = orderReturnModel.getOrderReturnByAfsSn(afsSn);
        AssertUtil.notNull(orderReturnPO, "获取退货信息为空，请重试！");

        //查询订单信息
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderReturnPO.getOrderSn());

        //查询售后信息
        OrderAfterPO orderAfterServicePO = orderAfterServiceModel.getAfterServiceByAfsSn(orderReturnPO.getAfsSn());
        //查询订单货品
        OrderProductPO orderProductPO = orderProductModel.getOrderProductByOrderProductId(orderAfterServicePO.getOrderProductId());
        AssertUtil.notNull(orderProductPO, "获取订单货品信息为空，请重试");
        //查询售后日志
        OrderAfterSaleLogExample example = new OrderAfterSaleLogExample();
        example.setAfsSn(afsSn);
        example.setOrderBy("create_time asc");
        List<OrderAfterSaleLogPO> logList = orderAfterSaleLogModel.getOrderAfterSaleLogList(example, null);
        AssertUtil.notEmpty(logList, "获取售后记录为空，请重试");

        StoreAddress storeAddress = null;
        if (!StringUtils.isEmpty(orderAfterServicePO.getStoreAfsAddress())) {
            storeAddress = storeAddressFeignClient.getStoreAddressByAddressId(Integer.valueOf(orderAfterServicePO.getStoreAfsAddress()));
            if (Objects.isNull(storeAddress)) {
                ErpDepotVo erpDepotVo = erpIntegration.getDepotById(Long.parseLong(orderAfterServicePO.getStoreAfsAddress()));
                if (Objects.nonNull(erpDepotVo)) {
                    storeAddress = new StoreAddress();
                    storeAddress.setContactName(erpDepotVo.getName());
                    storeAddress.setTelphone(erpDepotVo.getMobile());
                    storeAddress.setAreaInfo(erpDepotVo.getProvinceDesc() + erpDepotVo.getCityDesc() + erpDepotVo.getCountyDesc());
                    storeAddress.setAddress(erpDepotVo.getAddress());
                }
            }
        }
        Result<CDMallRefundTryResultVO> tryCaculateResult = null;
        // 代还退款信息审批的时候展示（代还退款 和 组合退款中包含代还退款）
        List<OrderRefundRecordPO> assistPaymentRefundRecords = this.getCombinationRefundContainsAssistPayment(afsSn);
        if ((orderReturnPO.getRefundType().equals(RefundType.ASSIST_PAYMENT.getValue()) || (CollectionUtils.isNotEmpty(assistPaymentRefundRecords)))
                && ObjectUtils.isEmpty(orderReturnPO.getRefundEndTime())) {// 代还试算&还清后不试算
            try {
                if (CollectionUtils.isNotEmpty(assistPaymentRefundRecords)) {
                    tryCaculateResult = orderAfterServiceModel.getTryCaculateResult(assistPaymentRefundRecords.get(0).getAmount(), afsSn);
                } else {
                    tryCaculateResult = orderAfterServiceModel.getTryCaculateResult(orderReturnPO.getReturnMoneyAmount().add(orderReturnPO.getReturnExpressAmount()), afsSn);
                }
            } catch (Exception e) {
                log.error("adminAfterSaleDetail 试算异常, 退款单号：{}", afsSn, e);
            }
        }
        CDMallRefundTryResultVO setlTryResultVo = ObjectUtils.isEmpty(tryCaculateResult) ? null : tryCaculateResult.getData();
        Member member = memberFeignClient.getMemberByMemberId(orderReturnPO.getMemberId());
        ExchangeOrderReturnDetailDTO exchangeOrderReturnDetailDTO = null;
        //如果是换货单，需要查询换货订单详情
        if(OrdersAfsConst.RETURN_TYPE_3 == orderReturnPO.getReturnType() ){
            exchangeOrderReturnDetailDTO = orderExchangeDetailService.dealExchangeOrderReturnDetail(afsSn);
        }
        OrderReturnDetailVO detailVO = new OrderReturnDetailVO(orderPO, orderReturnPO, orderAfterServicePO, orderProductPO,
                storeAddress, setlTryResultVo, member, exchangeOrderReturnDetailDTO);
        detailVO.setChannel(orderPO.getChannel());
        //查询商品行是否关联赠品
        detailVO.setOrderGiftProductList(orderLocalUtils.getOrderGiftProductListVOS(orderPO.getOrderType(), orderPO.getOrderSn()
                , orderProductPO.getGiftGroup(), orderAfterServicePO.getGiftReturnOrderProductId()));

        List<OrderReturnDetailVO.ReturnLogVO> returnLogList = new ArrayList<>();
        logList.forEach(orderAfterSaleLog -> {
            returnLogList.add(new OrderReturnDetailVO.ReturnLogVO(orderAfterSaleLog));
        });
        detailVO.setReturnLogList(returnLogList);

        //如果是组合支付，获取退款详情
        if(RefundType.COMBINATION_REFUND.getValue().equals(orderReturnPO.getRefundType())){
            getDepositDetailList(detailVO);
        }
        // 是否展示退款扣罚提示语句
        detailVO.setIsShowRefundPunishLabel(orderReturnValidation.refundPunishAmountSupport(orderReturnPO));
        //如果是线下退款，则获取现在退款的最新凭证
        if(RefundType.OFFLINE_REFUND.getValue() == detailVO.getRefundType()){
            List<String> proofImageList = proofService.getProofImages(ProofConst.OFFLINE_REFUND_PROOF_CODE, detailVO.getAfsSn());
            detailVO.setRefundVoucherImages(proofImageList);
        }
        return detailVO;
    }

    @Override
    public OrderAfterPO getByAfsSn(String afsSn) {
        LambdaQueryWrapper<OrderAfterPO> orderAfterQuery = new LambdaQueryWrapper<>();
        orderAfterQuery.eq(OrderAfterPO::getAfsSn, afsSn);
        orderAfterQuery.eq(OrderAfterPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return this.getOne(orderAfterQuery);
    }

    /**
     * 查询组合退款单中的代还退款
     *
     * @param afsSn     退款单号
     * @return          组合退款单中的代还退款信息
     */
    private List<OrderRefundRecordPO> getCombinationRefundContainsAssistPayment(String afsSn) {
        LambdaQueryWrapper<OrderRefundRecordPO> orderRefundRecordQuery = Wrappers.lambdaQuery();
        orderRefundRecordQuery.eq(OrderRefundRecordPO::getAfsSn, afsSn)
                .eq(OrderRefundRecordPO::getRefundType, RefundType.ASSIST_PAYMENT.getValue())
                .eq(OrderRefundRecordPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return orderRefundRecordMapper.selectList(orderRefundRecordQuery);
    }

    @Override
    public DistributionOrderInfoVO balanceReminder(String afsSn) {
        DistributionOrderInfoVO distributionOrderInfoVO = new DistributionOrderInfoVO();
        distributionOrderInfoVO.setDistributionOrder(false);
        distributionOrderInfoVO.setDistributionAmount(BigDecimal.ZERO);
        OrderReturnPO orderReturnPO = orderReturnModel.getOrderReturnByAfsSn(afsSn);
        if(orderReturnPO.getOrderCommission() != null && orderReturnPO.getOrderCommission().compareTo(BigDecimal.ZERO) > 0 ){
            distributionOrderInfoVO.setDistributionOrder(true);
            distributionOrderInfoVO.setDistributionAmount(orderReturnPO.getOrderCommission());
        }
        return distributionOrderInfoVO;
    }

    @Override
    public AfsCountVO refundCountMoneyInfo(String orderSn, String afsOrderProductInfos, Member member) {
        // 查询订单信息
        OrderPO order = orderModel.getOrderByOrderSn(orderSn);
        if (Objects.nonNull(member)){
            // 用户申请退款，校验权限
            BizAssertUtil.isTrue(!order.getMemberId().equals(member.getMemberId()), "您无权操作此订单");
        }

        String[] orderProductArray = afsOrderProductInfos.split(",");
        BizAssertUtil.isTrue(orderProductArray.length == 0, "售后商品信息不能为空，请确认~");

        // 最大申请退的金额
        BigDecimal maxReturnMoney = BigDecimal.ZERO;
        // 乡助卡最大能退金额
        BigDecimal maxCardAmount = BigDecimal.ZERO;
        // 最大可退积分
        Integer maxReturnIntegral = 0;
        // 是否退运费
        Boolean containsExpressFee = Boolean.FALSE;
        // 退运费金额
        BigDecimal returnExpressFee = BigDecimal.ZERO;

        for (String afsOrderProductInfo : orderProductArray) {
            // 查询对应商品信息
            String[] orderProduct2Num = afsOrderProductInfo.split("-");
            BizAssertUtil.isTrue(orderProduct2Num.length != 2, "商品售后信息不完整，请确认~");
            OrderProductPO orderProductPO = orderProductService.selectOneByOrderProductId(Long.valueOf(orderProduct2Num[0]));
            BizAssertUtil.notNull(orderProductPO, "售后商品信息不存在，请确认~");
            BizAssertUtil.isTrue(!org.apache.commons.lang3.StringUtils.isNumeric(orderProduct2Num[1]), "售后数量输入异常~，请确认");

            Integer applyReturnNum = Integer.valueOf(orderProduct2Num[1]);
            // 退换的数量大于商品数量，默认计算剩余可退款商品数量
            if (applyReturnNum + orderProductPO.getReturnNumber() > orderProductPO.getProductNum()){
                applyReturnNum = orderProductPO.getProductNum() - orderProductPO.getReturnNumber();
            }

            if (applyReturnNum.compareTo(orderProductPO.getProductNum()) >= 0) {
                /**
                 * 该商品行全退
                 */
                //预付订金退款
                if (order.getOrderType() != null && order.getOrderType() == OrderTypeEnum.PRE_SELL_DEPOSIT.getValue()) {
                    maxReturnMoney = maxReturnMoney.add(frontAfterSaleApplyService.getDepositProductRefundMoney(orderSn, orderProductPO.getMoneyAmount()));
                } else {
                    maxReturnMoney = maxReturnMoney.add(orderProductPO.getMoneyAmount());
                }
                maxCardAmount = maxCardAmount.add(orderProductPO.getXzCardAmount());
                maxReturnIntegral += orderProductPO.getIntegral();
            } else if (applyReturnNum + orderProductPO.getReturnNumber() < orderProductPO.getProductNum()) {
                /**
                 * 该商品行部分退，按比例计算金额
                 */
                // 金额保留两位小数
                BigDecimal returnMoney = orderProductPO.getMoneyAmount().multiply(BigDecimal.valueOf(applyReturnNum))
                        .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.DOWN);
                maxReturnMoney = maxReturnMoney.add(returnMoney);

                BigDecimal cardAmount = orderProductPO.getXzCardAmount().multiply(BigDecimal.valueOf(applyReturnNum))
                        .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.DOWN);
                maxCardAmount = maxCardAmount.add(cardAmount);

                maxReturnIntegral += orderProductPO.getIntegral() * applyReturnNum / orderProductPO.getProductNum();
            } else {
                /**
                 * 最后一次退款，进行钆差
                 */
                Map<String, BigDecimal> sumAmountByProduct = orderReturnService.getSumAmountByProductId(orderProductPO.getOrderProductId());
                BigDecimal returnMoneyAmount = orderProductPO.getMoneyAmount().subtract(sumAmountByProduct.get("returnMoneyAmount"));
                BigDecimal xzCardAmount = orderProductPO.getXzCardAmount().subtract(sumAmountByProduct.get("xzCardAmount"));
                int integral = orderProductPO.getIntegral() - sumAmountByProduct.get("returnIntegralAmount").intValue();

                maxReturnMoney = maxReturnMoney.add(returnMoneyAmount);
                maxCardAmount = maxCardAmount.add(xzCardAmount);
                maxReturnIntegral += integral;
            }
        }

        // 最后一次退款，退运费
        if (OrderStatusEnum.WAIT_DELIVER.getValue().equals(order.getOrderState())
                && orderReturnValidation.isAllProductsRefundApply(orderSn, Arrays.asList(orderProductArray))) {
            containsExpressFee = Boolean.TRUE;
            returnExpressFee = order.getExpressFee();
        }

        // 售后金额信息
        AfsCountVO vo = new AfsCountVO();
        vo.setMaxReturnMoney(maxReturnMoney);
        vo.setContainsFee(containsExpressFee);
        vo.setReturnExpressFee(returnExpressFee);
        vo.setMaxCardAmount(maxCardAmount);
        vo.setSumRefundMoney(maxReturnMoney.add(returnExpressFee));
        vo.setMaxReturnIntegral(maxReturnIntegral);
        // 存在待退乡助卡金额才进行试算
        if (vo.getMaxCardAmount().compareTo(BigDecimal.ZERO) > 0) {
            // 乡助卡退款试算
            CardRefundCheckResponse checkResponse =
                    orderPayService.payRefundCheck(order.getPaySn(), vo.getMaxCardAmount(), member.getUserNo());
            vo.setNoUsedAmount(checkResponse.getNoUsedAmount());
        }

        log.info("orderAfterService refundCountMoneyInfo is : {}", JSONObject.toJSONString(vo));
        return vo;
    }

    @Override
    public List<AfsOrderProductVO> getRefundableOrderProduct(String orderSn, Long orderProductId, Member member) {
        OrderPO orderPO = orderService.lambdaQuery().eq(OrderPO::getOrderSn, orderSn).one();
        // 查询订单现有可退款商品：退款数量 < 商品数量
        List<OrderProductPO> orderProductPOList = orderProductModel.selectRefundableOrderProduct(orderSn);
        BizAssertUtil.notEmpty(orderProductPOList, "订单不存在或没有可退商品，请确认～");
        orderProductPOList = orderLocalUtils.sortFullGiftProduct(orderPO.getOrderType(), orderProductPOList);
        if (Objects.nonNull(member)) {
            BizAssertUtil.isTrue(!member.getMemberId().equals(orderProductPOList.get(0).getMemberId()), "您无权操作该订单～");
        }

        List<AfsOrderProductVO> vos = new ArrayList<>();
        // 遍历商品，将指定商品放到第一个
        for (OrderProductPO orderProductPO : orderProductPOList) {
            if (orderProductPO.getOrderProductId().equals(orderProductId)) {
                vos.add(0, new AfsOrderProductVO(orderProductPO));
            } else {
                vos.add(new AfsOrderProductVO(orderProductPO));
            }
        }
        return vos;
    }

<<<<<<< HEAD
    @Override
    public OrderReturnRiskMessageVO riskMessage(String afsSn) {
        // 查询售后订单信息
        OrderAfterPO orderAfterPO = this.getByAfsSn(afsSn);
        BizAssertUtil.notNull(orderAfterPO, "请输入正确的售后单号");

        // 查询售后商品信息
        LambdaQueryWrapper<OrderProductPO> afsOrderProductQuery = Wrappers.lambdaQuery();
        afsOrderProductQuery.eq(OrderProductPO::getOrderProductId, orderAfterPO.getOrderProductId());
        OrderProductPO orderProductPO = orderProductService.getOne(afsOrderProductQuery);

        // 拼单赠品订单风险检查
        OrderReturnRiskMessageVO riskMessageVO = groupBuyingRiskCheck(orderAfterPO, orderProductPO);
        if (riskMessageVO.getHasRisk()){
            return riskMessageVO;
        }

        riskMessageVO = erpLogisticsRiskCheck(orderAfterPO, orderProductPO);



        return riskMessageVO;
    }

    /**
     * 拼单满赠风险提示：
     *  订单商品参与了拼单，生成了【赠品订单】
     *      ->赠品订单商品未全部退款完成 ：进行风险提示
     *      ->赠品订单商品已全退，退款单未审批完成：根据退款状态进行风险提示
     */
    @Override
    public OrderReturnRiskMessageVO groupBuyingRiskCheck(OrderAfterPO orderAfterPO,OrderProductPO orderProductPO) {
        OrderReturnRiskMessageVO riskMessageVO = new OrderReturnRiskMessageVO();

        /**
         * 商品未拼单，无需风险提示
         */
        if (!OrderGroupBuyingEnum.isFinished(orderProductPO.getGroupBuyingTag())) {
            return riskMessageVO;
        }

        // 售后商品关联拼单记录
        OrderGroupBuyingBindQuery query = new OrderGroupBuyingBindQuery();
        query.setGroupOrderSn(orderAfterPO.getOrderSn());
        query.setOrderProductId(orderAfterPO.getOrderProductId());
        List<OrderGroupBuyingBindPO> groupBuyingBindList = orderGroupBuyingBindService.getGroupBuyingBindList(query);

        /**
         * 未关联赠品订单，无需风险提示
         */
        if (CollectionUtils.isEmpty(groupBuyingBindList)) {
            return riskMessageVO;
        }

        // 查询赠品订单商品退款情况
        OrderGroupBuyingBindPO groupBuyingBindPO = groupBuyingBindList.get(0);
        // 赠品订单商品信息
        LambdaQueryWrapper<OrderProductPO> giftOrderProductQuery = Wrappers.lambdaQuery();
        giftOrderProductQuery.eq(OrderProductPO::getOrderSn, groupBuyingBindPO.getGiftOrderSn())
                .select(OrderProductPO::getOrderProductId, OrderProductPO::getGroupBuyingTag,
                        OrderProductPO::getProductNum, OrderProductPO::getReturnNumber, OrderProductPO::getStatus);
        List<OrderProductPO> giftOrderProductList = orderProductService.list(giftOrderProductQuery);

        /**
         * 赠品订单未全部退款完成，进行提示
         */
        Map<String, String> riskMessage = new HashMap<>();
        Integer waitingReturnCount = giftOrderProductList.stream()
                .map(p -> p.getProductNum() - p.getReturnNumber())
                .reduce(0, Integer::sum);
        if (waitingReturnCount > 0) {
            riskMessage.put("waitingReturnCount", waitingReturnCount.toString());
            riskMessage.put("giftOrderSn", groupBuyingBindPO.getGiftOrderSn());
            String message = String.format("该退货商品关联的赠品订单剩余%d个赠品没有退货，建议联系分支主任将关联赠品订单(%s)退货后再审批通过",
                    waitingReturnCount, groupBuyingBindPO.getGiftOrderSn());
            riskMessage.put("riskMessage", message);

            riskMessageVO.setHasRisk(true);
            riskMessageVO.setRiskType(ReturnRiskType.GROUP_BUYING_RETURN.getValue());
            riskMessageVO.setRiskTypeDesc(ReturnRiskType.GROUP_BUYING_RETURN.getDesc());
            riskMessageVO.setRiskMessage(message);
            return riskMessageVO;
        }

        // 查询赠品订单商品退款状态
        LambdaQueryWrapper<OrderReturnPO> giftReturnOrder = Wrappers.lambdaQuery();
        giftReturnOrder.eq(OrderReturnPO::getOrderSn, groupBuyingBindPO.getGiftOrderSn())
                .notIn(OrderReturnPO::getState, OrderReturnStatus.endStatus())
                .select(OrderReturnPO::getReturnId, OrderReturnPO::getState);
        List<OrderReturnPO> giftReturnList = orderReturnService.list(giftReturnOrder);

        /**
         * 已全部退款，退款审批未完成：提示
         */
        List<OrderReturnPO> duringRefundOrderList = giftReturnList.stream()
                .filter(r -> OrderReturnStatus.duringRefund(r.getState()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(duringRefundOrderList)){
            OrderReturnPO giftReturn = duringRefundOrderList.get(0);
            OrderReturnStatus returnStatus = OrderReturnStatus.valueOf(giftReturn.getState());
            riskMessage.put("returnStatus", returnStatus.getShowDesc());
            riskMessage.put("giftOrderSn", groupBuyingBindPO.getGiftOrderSn());
            String message = String.format("该退货商品关联的赠品订单%s，建议先同意关联赠品订单(%s)退货后再审批通过",
                    returnStatus.getShowDesc(), groupBuyingBindPO.getGiftOrderSn());
            riskMessage.put("riskMessage", message);

            riskMessageVO.setHasRisk(true);
            riskMessageVO.setRiskType(ReturnRiskType.GROUP_BUYING_RETURN.getValue());
            riskMessageVO.setRiskTypeDesc(ReturnRiskType.GROUP_BUYING_RETURN.getDesc());
            riskMessageVO.setRiskMessage(message);
            return riskMessageVO;
        }

        return riskMessageVO;
    }

    public Boolean isJdInterceptOrderProduct(OrderProductPO orderProductPO) {
        OrderPO orderPO = orderModel.getOrderByOrderSn(orderProductPO.getOrderSn());
        boolean result = false;
        //指定店铺进行京东物流拦截
        List<String> storeIdList = Arrays.asList(storeIds.split(","));
        if(!storeIdList.contains(orderPO.getStoreId().toString())) {
            return result;
        }

        //非自提订单 && erp履约出库的订单进行京东物流拦截（风险提示）
        if(OrderPatternEnum.SELF_LIFT.getValue().equals(orderPO.getOrderPattern())
                || !OrderPerformanceChannelEnum.PERFORMANCE_CHANNEL_ERP.getValue().equals(orderProductPO.getPerformanceChannel())) {
            return result;
        }
        return Boolean.TRUE;
    }

    @Override
    public OrderReturnRiskMessageVO erpLogisticsRiskCheck(OrderAfterPO orderAfterPO, OrderProductPO orderProductPO) {
        OrderReturnRiskMessageVO riskMessageVO = new OrderReturnRiskMessageVO();

        if (!isJdInterceptOrderProduct(orderProductPO)) {
            return riskMessageVO;
        }

        OrderReturnPO orderReturnPO = orderReturnModel.getOrderReturnByAfsSn(orderAfterPO.getAfsSn());
        // 对接erp物流风险检查(仅退款&&待商家审批时需要)
        if(!OrderReturnStatus.REFUND_APPLY.getValue().equals(orderReturnPO.getState())) {
            return riskMessageVO;
        }

        /**
         * 售后单是否风险，
         *  无风险：无需拦截，拦截成功
         *  有风险：未拦截--》
         *        拦截失败，拦截中
         *
         */

        switch (orderReturnPO.getJdInterceptStatus()) {
            //未拦截： ERP发货数校验，申请数 > 未发货数 ，风险提示
            case OrdersAfsConst.JD_INTERCEPT_STATE_DEFAULT:
                boolean checkResult = wmsIntegration.jingDongInterceptCheck(orderAfterPO.getAfsSn(),orderProductPO.getOrderSn(),orderReturnPO.getReturnNum(),orderProductPO.getChannelSkuId(),orderProductPO.getChannelSkuUnit());
                BizAssertUtil.isTrue(!checkResult, "商品已推送至【外部渠道】发货，暂不支持仅退款操作");
                if(!checkResult) {
                    riskMessageVO.setShowFlag(Boolean.TRUE);
                    riskMessageVO.setHasRisk(Boolean.TRUE);
                    riskMessageVO.setRiskMessage("商品已推送至【外部渠道】发货，为避免产生货损,需要先拦截快递再同意售后");
                    riskMessageVO.setRiskType(ReturnRiskType.ERP_DELIVER_OVER_RISK.getValue());
                    riskMessageVO.setRiskTypeDesc(ReturnRiskType.ERP_DELIVER_OVER_RISK.getDesc());
                }
                int jdInterceptStatus = checkResult ? OrdersAfsConst.JD_INTERCEPT_STATE_NO_INTERCEPTED : OrdersAfsConst.JD_INTERCEPT_STATE_WAIT_INTERCEPTED;
                orderReturnModel.updateJdInterceptStatus(orderAfterPO.getAfsSn(), jdInterceptStatus);
                break;
            case OrdersAfsConst.JD_INTERCEPT_STATE_NO_INTERCEPTED:
                riskMessageVO.setShowFlag(Boolean.FALSE);
                riskMessageVO.setHasRisk(Boolean.FALSE);
                riskMessageVO.setRiskType(ReturnRiskType.NONE.getValue());
                riskMessageVO.setRiskTypeDesc(ReturnRiskType.NONE.getDesc());
                break;
            case OrdersAfsConst.JD_INTERCEPT_STATE_INTERCEPTED_SUCCESS:
                riskMessageVO.setShowFlag(Boolean.FALSE);
                riskMessageVO.setHasRisk(Boolean.FALSE);
                //riskMessageVO.setRiskMessage("商品已推送至【外部渠道】发货，快递拦截成功，为避免货物漏发，不可拒绝");
                riskMessageVO.setRiskType(ReturnRiskType.NONE.getValue());
                riskMessageVO.setRiskTypeDesc(ReturnRiskType.NONE.getDesc());
                break;
            case OrdersAfsConst.JD_INTERCEPT_STATE_INTERCEPTED_FAIL:
                riskMessageVO.setShowFlag(Boolean.TRUE);
                riskMessageVO.setRiskMessage("商品已推送至【外部渠道】发货，快递拦截失败，为避免产生货损，请让用户发起退货退款");
                break;
            case OrdersAfsConst.JD_INTERCEPT_STATE_INTERCEPTING:
                riskMessageVO.setShowFlag(Boolean.TRUE);
                riskMessageVO.setRiskType(ReturnRiskType.COMMON_RISK.getValue());
                riskMessageVO.setRiskTypeDesc(ReturnRiskType.COMMON_RISK.getDesc());
                riskMessageVO.setRiskMessage("商品已推送至【外部渠道】发货，快递正在拦截中为避免产生货损，需要等拦截成功再进行审批");
                break;
            case OrdersAfsConst.JD_INTERCEPT_STATE_WAIT_INTERCEPTED:
                riskMessageVO.setShowFlag(Boolean.TRUE);
                riskMessageVO.setHasRisk(Boolean.TRUE);
                riskMessageVO.setRiskType(ReturnRiskType.ERP_DELIVER_OVER_RISK.getValue());
                riskMessageVO.setRiskTypeDesc(ReturnRiskType.ERP_DELIVER_OVER_RISK.getDesc());
                riskMessageVO.setRiskMessage("商品已推送至【外部渠道】发货，为避免产生货损,需要先拦截快递再同意售后");
                break;
            default:
                break;
        }
        return riskMessageVO;
    }

    @Override
    public List<OrderAfterPO> getByAfsSnList(List<String> afsSnList) {
        if (CollectionUtils.isEmpty(afsSnList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<OrderAfterPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(OrderAfterPO::getAfsSn, afsSnList);
        return getBaseMapper().selectList(queryWrapper);
    }

    @Override
    public String erpDeliveryIntercept(String afsSn, UserDTO userDTO) {
        OrderReturnPO orderReturnPO = orderReturnModel.getOrderReturnByAfsSn(afsSn);
        OrderAfterPO orderAfterPO = this.getByAfsSn(afsSn);
        OrderProductPO orderProductPO = orderProductModel.getOrderProductById(orderAfterPO.getOrderProductId());
        String result = "";
        Integer wmsResult = null;
        switch (orderReturnPO.getJdInterceptStatus()) {
            case OrdersAfsConst.JD_INTERCEPT_STATE_DEFAULT:
            case OrdersAfsConst.JD_INTERCEPT_STATE_WAIT_INTERCEPTED:
                wmsResult = wmsIntegration.jingDongIntercept(afsSn,orderReturnPO.getOrderSn(),orderReturnPO.getReturnNum(),
                        orderProductPO.getChannelSkuId(),orderProductPO.getChannelSkuUnit(),userDTO.getUserName());

                JdInterceptStatusEnum jdInterceptStatusEnum = JdInterceptStatusEnum.JD_INTERCEPT_STATE_DEFAULT;
                if(WmsConst.JD_INTERCEPT_STATE_100 == wmsResult){
                    jdInterceptStatusEnum = JdInterceptStatusEnum.JD_INTERCEPT_STATE_NO_INTERCEPTED;
                } else if (WmsConst.JD_INTERCEPT_STATE_150 == wmsResult){
                    jdInterceptStatusEnum = JdInterceptStatusEnum.JD_INTERCEPT_STATE_INTERCEPTING;
                } else if (WmsConst.JD_INTERCEPT_STATE_200 == wmsResult){
                    jdInterceptStatusEnum = JdInterceptStatusEnum.JD_INTERCEPT_STATE_INTERCEPTED_SUCCESS;
                } else if (WmsConst.JD_INTERCEPT_STATE_300 == wmsResult){
                    jdInterceptStatusEnum = JdInterceptStatusEnum.JD_INTERCEPT_STATE_INTERCEPTED_FAIL;
                } else {
                    log.warn("wms京东拦截接口返回码异常,请检查！");
                }
                //更新拦截状态
                orderReturnModel.updateJdInterceptStatus(orderAfterPO.getAfsSn(),jdInterceptStatusEnum.getValue());
                result = jdInterceptStatusEnum.getDesc();

                break;
            case OrdersAfsConst.JD_INTERCEPT_STATE_NO_INTERCEPTED:
            case OrdersAfsConst.JD_INTERCEPT_STATE_INTERCEPTING:
            case OrdersAfsConst.JD_INTERCEPT_STATE_INTERCEPTED_SUCCESS:
            case OrdersAfsConst.JD_INTERCEPT_STATE_INTERCEPTED_FAIL:
                result = String.format("改售后单已拦截，目前状态为:%s", JdInterceptStatusEnum.valueOf(orderReturnPO.getJdInterceptStatus()).getDesc());
            default:
                break;
        }

        return result;
    }
=======
    private AfsCountVO calculateReturnMoney(String orderSn, String orderProductInfos, Member member) {
        // 查询订单信息
        OrderPO orderPOS = orderModel.getOrderByOrderSn(orderSn);
        BizAssertUtil.isTrue(!orderPOS.getMemberId().equals(member.getMemberId()), "您无权操作此订单");

        String[] orderProductArray = orderProductInfos.split(",");
        // 是否计算运费
        boolean isCalculateFee = false;
        // 计算是否包含运费,未发货状态，并且此次售后为最后一次售后时，才退运费
        boolean containsFee = true;
        // 查询订单货品并且未退过货的
        OrderProductExample orderProductExample = new OrderProductExample();
        orderProductExample.setMemberId(member.getMemberId());
        orderProductExample.setOrderSn(orderSn);
        orderProductExample.setReturnNumber(0);
        List<OrderProductPO> orderProductPOList = orderProductModel.getOrderProductList(orderProductExample, null);
        // 退货商品数量等于订单货品明细数量，则判断是否计算运费
        if (orderProductArray.length == orderProductPOList.size()) {
            isCalculateFee = true;
        }
        // 返回信息
        AfsCountVO vo = new AfsCountVO();
        // 最大可退金额
        BigDecimal maxReturnMoney = new BigDecimal("0.00");
        // 最大可退积分
        Integer maxReturnIntegral = 0;
        // 申请总数
        Integer number = 0;
        // 乡助卡最大能退
        BigDecimal maxCardAmount = BigDecimal.ZERO;

        for (String orderProductInfo : orderProductArray) {
            String[] split = orderProductInfo.split("-");
            Long orderProductId = Long.valueOf(split[0]);
            OrderProductPO orderProductPO = orderProductModel.getOrderProductByOrderProductId(orderProductId);
            if (split.length == 1 || StringUtils.isEmpty(split[1])
                    || Integer.valueOf(split[1]).compareTo(orderProductPO.getProductNum()) >= 0) {
                // 未传商品数量,或者要退换的数量大于等于商品数量，默认计算所有商品数量
                //预付订金退款
                if (orderPOS.getOrderType() != null && orderPOS.getOrderType() == OrderTypeEnum.PRE_SELL_DEPOSIT.getValue()) {
                    maxReturnMoney = maxReturnMoney.add(frontAfterSaleApplyService.getDepositProductRefundMoney(orderSn, orderProductPO.getMoneyAmount()));
                } else {
                    maxReturnMoney = maxReturnMoney.add(orderProductPO.getMoneyAmount());
                }
                maxCardAmount = maxCardAmount.add(orderProductPO.getXzCardAmount());
                maxReturnIntegral += orderProductPO.getIntegral();
                number += orderProductPO.getProductNum();
                // 如果不计算运费，设置false
                if (!isCalculateFee) {
                    containsFee = false;
                }
            } else {
                // 根据传来的数量计算要退的金额
                int actualNum = Integer.parseInt(split[1]);
                if (isCalculateFee) {
                    // 如果购买商品数量等于退货数量
                    if (orderProductPO.getProductNum() != actualNum) {
                        containsFee = false;
                    }
                } else {
                    // 如果没有全部退货，不计算运费
                    containsFee = false;
                }

                //预付订金退款（因订金不支持乡助卡付款，所以乡助卡的地方没改）
                if (orderPOS.getOrderType() != null && orderPOS.getOrderType() == OrderTypeEnum.PRE_SELL_DEPOSIT.getValue()) {
                    BizAssertUtil.isTrue(orderProductPO.getProductNum() != actualNum, "预付订金商品不能退部分数量，只能全退");
                    maxReturnMoney = maxReturnMoney.add(frontAfterSaleApplyService.getDepositProductRefundMoney(orderSn, orderProductPO.getMoneyAmount()));
                } else {
                    // 舍去第三位小数
                    BigDecimal returnMoney = orderProductPO.getMoneyAmount().multiply(BigDecimal.valueOf(actualNum))
                            .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.DOWN);
                    maxReturnMoney = maxReturnMoney.add(returnMoney);
                }
                // 舍去第三位小数
                BigDecimal cardAmount = orderProductPO.getXzCardAmount().multiply(BigDecimal.valueOf(actualNum))
                        .divide(BigDecimal.valueOf(orderProductPO.getProductNum()), 2, RoundingMode.DOWN);
                maxCardAmount = maxCardAmount.add(cardAmount);

                // 只保留整数位
                int returnIntegral = orderProductPO.getIntegral() * actualNum / orderProductPO.getProductNum();
                maxReturnIntegral += returnIntegral;
                number += actualNum;
            }
        }

        if (orderPOS.getOrderState().equals(OrderConst.ORDER_STATE_20)) {
            // 未发货状态才退运费
            if (containsFee) {
                // 是最后一次退，退还运费
                maxReturnMoney = maxReturnMoney.add(orderPOS.getExpressFee());
                // 是最后一次退，退还乡助卡运费
                maxCardAmount = maxCardAmount.add(orderPOS.getXzCardExpressFeeAmount());
                vo.setReturnExpressFee(orderPOS.getExpressFee());
            } else {
                vo.setReturnExpressFee(BigDecimal.ZERO);
            }
            vo.setContainsFee(containsFee);
        } else {
            // 不退运费设置false
            vo.setContainsFee(false);
            vo.setReturnExpressFee(BigDecimal.ZERO);
        }
        vo.setMaxReturnIntegral(maxReturnIntegral);
        vo.setMaxReturnMoney(maxReturnMoney);
        vo.setMaxCardAmount(maxCardAmount);
        vo.setNumber(number);
        // 存在待退乡助卡金额才进行试算
        if (vo.getMaxCardAmount().compareTo(BigDecimal.ZERO) > 0) {
            // 乡助卡退款试算
            CardRefundCheckResponse checkResponse =
                    orderPayService.payRefundCheck(orderPOS.getPaySn(), vo.getMaxCardAmount(), member.getUserNo());
            vo.setNoUsedAmount(checkResponse.getNoUsedAmount());
        }
        return vo;

    }

>>>>>>> F-20231112-removeCard-ml
}
