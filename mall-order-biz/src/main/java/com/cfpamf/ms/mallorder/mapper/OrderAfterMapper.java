package com.cfpamf.ms.mallorder.mapper;

import com.cfpamf.framework.autoconfigure.mybatis.MyBaseMapper;
import com.cfpamf.ms.mallorder.dto.OrderRefundCountDTO;
import com.cfpamf.ms.mallorder.po.OrderAfterPO;
import com.cfpamf.ms.mallorder.request.OrderAfterServiceExample;
import com.slodon.bbc.core.database.BaseWriteMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 订单售后服务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
public interface OrderAfterMapper extends MyBaseMapper<OrderAfterPO>, BaseWriteMapper<OrderAfterPO, OrderAfterServiceExample> {

    int updateByAfsSn(OrderAfterPO orderAfterServicePO);

    List<OrderRefundCountDTO> getProductRefundCount(@Param("orderProductIdList") List<Long> orderProductIdList);
}
