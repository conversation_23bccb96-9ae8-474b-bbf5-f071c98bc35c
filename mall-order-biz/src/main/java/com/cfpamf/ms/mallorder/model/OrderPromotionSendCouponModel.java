package com.cfpamf.ms.mallorder.model;


import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderSubmitDTO;
import com.cfpamf.ms.mallorder.mapper.OrderPromotionSendCouponMapper;
import com.cfpamf.ms.mallorder.po.OrderPromotionSendCouponPO;
import com.cfpamf.ms.mallorder.request.OrderPromotionSendCouponExample;
import com.cfpamf.ms.mallorder.v2.service.OrderPromotionSendCouponService;
import com.google.api.client.util.Lists;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.PagerInfo;

import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 订单活动赠送优惠券表model
 */
@Component
@Slf4j
public class OrderPromotionSendCouponModel {
    @Resource
    private OrderPromotionSendCouponMapper orderPromotionSendCouponMapper;

    @Autowired
    private OrderPromotionSendCouponService orderPromotionSendCouponService;


    /**
     * 新增订单活动赠送优惠券表
     *
     * @param orderPromotionSendCouponPO
     * @return
     */
    public Integer saveOrderPromotionSendCoupon(OrderPromotionSendCouponPO orderPromotionSendCouponPO) {
        int count = orderPromotionSendCouponMapper.insert(orderPromotionSendCouponPO);
        if (count == 0) {
            throw new MallException("添加订单活动赠送优惠券表失败，请重试");
        }
        return count;
    }

    /**
     * 根据sendCouponId删除订单活动赠送优惠券表
     *
     * @param sendCouponId sendCouponId
     * @return
     */
    public Integer deleteOrderPromotionSendCoupon(Integer sendCouponId) {
        if (StringUtils.isEmpty(sendCouponId)) {
            throw new MallException("请选择要删除的数据");
        }
        int count = orderPromotionSendCouponMapper.deleteByPrimaryKey(sendCouponId);
        if (count == 0) {
            log.error("根据sendCouponId：" + sendCouponId + "删除订单活动赠送优惠券表失败");
            throw new MallException("删除订单活动赠送优惠券表失败,请重试");
        }
        return count;
    }

    /**
     * 根据sendCouponId更新订单活动赠送优惠券表
     *
     * @param orderPromotionSendCouponPO
     * @return
     */
    public Integer updateOrderPromotionSendCoupon(OrderPromotionSendCouponPO orderPromotionSendCouponPO) {
        if (StringUtils.isEmpty(orderPromotionSendCouponPO.getSendCouponId())) {
            throw new MallException("请选择要修改的数据");
        }
        int count = orderPromotionSendCouponMapper.updateByPrimaryKeySelective(orderPromotionSendCouponPO);
        if (count == 0) {
            log.error("根据sendCouponId：" + orderPromotionSendCouponPO.getSendCouponId() + "更新订单活动赠送优惠券表失败");
            throw new MallException("更新订单活动赠送优惠券表失败,请重试");
        }
        return count;
    }

    /**
     * 根据sendCouponId获取订单活动赠送优惠券表详情
     *
     * @param sendCouponId sendCouponId
     * @return
     */
    public OrderPromotionSendCouponPO getOrderPromotionSendCouponBySendCouponId(Integer sendCouponId) {
        return orderPromotionSendCouponMapper.getByPrimaryKey(sendCouponId);
    }

    /**
     * 根据条件获取订单活动赠送优惠券表列表
     *
     * @param example 查询条件信息
     * @param pager   分页信息
     * @return
     */
    public List<OrderPromotionSendCouponPO> getOrderPromotionSendCouponList(OrderPromotionSendCouponExample example, PagerInfo pager) {
        List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOList;
        if (pager != null) {
            pager.setRowsCount(orderPromotionSendCouponMapper.countByExample(example));
            orderPromotionSendCouponPOList = orderPromotionSendCouponMapper.listPageByExample(example, pager.getStart(), pager.getPageSize());
        } else {
            orderPromotionSendCouponPOList = orderPromotionSendCouponMapper.listByExample(example);
        }
        return orderPromotionSendCouponPOList;
    }

    /**
     * 提交订单-保存订单活动赠送优惠券
     * @param orderInfo
     * @param orderSn
     */
    public void insertOrderPromotionSendCoupons(OrderSubmitDTO.OrderInfo orderInfo, String orderSn){
        List<OrderSubmitDTO.PromotionInfo> promotionInfoList = orderInfo.getPromotionInfoList();
        if (!CollectionUtils.isEmpty(promotionInfoList)){
            //参与了优惠活动
            promotionInfoList.forEach(promotionInfo -> {
                List<OrderSubmitDTO.PromotionInfo.SendCoupon> sendCouponList = promotionInfo.getSendCouponList();
                if (!CollectionUtils.isEmpty(sendCouponList)){
                    //此活动赠送了优惠券
                    sendCouponList.forEach(sendCoupon -> {
                        OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
                        orderPromotionSendCouponPO.setOrderSn(orderSn);
                        orderPromotionSendCouponPO.setPromotionGrade(promotionInfo.getPromotionType() / 100);
                        orderPromotionSendCouponPO.setPromotionType(promotionInfo.getPromotionType());
                        orderPromotionSendCouponPO.setPromotionId(promotionInfo.getPromotionId());
                        orderPromotionSendCouponPO.setIsStore(promotionInfo.getIsStore() ? OrderConst.IS_STORE_PROMOTION_YES : OrderConst.IS_STORE_PROMOTION_NO);
                        orderPromotionSendCouponPO.setCouponId(sendCoupon.getCouponId());
                        orderPromotionSendCouponPO.setNumber(sendCoupon.getNum());
                        this.saveOrderPromotionSendCoupon(orderPromotionSendCouponPO);
                    });
                }
            });
        }
    }


    /**
     * 提交订单-保存订单活动赠送优惠券
     * @param orderInfo
     * @param orderSn
     */
    public List<OrderPromotionSendCouponPO> buildOrderPromotionSendCoupons(OrderSubmitDTO.OrderInfo orderInfo, String orderSn){
        List<OrderSubmitDTO.PromotionInfo> promotionInfoList = orderInfo.getPromotionInfoList();
        List<OrderPromotionSendCouponPO> poList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(promotionInfoList)){
            //参与了优惠活动
            promotionInfoList.forEach(promotionInfo -> {
                List<OrderSubmitDTO.PromotionInfo.SendCoupon> sendCouponList = promotionInfo.getSendCouponList();
                if (!CollectionUtils.isEmpty(sendCouponList)){
                    //此活动赠送了优惠券
                    sendCouponList.forEach(sendCoupon -> {
                        OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
                        orderPromotionSendCouponPO.setOrderSn(orderSn);
                        orderPromotionSendCouponPO.setPromotionGrade(promotionInfo.getPromotionType() / 100);
                        orderPromotionSendCouponPO.setPromotionType(promotionInfo.getPromotionType());
                        orderPromotionSendCouponPO.setPromotionId(promotionInfo.getPromotionId());
                        orderPromotionSendCouponPO.setIsStore(promotionInfo.getIsStore() ? OrderConst.IS_STORE_PROMOTION_YES : OrderConst.IS_STORE_PROMOTION_NO);
                        orderPromotionSendCouponPO.setCouponId(sendCoupon.getCouponId());
                        orderPromotionSendCouponPO.setNumber(sendCoupon.getNum());
                        poList.add(orderPromotionSendCouponPO);
                    });
                }
            });
        }
        return poList;
    }

    /**
     * 批量保存
     *
     * @param couponPromotionPoTotalList 优惠券促销赠送po列表
     */
    public void saveBatch(List<OrderPromotionSendCouponPO> couponPromotionPoTotalList) {
        orderPromotionSendCouponService.saveBatch(couponPromotionPoTotalList);
    }
}