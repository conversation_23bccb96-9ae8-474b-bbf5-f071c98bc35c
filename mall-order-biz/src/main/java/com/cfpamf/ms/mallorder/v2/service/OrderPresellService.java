package com.cfpamf.ms.mallorder.v2.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderPresellPO;
import com.cfpamf.ms.mallorder.v2.domain.dto.OrderPresellDTO;
import com.cfpamf.ms.mallorder.v2.domain.vo.OperationUserVO;
import com.cfpamf.ms.mallorder.vo.OrderPresellVO;
import com.slodon.bbc.core.response.JsonResult;

import java.util.List;

/**
 * <AUTHOR> 2022/5/19.
 */
public interface OrderPresellService extends IService<OrderPresellPO> {

    /**
     * 校验paySn下订单是否全部支付完成
     * 
     * @param paySn
     * @return
     */
    boolean verifyPayOrderAllFinishByPaySn(String paySn);
    
    /**
     * 校验paySn订单指定定金或是否支付完成
     * 
     * @param paySn
     * @return
     */
    boolean verifyPayOrderFinishByPaySn(String paySn,Integer type);
    
    /**
     * 交易订单是否待支付尾款
     * @param paySn
     * @return
     */
    boolean verifyOrderWaitPayBalanceByPaySn(String paySn);
    
    
    /**
     * 根据payNo查询预付订单信息
     * 
     * @param payNo
     * @return
     */
    OrderPresellPO queryByPayNo(String payNo);

    /**
     * 支付成功更新
     * 
     * @param orderPresell
     * @return
     */
    boolean updatePaySuccessByPayNo(OrderPresellPO orderPresell);

    /**
     * 取消预售订单
     *
     * @param orderPo           需要取消的订单
     * @param operationUser     取消人信息
     */
    void cancelPresellOrder(OrderPO orderPo, OperationUserVO operationUser);

    boolean closeBankTransferOrder(String paySn);

    List<OrderPresellVO> listByOrderSn(String OrderSn,Integer type);
    
    /**
     * 根据订单号，查询尾款信息
     * 
     * @param OrderSn
     * @return
     */
    OrderPresellPO queryBalanceInfoByOrderSn(String OrderSn);
    
    OrderPresellPO queryBalanceInfoByPayNo(String payNo);

    OrderPresellVO findByPayNo(String payNo);

    /**
     * 根据订单号获取预售订单信息
     * @param orderSn 订单号
     * @return OrderPresellDTO
     * */
    OrderPresellDTO getDepositOrderDetail(String orderSn);

    /**
     * 预售订单订金尾款PO转换成DTO
     * @param orderPreSellPOList
     * @return List<OrderPresellDTO>
     * */
    OrderPresellDTO convertOrderPresellPOToDTO(List<OrderPresellPO> orderPreSellPOList);


    /**
     * 根据支付单号，判断预付是否银行卡汇款支付
     * @param paySn
     * @return
     */
    JsonResult<Boolean> checkPresellPayMethod(String paySn);
}
