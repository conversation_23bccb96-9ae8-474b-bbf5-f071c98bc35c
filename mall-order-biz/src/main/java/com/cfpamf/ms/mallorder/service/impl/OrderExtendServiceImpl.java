package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.OrderAddressDTO;
import com.cfpamf.ms.mallorder.dto.OrderPointFixDTO;
import com.cfpamf.ms.mallorder.integration.shop.ShopIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderExtendMapper;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.service.IOrderExtendService;
import com.cfpamf.ms.mallshop.vo.SelfLiftingPointVo;
import com.cfpamf.ms.mallshop.vo.SimpleSelfLiftingPointVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2021/10/18.
 */
@Slf4j
@Service
public class OrderExtendServiceImpl extends ServiceImpl<OrderExtendMapper, OrderExtendPO> implements IOrderExtendService {

    @Resource
    private OrderExtendMapper orderExtendMapper;

	@Autowired
	private ShopIntegration shopIntegration;

	@Autowired
	private OrderMapper orderMapper;

    @Override
    public OrderExtendPO getOrderExtendByOrderSn(String orderSn) {
    	if(StringUtils.isEmpty(orderSn)) {
    		return null;
    	}
        LambdaQueryWrapper<OrderExtendPO> queryWrapper = Wrappers.lambdaQuery(OrderExtendPO.class);
        queryWrapper.eq(OrderExtendPO::getOrderSn, orderSn)
                .eq(OrderExtendPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return orderExtendMapper.selectOne(queryWrapper);
    }
    
    /**
     * 修改订单备注
     * @param orderSns
     * @param remark
     * @return
     */
    @Override
    public boolean updateOrderRemark(Set<String> orderSns,String remark) {
    	if(CollectionUtils.isEmpty(orderSns)) {
    		return true;
		}
		if (StringUtils.isEmpty(remark)) {
			return true;
		}
		LambdaUpdateWrapper<OrderExtendPO> updateOrderExtendWrapper = new LambdaUpdateWrapper<>();
		updateOrderExtendWrapper.in(OrderExtendPO::getOrderSn, orderSns);
		OrderExtendPO orderExtendPO = new OrderExtendPO();
		orderExtendPO.setOrderRemark(remark);
		return super.update(orderExtendPO, updateOrderExtendWrapper);
	}

	@Override
	public OrderAddressDTO buildReceiveInfo(OrderExtendPO orderExtendPO) {
		OrderAddressDTO orderReceiveInfoVO = new OrderAddressDTO();
		orderReceiveInfoVO.setReceiverName(orderExtendPO.getReceiverName());
		orderReceiveInfoVO.setReceiverMobile(orderExtendPO.getReceiverMobile());
		orderReceiveInfoVO.setProvince(orderExtendPO.getReceiverProvinceCode());
		orderReceiveInfoVO.setCity(orderExtendPO.getReceiverCityCode());
		orderReceiveInfoVO.setDistrict(orderExtendPO.getReceiverDistrictCode());
		orderReceiveInfoVO.setTown(orderExtendPO.getReceiverTownCode());
		orderReceiveInfoVO.setDetailAddress(orderExtendPO.getReceiverAddress());
		orderReceiveInfoVO.setReceiveInfo(orderExtendPO.getReceiverInfo());

		return orderReceiveInfoVO;
	}

	/**
	 * 更新订单签收验证码
	 */
	public void updateOrderReceiveCode(String orderSn, String receiveCode) {
		if(org.springframework.util.StringUtils.isEmpty(orderSn) || org.springframework.util.StringUtils.isEmpty(receiveCode)) {
			return;
		}
		OrderExtendPO extendPO = getOrderExtendByOrderSn(orderSn);
		if(!org.springframework.util.StringUtils.isEmpty(extendPO.getReceiveCode())) {
			return;
		}
		LambdaUpdateWrapper<OrderExtendPO> updateWrapper = Wrappers.lambdaUpdate(OrderExtendPO.class);
		updateWrapper.eq(OrderExtendPO::getOrderSn, orderSn)
				.set(OrderExtendPO::getReceiveCode, receiveCode);
		boolean update = this.update(updateWrapper);
		log.info("updateOrderReceiveCode result :{}",update);
	}

	/**
	 * 处理历史实仓数据
	 *
	 * @param ignoreOrderSn 需要忽略的订单编号
	 */
	@Override
	public void dealHistoryActualWareHouse(String ignoreOrderSn) {
		LambdaQueryWrapper<OrderExtendPO> orderExtendQuery = Wrappers.lambdaQuery(OrderExtendPO.class);
		if (!StringUtils.isBlank(ignoreOrderSn)){
			List<String> ignoreOrderSnList = Arrays.asList(ignoreOrderSn.split(","));
			orderExtendQuery.notIn(OrderExtendPO::getOrderSn,ignoreOrderSnList);
		}
		orderExtendQuery.isNull(OrderExtendPO::getActualWarehouseCode);
		orderExtendQuery.isNotNull(OrderExtendPO::getPointId);
		orderExtendQuery.orderByDesc(OrderExtendPO::getCreateTime);
		orderExtendQuery.select(OrderExtendPO::getExtendId,OrderExtendPO::getPointId);
		// 一次处理500条
		orderExtendQuery.last(" limit 500");
		List<OrderExtendPO> dealList = list(orderExtendQuery);
		if (!CollectionUtils.isEmpty(dealList)){
			List<Long> pointIdList = dealList.stream().map(OrderExtendPO::getPointId).distinct().collect(Collectors.toList());
			List<SimpleSelfLiftingPointVo> selfLiftingPointBatchByPointId = shopIntegration.getSelfLiftingPointBatchByPointId(pointIdList);
			Map<Long, SimpleSelfLiftingPointVo> pointVoMap = selfLiftingPointBatchByPointId.stream().collect(Collectors.toMap(SimpleSelfLiftingPointVo::getPointId, Function.identity(), (oldObj, newObj) -> oldObj));
			for (OrderExtendPO extendPO : dealList) {
				Long pointId = extendPO.getPointId();
				SimpleSelfLiftingPointVo pointVo = pointVoMap.get(pointId);
				if (null != pointVo){
					extendPO.setActualWarehouseCode(pointVo.getActualDeptCode());
				}
			}
			updateBatchById(dealList);
		}else{
			log.info("dealHistoryActualWareHouse 没有需要处理的数据");
		}
	}

	/**
	 * 处理历史无自提点的自提订单
	 *
	 * @param orderSnList 订单编号列表，传空时处理全量
	 */
	@Override
	public void orderPointFix(List<String> orderSnList){
		List<OrderPointFixDTO> orderPointFixDTOS = orderMapper.listOrderPointFix(orderSnList);
		for (OrderPointFixDTO dto : orderPointFixDTOS) {
			try {
				log.info("orderPointFixJob 开始处理dto:{}",dto);
				SelfLiftingPointVo pointVo = shopIntegration.getSelfLiftingPointByStoreIdAndBranch(dto.getStoreId(), dto.getBranch());
				if (Objects.isNull(pointVo)){
					log.warn("OrderPointFixJob 无法匹配到自提点,dto:{}",dto);
					continue;
				}
				LambdaUpdateWrapper<OrderExtendPO> extendUpdate = Wrappers.lambdaUpdate(OrderExtendPO.class);
				extendUpdate.eq(OrderExtendPO::getExtendId,dto.getExtendId());
				extendUpdate.set(OrderExtendPO::getPointId,pointVo.getPointId());
				extendUpdate.set(OrderExtendPO::getPointName,pointVo.getPointName());
				update(extendUpdate);
				log.info("OrderPointFixJob 处理完成,dto:{}",dto);
			} catch (Exception e) {
				log.info("OrderPointFixJob 执行失败,dto:{}",dto);
			}
		}
	}
}
