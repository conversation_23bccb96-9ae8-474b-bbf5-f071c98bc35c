package com.cfpamf.ms.mallorder.scheduler;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.framework.autoconfigure.redis.lock.DistributeLock;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.LoanStatusEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.enums.OrderReturnStatus;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.common.util.UserUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.MessagePushFacade;
import com.cfpamf.ms.mallorder.mapper.OrderPresellMapper;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.req.msg.MessageRequest;
import com.cfpamf.ms.mallorder.req.msg.SendAllMessageRequest;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 系统自动取消没有支付尾款的预售订单（包括：尾款贷款类支付申请失败、尾款支付超时）
 */
@Component
@Slf4j
public class OrderCancelPreSellBalanceJob {

    @Resource
    private IOrderService orderService;

    @Resource
    private OrderPresellMapper orderPresellMapper;

    @Resource
    private OrderModel orderModel;

    @Resource
    private OrderReturnModel orderReturnModel;

    @Resource
    private IOrderReturnService orderReturnService;

    @Resource
    private MessagePushFacade messagePushFacade;

    @Autowired
    private DistributeLock distributeLock;

    @XxlJob(value = "OrderCancelPreSellBalanceJob")
    public ReturnT<String> execute(String s) {
        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("start job at local time: {}", LocalDateTime.now());
        log.info("jobSystemCancelOrder() start");
        String lockKey = "scheduler:OrderCancelPreSellBalanceJob:execute:";
        try {
            distributeLock.lockAndProcess(lockKey, 0, 5, TimeUnit.MINUTES,
                    () -> {
                        try {
                            // 处理尾款贷款类支付申请失败的预售订单
                            this.dealLoanFailPresellOrder();
                            // 处理尾款支付超时的预售订单
                            this.dealOutTimePresellOrder();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        return null;
                    });
        } catch (Exception e) {
            log.error("jobSystemCancelOrder()", e);
        }
        XxlJobHelper.log("finish job at local time: {}  cost:{}", LocalDateTime.now(),
                (System.currentTimeMillis() - startTime) / 1000);
        return ReturnT.SUCCESS;
    }

    /**
     * 处理尾款贷款类支付申请失败的预售订单
     */
    private void dealLoanFailPresellOrder() {
        // 查询尾款放款申请失败的预售订单
        LambdaQueryWrapper<OrderPO> loanFailPresellOrderQuery = Wrappers.lambdaQuery(OrderPO.class);
        loanFailPresellOrderQuery.eq(OrderPO::getOrderType, PromotionConst.PROMOTION_TYPE_107)
                .eq(OrderPO::getLoanPayState, LoanStatusEnum.APPLY_FAIL.getValue())
                .eq(OrderPO::getOrderState, OrderStatusEnum.DEAL_PAY.getValue());
        List<OrderPO> loanFailPresellOrders = orderService.list(loanFailPresellOrderQuery);
        XxlJobHelper.log("系统自动取消尾款支付放款失败的预售订单，查询尾款支付放款失败数为：{}", loanFailPresellOrders.size());
        log.info("系统自动取消尾款支付放款失败的预售订单，查询尾款支付放款失败数为：{}", loanFailPresellOrders.size());
        for (OrderPO orderPo : loanFailPresellOrders) {
            try {
                if (this.dealBalanceReturn(orderPo, "系统自动取消尾款支付放款失败的预售订单")) {
                    this.sendMessage(String.format("订单%s因尾款支付放款失败，系统自动关闭，订金已原路退回", orderPo.getOrderSn()), orderPo.getUserMobile());
                }
            } catch (Exception e) {
                log.error("系统自动取消尾款支付放款失败的预售订单失败，订单号：{}", orderPo.getOrderSn(), e);
            }
        }
    }

    /**
     * 处理尾款支付超时的预售订单
     */
    private void dealOutTimePresellOrder() {
        // 查询超时的待支付尾款的预售订单
        List<OrderPO> orderPos = orderPresellMapper.selectBalanceDealLineOrder();
        // TODO: 2023/2/17 收银台预付 -预付子单关单是否必要？
        XxlJobHelper.log("系统自动取消尾款支付超时的预售订单，查询尾款支付超时数为：{}", orderPos.size());
        log.info("系统自动取消尾款支付超时的预售订单，查询尾款支付超时数为：{}", orderPos.size());
        for (OrderPO orderPo : orderPos) {
            try {
                if (this.dealBalanceReturn(orderPo, "系统自动取消尾款支付超时的预售订单")) {
                    this.sendMessage(String.format("订单%s因尾款长时间未支付，系统自动关闭，订金已原路退回", orderPo.getOrderSn()), orderPo.getUserMobile());
                }
            } catch (Exception e) {
                log.error("系统自动取消尾款支付超时的预售订单失败，订单号：{}", orderPo.getOrderSn(), e);
            }
        }
    }

    private Boolean dealBalanceReturn(OrderPO orderPo, String cancelReason) {
        // 该订单已经有退款中的退款单时，不做处理
        if (orderReturnModel.whetherHasReturningProduct(orderPo.getOrderSn())) {
            return Boolean.FALSE;
        }

        // 生成退款单
        orderModel.orderCancelInsertAfterServiceAndReturn(orderPo, cancelReason, cancelReason, OrderConst.RETURN_BY_0,
                OrderConst.ADMIN_ROLE, (long) OrderConst.LOG_ROLE_ADMIN, OrderConst.LOG_USER_NAME, null);

        // 查询订单对应的退款单
        LambdaQueryWrapper<OrderReturnPO> returnQueryWrapper = Wrappers.lambdaQuery();
        returnQueryWrapper.select(OrderReturnPO::getAfsSn)
                .eq(OrderReturnPO::getOrderSn, orderPo.getOrderSn())
                .in(OrderReturnPO::getState, Arrays.asList(OrderReturnStatus.STORE_AGREE_REFUND.getValue(), OrderReturnStatus.STORE_RECEIVED.getValue()))
                .eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .last("limit 1");
        OrderReturnPO orderReturnPo = orderReturnService.getOne(returnQueryWrapper);
        log.info("系统自动取消尾款支付放款失败的预售订单, 对退款单：{} 进行退款", orderReturnPo.getAfsSn());

        // 自动审批通过
        orderReturnModel.adminRefundOperation(UserUtil.moocAdmin(), orderReturnPo.getAfsSn(), CommonConst.SYSTEM_PASS,
                null, true, OrderCreateChannel.WEB.getValue(), BigDecimal.ZERO);

        return Boolean.TRUE;
    }


    /**
     * 发送短信消息
     *
     * @param message       消息内容
     * @param telephone     接收号码
     */
    private void sendMessage(String message, String telephone) {
        try {
            SendAllMessageRequest sendRequest = new SendAllMessageRequest();
            sendRequest.setMqMsgId(UUID.randomUUID().toString());
            sendRequest.setMobile(telephone);
            sendRequest.setTargetPeople("Customer");
            sendRequest.setBusinessType("sldMallSms");
            sendRequest.setMessageRequest(new MessageRequest());
            sendRequest.getMessageRequest().setAppId("SldMall");
            sendRequest.getMessageRequest().setBody(JSONObject.toJSONString(Collections.singletonList(message)));
            messagePushFacade.sendAllMessageToChannels(sendRequest);
        } catch (Exception e) {
            log.error("预售订单尾款支付超时，发送短信消息失败，消息内容【{}】", message, e);
        }
    }

}
