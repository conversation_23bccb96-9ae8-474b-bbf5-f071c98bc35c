package com.cfpamf.ms.mallorder.v2.strategy.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.cfpamf.mallpayment.facade.request.CDMallRefundTryRequest;
import com.cfpamf.mallpayment.facade.vo.CDMallRefundTryResultVO;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.dto.UserDTO;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.mapper.OrderReturnTrackMapper;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.service.impl.OrderReturnTrackServiceImpl;
import com.cfpamf.ms.mallorder.vo.OrderReturnTrackVO;
import com.cfpamf.ms.mallorder.service.IOrderReturnLoanExtendService;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.mallpayment.facade.request.loan.CdmallSetlTryRequest;
import com.cfpamf.mallpayment.facade.request.loan.SetlTryResultVo;
import com.cfpamf.ms.mall.liquidate.enums.ActionEnum;
import com.cfpamf.ms.mall.liquidate.request.RevokeReq;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.CustomerServiceFeign;
import com.cfpamf.ms.mallorder.enums.InterestPayerEnum;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.model.OrderAfterServiceModel;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.service.IOrderAfterService;
import com.cfpamf.ms.mallorder.service.IOrderPlacingService;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.cfpamf.ms.mallorder.service.payment.IRefundHandleService;
import com.cfpamf.ms.mallorder.service.payment.RetundHandleType;
import com.cfpamf.ms.mallorder.v2.builder.PresellCombinationRefundBuilder;
import com.cfpamf.ms.mallorder.v2.common.OrderUtils;
import com.cfpamf.ms.mallorder.v2.common.annotation.Strategy;
import com.cfpamf.ms.mallorder.v2.domain.bo.OrderPayAddBalanceBO;
import com.cfpamf.ms.mallorder.v2.service.OrderPayRecordService;
import com.cfpamf.ms.mallorder.v2.service.OrderRefundRecordService;
import com.cfpamf.ms.mallorder.v2.service.PresellRefundService;
import com.cfpamf.ms.mallorder.v2.strategy.LoanRefundStrategy;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.util.AssertUtil;

import io.seata.core.context.RootContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 类PresellRefundStrategyImpl.java的实现描述：
 *
 * <AUTHOR> 10:08
 */
@Slf4j
@Service
@Strategy(code = PromotionConst.PROMOTION_TYPE_107)
public class PresellRefundStrategyImpl implements LoanRefundStrategy {

	@Autowired
	private OrderRefundRecordService orderRefundRecordService;

	@Autowired
	private OrderPayRecordService orderPayRecordService;

	@Autowired
	private IOrderAfterService orderAfterService;

	@Autowired
	private OrderAfterServiceModel orderAfterServiceModel;

	@Autowired
	private OrderReturnModel orderReturnModel;

	@Autowired
	private MemberFeignClient memberFeignClient;

	@Autowired
	private PayIntegration payIntegration;

	@Autowired
	private StoreFeignClient storeFeignClient;

	@Resource
	private IOrderReturnService orderReturnService;

	@Resource
	private IOrderReturnLoanExtendService orderReturnLoanExtendService;

	@Autowired
	private IOrderPlacingService orderPlacingService;

	@Autowired
	private CustomerServiceFeign customerServiceFeign;

	@Autowired
	private IRefundHandleService refundHandleService;
	
    @Autowired
    private PresellRefundService presellRefundService;

	@Resource
	private OrderReturnTrackMapper orderReturnTrackMapper;

	@Autowired
	private OrderReturnTrackServiceImpl orderReturnTrackService;

	@Override
	public boolean refundRefuse(String afsSn) {
		log.info("【refundRefuse】预付退款拒绝，恢复支付退款余额 afsSn：{}", afsSn);
		List<OrderRefundRecordPO> data = orderRefundRecordService.queryRefundOrderByAfsSn(afsSn);
		BizAssertUtil.notEmpty(data, String.format("预付订单退款单【%s】，拒绝退款失败。退款记录为找到，请联系技术支持！", afsSn));
		List<String> payNoList = data.stream().map(OrderRefundRecordPO::getPayNo).collect(Collectors.toList());
		List<OrderPayRecordPO> record = orderPayRecordService.queryOrderPayByPayNos(payNoList);
		log.info("【refundRefuse】预付退款拒绝，恢复支付退款余额 afsSn：{} data:{} record:{}", afsSn, data, record);
		// key=payNo,value=balance
		Map<String, BigDecimal> orderPayRecordMap = new HashMap<>();
		for (OrderPayRecordPO item : record) {
			// payNo是唯一，不需要考虑重复
			orderPayRecordMap.put(item.getPayNo(), item.getBalance());
		}
		BizAssertUtil.isTrue(ObjectUtils.isEmpty(orderPayRecordMap),
				String.format("预付订单退款单【%s】，拒绝退款失败。退款支付记录为找到，请联系技术支持！", afsSn));
		log.info("【refundRefuse】预付退款拒绝，恢复支付退款余额 afsSn：{} data:{} record:{} orderPayRecordMap:{}", afsSn, data, record,
				orderPayRecordMap);
		List<OrderPayAddBalanceBO> addBalanceList = new ArrayList<>();
		for (OrderRefundRecordPO item : data) {
			AssertUtil.isTrue(item.getRefundStatus() == CommonConst.REFUND_STATUS_0, "抱歉，退款单不能重复拒绝，系统出现未知异常，请联系管理员处理！");
			BigDecimal oldBalance = orderPayRecordMap.get(item.getPayNo());
			OrderPayAddBalanceBO orderPayAddBalanceBO = OrderPayAddBalanceBO.builder()
					.balance(oldBalance.add(item.getAmount())).oldBalance(oldBalance).payNo(item.getPayNo())
					.paySn(afsSn).build();
			addBalanceList.add(orderPayAddBalanceBO);
		}
		boolean status = orderPayRecordService.addBalanceBatchByPayNos(addBalanceList);
		log.info("【refundRefuse】预付退款拒绝，恢复支付退款余额 afsSn：{} data:{} record:{} status:{}", afsSn, data, record, status);
		if (status) {// 恢复额度成功，更新退款明细关闭
			status = orderRefundRecordService.updateRefundCloseByAfsSn(afsSn);
			log.info("【refundRefuse】预付退款拒绝，恢复支付退款余额 afsSn：{} 恢复额度成功，更新退款明细关闭，结果：{}", afsSn, status);
		}
		return status;
	}

	@Override
	public void verifyElectronicAccountBalance(String afsSn,OrderPO order) {
		log.info("【verifyElectronicAccountBalance】预付校验帐号余额afsSn：{} 预付无运费，newOrder:{}", afsSn,order.getNewOrder());

		List<OrderRefundRecordPO> data = orderRefundRecordService.queryWaitRefundOrderByAfsSn(afsSn);
		if (ObjectUtils.isEmpty(data)) {
			log.info("【verifyElectronicAccountBalance】预付，退款拒绝。原因：组合退款待退款数据明细为空未匹配，afsSn:{}", afsSn);
			throw new MallException("预付订单，退款拒绝。原因：组合退款待退款数据明细为空未匹配:" + afsSn, ErrorCodeEnum.U.CHECK_FAILURE.getCode());
		}
		if (ObjectUtils.isEmpty(order)) {
            log.info("【verifyElectronicAccountBalance】预付，退款拒绝。原因：传入的订单信息为空，afsSn:{}", afsSn);
            throw new MallException("预付订单，校验帐号余额不通过，退款失败。退款单号:" + afsSn, ErrorCodeEnum.U.CHECK_FAILURE.getCode());
        }
		for (OrderRefundRecordPO orderRefundRecordItem : data) {// 开始分配退款组合支付
			RevokeReq req = new RevokeReq();
			req.setActionType(ActionEnum.PRESELLDEPOSIT);
			req.setBizNo(orderRefundRecordItem.getRefundNo());
			req.setExecuteBizNo(orderRefundRecordItem.getPayNo());
			if(order.getNewOrder()) {//云直通
			    log.info("【verifyElectronicAccountBalance】预付校验帐号余额afsSn：{} 云直通校验", afsSn);
    			// 校验电子账户余额
    			refundHandleService.verifyElectronicAccountBalance(RetundHandleType.COMMON, req);
			} else {
			    log.info("【verifyElectronicAccountBalance】预付校验帐号余额afsSn：{} 非云直通校验，refundNo:{}", afsSn,orderRefundRecordItem.getRefundNo());
			    // 查询orderReturn
		        OrderReturnPO orderReturnPO = orderReturnService.getByAfsSn(afsSn);
		        log.info("【verifyElectronicAccountBalance】预付校验帐号余额afsSn：{} orderSn:{}", afsSn, orderReturnPO.getOrderSn());
			    orderReturnModel.thirdAccountBalanceCheck(order, orderReturnPO,orderRefundRecordItem.getActualAmount());
			}
		}
	}

	@Override
	public String tryCaculateChanged(String afsSn) {
		log.info("【tryCalculateChanged】预付试算afsSn：{} 预付无运费", afsSn);

		// 查询orderReturn
		OrderReturnPO orderReturnPO = orderReturnService.getByAfsSn(afsSn);

		log.info("预付试算，afsSn：{} orderSn:{}", afsSn, orderReturnPO.getOrderSn());
		Result<CDMallRefundTryResultVO> tryCaculateResult = null;

		OrderPO orderPo = orderPlacingService.getByOrderSn(orderReturnPO.getOrderSn());
		if (Objects.isNull(orderPo.getLendingSuccessTime())) {
			throw new MallException("预付还没有放款成功，请稍后重试！");
		}
		int days = DateUtil.days(DateUtil.format(orderPo.getLendingSuccessTime(), DateUtil.FORMAT_TIME),
				DateUtil.format(orderReturnPO.getApplyTime(), DateUtil.FORMAT_TIME), DateUtil.FORMAT_TIME);

		// 预付贷款支付是否参与本次还款
		OrderRefundRecordPO orderRefundRecordPO = orderRefundRecordService.queryLoanRefundByAfsSn(afsSn);
		if (ObjectUtils.isEmpty(orderRefundRecordPO) || ObjectUtils.isEmpty(orderRefundRecordPO.getPayNo())) {
			log.warn("【tryCalculateChanged】预付试算，预付订单未使用贷款支付，无需采用贷款退款，无需试算。afsSn：{} 订单号：{}",
					orderRefundRecordPO.getAfsSn(), orderReturnPO.getOrderSn());
			return OrderConst.RESULT_CODE_SUCCESS;
		}

		if(orderRefundRecordPO.getRefundType().equals(RefundType.SQUARE_OFFLINE_REFUND.getValue())) {
			return OrderConst.RESULT_CODE_SUCCESS;
		}

		// 试算发起金额
		BigDecimal tryAmount = orderRefundRecordPO.getAmount().subtract(orderReturnPO.getRefundPunishAmount());
		if (BigDecimal.ZERO.compareTo(tryAmount) == 0) {
			throw new MallException("因还款试算金额不足1元，系统无法进行在线退款，可返回调整“退款扣罚金额”。");
		}
		CDMallRefundTryRequest cdmallSetlTryRequest = new CDMallRefundTryRequest();
		cdmallSetlTryRequest.setOrderBatchId(orderRefundRecordPO.getPayNo());
		cdmallSetlTryRequest.setAmount(tryAmount);
		cdmallSetlTryRequest.setTryDate(DateUtil.format(new Date()));
		cdmallSetlTryRequest.setOperator(CommonConst.ADMIN_NAME_EN);
		log.info("【tryCalculateChanged】预付试算,-试算参数：{}", JSON.toJSONString(cdmallSetlTryRequest));
		tryCaculateResult = orderAfterServiceModel.getTryCaculateResult(cdmallSetlTryRequest);

		if (this.tryCalculateSettleValidate(tryCaculateResult) && !orderAfterServiceModel.isSquareOfflineRefundWhiteList(orderPo.getStoreId())) {
			 return "该借据已结清，请手动终止退款单！";
			//return OrderConst.RESULT_CODE_SUCCESS;
		}
		//退款额度小于=1元处理
        if("002005099".equals(tryCaculateResult.getErrorCode())) {
        	//tryCaculateResult = orderAfterServiceModel.getTryCaculateResult(BigDecimal.ZERO, afsSn);
        	log.info("【tryCalculateChanged】预付试算，退款额度小于=1元处理:{}，tryCalculateResult：{}",afsSn,tryCaculateResult);
			return "因还款试算金额不足1元，系统无法进行在线退款，可返回调整“退款扣罚金额”。";
		}

		//判断是否已结清，已结清&白名单商家，更改退款类型为线下退款 && 设置 退款扣罚、其他赔偿和客户承担为0
		//强制退款，已结清 && 白名单，改为商家线下退款；非强制退款，商家端审批 已结清 && 白名单 改为 商家线下退款
		if(orderAfterServiceModel.isOfflineWhiteListAndSettled(tryCaculateResult, orderPo.getStoreId())) {
			String content = "商家线下退款-因该用户已主动还清贷款,系统无法进行在线退款，只能由商家进行线下退款处理。";
			String isConfirmRefund = MDC.get("isConfirmRefund");
			//非平台端发起的退款
			if(!StringUtils.isEmpty(isConfirmRefund) && "true".equals(isConfirmRefund)
					&& OrderConst.RETURN_BY_3 != orderReturnPO.getReturnBy()) {
				return content;
			}

			//更改尾款 相应的数据
			LambdaUpdateWrapper<OrderRefundRecordPO> recordPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
			recordPOLambdaUpdateWrapper.set(OrderRefundRecordPO::getRefundType, RefundType.SQUARE_OFFLINE_REFUND.getValue())
					.set(OrderRefundRecordPO::getRefundTypeValue, RefundType.SQUARE_OFFLINE_REFUND.getDesc())
					.eq(OrderRefundRecordPO::getId, orderRefundRecordPO.getId());

			orderRefundRecordService.update(recordPOLambdaUpdateWrapper);

			OrderReturnPO orderReturnPOLatest = new OrderReturnPO();
			orderReturnPOLatest.setReturnId(orderReturnPO.getReturnId());
			orderReturnPOLatest.setCustomerAssumeAmount(BigDecimal.ZERO);
			orderReturnPOLatest.setOtherCompensationAmount(BigDecimal.ZERO);
			orderReturnPOLatest.setRefundPunishAmount(BigDecimal.ZERO);

			orderReturnModel.updateOrderReturn(orderReturnPOLatest);


			OrderAfterPO orderAfterPO = orderAfterServiceModel.getAfterServiceByAfsSn(afsSn);

			UserDTO userDTO;
			if(StringUtils.isEmpty(MDC.get("userDTO"))) {
				userDTO = new UserDTO(1L,"system",1);
			} else {
				userDTO = JSONObject.parseObject(MDC.get("userDTO"), UserDTO.class);
			}

			// 记录退款轨迹
			Integer userRole = userDTO.getUserRole() == 1 ? 3 : 2;

			orderReturnModel.insertReturnLog(afsSn, userRole, userDTO.getUserRole(), userDTO.getUserId(), userDTO.getUserName(),
					orderAfterPO.getAfsType(), orderReturnPO.getState().toString(), content, "WEB");


			return content;
		} else {
            CDMallRefundTryResultVO data = tryCaculateResult.getData();
			// 信贷应退总金额
			BigDecimal sumAmout = data.getSumAmount();
			if (sumAmout.compareTo(tryAmount) != 0) {
				log.info("【tryCalculateChanged】预付试算afsSn：{} orderSn:{} refundNo:{} 更新退款明细实际支付金额", afsSn,
						orderReturnPO.getOrderSn(), orderRefundRecordPO.getRefundNo());
				// 试算出来的金额与实际支付金额不一致，更新最新的退款金额
				orderRefundRecordService.updateRefundActualAmountById(orderRefundRecordPO.getId(), sumAmout,
						orderRefundRecordPO.getActualAmount());
			}
            if (!orderReturnLoanExtendService.saveUpdateOrderReturnLoanExtend(orderReturnPO.getOrderSn(), afsSn, orderRefundRecordPO.getPayNo(), data)) {
                throw new MallException(String.format("更新售后单贷款类信息失败,orderSn:%s,afsSn:%s,bizSn:%s,CDMallRefundTryResultVO:%s",
                        orderReturnPO.getOrderSn(), orderReturnPO.getAfsSn(), orderRefundRecordPO.getPayNo(), data));
            }
			log.info("【tryCalculateChanged】退款处理逻辑afsSn:{},sumAmount:{},tryAmount:{}", afsSn, sumAmout, orderRefundRecordPO.getAmount());
			OrderReturnPO orderReturnPOLatest = new OrderReturnPO();
			orderReturnPOLatest.setReturnId(orderReturnPO.getReturnId());
			orderReturnPOLatest.setActualReturnMoneyAmount(orderRefundRecordService.refundActualTotalAmountByAfsSn(afsSn));
			orderReturnPOLatest.setCustomerAssumeAmount(BigDecimal.ZERO);
			orderReturnPOLatest.setOtherCompensationAmount(BigDecimal.ZERO);
			orderReturnPOLatest.setInterestPayer(InterestPayerEnum.NULL.getDesc());
			if (sumAmout.compareTo(orderRefundRecordPO.getActualAmount()) == 0) {
				log.info("【tryCalculateChanged】退款处理逻辑afsSn:{},sumAmount:{},actualAmount:{} 信贷应退款金额与电商应退款金额一致。", afsSn, sumAmout, orderRefundRecordPO.getActualAmount());
				if (sumAmout.compareTo(tryAmount) > 0) {
					orderReturnPOLatest.setInterestPayer(days < 7 ? InterestPayerEnum.PLATFORM.getDesc() : InterestPayerEnum.STORE.getDesc());
					//计算其他赔偿
					orderReturnPOLatest.setOtherCompensationAmount(sumAmout.subtract(tryAmount));
					log.info("recalculate orderReturnPOLatest is {},", orderReturnPOLatest.toString());
					orderReturnModel.updateOrderReturn(orderReturnPOLatest);
				} else if (data.getSumAmount().compareTo(tryAmount) < 0) {
					//计算客户承担
					orderReturnPOLatest.setCustomerAssumeAmount(tryAmount.subtract(sumAmout));
					log.info("recalculate orderReturnPOLatest is {},", orderReturnPOLatest.toString());
					orderReturnModel.updateOrderReturn(orderReturnPOLatest);
				}
				return OrderConst.RESULT_CODE_SUCCESS;
			}

			//信贷应退总金额>电商实际退款金额 ->计算其他赔偿
			if (sumAmout.compareTo(tryAmount) > 0) {
				orderReturnPOLatest.setInterestPayer(days < 7 ? InterestPayerEnum.PLATFORM.getDesc() : InterestPayerEnum.STORE.getDesc());
				//计算其他赔偿
				orderReturnPOLatest.setOtherCompensationAmount(sumAmout.subtract(tryAmount));
				// 更新orderReturn表金额字段
				orderReturnModel.updateOrderReturn(orderReturnPOLatest);
				return "代还退款，产生差额" + orderReturnPOLatest.getOtherCompensationAmount() + "，计入【其他赔偿】，该笔金额" + orderReturnPOLatest.getInterestPayer();
			}
			//信贷应退总金额<电商实际退款金额 ->计算客户承担
			if (data.getSumAmount().compareTo(tryAmount) < 0) {
				//计算客户承担
				orderReturnPOLatest.setCustomerAssumeAmount(tryAmount.subtract(sumAmout));
				orderReturnModel.updateOrderReturn(orderReturnPOLatest);
				return "代还退款，产生差额" + orderReturnPOLatest.getCustomerAssumeAmount() + "，计入【客户承担】";
			}
			log.info("【tryCalculateChanged】试算afsSn：{} orderSn:{} 情况未匹配异常", afsSn, orderReturnPO.getOrderSn());
			throw new MallException("试算异常，退款单号【" + afsSn + "】请联系技术小哥哥，协助解决。");
		}
	}

	/**
	 * 是否为借据结清
	 *
	 * @param tryCalculateResult 借贷试算返回结果
	 * @return true / false
	 */
	private boolean tryCalculateSettleValidate(Result<CDMallRefundTryResultVO> tryCalculateResult) {
		return !tryCalculateResult.isSuccess() && "0024990099".equals(tryCalculateResult.getErrorCode());
	}

	@Override
	public void doReturnMoney(OrderReturnPO orderReturnPODb, OrderAfterPO orderAfterServicePODb, Admin admin,
			OrderPO orderPODb) {
		log.info("【doReturnMoney】预付afsSn：{} orderSn:{} xid:{}", orderReturnPODb.getAfsSn(),
				orderReturnPODb.getOrderSn(), RootContext.getXID());
		if (orderReturnPODb.getState().equals(OrderReturnStatus.REFUND_SUCCESS.getValue())) {
			log.warn("【doReturnMoney】预付，退款单状态400无需重复退款，afsSn：{} state:{}", orderReturnPODb.getAfsSn(),
					orderReturnPODb.getState());
			return;
		}
		// 查询订单信息
//		OrderPO orderPODb = orderModel.getOrderByOrderSn(orderAfterServicePODb.getOrderSn());
		// 查询会员信息
		Member memberDb = memberFeignClient.getMemberByMemberId(orderAfterServicePODb.getMemberId());
		AssertUtil.notNull(memberDb, "获取会员信息为空，请重试");

		if (orderPODb.getOrderType() == OrderConst.ORDER_TYPE_0) {
			log.warn("【doReturnMoney】预付，货到付款订单无需退款，orderSn:{} orderPO:{}", orderPODb.getOrderSn(), orderPODb);
			return;
		}
		if (OrderUtils.noThirdPartyOnlinePay(orderPODb)) {
			log.warn("【doReturnMoney】预付，非第三方支付方式支付订单无需第三方支付退款，orderSn:{} paymentCode:{} orderPO:{}",
					orderPODb.getOrderSn(), orderPODb.getPaymentCode(), orderPODb);
			return;
		}

		List<OrderRefundRecordPO> data = orderRefundRecordService
				.queryWaitRefundOrderByAfsSn(orderReturnPODb.getAfsSn());
		if (ObjectUtils.isEmpty(data)) {
			log.error("【doReturnMoney】预付，退款被拒绝。原因：组合退款待退款数据明细为空未匹配，orderSn:{} paymentCode:{} orderPO:{}",
					orderPODb.getOrderSn(), orderPODb.getPaymentCode(), orderPODb);
			throw new MallException("预付订单，退款被拒绝。原因：组合退款待退款数据明细为空未匹配:" + orderReturnPODb.getAfsSn(),
					ErrorCodeEnum.U.CHECK_FAILURE.getCode());
		}

		List<Integer> refundTypeList = data.stream().map(OrderRefundRecordPO::getRefundType)
				.collect(Collectors.toList());

		for (OrderRefundRecordPO orderRefundRecordItem : data) {// 开始分配退款组合支付
			if(CommonConst.REFUND_STATUS_2==orderRefundRecordItem.getRefundStatus()) {
				log.info("【doReturnMoney】预付 ActionEnum.PRESELLDEPOSIT 已经下账完成，无需重复下账和下载资金验证 afsSn：{} orderSn:{} refundNo:{}",
						orderReturnPODb.getAfsSn(), orderReturnPODb.getOrderSn(), orderRefundRecordItem.getRefundNo());
				continue;
			}
			
			// 实际退款金额<=0
			if (orderRefundRecordItem.getActualAmount().compareTo(BigDecimal.ZERO) <= 0) {
				log.info("【doReturnMoney】预付afsSn：{} orderSn:{} refundNo:{} newOrder：{} refundType:{} 实际退款金额<=0，拒绝操作！",
						orderReturnPODb.getAfsSn(), orderReturnPODb.getOrderSn(), orderRefundRecordItem.getRefundNo(),
						orderPODb.getNewOrder(), orderRefundRecordItem.getRefundType());
				continue;
			}
			log.info("【doReturnMoney】预付afsSn：{} orderSn:{} refundNo:{} newOrder：{} refundType:{}",
					orderReturnPODb.getAfsSn(), orderReturnPODb.getOrderSn(), orderRefundRecordItem.getRefundNo(),
					orderPODb.getNewOrder(), orderRefundRecordItem.getRefundType());

			// 获取退款类型，新订单 支付宝、微信、代还退款
			if (orderPODb.getNewOrder()
					&& !RefundType.RESTORE_LIMIT.getValue().equals(orderRefundRecordItem.getRefundType())) {
				RevokeReq req = new RevokeReq();
				//银行卡汇款，下账类型为单独的银行卡汇款回退。其余为预售订单充值回退
				if (RefundType.TRANSFER_REFUND.getValue().equals(orderRefundRecordItem.getRefundType())) {
					req.setActionType(ActionEnum.BANKTRANSFER);
				} else {
					req.setActionType(ActionEnum.PRESELLDEPOSIT);
				}
				req.setBizNo(orderRefundRecordItem.getRefundNo());
				req.setExecuteBizNo(orderRefundRecordItem.getPayNo());
				// 校验电子账户余额
				refundHandleService.verifyElectronicAccountBalance(RetundHandleType.COMMON, req);
				log.info("【doReturnMoney】预付afsSn：{} orderSn:{} refundNo:{} newOrder：{} orderState:{} orderType:{}",
						orderReturnPODb.getAfsSn(), orderReturnPODb.getOrderSn(), orderRefundRecordItem.getRefundNo(),
						orderPODb.getNewOrder(), orderPODb.getOrderState(), orderPODb.getOrderType());
				// 定金退款，无需分账+营销补差
				// 定金+尾款的退还才进入（不包括恢复额度）
				if (!OrderStatusEnum.isPresellDeposit(orderPODb.getOrderState(), orderPODb.getOrderType())
						&& !refundTypeList.contains(RefundType.RESTORE_LIMIT.getValue())
						&& !OrderStatusEnum.isPresellDeposit(orderPODb)
						&& !this.checkBalancePayMethod(orderPODb)) {
					log.info("【doReturnMoney】预付 开始 ActionEnum.SHAREPROFIT afsSn：{} orderSn:{} refundNo:{}",
							orderReturnPODb.getAfsSn(), orderReturnPODb.getOrderSn(),
							orderRefundRecordItem.getRefundNo());
					req = new RevokeReq();
					req.setBizNo(orderRefundRecordItem.getAfsSn());
					req.setActionType(ActionEnum.SHAREPROFIT);
					req.setNextActionType(new ActionEnum[]{ActionEnum.MARKETING});
					req.setExecuteBizNo(orderRefundRecordItem.getOrderSn());
					refundHandleService.issuedBill(RetundHandleType.COMMON, req);
				}
				log.info("【doReturnMoney】预付 ActionEnum.PRESELLDEPOSIT 下账afsSn：{} orderSn:{} refundNo:{}",
						orderReturnPODb.getAfsSn(), orderReturnPODb.getOrderSn(), orderRefundRecordItem.getRefundNo());
				req = new RevokeReq();
				//银行卡汇款，下账类型为单独的银行卡汇款回退。其余为预售订单充值回退
				if (RefundType.TRANSFER_REFUND.getValue().equals(orderRefundRecordItem.getRefundType())) {
					req.setActionType(ActionEnum.BANKTRANSFER);
				} else {
					req.setActionType(ActionEnum.PRESELLDEPOSIT);
				}
				req.setBizNo(orderRefundRecordItem.getRefundNo());
				req.setExecuteBizNo(orderRefundRecordItem.getPayNo());
				// 下账
				refundHandleService.issuedBill(RetundHandleType.COMMON, req);
				log.info("【doReturnMoney】预付 下账处理完成，afsSn：{} orderSn:{} refundNo:{}", orderReturnPODb.getAfsSn(),
						orderReturnPODb.getOrderSn(), orderRefundRecordItem.getRefundNo());
			    //下账完成，更新订单状态为退款中
				orderRefundRecordService.updateRefundProceedByRefundNo(orderRefundRecordItem.getRefundNo());
				continue;
			}
			
			log.info("【doReturnMoney】预付，开始确认是否为非云直通退款。RefundNo：{}", orderRefundRecordItem.getRefundNo());
			if(!orderPODb.getNewOrder() && !RefundType.RESTORE_LIMIT.getValue().equals(orderRefundRecordItem.getRefundType())){
                //如果是预售订单，退款退款走新的流程，Andy.2022.11.08
                log.info("预付订单非云直通模式，开始退款，afsSn：{} refundNo:{}",orderReturnPODb.getAfsSn(),orderRefundRecordItem.getRefundNo());
                presellRefundService.refund(orderRefundRecordItem.getActualAmount(),orderReturnPODb, orderAfterServicePODb ,orderRefundRecordItem.getRefundNo(),admin,null);
                orderRefundRecordService.updateRefundProceedByRefundNo(orderRefundRecordItem.getRefundNo());
                continue;
            }

			log.info("【doReturnMoney】预付，开始确认是否恢复额度处理RefundNo：{}", orderRefundRecordItem.getRefundNo());
			// refundType：3=用呗恢复额度
			if (RefundType.RESTORE_LIMIT.getValue().equals(orderRefundRecordItem.getRefundType())) {
				log.info("【doReturnMoney】预付，afsSn：{} orderSn:{} refundNo:{} 贷款类退款，开始用呗恢复额度.",
						orderReturnPODb.getAfsSn(), orderReturnPODb.getOrderSn(), orderRefundRecordItem.getRefundNo());
				payIntegration.loanRefundByPayment(orderPODb,
						PresellCombinationRefundBuilder.buildLoanPaymentRefundRequest(orderRefundRecordItem, orderPODb,
								orderReturnPODb, admin, orderAfterServiceModel, payIntegration, memberFeignClient,
								orderPlacingService, customerServiceFeign, orderAfterService, storeFeignClient,
								orderReturnService, orderRefundRecordService, orderReturnLoanExtendService ,null),
						orderReturnPODb.getAfsSn());
				log.info("【doReturnMoney】预付，afsSn：{} orderSn:{} refundNo:{} 贷款类退款，用呗恢复额度.截止",
						orderReturnPODb.getAfsSn(), orderReturnPODb.getOrderSn(), orderRefundRecordItem.getRefundNo());
				orderRefundRecordService.updateRefundProceedByRefundNo(orderRefundRecordItem.getRefundNo());
				continue;
			}
			
			log.error("【doReturnMoney】预付，未命中新订单和恢复额度，出现未在系统计划退款原因内的退款，系统暂不支持此种退款方式拒绝退款，afsSn：{} orderSn:{} refundNo:{}",
					orderReturnPODb.getAfsSn(), orderReturnPODb.getOrderSn(), orderRefundRecordItem.getRefundNo());
			throw new MallException("抱歉，预付订单，系统当前只支持云直通模式退款，当退款单出现非云直通退款方式，拒绝退款，请联系技术小哥哥协助处理!" + orderReturnPODb.getAfsSn(),
					ErrorCodeEnum.U.CHECK_FAILURE.getCode());
		}
		orderReturnModel.updateOrderRefundAmount(orderReturnPODb, orderPODb);
	}

	/**
	 *  判断尾款是不是银行卡汇款，汇款中，该情况下只退定金
	 * @param orderPO
	 * @return
	 */
	private boolean checkBalancePayMethod(OrderPO orderPO) {
		if (Objects.equals(orderPO.getOrderType(), OrderTypeEnum.PRE_SELL_DEPOSIT.getValue())
				&&orderPO.getOrderState().equals(OrderStatusEnum.DEAL_PAY.getValue())) {
			List<OrderPayRecordPO> orderPayRecordPOS = orderPayRecordService.lambdaQuery()
					.eq(OrderPayRecordPO::getPayOrder, PresellCapitalTypeEnum.BALANCE.getValue())
					.eq(OrderPayRecordPO::getPaySn, orderPO.getPaySn())
					.eq(OrderPayRecordPO::getPayStatus, CommonConst.PAY_STATUS_2)
					.eq(OrderPayRecordPO::getPaymentCode, PayMethodEnum.BANK_TRANSFER.getValue())
					.list();
			return !CollectionUtils.isEmpty(orderPayRecordPOS);
		}
		return false;
	}

}
