package com.cfpamf.ms.mallorder.common.enums;

import com.cfpamf.ms.mallorder.common.mq.RabbitMqConfig;
import com.cfpamf.ms.mallorder.dto.OrderAmountUpdateDTO;
import com.cfpamf.ms.mallorder.dto.OrderEventNotifyDTO;
import com.cfpamf.ms.mallorder.dto.OrderOperationEventNotifyDTO;
import com.cfpamf.ms.mallorder.dto.OrderRefundEventNotifyDTO;
import com.slodon.bbc.core.constant.StarterConfigConst;
import com.slodon.bbc.starter.mq.entity.MessageSendVO;

/**
 * MQ推送消息，交换机与消息题转换对象对应关系
 * <AUTHOR>
 *
 */
public enum RabbitMqEventEnum {

	ORDER_CHANGE(RabbitMqConfig.EXCHANGE_ORDER_CHANGE, OrderEventNotifyDTO.class),
	ORDER_REFUND_CHANGE(RabbitMqConfig.EXCHANGE_ORDER_REFUND_CHANGE, OrderRefundEventNotifyDTO.class),
	EXCHANGE_ORDER_OPERATION(RabbitMqConfig.EXCHANGE_ORDER_OPERATION_EVENT, OrderOperationEventNotifyDTO.class),
	ORDER_RELATION_AMOUNT_EXCHANGE(RabbitMqConfig.ORDER_RELATION_AMOUNT_UPDATE_FANOUT_EXCHANGE, OrderAmountUpdateDTO.class),
	MALL_MESSAGE_SEND_EXCHANGE(StarterConfigConst.MQ_EXCHANGE_NAME, MessageSendVO.class),
	// 电商订单支付成功，推送喜报MQ
	GOOD_NEWS_DATA_PUSH_EXCHANGE(RabbitMqConfig.PERFORMANCE_GOOD_NEWS_EXCHANGE_NAME, String.class);

	RabbitMqEventEnum(String exchang, Class<?> messageClass) {
		this.exchang = exchang;
		this.messageClass = messageClass;
	}

	private String exchang;

	private Class<?> messageClass;

	public String getExchang() {
		return exchang;
	}

	public void setExchang(String exchang) {
		this.exchang = exchang;
	}

	public Class<?> getMessageClass() {
		return messageClass;
	}

	public void setMessageClass(Class<?> messageClass) {
		this.messageClass = messageClass;
	}
	
	public static Class<?> messageClassByExchang(String exchang) {
		for (RabbitMqEventEnum item :RabbitMqEventEnum.values()) {
			if(item.exchang.equals(exchang)) {
				return item.getMessageClass();
			}
		}
		return null;
	}
	
}
