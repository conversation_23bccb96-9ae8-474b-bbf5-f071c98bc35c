package com.cfpamf.ms.mallorder.scheduler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.enums.OrderReturnStatus;
import com.cfpamf.ms.mallorder.common.enums.RefundType;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.model.OrderReturnModel;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.service.IOrderReturnService;
import com.cfpamf.ms.mallorder.service.IOrderService;
import com.cfpamf.ms.mallorder.v2.config.CommonConfig;
import com.cfpamf.ms.mallorder.v2.service.OrderRefundService;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Nmall-4587 微信退款/支付宝退款/乡助卡退款/卡券退款/乡助卡+微信（混合支付）/乡助卡+支付宝退款（混合支付）， 系统在待平台处理节点时（含【平台审核（仅退款）】【平台审核（退货退款）】，
 * 增加自动化任务，每5分钟执行一次；并且记录【平台审核】处理人员的轨迹为system，来源为web。 注意：如遇微信退款失败，系统每隔5分钟自动执行，5次后，监控预警挂起，等待人工处理。
 *
 * 0 0/5 * * * ?
 */
@Component
@Slf4j
public class PlatformRefundAutoAuditJob {

    private static final Integer PLATFORM_REFUND_AUTO_AUDIT_MAX_TIMES = 5;

    @Resource
    private IOrderReturnService orderReturnService;

    @Resource
    private OrderReturnModel orderReturnModel;

    @Autowired
    private OrderRefundService orderRefundService;

    @Autowired
    private IOrderService orderService;

    @Autowired
    private CommonConfig commonConfig;

    @XxlJob("PlatformRefundAutoAuditJob")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobHelper.log("=============== START PlatformRefundAutoAuditJob , DATE :{}", LocalDateTime.now());
        try {
            if (commonConfig.isOpenV2()) {
                log.info("开启V2流程系统自动审批平台审批订单，platformAutoAuditRefundExecute()");
                this.platformAutoAuditRefundExecute();
                return ReturnT.SUCCESS;
            }
        } catch (Exception e) {
            log.error("OrderCancelNewPeopleJob()", e);
        }
        // 查询自动平台审批通过的退款单
//        LambdaQueryWrapper<OrderReturnPO> orderReturnQuery = Wrappers.lambdaQuery(OrderReturnPO.class);
//        orderReturnQuery.eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
//            .in(OrderReturnPO::getState,
//                Arrays.asList(OrderReturnStatus.STORE_AGREE_REFUND.getValue(),
//                    OrderReturnStatus.STORE_RECEIVED.getValue()))
//            .in(OrderReturnPO::getRefundType,
//                Arrays.asList(RefundType.WXPAY_REFUND.getValue(), RefundType.APLIPAY_REFUND.getValue(),
//                    RefundType.CARD_REFUND.getValue(), RefundType.CARD_VOUCHER_REFUND.getValue()));
//        List<OrderReturnPO> orderReturnPos = orderReturnService.list(orderReturnQuery);

        // 获取待平台自动审批的订单：过滤退款状态（200、203），退款类型（5、6、9、11），非厂商模式订单
        List<OrderReturnPO> orderReturnPos = orderReturnService.getPlatformAutoAuditOrder();

        if (!CollectionUtils.isEmpty(orderReturnPos)) {
            XxlJobHelper.log("发起平台自动审核通过退款单数量为：{}", orderReturnPos.size());
            log.info("platformRefundAutoAuditJob : refund afs count is : {}", orderReturnPos.size());
            Admin admin = this.moocAdmin();

            // 一个周期一个支付单只处理一次：一个订单下存在多个退款单，不能并发处理，下一个周期再处理
            List<String> refundedPaySns = new ArrayList<>();

            for (OrderReturnPO orderReturnPo : orderReturnPos) {
                if (orderReturnPo
                    .getPlatformAutoAuditFailTimes() >= PlatformRefundAutoAuditJob.PLATFORM_REFUND_AUTO_AUDIT_MAX_TIMES) {
                    XxlJobHelper.log("退款单{}平台自动退款失败次数已达{}次，拒绝处理", orderReturnPo.getAfsSn(),
                        orderReturnPo.getPlatformAutoAuditFailTimes());
                    continue;
                }

                // 查售后单对应支付单信息
                OrderPO orderPO = orderService.getByOrderSn(orderReturnPo.getOrderSn());

                if (refundedPaySns.contains(orderPO.getPaySn())) {
                    log.info("platformRefundAutoAuditJob : orderSn{} has refunded once this time , pass", orderReturnPo.getOrderSn());
                    continue;
                }

                try {
                    log.info("退款单{}发起平台自动退款", orderReturnPo.getAfsSn());
                    orderReturnModel.adminRefundOperation(admin, orderReturnPo.getAfsSn(), CommonConst.SYSTEM_PASS,
                        null, true, OrderCreateChannel.WEB.getValue(), BigDecimal.ZERO);
                    refundedPaySns.add(orderPO.getPaySn());
                } catch (Exception e) {
                    log.warn("平台自动审核通过退款单异常，退款单号{}", orderReturnPo.getAfsSn(), e);
                    // 退款单平台自动审核失败次数增加1
                    LambdaUpdateWrapper<OrderReturnPO> orderReturnUpdate = Wrappers.lambdaUpdate(OrderReturnPO.class);
                    orderReturnUpdate.eq(OrderReturnPO::getAfsSn, orderReturnPo.getAfsSn()).set(
                        OrderReturnPO::getPlatformAutoAuditFailTimes,
                        Optional.ofNullable(orderReturnPo.getPlatformAutoAuditFailTimes()).orElse(0) + 1);
                    orderReturnService.update(orderReturnUpdate);
                }
            }
        }
        XxlJobHelper.log("=============== END PlatformRefundAutoAuditJob , DATE :{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

    private void platformAutoAuditRefundExecute() throws Exception {
        XxlJobHelper.log("=============== START platformAutoAuditRefundExecute 开启V2流程 , DATE :{}", LocalDateTime.now());

        // 查询自动平台审批通过的退款单
        LambdaQueryWrapper<OrderReturnPO> orderReturnQuery = Wrappers.lambdaQuery(OrderReturnPO.class);
        orderReturnQuery.eq(OrderReturnPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
            .in(OrderReturnPO::getState,
                Arrays.asList(OrderReturnStatus.STORE_AGREE_REFUND.getValue(),
                    OrderReturnStatus.STORE_RECEIVED.getValue()))
            .in(OrderReturnPO::getRefundType,
                Arrays.asList(RefundType.WXPAY_REFUND.getValue(), RefundType.APLIPAY_REFUND.getValue(),
                    RefundType.CARD_REFUND.getValue(), RefundType.CARD_VOUCHER_REFUND.getValue()));
        List<OrderReturnPO> orderReturnPos = orderReturnService.list(orderReturnQuery);

        if (!CollectionUtils.isEmpty(orderReturnPos)) {
            XxlJobHelper.log("开启V2流程-发起平台自动审核通过退款单数量为：{}", orderReturnPos.size());
            Admin admin = this.moocAdmin();
            for (OrderReturnPO orderReturnPo : orderReturnPos) {
                if (orderReturnPo
                    .getPlatformAutoAuditFailTimes() >= PlatformRefundAutoAuditJob.PLATFORM_REFUND_AUTO_AUDIT_MAX_TIMES) {
                    XxlJobHelper.log("开启V2流程-退款单{}平台自动退款失败次数已达{}次，拒绝处理", orderReturnPo.getAfsSn(),
                        orderReturnPo.getPlatformAutoAuditFailTimes());
                    continue;
                }
                try {
                    log.info("开启V2流程-退款单{}发起平台自动退款", orderReturnPo.getAfsSn());
                    orderRefundService.adminRefundAudit(admin, Arrays.asList(orderReturnPo.getAfsSn()), CommonConst.SYSTEM_PASS, null, 
                        OrderCreateChannel.WEB.getValue(),true);
                } catch (Exception e) {
                    log.warn("开启V2流程-平台自动审核通过退款单异常，退款单号{}", orderReturnPo.getAfsSn(), e);
                    // 退款单平台自动审核失败次数增加1
                    LambdaUpdateWrapper<OrderReturnPO> orderReturnUpdate = Wrappers.lambdaUpdate(OrderReturnPO.class);
                    orderReturnUpdate.eq(OrderReturnPO::getAfsSn, orderReturnPo.getAfsSn()).set(
                        OrderReturnPO::getPlatformAutoAuditFailTimes,
                        Optional.ofNullable(orderReturnPo.getPlatformAutoAuditFailTimes()).orElse(0) + 1);
                    orderReturnService.update(orderReturnUpdate);
                }
            }
        }
        XxlJobHelper.log("=============== END PlatformRefundAutoAuditJob 开启V2流程, DATE :{}", LocalDateTime.now());
    }

    /**
     * 构造审核用户
     */
    private Admin moocAdmin() {
        Admin moocAdmin = new Admin();
        moocAdmin.setAdminId(CommonConst.SYSTEM_ID);
        moocAdmin.setAdminName(CommonConst.ADMIN_NAME_EN);
       // moocAdmin.setRoleId(CommonConst.SYSTEM_ID);
        moocAdmin.setPhone(CommonConst.ADMIN_PHONE);
        return moocAdmin;
    }

}
