package com.cfpamf.ms.mallorder.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR> 2021/8/27.
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelImportProperty {

    /**
     * 是否需要获取
     */
    boolean required() default true;

    /**
     * 获取字段
     */
    String name();

}
