package com.cfpamf.ms.mallorder.v2.builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.constant.OrderPaymentConst;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.po.OrderPresellPO;
import com.cfpamf.ms.mallorder.v2.domain.dto.OrderPresellDTO;
import org.apache.commons.lang3.StringUtils;

/**
 * 订单预售信息构造
 */
public class OrderPresellBuilder {

    public static OrderPresellPO buildOrderPresellPO(String orderSn, String paySn, String payNo, Integer type,
        BigDecimal totalAmount, BigDecimal discountAmount, BigDecimal payAmount, LocalDateTime deadTime,
        String memberName) {
        OrderPresellPO orderPresellPO = new OrderPresellPO();
        orderPresellPO.setOrderSn(orderSn);
        orderPresellPO.setPaySn(paySn);
        orderPresellPO.setPayNo(payNo);
        orderPresellPO.setTotalAmount(payAmount);
        orderPresellPO.setPayAmount(payAmount);
        orderPresellPO.setAmount(totalAmount);
        orderPresellPO.setDiscountAmount(discountAmount);
        orderPresellPO.setType(type);
        orderPresellPO.setDeadTime(deadTime);
        orderPresellPO.setPaymentCode(OrderPaymentConst.PAYMENT_CODE_ONLINE);
        orderPresellPO.setPaymentName(OrderPaymentConst.PAYMENT_NAME_ONLINE);
        orderPresellPO.setCreateBy(memberName);
        orderPresellPO.setUpdateBy(memberName);
        return orderPresellPO;
    }

    /**
     * 构建支付成功更新对象
     * 
     * @param payNo
     * @param paymentCode
     * @param paymentName
     * @param payTime
     * @return
     */
    public static OrderPresellPO buildPaySuccessOrderPresellPO(String payNo, String paymentCode, String paymentName,
        Date payTime) {
        OrderPresellPO orderPresellPO = new OrderPresellPO();
        orderPresellPO.setPayNo(payNo);
        orderPresellPO.setPaymentCode(paymentCode);
        orderPresellPO.setPaymentName(paymentName);
        orderPresellPO.setPayTime(DateUtil.dateToLocalDateTime(payTime));
        orderPresellPO.setPayStatus(CommonConst.PAY_STATUS_3);
        orderPresellPO.setUpdateTime(new Date());
        return orderPresellPO;
    }

    public static String dealPreSellComposePayName(OrderPresellDTO orderPresellDTO) {

        String composePayName = "";
        if(StringUtils.isNotEmpty(orderPresellDTO.getDepositPaymentCode()) && PayMethodEnum.getValue(orderPresellDTO.getDepositPaymentCode()) != null){
            composePayName += PayMethodEnum.getValue(orderPresellDTO.getDepositPaymentCode()).getDesc();
        }
        if(StringUtils.isNotEmpty(orderPresellDTO.getRemainPaymentCode()) && PayMethodEnum.getValue(orderPresellDTO.getRemainPaymentCode()) != null){
            composePayName = composePayName + "+" + PayMethodEnum.getValue(orderPresellDTO.getRemainPaymentCode()).getDesc();
        }
        return composePayName;

    }

}
