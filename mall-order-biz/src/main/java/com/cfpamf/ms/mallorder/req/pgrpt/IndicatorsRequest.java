package com.cfpamf.ms.mallorder.req.pgrpt;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class IndicatorsRequest {

    @NotBlank(message = "类型不能为空")
    @ApiModelProperty(value = "1.区域 2.分支 3.客户经理 4.督导， 必填")
    private String type;

    @NotEmpty(message = "查询编码不能为空")
    @ApiModelProperty(value = "多个编码列表，客户经理、督导为工号，分支以及区域为分支以及区域编码 必填")
    private List<String> codeList;

    @NotEmpty(message = "查询指标不能为空")
    @ApiModelProperty(value = "查询指标类型列表")
    private List<String> indicatorTypeList;

    @ApiModelProperty(value = "开始日期： 2023-08-01")
    private String startDate;

    @ApiModelProperty(value = "结束日期 ： 2023-08-03")
    private String endDate;
}
