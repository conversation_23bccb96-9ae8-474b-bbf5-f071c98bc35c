package com.cfpamf.ms.mallorder.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;

/**
 * 酒水PK-销售订单明细表
 */
@Data
@Builder
public class JsPkDynamicPerformanceVO implements Serializable {
    @ApiModelProperty("统计日期")
    private String rptDate;

    @ApiModelProperty("订单号")
    private String mallOrderNo;

    @ApiModelProperty("子订单号")
    private String mallSubOrderNo;

    @ApiModelProperty(value = "支付时间")
    @TableField(value = "pay_time", jdbcType = JdbcType.TIMESTAMP)
    private Timestamp payTime;

    @ApiModelProperty("商品数量")
    @TableField(value = "product_num", jdbcType = JdbcType.INTEGER)
    private Integer productNum;

    @ApiModelProperty("成功退款数量")
    @TableField(value = "refund_Num", jdbcType = JdbcType.INTEGER)
    private Integer refundNum;

    @ApiModelProperty("商品单价")
    private BigDecimal unitPrice;

    @ApiModelProperty("erp系统toc货品单价")
    private BigDecimal toCUnitPrice;

    @ApiModelProperty("订单归属分支")
    private String bchCode;

    @ApiModelProperty("推荐人ID")
    private String recommendUserId;

    @ApiModelProperty("推荐人姓名")
    private String recommendUserName;

    @ApiModelProperty("订单归属分支名称")
    private String bchName;

    @ApiModelProperty("有效销售额")
    private BigDecimal wineValidSaleAmt;

    @ApiModelProperty("营收")
    private BigDecimal wineRevenueAmt;
}