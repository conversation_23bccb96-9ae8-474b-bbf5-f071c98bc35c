package com.cfpamf.ms.mallorder.v2.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import com.cfpamf.ms.mallorder.v2.utils.SpringContextHolder;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@RefreshScope
@Configuration
public class CommonConfig {

    @Value("${open.v2:false}")
    private String openV2;
    
    @Value("${enable.tcc:false}")
    private String enableTcc;

    @Value("${enable.transactionlog:true}")
    private String enableTransaction;

    
    public boolean isOpenV2() {
        try {
//            return this.openV2.trim().equalsIgnoreCase("true");
            return false;
        } catch (Exception e) {
            log.error("是否开启V2流程，异常", e);
            return Boolean.FALSE;
        }
    }
    
    public static boolean enableTcc() {
        try {
            CommonConfig commonConfig = SpringContextHolder.getApplicationContext().getBean(CommonConfig.class);  
            boolean enableTcc= commonConfig.getEnableTcc().trim().equalsIgnoreCase("true");
            log.info("是否开启tcc流程（enableTcc），enableTcc：{}",enableTcc);
            return enableTcc;
        } catch (Exception e) {
            log.error("是否开启tcc流程（enableTcc），异常", e);
            return Boolean.FALSE;
        }
    }

    /**
     * 是否开启了事务
     *
     * @return 是否开启
     */
    public static boolean enableTransactionLog() {
        try {
            CommonConfig commonConfig = SpringContextHolder.getApplicationContext().getBean(CommonConfig.class);
            boolean enableTransaction= commonConfig.getEnableTransaction().trim().equalsIgnoreCase("true");
            log.info("是否开启事务流程（enableTransaction），enableTransaction：{}",enableTransaction);
            return enableTransaction;
        } catch (Exception e) {
            log.error("是否开启事务流程（enableTransaction），异常", e);
            return Boolean.FALSE;
        }
    }

}
