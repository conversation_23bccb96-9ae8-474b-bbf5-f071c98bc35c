package com.cfpamf.ms.mallorder.service;

/**
 * 退款日志service
 */
public interface IOrderAfterSaleLogService {

    /**
     *
     * @param logRole           操作人角色
     * @param logUserId         操作人id
     * @param logUserName       操作人名称
     * @param afsSn             售后单号
     * @param afsType           售后类型
     * @param state             状态
     * @param content           内容
     * @param channel           渠道
     * @return                  新增结果 true/false
     */
    boolean insertOrderAfterSaleLog(Integer logRole, Long logUserId, String logUserName, String afsSn, Integer afsType,
                                   String state, String content, String channel);

}
