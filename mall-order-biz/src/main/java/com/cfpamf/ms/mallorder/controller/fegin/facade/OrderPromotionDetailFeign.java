package com.cfpamf.ms.mallorder.controller.fegin.facade;


import java.util.List;
import javax.annotation.Resource;

import com.cfpamf.ms.mallorder.model.OrderPromotionDetailModel;
import com.cfpamf.ms.mallorder.po.OrderPromotionDetailPO;
import com.cfpamf.ms.mallorder.request.OrderPromotionDetailExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 订单活动优惠明细feign
 */
@RestController
@Slf4j
public class OrderPromotionDetailFeign {

    @Resource
    private OrderPromotionDetailModel orderPromotionDetailModel;

    /**
     * 根据detailId获取订单活动优惠明细详情
     *
     * @param detailId detailId
     * @return
     */
    @GetMapping("/v1/feign/business/orderPromotionDetail/get")
    public OrderPromotionDetailPO getOrderPromotionDetailByDetailId(@RequestParam("detailId") Integer detailId) {
        return orderPromotionDetailModel.getOrderPromotionDetailByDetailId(detailId);
    }

    /**
     * 获取条件获取订单活动优惠明细列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/orderPromotionDetail/getList")
    public List<OrderPromotionDetailPO> getOrderPromotionDetailList(@RequestBody OrderPromotionDetailExample example) {
        return orderPromotionDetailModel.getOrderPromotionDetailList(example, example.getPager());
    }

    /**
     * 新增订单活动优惠明细
     *
     * @param orderPromotionDetailPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderPromotionDetail/addOrderPromotionDetail")
    public Integer saveOrderPromotionDetail(@RequestBody OrderPromotionDetailPO orderPromotionDetailPO) {
        return orderPromotionDetailModel.saveOrderPromotionDetail(orderPromotionDetailPO);
    }

    /**
     * 根据detailId更新订单活动优惠明细
     *
     * @param orderPromotionDetailPO
     * @return
     */
    @PostMapping("/v1/feign/business/orderPromotionDetail/updateOrderPromotionDetail")
    public Integer updateOrderPromotionDetail(@RequestBody OrderPromotionDetailPO orderPromotionDetailPO) {
        return orderPromotionDetailModel.updateOrderPromotionDetail(orderPromotionDetailPO);
    }

    /**
     * 根据detailId删除订单活动优惠明细
     *
     * @param detailId detailId
     * @return
     */
    @PostMapping("/v1/feign/business/orderPromotionDetail/deleteOrderPromotionDetail")
    public Integer deleteOrderPromotionDetail(@RequestParam("detailId") Integer detailId) {
        return orderPromotionDetailModel.deleteOrderPromotionDetail(detailId);
    }
}