package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.ms.mallorder.model.OrderAfterServiceModel;
import com.cfpamf.ms.mallorder.po.OrderAfterPO;
import com.cfpamf.ms.mallorder.request.OrderAfterServiceExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
public class OrderAfterServiceFeign {

    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;

    /**
     * 根据afsId获取订单售后服务表详情
     *
     * @param afsId afsId
     * @return
     */
    @GetMapping("/v1/feign/business/orderAfterService/get")
    public OrderAfterPO getOrderAfterServiceByAfsId(@RequestParam("afsId") Integer afsId) {
        return orderAfterServiceModel.getOrderAfterServiceByAfsId(afsId);
    }

    /**
     * 获取条件获取订单售后服务表列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/orderAfterService/getList")
    public List<OrderAfterPO> getOrderAfterServiceList(@RequestBody OrderAfterServiceExample example) {
        return orderAfterServiceModel.getOrderAfterServiceList(example, example.getPager());
    }

    /**
     * 新增订单售后服务表
     *
     * @param orderAfterServicePO
     * @return
     */
    @PostMapping("/v1/feign/business/orderAfterService/addOrderAfterService")
    public Integer saveOrderAfterService(@RequestBody OrderAfterPO orderAfterServicePO) {
        return orderAfterServiceModel.saveOrderAfterService(orderAfterServicePO);
    }

    /**
     * 根据afsId更新订单售后服务表
     *
     * @param orderAfterServicePO
     * @return
     */
    @PostMapping("/v1/feign/business/orderAfterService/updateOrderAfterService")
    public Integer updateOrderAfterService(@RequestBody OrderAfterPO orderAfterServicePO) {
        return orderAfterServiceModel.updateOrderAfterService(orderAfterServicePO);
    }

    /**
     * 根据afsId删除订单售后服务表
     *
     * @param afsId afsId
     * @return
     */
    @PostMapping("/v1/feign/business/orderAfterService/deleteOrderAfterService")
    public Integer deleteOrderAfterService(@RequestParam("afsId") Integer afsId) {
        return orderAfterServiceModel.deleteOrderAfterService(afsId);
    }

    /**
     * 系统自动处理售后单
     * 1.商户未审核的退货退款申请
     * 2.商户未审核的换货
     * 3.商户未审核的仅退款申请
     * 4.用户退货发货但是到时间限制商户还未收货
     *
     * @return
     */
    @GetMapping("/v1/feign/business/orderAfterService/jobSystemDealAfterService")
    public Map<String, List<?>> jobSystemDealAfterService() {
        return orderAfterServiceModel.jobSystemDealAfterService();
    }
}