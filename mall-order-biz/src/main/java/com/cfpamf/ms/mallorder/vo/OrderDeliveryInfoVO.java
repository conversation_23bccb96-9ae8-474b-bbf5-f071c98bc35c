package com.cfpamf.ms.mallorder.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class OrderDeliveryInfoVO {

	@ApiModelProperty("物流信息集合")
	private List<OrderFrontDeliveryVO> orderFrontDeliveryVOList;

	@ApiModelProperty(value = "订单号")
	private String orderSn;

	@ApiModelProperty("发货时间")
	private Date deliverTime;

	@ApiModelProperty("订单状态：0-已取消；10-未付款；15-付款中；20-已付款；30-已发货；40-已完成;50-已关闭")
	private Integer orderState;

}
