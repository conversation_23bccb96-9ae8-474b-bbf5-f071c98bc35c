package com.cfpamf.ms.mallorder.controller.fegin.facade;

import com.cfpamf.ms.mallorder.model.ComplainModel;
import com.cfpamf.ms.mallorder.po.ComplainPO;
import com.cfpamf.ms.mallorder.request.ComplainExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Slf4j
public class ComplainFeign {

    @Resource
    private ComplainModel complainModel;

    /**
     * 根据complainId获取投诉表详情
     *
     * @param complainId complainId
     * @return
     */
    @GetMapping("/v1/feign/business/complain/get")
    public ComplainPO getComplainByComplainId(@RequestParam("complainId") Integer complainId) {
        return complainModel.getComplainByComplainId(complainId);
    }

    /**
     * 获取条件获取投诉表列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping("/v1/feign/business/complain/getList")
    public List<ComplainPO> getComplainList(@RequestBody ComplainExample example) {
        return complainModel.getComplainList(example, example.getPager());
    }

    /**
     * 新增投诉表
     *
     * @param complainPO
     * @return
     */
    @PostMapping("/v1/feign/business/complain/addComplain")
    public Integer saveComplain(@RequestBody ComplainPO complainPO) {
        return complainModel.saveComplain(complainPO);
    }

    /**
     * 根据complainId更新投诉表
     *
     * @param complainPO
     * @return
     */
    @PostMapping("/v1/feign/business/complain/updateComplain")
    public Integer updateComplain(@RequestBody ComplainPO complainPO) {
        return complainModel.updateComplain(complainPO);
    }

    /**
     * 根据complainId删除投诉表
     *
     * @param complainId complainId
     * @return
     */
    @PostMapping("/v1/feign/business/complain/deleteComplain")
    public Integer deleteComplain(@RequestParam("complainId") Integer complainId) {
        return complainModel.deleteComplain(complainId);
    }

    /**
     * 定时处理超过3天的交易投诉交由商家申诉
     *
     * @return
     */
    @GetMapping("/v1/feign/business/complain/jobSubmitComplaint")
    public boolean jobSubmitStoreComplaint() {
        return complainModel.jobSubmitStoreComplaint();
    }

    /**
     * 定时处理商家申诉超过7天的交易投诉自动投诉成功
     *
     * @return
     */
    @GetMapping("/v1/feign/business/complain/jobAutoComplaintSuccess")
    public boolean jobAutoComplaintSuccess() {
        return complainModel.jobAutoComplaintSuccess();
    }
}