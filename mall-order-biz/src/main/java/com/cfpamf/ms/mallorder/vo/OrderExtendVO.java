package com.cfpamf.ms.mallorder.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单信息扩展表
 */
@Data
public class OrderExtendVO implements Serializable {
    private static final long serialVersionUID = -8353312635429901376L;

    @ApiModelProperty("扩展id")
    private Integer extendId;

    @ApiModelProperty("关联的订单编号")
    private String orderSn;

    @ApiModelProperty("客户ID")
    private String customerId;

    @ApiModelProperty("客户姓名")
    private String customerName;

    @ApiModelProperty("客户经理工号")
    private String manager;

    @ApiModelProperty("客户经理姓名")
    private String managerName;

    @ApiModelProperty("督导工号")
    private String supervisor;

    @ApiModelProperty("发货地址")
    private String deliverPlace;

    @ApiModelProperty("预估运费")
    private BigDecimal estimateExpressFee;

    @ApiModelProperty("发货要求：1：随时发货 2：10至20个自然日之间")
    private Integer deliveryRequirements;

    @ApiModelProperty("运费计算方式：1：普通方式；2：按距离计算")
    private Integer expressCalculateType;

    @ApiModelProperty("督导姓名")
    private String supervisorName;

    @ApiModelProperty("分支编号")
    private String branch;

    @ApiModelProperty("分支名称")
    private String branchName;

    @ApiModelProperty("区域编号")
    private String areaCode;

    @ApiModelProperty("区域名称")
    private String areaName;

    @ApiModelProperty("店铺ID")
    private Long storeId;

    @ApiModelProperty("评价时间")
    private Date evaluationTime;

    @ApiModelProperty("用户订单备注")
    private String orderRemark;

    @ApiModelProperty("订单赠送积分")
    private Integer orderPointsCount;

    @ApiModelProperty("优惠券面额")
    private BigDecimal voucherPrice;

    @ApiModelProperty("优惠券编码")
    private String voucherCode;

    @ApiModelProperty("乡助卡优惠券使用列表")
    private String xzCardList;

    @ApiModelProperty("订单来源1、pc；2、H5；3、Android；4、IOS; 5-微信小程序")
    private Integer orderFrom;

    @ApiModelProperty("发货地址ID")
    private Integer deliverAddressId;

    @ApiModelProperty("收货省份编码")
    private String receiverProvinceCode;

    @ApiModelProperty("省市区组合")
    private String receiverAreaInfo;

    @ApiModelProperty("收货人详细地址")
    private String receiverAddress;

    @ApiModelProperty("收货人手机号")
    private String receiverMobile;

    @ApiModelProperty("收货城市编码")
    private String receiverCityCode;

    @ApiModelProperty("收货区县编码")
    private String receiverDistrictCode;

    @ApiModelProperty("收货县/镇编码")
    private String receiverTownCode;

    @ApiModelProperty("收货人姓名")
    private String receiverName;

    @ApiModelProperty("收货人详细地址信息")
    private String receiverInfo;

    @ApiModelProperty("发票信息 json格式")
    private String invoiceInfo;

    @ApiModelProperty("促销信息备注")
    private String promotionInfo;

    @ApiModelProperty("是否是电子面单")
    private Integer isDzmd;

    @ApiModelProperty("发票状态0-未开、1-已开")
    private Integer invoiceStatus;

    @ApiModelProperty("商家优惠券优惠金额")
    private BigDecimal storeVoucherAmount;

    @ApiModelProperty("平台优惠券优惠金额")
    private BigDecimal platformVoucherAmount;

    @ApiModelProperty("邀请人分享码")
    private String shareCode;

    @ApiModelProperty("传输金蝶 1:是 0:否 默认否")
    private Integer jindieTransFlag;

    @ApiModelProperty("员工工号")
    private String userCode;

    @ApiModelProperty(value = "分类名称编码")
    private String categoryCode;

    @ApiModelProperty(value = "分类名称")
    private String categoryName;
}