package com.cfpamf.ms.mallorder.common.enums;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 银行转账汇款枚举，来自需求M109-银行卡汇款支付
 *
 * <AUTHOR>
 * @date 2021-12-16
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum TransferStatusEnum implements IEnum<Integer> {

    WAIT_TRANSFER(0, "待支付"),
    DEAL_TRANSFER(1, "汇款中"),
    SUCCESS(2, "汇款成功"),
    EXPIRED(3, "已失效过期"),
    APPLY_CANCEL(4, "申请取消"),
    CANCEL_TRANSFER(5, "取消汇款");


    @Override
    public Integer getValue() {
        return this.value;
    }

    Integer value;
    String desc;
}
