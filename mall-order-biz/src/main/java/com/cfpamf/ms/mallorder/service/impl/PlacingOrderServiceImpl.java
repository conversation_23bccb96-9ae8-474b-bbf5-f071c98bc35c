package com.cfpamf.ms.mallorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.framework.autoconfigure.redis.lock.SlodonLock;
import com.cfpamf.ms.mallgoods.facade.api.GoodsFeignClient;
import com.cfpamf.ms.mallgoods.facade.vo.Goods;
import com.cfpamf.ms.mallgoods.facade.vo.GoodsPromotion;
import com.cfpamf.ms.mallgoods.facade.vo.GoodsPurchaseDeliveryVO;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallmember.request.MemberExample;
import com.cfpamf.ms.mallorder.builder.OrderBuilder;
import com.cfpamf.ms.mallorder.builder.OrderOfflineBuilder;
import com.cfpamf.ms.mallorder.common.constant.TaskConstant;
import com.cfpamf.ms.mallorder.common.enums.OrderPatternEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderStatusEnum;
import com.cfpamf.ms.mallorder.common.enums.TaskQueueBizTypeEnum;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.strategy.expresscalculate.ExpressCalculateStrategy;
import com.cfpamf.ms.mallorder.common.template.ordersubmitdto.OrderSubmitDTOContext;
import com.cfpamf.ms.mallorder.common.util.*;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.enums.SettleModeEnum;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.model.CartModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.CartPO;
import com.cfpamf.ms.mallorder.po.OrderOfflinePO;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.validation.OrderOfflineValidation;
import com.cfpamf.ms.mallorder.validation.OrderValidation;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallpromotion.api.PromotionCommonFeignClient;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.smartid.client.utils.sharding.SeqEnum;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import com.slodon.bbc.core.constant.GoodsConst;
import com.slodon.bbc.core.constant.RedisConst;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.util.UserUtil;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单正向操作 service
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/30 17:02
 */
@Slf4j
@Service
public class PlacingOrderServiceImpl extends ServiceImpl<OrderMapper, OrderPO> implements IOrderPlacingService {

	@Autowired
	private HttpServletRequest request;

    @Autowired
    private OrderSubmitUtil orderSubmitUtil;
    @Autowired
    private MemberFeignClient memberFeignClient;
    @Resource
    private GoodsFeignClient goodsFeignClient;
    @Resource
    private StoreFeignClient storeFeignClient;
    @Autowired
    private OrderModel orderModel;
    @Autowired
    private SlodonLock slodonLock;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private PromotionCommonFeignClient promotionCommonFeignClient;
    @Autowired
    private ITaskQueueService taskQueueService;
    @Autowired
    private PayIntegration payIntegration;
    @Resource
    private OrderMapper orderMapper;
    @Autowired
    private ICartService cartService;
    @Autowired
    private ShardingId shardingId;
    @Autowired
    private IOrderService iOrderService;
    @Resource
    private IPayMethodService payMethodService;
    @Autowired
    private OrderLocalUtils orderLocalUtils;
    @Autowired
    private PromotionUtils promotionUtils;
    @Resource
    private CartModel cartModel;
    @Resource
    private ValidUtils validUtils;
    @Resource
    private OrderCreateHelper orderCreateHelper;

    @Autowired
   	private EmployeeService employeeService;

    @Autowired
	private OrderOfflineService orderOfflineService;

    @Resource
    private IOrderOfflineExtendService orderOfflineExtendService;

    @Override
    public ChannelOrderSubmitVO submitChannelOrder(ChannelOrderSubmitDTO dto) {

        PreOrderDTO preOrderDTO = new PreOrderDTO();

        /**
         * 封装购买商品信息
         */
//        List<CartPO> cartPOList = cartService.buildCartList(dto.getSkuInfoList(), dto.getOrderType());
//
//        Map<String, List<CartPO>> orderMap = new HashMap<>();
//
//        for (CartPO cartPO : cartPOList) {
//            String orderShardingKey = String.valueOf(cartPO.getStoreId());
//            List<CartPO> currentCart = orderMap.get(orderShardingKey);
//            if (currentCart == null){
//                List<CartPO> items = new ArrayList<CartPO>();
//                items.add(cartPO);
//                orderMap.put(orderShardingKey,  items);
//            } else {
//
//            }
//        }


        long pno = shardingId.next(SeqEnum.PNO, null);

//
//        // 创建订单
//        this.createOrder(consumerDTO);


        /**
         * step.1 业务参数校验
         */

        /**
         * step.2 查询商品信息
         */

        /**
         * step.3 查询活动信息
         */

        /**
         * step.4 封装订单信息
         */

        /**
         * step.5 扣除库存、卡券
         */

        /**
         * step.6 写入订单信息
         */


        return null;
    }


    /**
     * 创建订单接口
     *
     * @param req
     * @return long
     * <AUTHOR>
     * @date 2021/5/30 17:01
     */
    @Override
    public long createOrder(OrderSubmitMqConsumerDTO req) {

//        Set<String> lock = new TreeSet<>();
        OrderSubmitParamDTO paramDTO = req.getParamDTO();
        Set<String> lockSet = new TreeSet<>();
        try {
            //构造计算优惠dto
            OrderSubmitDTO orderSubmitDTO = null;
            if (paramDTO.isChannelOrder()
                    || OrderTypeEnum.isOfflineAll(req.getOrderType())
                    || req.isSimulationShoppingCart()) {
                List<CartPO> cartItems = cartService.buildCartList(req.getSkuInfoList(), req.getOrderType(),
                        req.getMemberId(), req.getAreaCode(), req.getFinanceRuleCode(), paramDTO.getChannel(), paramDTO.getOrderAddress());

                orderSubmitDTO = orderSubmitUtil.getOrderSubmitDTOV2(cartItems, paramDTO, req.getMemberId(), Boolean.TRUE.equals(req.isSimulationShoppingCart()));
                if (SettleModeEnum.BORROW.getCode().equals(orderSubmitDTO.getOrderInfoList().get(0).getSettleMode())) {
                    throw new BusinessException("暂不支持购买现款现货的商品");
                }
            } else {
                orderSubmitDTO = orderSubmitUtil.getOrderSubmitDTO(paramDTO, req.getMemberId(), paramDTO.getProductType(),
                        paramDTO.getOrderAddress(), true, true);
            }

            Result<List<OrderSubmitDTO.OrderInfo.OrderProductInfo>> fundsBorrowVerify = IFundsBorrowBizService.verifyCartOrder(orderSubmitDTO.getOrderInfoList());
            if (!fundsBorrowVerify.isSuccess()) {
                throw new BusinessException(fundsBorrowVerify.getMessage());
            }

            //定金不计算优惠
            if (req.getPreOrderDTO() != null && req.getPreOrderDTO().getIsCalculateDiscount()) {
                //调用活动模块计算优惠：封装活动对象，计算活动金额
                JsonResult<OrderSubmitDTO> result = promotionCommonFeignClient.orderSubmitCalculationDiscountV2(orderSubmitDTO, paramDTO.getSource());
                if (result.getState() != 200) {
                    throw new MallException(result.getMsg());
                }
                orderSubmitDTO = result.getData();
            }

            //计算运费
            OrderAddressDTO orderAddress = req.getParamDTO().getOrderAddress();
            List<BigDecimal> expressFeeList = new ArrayList<>();
            for (OrderSubmitDTO.OrderInfo orderInfo : orderSubmitDTO.getOrderInfoList()) {
                BigDecimal expressFee;
                //传了地址信息，计算运费
                expressFee = orderLocalUtils.getOrderExpressFee(orderAddress.getCityCode(), orderInfo);
                expressFeeList.add(expressFee);
                //立即购买时，orderType会由于单品活动查询接口，预先赋值205满赠类型，该处先还原为普通订单
                if (orderInfo.getOrderType().equals(OrderTypeEnum.FULL_GIFT.getValue())
                        || orderInfo.getOrderType().equals(PromotionConst.PROMOTION_TYPE_205)) {
                    orderInfo.setOrderType(OrderTypeEnum.NORMAL.getValue());
                }
                //存在赠品，则将订单置为满赠订单
                if (!CollectionUtils.isEmpty(orderInfo.getPromotionInfoList())) {
                    for (OrderSubmitDTO.PromotionInfo promotionInfo : orderInfo.getPromotionInfoList()) {
                        if (!CollectionUtils.isEmpty(promotionInfo.getConsumptionFreebieList())) {
                            orderInfo.setOrderType(OrderTypeEnum.FULL_GIFT.getValue());
                            break;
                        }
                    }
                }
            }
            //处理已计算好的运费
            iOrderService.dealExpress(orderSubmitDTO, expressFeeList);

            //计算乡助卡优惠金额
            iOrderService.calculateCard(orderSubmitDTO, paramDTO.getCardCodeList(), req.getUserNo());

            log.info("============= orderSubmitDTO:{}", JSON.toJSONString(orderSubmitDTO));

            //查询用户信息
            Member member = memberFeignClient.getMemberByMemberId(req.getMemberId());
            //通用提交订单
            lockSet = orderModel.submitOrder(orderSubmitDTO, member, req);
        } catch (Exception e) {
            // 订单提交失败 活动库存回退
            promotionUtils.deductionStockNumber(req.getPreOrderDTO(), req.getMemberId(), paramDTO.getNumber());

            if (e instanceof BusinessException || e instanceof MallException) {
                throw e;
            }

            throw new MallException(
                    "创建订单失败,请联系管理员！",
                    "创建订单失败, 支付单号:" + req.getPaySn(),
                    ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode(),
                    e);

        } finally {
            //订单处理成功或失败，都删除redis中的订单处理标识
            stringRedisTemplate.delete(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + req.getPaySn());
            //释放此次获取的所有锁
            lockSet.forEach(lockName -> {
                slodonLock.unlock(lockName);
            });
        }
        if (req.getPreOrderDTO() != null
                && req.getPreOrderDTO().getOrderType().equals(PromotionConst.PROMOTION_TYPE_104)) {
            //秒杀订单提交完毕，增加会员购买数量
            String key = RedisConst.REDIS_SECKILL_MEMBER_BUY_NUM_PREFIX + paramDTO.getProductId() + "_" + req.getMemberId();
            String memberAlreadyBuyNum = stringRedisTemplate.opsForValue().get(key);
            if (memberAlreadyBuyNum == null) {
                stringRedisTemplate.opsForValue().set(key, paramDTO.getNumber().toString());
            } else {
                stringRedisTemplate.opsForValue().set(key, (paramDTO.getNumber() + Integer.parseInt(memberAlreadyBuyNum)) + "");
            }
        }
        return 0;
    }

    @Override
    public void profitWithBackup(String orderSn) {
        try {
            payIntegration.profitSharding(orderSn);
        } catch (Exception e) {
            log.error("profitSharding()订单微信分账失败", e);
            taskQueueService.saveTaskQueue(
                    Long.valueOf(orderSn),
                    TaskQueueBizTypeEnum.PROFIT_COMP,
                    new Date(),
                    TaskConstant.DEFAULT_JOB_NUMBER
            );
        }
    }

    @Override
    public boolean closeOrderStatus(String orderSn) {
        //订单状态-已关闭
        OrderPO orderPO = new OrderPO();
        orderPO.setOrderState(OrderConst.ORDER_STATE_50);

        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper();
        orderQuery.eq(OrderPO::getOrderSn, orderSn);

        return this.update(orderPO, orderQuery);
    }

    @Override
    public boolean updateOrderStatus(String orderSn,int orderState) {

        OrderStatusEnum orderStatusEnum = OrderStatusEnum.valueOf(orderState);
        BizAssertUtil.isTrue(orderStatusEnum == null, "请传入正确的订单状态!");

        OrderPO orderPO = new OrderPO();
        orderPO.setOrderState(orderState);

        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper();
        orderQuery.eq(OrderPO::getOrderSn, orderSn);

        return this.update(orderPO, orderQuery);
    }


    @Override
    public OrderPO getByOrderSn(String orderSn) {
        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper();
        orderQuery.eq(OrderPO::getOrderSn, orderSn);
        orderQuery.eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return orderMapper.selectOne(orderQuery);
    }


    //region    模式订单


    /**
     * 确认下单
     */
    @Override
    @SuppressWarnings("all")
    public OrderSubmitPageVO confirmPurchaseOrder(OrderSubmitParamDTO submitParam, Member member) {

        // 1、解析地址信息
        OrderAddressDTO addressDTO = submitParam.getPurchaseOrderAddress();
        if (Objects.isNull(addressDTO)) {
            addressDTO = orderCreateHelper.buildOrderAddressById(submitParam.getAddressId());
        }

        // 2、解析入参
        OrderSubmitDTO orderSubmitDTO = OrderSubmitDTOContext.ORDER_SUBMIT_DTO_CREATOR_MAP
                .get(OrderPatternEnum.valueOf(submitParam.getOrderPattern()))
                .getOrderSubmitDto(submitParam, member, submitParam.getAreaCode(), addressDTO, false);

        // 4、运费计算
        if (OrderPatternEnum.PURCHASE_CENTRE.getValue().equals(submitParam.getOrderPattern())) {
            List<GoodsPurchaseDeliveryVO> purchaseDeliveryVOs = (List<GoodsPurchaseDeliveryVO>) ExpressCalculateStrategy
                    .calculate(OrderPatternEnum.PURCHASE_CENTRE, orderSubmitDTO.getOrderInfoList(), addressDTO);
            this.purchaseOrderDealExpress(orderSubmitDTO, purchaseDeliveryVOs);
        } else {
            List<BigDecimal> expressFeeList = (List<BigDecimal>) ExpressCalculateStrategy
                    .calculate(OrderPatternEnum.SHOP_STREET, orderSubmitDTO.getOrderInfoList(), addressDTO);
            iOrderService.dealExpress(orderSubmitDTO, expressFeeList);
        }

        // 5、构建返回对象
        OrderSubmitPageVO vo = new OrderSubmitPageVO(orderSubmitDTO);

        // 6、是否可以开发票
        vo.setIsVatInvoice(allowDrawInvoice(orderSubmitDTO.getOrderInfoList()));

        return vo;
    }

    /**
     * 订单处理运费信息
     *
     * @param orderSubmitDTO      解析的订单信息
     * @param purchaseDeliveryVOs 运费信息
     */
    private void purchaseOrderDealExpress(OrderSubmitDTO orderSubmitDTO, List<GoodsPurchaseDeliveryVO> purchaseDeliveryVOs) {
        for (int i = 0; i < orderSubmitDTO.getOrderInfoList().size(); i++) {
            OrderSubmitDTO.OrderInfo orderInfo = orderSubmitDTO.getOrderInfoList().get(i);
            GoodsPurchaseDeliveryVO purchaseDeliveryVO = purchaseDeliveryVOs.get(i);
            orderInfo.setDeliverPlace(purchaseDeliveryVO.getDeliveryAreaInfo());
            orderInfo.setExpressFee(purchaseDeliveryVO.getFreightFee());
            orderInfo.setExpressFeeTotal(purchaseDeliveryVO.getFreightFee());
            orderInfo.setEstimateExpressFee(purchaseDeliveryVO.getDeliveryFee());
            orderInfo.setExpressCalculateType(purchaseDeliveryVO.getCalculateDeliveryFeeByDistance() ? 2 : 1);
            orderSubmitDTO.setExpressFee(Optional.ofNullable(orderSubmitDTO.getExpressFee()).orElse(new BigDecimal(0))
                    .add(purchaseDeliveryVO.getFreightFee()));
        }
    }

    /**
     * 下单前校验
     */
    @Override
    @SuppressWarnings("all")
    public OrderSubmitCheckVO checkPurchaseOrder(OrderSubmitParamDTO submitParam, Member member) {
        // 1、解析地址信息
        OrderAddressDTO addressDTO = submitParam.getPurchaseOrderAddress();
        if (Objects.isNull(addressDTO)) {
            addressDTO = orderCreateHelper.buildOrderAddressById(submitParam.getAddressId());
        }
        OrderSubmitDTO orderSubmitDTO = OrderSubmitDTOContext.ORDER_SUBMIT_DTO_CREATOR_MAP
                .get(OrderPatternEnum.valueOf(submitParam.getOrderPattern()))
                .getOrderSubmitDto(submitParam, member, submitParam.getAreaCode(), addressDTO, true);
        return cartModel.checkCart(orderSubmitDTO);
    }

    /**
     * 提交订单
     */
    @Override
    @SuppressWarnings("all")
    public Map<String, String> submitPurchaseOrder(OrderSubmitParamDTO submitParam, Member member) {
        // 1、零元订单验证码校验
        validUtils.checkVerifyCode(submitParam.getVerifyCode(), member, 0);

        // 2、解析地址信息
        OrderAddressDTO addressDTO = submitParam.getPurchaseOrderAddress();
        if (Objects.isNull(addressDTO)) {
            addressDTO = orderCreateHelper.buildOrderAddressById(submitParam.getAddressId());
        }
        submitParam.setOrderAddress(addressDTO);

        // 3、创建支付单号（有传入使用传入的订单号）
        String pno = StringUtils.isEmpty(submitParam.getPno()) ?
                String.valueOf(shardingId.next(SeqEnum.PNO, member.getMemberId().toString())) : submitParam.getPno();

        // 4、构造订单传输对象，用于下单处理
        OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        consumerDTO.setParamDTO(submitParam);
        consumerDTO.setMemberId(member.getMemberId());
        consumerDTO.setUserNo(member.getUserNo());
        consumerDTO.setPaySn(pno);
        consumerDTO.setAreaCode(submitParam.getAreaCode());

        //将表标识放入redis，key=标识前缀+paySn，mq处理成功或失败后删除标识
        stringRedisTemplate.opsForValue().set(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + pno, "");

        // 5、解析入参
        OrderSubmitDTO orderSubmitDTO = OrderSubmitDTOContext.ORDER_SUBMIT_DTO_CREATOR_MAP
                .get(OrderPatternEnum.valueOf(submitParam.getOrderPattern()))
                .getOrderSubmitDto(submitParam, member, submitParam.getAreaCode(), addressDTO, true);

        if (SettleModeEnum.BORROW.getCode().equals(orderSubmitDTO.getOrderInfoList().get(0).getSettleMode())) {
            throw new BusinessException("暂不支持购买现款现货的商品");
        }

        // 4、运费计算
        if (OrderPatternEnum.PURCHASE_CENTRE.getValue().equals(submitParam.getOrderPattern())) {
            List<GoodsPurchaseDeliveryVO> purchaseDeliveryVOs = (List<GoodsPurchaseDeliveryVO>) ExpressCalculateStrategy
                    .calculate(OrderPatternEnum.PURCHASE_CENTRE, orderSubmitDTO.getOrderInfoList(), addressDTO);
            this.purchaseOrderDealExpress(orderSubmitDTO, purchaseDeliveryVOs);
        } else {
            List<BigDecimal> expressFeeList = (List<BigDecimal>) ExpressCalculateStrategy
                    .calculate(OrderPatternEnum.SHOP_STREET, orderSubmitDTO.getOrderInfoList(), addressDTO);
            iOrderService.dealExpress(orderSubmitDTO, expressFeeList);
        }

        // 6、执行创建订单
        Set<String> lockSet = new TreeSet<>();
        try {
            //通用提交订单
            lockSet = orderModel.submitOrder(orderSubmitDTO, member, consumerDTO);
        } catch (Exception e) {
            log.warn("异常为：{}", e.getClass().getName());
            if (e instanceof BusinessException || e instanceof MallException) {
                throw e;
            }
            if (e instanceof DuplicateKeyException) {
                throw new MallException("创建订单失败,请联系管理员！",
                        "创建订单失败," + ErrorCodeEnum.S.DATA_UNIQUE_CONFLICT.getMsg() + ",支付单号:" + pno,
                        ErrorCodeEnum.S.DATA_UNIQUE_CONFLICT.getCode(), e);
            } else {
                throw new MallException("创建订单失败,请联系管理员！", "创建订单失败, 支付单号:" + pno,
                        ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode(), e);
            }
        } finally {
            // 释放此次获取的所有锁
            lockSet.forEach(lockName -> {
                slodonLock.unlock(lockName);
            });
        }

        // 7、查询要返回的信息
        List<OrderPO> orderPos = iOrderService.listByPaySn(pno);
        List<String> orderSnList = orderPos.stream().map(OrderPO::getOrderSn).collect(Collectors.toList());

        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("paySn", pno);
        dataMap.put("orderList", JSONObject.toJSONString(orderSnList));

        return dataMap;
    }

    @Override
    public ChannelOrderSubmitVO submitCouponOrder(OrderSubmitParamDTO submitParam, Member member, ChannelCouponOrderSubmitDTO channelSubmitDTO) {
        // 1、零元订单验证码校验
        validUtils.checkVerifyCode(submitParam.getVerifyCode(), member, 0);

        // 解析入参
        OrderSubmitDTO orderSubmitDTO = orderSubmitUtil.getOrderSubmitDTO(submitParam, member.getMemberId(),
                submitParam.getProductType(), null, true, true);

        // 收货地址设为空
        OrderAddressDTO addressDTO = new OrderAddressDTO();
        submitParam.setOrderAddress(addressDTO);

        // 查询商品是否参与单品活动
        GoodsPromotion singlePromotion = cartModel.getSinglePromotion(submitParam.getProductId(), member.getMemberId());
        PreOrderDTO preOrderDTO = new PreOrderDTO();
        if (singlePromotion != null && !submitParam.getIsAloneBuy()) {
            // 活动预校验
            preOrderDTO = promotionUtils.preCheckSubmit(singlePromotion, submitParam.getProductId(), submitParam.getNumber(), member.getMemberId());

            // dto活动参数赋值
            promotionUtils.promotionParamSet(singlePromotion, submitParam, orderSubmitDTO);
        }

        if (preOrderDTO.getIsCalculateDiscount()){
            // 调用活动模块计算优惠：封装活动对象，计算活动金额
            JsonResult<OrderSubmitDTO> result = promotionCommonFeignClient.orderSubmitCalculationDiscountV2(orderSubmitDTO, submitParam.getSource());
            if (result.getState() != 200) {
                throw new MallException(result.getMsg());
            }
            orderSubmitDTO = result.getData();
        }

        // 运费设置为0
        orderSubmitDTO.setExpressFee(BigDecimal.ZERO);
        //子单运费
        for (int i = 0; i < orderSubmitDTO.getOrderInfoList().size(); i++) {
            orderSubmitDTO.getOrderInfoList().get(i).setExpressFee(BigDecimal.ZERO);
            orderSubmitDTO.getOrderInfoList().get(i).setExpressFeeTotal(BigDecimal.ZERO);
        }

        //计算乡助卡优惠金额
        iOrderService.calculateCard(orderSubmitDTO, submitParam.getCardCodeList(), member.getUserNo());

        // 创建支付单号（有传入使用传入的订单号）
        String pno = StringUtils.isEmpty(submitParam.getPno()) ?
                String.valueOf(shardingId.next(SeqEnum.PNO, member.getMemberId().toString())) : submitParam.getPno();

        //将表标识放入redis，key=标识前缀+paySn，mq处理成功或失败后删除标识
        stringRedisTemplate.opsForValue().set(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + pno, "");

        // 构造订单传输对象，用于下单处理
        OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        consumerDTO.setParamDTO(submitParam);
        consumerDTO.setMemberId(member.getMemberId());
        consumerDTO.setUserNo(member.getUserNo());
        consumerDTO.setPaySn(pno);
        consumerDTO.setPreOrderDTO(preOrderDTO);
        consumerDTO.setAreaCode(submitParam.getAreaCode());
        if (Objects.nonNull(channelSubmitDTO)){
            // 乡助福袋订单，传乡助订单来源及业务单号
            ChannelOrderSubmitDTO channelOrderSubmitDTO = new ChannelOrderSubmitDTO();
            channelOrderSubmitDTO.setOutBizId(channelSubmitDTO.getOutBizId());
            channelOrderSubmitDTO.setOutBizSource(channelSubmitDTO.getOutBizSource());
            channelOrderSubmitDTO.setChannel(submitParam.getChannel());
            consumerDTO.setChannelOrderSubmitDTO(channelOrderSubmitDTO);
        }
        log.info("卡券订单下单参数：{}", JSONObject.toJSONString(orderSubmitDTO));

        // 执行创建订单
        Set<String> lockSet = new TreeSet<>();
        try {
            //通用提交订单
            lockSet = orderModel.submitOrder(orderSubmitDTO, member, consumerDTO);
        } catch (Exception e) {
            log.warn("下单失败，异常为：{}", e.getClass().getName());

            // 订单提交失败 redis活动库存回退
            promotionUtils.deductionStockNumber(preOrderDTO, member.getMemberId(), submitParam.getNumber());

            if (e instanceof BusinessException || e instanceof MallException) {
                throw e;
            }

            throw new MallException(
                    "创建订单失败,请联系管理员！",
                    "创建订单失败, 支付单号:" + pno,
                    ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode(),
                    e);
        } finally {
            // 释放此次获取的所有锁
            lockSet.forEach(lockName -> {
                slodonLock.unlock(lockName);
            });
        }

        // 查询要返回的信息
        LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(OrderPO::getOrderSn);
        queryWrapper.eq(OrderPO::getPaySn, pno);
        List<OrderPO> orderPOList = iOrderService.list(queryWrapper);

        ChannelOrderSubmitVO vo = new ChannelOrderSubmitVO();
        vo.setPaySn(String.valueOf(pno));
        vo.setOrderSnList(orderPOList.stream().map(OrderPO::getOrderSn).collect(Collectors.toList()));

        return vo;
    }

    /**
     * 是否允许开发票
     *
     * @param orderInfos 订单信息
     * @return true/false
     */
    private Boolean allowDrawInvoice(List<OrderSubmitDTO.OrderInfo> orderInfos) {
        Map<Long, Goods> goodsMap = new HashMap<>();//保存已查询的商品，减少查库次数
        for (OrderSubmitDTO.OrderInfo orderInfo : orderInfos) {
            for (OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo : orderInfo.getOrderProductInfoList()) {
                if (goodsMap.containsKey(orderProductInfo.getGoodsId())) {
                    continue;
                }
                Goods goods = goodsFeignClient.getGoodsByGoodsId(orderProductInfo.getGoodsId());
                if (goods.getIsVatInvoice() != null && goods.getIsVatInvoice() == GoodsConst.IS_VAT_INVOICE_NO) {
                    //商品不允许开增值税发票,跳出循环
                    return false;
                }
                goodsMap.put(goods.getGoodsId(), goods);
            }
        }
        return true;
    }

    //region end


    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public OrderSubmitVO createOfflineOrder(OrderOfflineParamDTO dto) {
        Vendor vendor = UserUtil.getUser(request, Vendor.class);
        log.info("【createOfflineOrder】线下补录订单：{}", dto);
        /////////////////////////////出单条件验证/////////////////////////////
        OrderOfflineValidation.isValidOfflineOrderBaseInfo(dto);
        //查询员工信息 20230710修改：获取员工管护分支信息，不从hr获取
        CustInfoVo custInfoVo = orderOfflineService.queryCustInfo(dto.getEmployeeCode());
        OrderOfflineValidation.isValidOfflineOrderEmployeeInfo(custInfoVo, dto);
        //查询用户信息
        MemberExample example = new MemberExample();
        example.setMemberMobile(dto.getUserMobile());
        List<Member> members = memberFeignClient.getMemberList(example);
        OrderOfflineValidation.isValidOfflineOrderMemberInfo(dto.getUserMobile(), members);
        // 自提订单，查询自提点信息
        if (OrderPatternEnum.SELF_LIFT.getValue().equals(dto.getOrderPattern())) {
            OrderAddressDTO orderAddressDTO = orderCreateHelper.buildOrderAddressByPointId(dto.getPointId());
            dto.setAddress(orderAddressDTO);
        }

        //线下补录订单扩展信息校验
        OrderOfflineValidation.validOfflineInfoOrder(dto.getOfflineInfoDTO());

        /////////////////////////////出单参数构建/////////////////////////////
        Member member = members.get(0);
    	long paySn = shardingId.next(SeqEnum.PNO, member.getMemberId().toString());

        OrderSubmitParamDTO orderSubmitParam = orderCreateHelper.buildOrderSubmit(dto, member.getUserNo(), custInfoVo);

        // 构造入mq对象
        OrderSubmitMqConsumerDTO consumerDTO =orderCreateHelper.buildOrderSubmit(orderSubmitParam, dto, member, paySn);
        // 将表标识放入redis，key=标识前缀+paySn，mq处理成功或失败后删除标识
        stringRedisTemplate.opsForValue().set(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + paySn, "");
        log.info("【createOfflineOrder】线下补录订单，下单前处理：{}",consumerDTO);

        /////////////////////////////创建订单/////////////////////////////
        this.createOrder(consumerDTO);

        log.info("【createOfflineOrder】线下补录订单--------------paySn:{}",paySn);
        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper<>();
        //orderQuery.select(OrderPO::getOrderSn);
        orderQuery.eq(OrderPO::getPaySn, consumerDTO.getPaySn());
        List<OrderPO> orderPOList = super.list(orderQuery);
        OrderSubmitVO vo = new OrderSubmitVO();
        vo.setPaySn(String.valueOf(paySn));
        vo.setOrderSnList(orderPOList.stream().map(OrderPO::getOrderSn).collect(Collectors.toList()));

        /////////////////////////////创建线下订单收款信息/////////////////////////////
        if (!CollectionUtils.isEmpty(dto.getOrderOfflineList())) {
            List<OrderOfflinePO> orderOfflines = OrderOfflineBuilder.buildOrderOfflinePOList(
                    vo.getPaySn(), member.getMemberName(), dto.getOrderOfflineList());
            orderOfflineService.saveBatch(orderOfflines);
        }

        /////////////////////////////线下补录扩展信息/////////////////////////////
        if (ObjectUtils.isNotEmpty(dto.getOfflineInfoDTO())) {
            orderOfflineExtendService.saveOfflineOrder(orderPOList,dto.getOfflineInfoDTO(), vendor.getVendorName());
        }
        log.info("【createOfflineOrder】线下补录订单--------------paySn:{} vo:{}", paySn, vo);
        return vo;
    }

	@Override
	public OrderSubmitVO createBatchOrder(OrderParamDTO dto) {
		log.info("【createBatchOrder】批量非购物车订单：{}",dto);
    	/////////////////////////////出单条件验证/////////////////////////////
		Member member = UserUtil.getUser(request, Member.class);
		OrderValidation.isValidOrderMemberInfo(member);
        log.info("【createBatchOrder】member:{}" ,JSON.toJSONString(member));
        OrderPlaceUserDTO orderPlaceUserDTO = OrderBuilder.buildOrderPlaceUser(request, member, dto.getChannel(), dto.getOrderPlaceUserRole());
        log.info("【createBatchOrder】orderPlaceUserDTO:{}" ,JSON.toJSONString(orderPlaceUserDTO));
        OrderValidation.isValidBatchOrderBaseInfo(dto);
    	/////////////////////////////出单参数构建/////////////////////////////
    	long paySn = shardingId.next(SeqEnum.PNO, member.getMemberId().toString());
        OrderSubmitParamDTO orderSubmitParam = orderCreateHelper.buildOrderSubmit(dto, member.getUserNo());
        // 构造入mq对象
        OrderSubmitMqConsumerDTO consumerDTO =orderCreateHelper.buildOrderSubmit(orderSubmitParam, dto, member, paySn, orderPlaceUserDTO);
        // 将表标识放入redis，key=标识前缀+paySn，mq处理成功或失败后删除标识
        stringRedisTemplate.opsForValue().set(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + paySn, "");
        log.info("【createBatchOrder】批量非购物车订单，下单前处理：{}",consumerDTO);
        /////////////////////////////创建订单/////////////////////////////
        this.createOrder(consumerDTO);

        log.info("【createBatchOrder】批量非购物车订单--------------paySn:{}",paySn);
        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper<>();
        orderQuery.select(OrderPO::getOrderSn);
        orderQuery.eq(OrderPO::getPaySn, consumerDTO.getPaySn());
        List<OrderPO> orderPOList = super.list(orderQuery);
        OrderSubmitVO vo = new OrderSubmitVO();
        vo.setPaySn(String.valueOf(paySn));
        vo.setOrderSnList(orderPOList.stream().map(OrderPO::getOrderSn).collect(Collectors.toList()));
        log.info("【createBatchOrder】批量非购物车订单--------------paySn:{} vo:{}",paySn,vo);
		return vo;
	}

}
