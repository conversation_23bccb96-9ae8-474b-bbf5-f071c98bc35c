package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.mapper.OrderReturnMapper;
import com.cfpamf.ms.mallorder.po.OrderProductPO;
import com.cfpamf.ms.mallorder.po.OrderReturnPO;
import com.cfpamf.ms.mallorder.service.IOrderProductService;
import com.cfpamf.ms.mallstock.facade.api.StockBalanceFeignClient;
import com.cfpamf.ms.mallstock.facade.dto.ProcurementCostPriceQueryDTO;
import com.slodon.bbc.core.response.JsonResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HistoryDataDealServiceImplTest {

    @Mock
    private OrderReturnMapper mockOrderReturnMapper;
    @Mock
    private StockBalanceFeignClient mockStockBalanceFeignClient;
    @Mock
    private OrderProductMapper mockOrderProductMapper;
    @Mock
    private IOrderProductService mockProductService;

    @InjectMocks
    private HistoryDataDealServiceImpl historyDataDealServiceImplUnderTest;

    @Test
    public void testReturnServiceFeeFix() {
        // Setup
        when(mockOrderReturnMapper.update(any(OrderReturnPO.class), any(LambdaUpdateWrapper.class))).thenReturn(0);

        // Run the test
        final int result = historyDataDealServiceImplUnderTest.returnServiceFeeFix("afsSn", new BigDecimal("0.00"));

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testOrderProductCostFix() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList))
                .thenReturn(new JsonResult<>(0, "errMsg"));

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
        verify(mockProductService).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    public void testOrderProductCostFix_OrderProductMapperReturnsNoItems() {
        // Setup
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(Collections.emptyList());

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList))
                .thenReturn(new JsonResult<>(0, "errMsg"));

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
        verify(mockProductService).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    public void testOrderProductCostFix_StockBalanceFeignClientReturnsNull() {
        // Setup
        // Configure OrderProductMapper.selectCostByOrderSn(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setCost(new BigDecimal("0.00"));
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO);
        when(mockOrderProductMapper.selectCostByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure StockBalanceFeignClient.queryCostPriceByBatchNumber(...).
        final ProcurementCostPriceQueryDTO procurementCostPriceQueryDTO = new ProcurementCostPriceQueryDTO();
        procurementCostPriceQueryDTO.setBatchNumber("batchNo");
        procurementCostPriceQueryDTO.setSkuId("channelSkuId");
        procurementCostPriceQueryDTO.setConvertSkuId("convertSkuId");
        procurementCostPriceQueryDTO.setUnitCode("unitCode");
        final List<ProcurementCostPriceQueryDTO> queryDTOList = Arrays.asList(procurementCostPriceQueryDTO);
        when(mockStockBalanceFeignClient.queryCostPriceByBatchNumber(queryDTOList)).thenReturn(null);

        // Run the test
        final String result = historyDataDealServiceImplUnderTest.orderProductCostFix("orderSn");

        // Verify the results
        assertThat(result).isEqualTo("error");
    }
}
