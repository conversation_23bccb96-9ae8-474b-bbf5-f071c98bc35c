
package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cfpamf.framework.autoconfigure.redis.lock.SlodonLock;
import com.cfpamf.ms.mallgoods.facade.enums.EventStockTypeEnum;
import com.cfpamf.ms.mallgoods.facade.vo.Goods;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallgoods.facade.vo.ProductPriceBranchRange;
import com.cfpamf.ms.mallgoods.facade.vo.ProductPriceVO;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.enums.BizTypeEnum;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.enums.OrderRefundRevokingPartyEnum;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.mapper.OrderAfterMapper;
import com.cfpamf.ms.mallorder.mapper.OrderExchangeDetailMapper;
import com.cfpamf.ms.mallorder.mapper.OrderExchangeMapper;
import com.cfpamf.ms.mallorder.model.*;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.ExchangeApplyListPcReq;
import com.cfpamf.ms.mallorder.req.ExchangeApplyListReq;
import com.cfpamf.ms.mallorder.req.admin.AdminForceRefundRequest;
import com.cfpamf.ms.mallorder.req.base.RevokeRefundBaseRequest;
import com.cfpamf.ms.mallorder.req.exchange.OrderExchangeRequest;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.request.OrderExchangeDetailExample;
import com.cfpamf.ms.mallorder.request.OrderExchangeExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.manager.GoodsStockService;
import com.cfpamf.ms.mallorder.vo.AfsProductVO;
import com.cfpamf.ms.mallorder.vo.OrderExchangeDetailVO;
import com.cfpamf.ms.mallorder.vo.OrderExchangeReturnListVO;
import com.cfpamf.ms.mallorder.vo.OrderProductListVO;
import com.cfpamf.ms.mallsystem.vo.Admin;
import com.cfpamf.smartid.client.utils.sharding.SeqEnum;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.starter.mq.entity.MessageSendProperty;
import com.slodon.bbc.starter.mq.entity.MessageSendVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OrderExchangeServiceImplTest {

    @Mock
    private OrderExchangeDetailMapper mockOrderExchangeDetailMapper;
    @Mock
    private OrderExchangeMapper mockOrderExchangeMapper;
    @Mock
    private OrderProductModel mockOrderProductModel;
    @Mock
    private OrderLocalUtils mockOrderLocalUtils;
    @Mock
    private OrderExtendModel mockOrderExtendModel;
    @Mock
    private OrderModel mockOrderModel;
    @Mock
    private IOrderService mockOrderService;
    @Mock
    private IOrderProductService mockOrderProductService;
    @Mock
    private IOrderExtendService mockOrderExtendService;
    @Mock
    private IOrderExchangeService mockOrderExchangeService;
    @Mock
    private StringRedisTemplate mockStringRedisTemplate;
    @Mock
    private MemberFeignClient mockMemberFeignClient;
    @Mock
    private SlodonLock mockSlodonLock;
    @Mock
    private ShardingId mockShardingId;
    @Mock
    private OrderLogModel mockOrderLogModel;
    @Mock
    private RabbitTemplate mockRabbitTemplate;
    @Mock
    private OrderPayModel mockOrderPayModel;
    @Mock
    private OrderAfterServiceModel mockOrderAfterServiceModel;
    @Mock
    private IOrderExchangeDetailService mockOrderExchangeDetailService;
    @Mock
    private GoodsStockService mockGoodsStockService;
    @Mock
    private IOrderPayService mockOrderPayService;
    @Mock
    private OrderReturnModel mockOrderReturnModel;
    @Mock
    private IOrderReturnService mockOrderReturnService;
    @Mock
    private IOrderAdminReturnService mockOrderAdminReturnService;
    @Mock
    private OrderAfterMapper mockOrderAfterMapper;

    @InjectMocks
    private OrderExchangeServiceImpl orderExchangeServiceImplUnderTest;

    @Test
    public void testApplyExchange() throws Exception {
        // Setup
        final OrderExchangeRequest orderExchangeRequest = new OrderExchangeRequest();
        orderExchangeRequest.setOrderSn("orderSn");
        orderExchangeRequest.setOrderProductId(0L);
        orderExchangeRequest.setProductNum(0);
        orderExchangeRequest.setExchangeReason("exchangeReason");
        orderExchangeRequest.setBuyerConfirmFlag(0);
        orderExchangeRequest.setProductId(0L);
        orderExchangeRequest.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest.setIsTrial(false);
        final UserDTO applicantInfo = new UserDTO();
        applicantInfo.setUserId(0L);
        applicantInfo.setUserName("operatorName");
        applicantInfo.setUserRole(0);
        applicantInfo.setStoreId(0L);
        applicantInfo.setMobile("mobile");
        orderExchangeRequest.setApplicantInfo(applicantInfo);
        orderExchangeRequest.setChannel("value");
        orderExchangeRequest.setDealerCode("dealerCode");

        final OrderExchangeDetailVO expectedResult = new OrderExchangeDetailVO();
        expectedResult.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo = new ExchangeOrderDTO();
        productInfo.setOrderSn("orderSn");
        productInfo.setOrderProductId(0L);
        productInfo.setProductId(0L);
        productInfo.setGoodsName("exchangeProductName");
        productInfo.setProductImage("productImage");
        productInfo.setGoodsId(0L);
        productInfo.setSpecValues("specValues");
        productInfo.setProductShowPrice(new BigDecimal("0.00"));
        productInfo.setProductNum(0);
        productInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo.setMoneyAmount(new BigDecimal("0.00"));
        productInfo.setXzCardAmount(new BigDecimal("0.00"));
        productInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setProductInfo(productInfo);
        final ExchangeOrderDTO newOrderInfo = new ExchangeOrderDTO();
        newOrderInfo.setOrderSn("orderSn");
        newOrderInfo.setOrderProductId(0L);
        newOrderInfo.setProductId(0L);
        newOrderInfo.setGoodsName("exchangeProductName");
        newOrderInfo.setProductImage("productImage");
        newOrderInfo.setGoodsId(0L);
        newOrderInfo.setSpecValues("specValues");
        newOrderInfo.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo.setProductNum(0);
        newOrderInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setNewOrderInfo(newOrderInfo);
        expectedResult.setRefundAmount(new BigDecimal("0.00"));
        expectedResult.setXzCardAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformActivityAmount(new BigDecimal("0.00"));
        expectedResult.setStoreActivityAmount(new BigDecimal("0.00"));
        expectedResult.setStoreVoucherAmount(new BigDecimal("0.00"));

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserNo("userNo");
        orderPO.setUserMobile("userMobile");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setPerformanceModes("performanceModes");
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure OrderProductModel.getOrderProductByOrderProductId(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsName("goodsName");
        orderProductPO.setProductImage("productImage");
        orderProductPO.setSpecValues("specValues");
        orderProductPO.setProductId(0L);
        orderProductPO.setFinanceRuleCode("financeRuleCode");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        when(mockOrderProductModel.getOrderProductByOrderProductId(0L)).thenReturn(orderProductPO);

        // Configure OrderExchangeMapper.getExchangeOrderList(...).
        final ExchangeOrderDetailDTO exchangeOrderDetailDTO = new ExchangeOrderDetailDTO();
        exchangeOrderDetailDTO.setMemberId(0);
        exchangeOrderDetailDTO.setStoreId(0L);
        exchangeOrderDetailDTO.setOrderSn("orderSn");
        exchangeOrderDetailDTO.setOrderProductId(0L);
        exchangeOrderDetailDTO.setProductNum(0);
        exchangeOrderDetailDTO.setExchangeOrderSn("exchangeOrderSn");
        exchangeOrderDetailDTO.setExchangeOrderProductId(0L);
        exchangeOrderDetailDTO.setExchangeOrderState(0);
        exchangeOrderDetailDTO.setExchangeOrderStateList(Arrays.asList(0));
        exchangeOrderDetailDTO.setAfsSn("afsSn");
        final List<ExchangeOrderDetailDTO> exchangeOrderDetailDTOList = Arrays.asList(exchangeOrderDetailDTO);
        final ExchangeOrderDetailDTO example = new ExchangeOrderDetailDTO();
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setOrderSn("orderSn");
        example.setOrderProductId(0L);
        example.setProductNum(0);
        example.setExchangeOrderSn("exchangeOrderSn");
        example.setExchangeOrderProductId(0L);
        example.setExchangeOrderState(0);
        example.setExchangeOrderStateList(Arrays.asList(0));
        example.setAfsSn("afsSn");
        when(mockOrderExchangeMapper.getExchangeOrderList(example)).thenReturn(exchangeOrderDetailDTOList);

        // Configure OrderLocalUtils.getProductPrice(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setSpecValues("specValues");
        product.setProductPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setMainImage("productImage");
        productPriceVO.setProduct(product);
        final ProductPriceBranchRange productPriceBranchRange = new ProductPriceBranchRange();
        productPriceBranchRange.setTaxPrice(new BigDecimal("0.00"));
        productPriceVO.setProductPriceBranchRange(productPriceBranchRange);
        final Goods goods = new Goods();
        goods.setGoodsId(0L);
        goods.setGoodsName("exchangeProductName");
        productPriceVO.setGoods(goods);
        when(mockOrderLocalUtils.getProductPrice(0L, "areaCode", "financeRuleCode")).thenReturn(productPriceVO);

        when(mockShardingId.next("name", "prefix", "memberId",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0L);

        // Configure IOrderExchangeService.createExchangeOrder(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderSn("orderSn");
        orderPO1.setUserNo("userNo");
        orderPO1.setUserMobile("userMobile");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setOrderState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setPerformanceModes("performanceModes");
        orderPO1.setExchangeFlag(0);
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderSn("orderSn");
        orderPO2.setUserNo("userNo");
        orderPO2.setUserMobile("userMobile");
        orderPO2.setPaySn("paySn");
        orderPO2.setStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setOrderState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setPerformanceModes("performanceModes");
        orderPO2.setExchangeFlag(0);
        final OrderExchangeDetailVO orderExchangeDetailVO = new OrderExchangeDetailVO();
        orderExchangeDetailVO.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo1 = new ExchangeOrderDTO();
        productInfo1.setOrderSn("orderSn");
        productInfo1.setOrderProductId(0L);
        productInfo1.setProductId(0L);
        productInfo1.setGoodsName("exchangeProductName");
        productInfo1.setProductImage("productImage");
        productInfo1.setGoodsId(0L);
        productInfo1.setSpecValues("specValues");
        productInfo1.setProductShowPrice(new BigDecimal("0.00"));
        productInfo1.setProductNum(0);
        productInfo1.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo1.setMoneyAmount(new BigDecimal("0.00"));
        productInfo1.setXzCardAmount(new BigDecimal("0.00"));
        productInfo1.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo1.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo1.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setProductInfo(productInfo1);
        final ExchangeOrderDTO newOrderInfo1 = new ExchangeOrderDTO();
        newOrderInfo1.setOrderSn("orderSn");
        newOrderInfo1.setOrderProductId(0L);
        newOrderInfo1.setProductId(0L);
        newOrderInfo1.setGoodsName("exchangeProductName");
        newOrderInfo1.setProductImage("productImage");
        newOrderInfo1.setGoodsId(0L);
        newOrderInfo1.setSpecValues("specValues");
        newOrderInfo1.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo1.setProductNum(0);
        newOrderInfo1.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo1.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo1.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo1.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo1.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo1.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setNewOrderInfo(newOrderInfo1);
        orderExchangeDetailVO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreVoucherAmount(new BigDecimal("0.00"));
        final OrderExchangeRequest orderExchangeRequest1 = new OrderExchangeRequest();
        orderExchangeRequest1.setOrderSn("orderSn");
        orderExchangeRequest1.setOrderProductId(0L);
        orderExchangeRequest1.setProductNum(0);
        orderExchangeRequest1.setExchangeReason("exchangeReason");
        orderExchangeRequest1.setBuyerConfirmFlag(0);
        orderExchangeRequest1.setProductId(0L);
        orderExchangeRequest1.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest1.setIsTrial(false);
        final UserDTO applicantInfo1 = new UserDTO();
        applicantInfo1.setUserId(0L);
        applicantInfo1.setUserName("operatorName");
        applicantInfo1.setUserRole(0);
        applicantInfo1.setStoreId(0L);
        applicantInfo1.setMobile("mobile");
        orderExchangeRequest1.setApplicantInfo(applicantInfo1);
        orderExchangeRequest1.setChannel("value");
        orderExchangeRequest1.setDealerCode("dealerCode");
        when(mockOrderExchangeService.createExchangeOrder(orderPO2, orderExchangeDetailVO,
                orderExchangeRequest1)).thenReturn(orderPO1);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setGoodsName("goodsName");
        orderProductPO1.setProductImage("productImage");
        orderProductPO1.setSpecValues("specValues");
        orderProductPO1.setProductId(0L);
        orderProductPO1.setFinanceRuleCode("financeRuleCode");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO1.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO1.setReturnNumber(0);
        orderProductPO1.setChannelSkuUnit("channelSkuUnit");
        orderProductPO1.setChannelSkuId("channelSkuId");
        orderProductPO1.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO1);
        when(mockOrderProductModel.getOrderProductListByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure OrderExtendModel.getOrderExtendByOrderSn(...).
        final OrderExtendPO extendPO = new OrderExtendPO();
        extendPO.setOrderSn("orderSn");
        extendPO.setBranch("branch");
        extendPO.setReceiverProvinceCode("province");
        extendPO.setReceiverAddress("detailAddress");
        extendPO.setReceiverMobile("receiverMobile");
        extendPO.setReceiverCityCode("city");
        extendPO.setReceiverDistrictCode("district");
        extendPO.setReceiverTownCode("receiverTownCode");
        extendPO.setReceiverName("receiverName");
        extendPO.setReceiveBranchCode("receiveBranchCode");
        extendPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        extendPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        extendPO.setWarehouseCode("warehouseCode");
        when(mockOrderExtendModel.getOrderExtendByOrderSn("orderSn")).thenReturn(extendPO);

        // Configure OrderExchangeMapper.getOrderExchange(...).
        final OrderExchangePO orderExchangePO = new OrderExchangePO();
        orderExchangePO.setExchangeSn("exchangeSn");
        orderExchangePO.setMemberId(0);
        orderExchangePO.setStoreId(0L);
        orderExchangePO.setExchangeReason("exchangeReason");
        orderExchangePO.setBuyerConfirmFlag(0);
        orderExchangePO.setExchangeOrderState(0);
        orderExchangePO.setApplicantId(0L);
        orderExchangePO.setApplicantName("operatorName");
        orderExchangePO.setApplicantRole(0);
        orderExchangePO.setApproverId(0L);
        orderExchangePO.setApproverName("approverName");
        orderExchangePO.setApproverRole(0);
        orderExchangePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExchangePO.setCreateBy("operatorName");
        orderExchangePO.setUpdateBy("updateBy");
        final OrderExchangeExample example1 = new OrderExchangeExample();
        example1.setExchangeOrderId(0);
        example1.setExchangeSn("exchangeSn");
        example1.setMemberId(0);
        example1.setStoreId(0L);
        example1.setExchangeReason("exchangeReason");
        when(mockOrderExchangeMapper.getOrderExchange(example1)).thenReturn(orderExchangePO);

        // Run the test
        final OrderExchangeDetailVO result = orderExchangeServiceImplUnderTest.applyExchange(orderExchangeRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockOrderExchangeService).saveExchangeApplyOrder("exchangeSn", 0L, 0,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"), 0, "exchangeReason");
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "operatorName", "orderSn", 0, 0, 0, "创建换货申请",
                OrderCreateChannel.H5);

        // Confirm IOrderExchangeService.saveExchangeOrderDetail(...).
        final OrderExchangeDetailVO orderExchangeDetailVO1 = new OrderExchangeDetailVO();
        orderExchangeDetailVO1.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo2 = new ExchangeOrderDTO();
        productInfo2.setOrderSn("orderSn");
        productInfo2.setOrderProductId(0L);
        productInfo2.setProductId(0L);
        productInfo2.setGoodsName("exchangeProductName");
        productInfo2.setProductImage("productImage");
        productInfo2.setGoodsId(0L);
        productInfo2.setSpecValues("specValues");
        productInfo2.setProductShowPrice(new BigDecimal("0.00"));
        productInfo2.setProductNum(0);
        productInfo2.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo2.setMoneyAmount(new BigDecimal("0.00"));
        productInfo2.setXzCardAmount(new BigDecimal("0.00"));
        productInfo2.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo2.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo2.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setProductInfo(productInfo2);
        final ExchangeOrderDTO newOrderInfo2 = new ExchangeOrderDTO();
        newOrderInfo2.setOrderSn("orderSn");
        newOrderInfo2.setOrderProductId(0L);
        newOrderInfo2.setProductId(0L);
        newOrderInfo2.setGoodsName("exchangeProductName");
        newOrderInfo2.setProductImage("productImage");
        newOrderInfo2.setGoodsId(0L);
        newOrderInfo2.setSpecValues("specValues");
        newOrderInfo2.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo2.setProductNum(0);
        newOrderInfo2.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo2.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo2.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo2.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo2.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo2.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setNewOrderInfo(newOrderInfo2);
        orderExchangeDetailVO1.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).saveExchangeOrderDetail(orderExchangeDetailVO1, "operatorName");
        verify(mockOrderExchangeService).updateOrderToExchange("orderSn", 0);

        // Confirm IOrderExchangeService.updateOrderAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO2 = new OrderExchangeDetailVO();
        orderExchangeDetailVO2.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo3 = new ExchangeOrderDTO();
        productInfo3.setOrderSn("orderSn");
        productInfo3.setOrderProductId(0L);
        productInfo3.setProductId(0L);
        productInfo3.setGoodsName("exchangeProductName");
        productInfo3.setProductImage("productImage");
        productInfo3.setGoodsId(0L);
        productInfo3.setSpecValues("specValues");
        productInfo3.setProductShowPrice(new BigDecimal("0.00"));
        productInfo3.setProductNum(0);
        productInfo3.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo3.setMoneyAmount(new BigDecimal("0.00"));
        productInfo3.setXzCardAmount(new BigDecimal("0.00"));
        productInfo3.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo3.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo3.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setProductInfo(productInfo3);
        final ExchangeOrderDTO newOrderInfo3 = new ExchangeOrderDTO();
        newOrderInfo3.setOrderSn("orderSn");
        newOrderInfo3.setOrderProductId(0L);
        newOrderInfo3.setProductId(0L);
        newOrderInfo3.setGoodsName("exchangeProductName");
        newOrderInfo3.setProductImage("productImage");
        newOrderInfo3.setGoodsId(0L);
        newOrderInfo3.setSpecValues("specValues");
        newOrderInfo3.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo3.setProductNum(0);
        newOrderInfo3.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo3.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo3.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo3.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo3.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo3.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setNewOrderInfo(newOrderInfo3);
        orderExchangeDetailVO2.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateOrderAmount("orderSn", orderExchangeDetailVO2);

        // Confirm IOrderExchangeService.updateOrderProductAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO3 = new OrderExchangeDetailVO();
        orderExchangeDetailVO3.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo4 = new ExchangeOrderDTO();
        productInfo4.setOrderSn("orderSn");
        productInfo4.setOrderProductId(0L);
        productInfo4.setProductId(0L);
        productInfo4.setGoodsName("exchangeProductName");
        productInfo4.setProductImage("productImage");
        productInfo4.setGoodsId(0L);
        productInfo4.setSpecValues("specValues");
        productInfo4.setProductShowPrice(new BigDecimal("0.00"));
        productInfo4.setProductNum(0);
        productInfo4.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo4.setMoneyAmount(new BigDecimal("0.00"));
        productInfo4.setXzCardAmount(new BigDecimal("0.00"));
        productInfo4.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo4.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo4.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setProductInfo(productInfo4);
        final ExchangeOrderDTO newOrderInfo4 = new ExchangeOrderDTO();
        newOrderInfo4.setOrderSn("orderSn");
        newOrderInfo4.setOrderProductId(0L);
        newOrderInfo4.setProductId(0L);
        newOrderInfo4.setGoodsName("exchangeProductName");
        newOrderInfo4.setProductImage("productImage");
        newOrderInfo4.setGoodsId(0L);
        newOrderInfo4.setSpecValues("specValues");
        newOrderInfo4.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo4.setProductNum(0);
        newOrderInfo4.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo4.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo4.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo4.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo4.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo4.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setNewOrderInfo(newOrderInfo4);
        orderExchangeDetailVO3.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateOrderProductAmount(orderExchangeDetailVO3);
        verify(mockOrderExchangeService).updatePayAmount("paySn", new BigDecimal("0.00"));

        // Confirm IOrderExchangeService.updateExtendAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO4 = new OrderExchangeDetailVO();
        orderExchangeDetailVO4.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo5 = new ExchangeOrderDTO();
        productInfo5.setOrderSn("orderSn");
        productInfo5.setOrderProductId(0L);
        productInfo5.setProductId(0L);
        productInfo5.setGoodsName("exchangeProductName");
        productInfo5.setProductImage("productImage");
        productInfo5.setGoodsId(0L);
        productInfo5.setSpecValues("specValues");
        productInfo5.setProductShowPrice(new BigDecimal("0.00"));
        productInfo5.setProductNum(0);
        productInfo5.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo5.setMoneyAmount(new BigDecimal("0.00"));
        productInfo5.setXzCardAmount(new BigDecimal("0.00"));
        productInfo5.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo5.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo5.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo5.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setProductInfo(productInfo5);
        final ExchangeOrderDTO newOrderInfo5 = new ExchangeOrderDTO();
        newOrderInfo5.setOrderSn("orderSn");
        newOrderInfo5.setOrderProductId(0L);
        newOrderInfo5.setProductId(0L);
        newOrderInfo5.setGoodsName("exchangeProductName");
        newOrderInfo5.setProductImage("productImage");
        newOrderInfo5.setGoodsId(0L);
        newOrderInfo5.setSpecValues("specValues");
        newOrderInfo5.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo5.setProductNum(0);
        newOrderInfo5.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo5.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo5.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo5.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo5.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo5.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo5.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setNewOrderInfo(newOrderInfo5);
        orderExchangeDetailVO4.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateExtendAmount("orderSn", orderExchangeDetailVO4);

        // Confirm GoodsStockService.goodsStock(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderSn("orderSn");
        orderPO3.setUserNo("userNo");
        orderPO3.setUserMobile("userMobile");
        orderPO3.setPaySn("paySn");
        orderPO3.setStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setOrderState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setPerformanceModes("performanceModes");
        orderPO3.setExchangeFlag(0);
        verify(mockGoodsStockService).goodsStock("orderSn", "exchangeSn", 0L, "areaCode", 0, "financeRuleCode",
                "batchNo", "purchaseSubCode","channelSkuId", "branch", "receiveBranchCode", "warehouseCode",
                EventStockTypeEnum.ORDER_EXCHANGE_CREATE_IN_STOCK, BizTypeEnum.EXCHANGE_ORDER_INCREASE_STOCK,
                "operatorName", orderPO3, "channelSkuUnit", 1, 1);

        // Confirm IOrderExchangeService.exchangeOrderAuditDeal(...).
        final OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO.setExchangeAuditState(0);
        orderExchangeAuditDTO.setChannel("value");
        verify(mockOrderExchangeService).exchangeOrderAuditDeal(orderExchangeAuditDTO,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"));

        // Confirm RabbitTemplate.convertAndSend(...).
        final MessageSendVO object = new MessageSendVO();
        object.setReceiveId(0L);
        object.setTplType("tplType");
        object.setMsgLinkInfo("msgLinkInfo");
        final MessageSendProperty messageSendProperty = new MessageSendProperty();
        messageSendProperty.setPropertyName("first");
        messageSendProperty.setPropertyValue("memberName");
        object.setPropertyList(Arrays.asList(messageSendProperty));
        final MessageSendProperty messageSendProperty1 = new MessageSendProperty();
        messageSendProperty1.setPropertyName("first");
        messageSendProperty1.setPropertyValue("memberName");
        object.setWxPropertyList(Arrays.asList(messageSendProperty1));
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg", object);
    }

    @Test
    public void testApplyExchange_OrderExchangeMapperGetExchangeOrderListReturnsNoItems() throws Exception {
        // Setup
        final OrderExchangeRequest orderExchangeRequest = new OrderExchangeRequest();
        orderExchangeRequest.setOrderSn("orderSn");
        orderExchangeRequest.setOrderProductId(0L);
        orderExchangeRequest.setProductNum(0);
        orderExchangeRequest.setExchangeReason("exchangeReason");
        orderExchangeRequest.setBuyerConfirmFlag(0);
        orderExchangeRequest.setProductId(0L);
        orderExchangeRequest.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest.setIsTrial(false);
        final UserDTO applicantInfo = new UserDTO();
        applicantInfo.setUserId(0L);
        applicantInfo.setUserName("operatorName");
        applicantInfo.setUserRole(0);
        applicantInfo.setStoreId(0L);
        applicantInfo.setMobile("mobile");
        orderExchangeRequest.setApplicantInfo(applicantInfo);
        orderExchangeRequest.setChannel("value");
        orderExchangeRequest.setDealerCode("dealerCode");

        final OrderExchangeDetailVO expectedResult = new OrderExchangeDetailVO();
        expectedResult.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo = new ExchangeOrderDTO();
        productInfo.setOrderSn("orderSn");
        productInfo.setOrderProductId(0L);
        productInfo.setProductId(0L);
        productInfo.setGoodsName("exchangeProductName");
        productInfo.setProductImage("productImage");
        productInfo.setGoodsId(0L);
        productInfo.setSpecValues("specValues");
        productInfo.setProductShowPrice(new BigDecimal("0.00"));
        productInfo.setProductNum(0);
        productInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo.setMoneyAmount(new BigDecimal("0.00"));
        productInfo.setXzCardAmount(new BigDecimal("0.00"));
        productInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setProductInfo(productInfo);
        final ExchangeOrderDTO newOrderInfo = new ExchangeOrderDTO();
        newOrderInfo.setOrderSn("orderSn");
        newOrderInfo.setOrderProductId(0L);
        newOrderInfo.setProductId(0L);
        newOrderInfo.setGoodsName("exchangeProductName");
        newOrderInfo.setProductImage("productImage");
        newOrderInfo.setGoodsId(0L);
        newOrderInfo.setSpecValues("specValues");
        newOrderInfo.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo.setProductNum(0);
        newOrderInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setNewOrderInfo(newOrderInfo);
        expectedResult.setRefundAmount(new BigDecimal("0.00"));
        expectedResult.setXzCardAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformActivityAmount(new BigDecimal("0.00"));
        expectedResult.setStoreActivityAmount(new BigDecimal("0.00"));
        expectedResult.setStoreVoucherAmount(new BigDecimal("0.00"));

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserNo("userNo");
        orderPO.setUserMobile("userMobile");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setPerformanceModes("performanceModes");
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure OrderProductModel.getOrderProductByOrderProductId(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsName("goodsName");
        orderProductPO.setProductImage("productImage");
        orderProductPO.setSpecValues("specValues");
        orderProductPO.setProductId(0L);
        orderProductPO.setFinanceRuleCode("financeRuleCode");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        when(mockOrderProductModel.getOrderProductByOrderProductId(0L)).thenReturn(orderProductPO);

        // Configure OrderExchangeMapper.getExchangeOrderList(...).
        final ExchangeOrderDetailDTO example = new ExchangeOrderDetailDTO();
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setOrderSn("orderSn");
        example.setOrderProductId(0L);
        example.setProductNum(0);
        example.setExchangeOrderSn("exchangeOrderSn");
        example.setExchangeOrderProductId(0L);
        example.setExchangeOrderState(0);
        example.setExchangeOrderStateList(Arrays.asList(0));
        example.setAfsSn("afsSn");
        when(mockOrderExchangeMapper.getExchangeOrderList(example)).thenReturn(Collections.emptyList());

        // Configure OrderLocalUtils.getProductPrice(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setSpecValues("specValues");
        product.setProductPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setMainImage("productImage");
        productPriceVO.setProduct(product);
        final ProductPriceBranchRange productPriceBranchRange = new ProductPriceBranchRange();
        productPriceBranchRange.setTaxPrice(new BigDecimal("0.00"));
        productPriceVO.setProductPriceBranchRange(productPriceBranchRange);
        final Goods goods = new Goods();
        goods.setGoodsId(0L);
        goods.setGoodsName("exchangeProductName");
        productPriceVO.setGoods(goods);
        when(mockOrderLocalUtils.getProductPrice(0L, "areaCode", "financeRuleCode")).thenReturn(productPriceVO);

        when(mockShardingId.next("name", "prefix", "memberId",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0L);

        // Configure IOrderExchangeService.createExchangeOrder(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderSn("orderSn");
        orderPO1.setUserNo("userNo");
        orderPO1.setUserMobile("userMobile");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setOrderState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setPerformanceModes("performanceModes");
        orderPO1.setExchangeFlag(0);
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderSn("orderSn");
        orderPO2.setUserNo("userNo");
        orderPO2.setUserMobile("userMobile");
        orderPO2.setPaySn("paySn");
        orderPO2.setStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setOrderState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setPerformanceModes("performanceModes");
        orderPO2.setExchangeFlag(0);
        final OrderExchangeDetailVO orderExchangeDetailVO = new OrderExchangeDetailVO();
        orderExchangeDetailVO.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo1 = new ExchangeOrderDTO();
        productInfo1.setOrderSn("orderSn");
        productInfo1.setOrderProductId(0L);
        productInfo1.setProductId(0L);
        productInfo1.setGoodsName("exchangeProductName");
        productInfo1.setProductImage("productImage");
        productInfo1.setGoodsId(0L);
        productInfo1.setSpecValues("specValues");
        productInfo1.setProductShowPrice(new BigDecimal("0.00"));
        productInfo1.setProductNum(0);
        productInfo1.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo1.setMoneyAmount(new BigDecimal("0.00"));
        productInfo1.setXzCardAmount(new BigDecimal("0.00"));
        productInfo1.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo1.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo1.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setProductInfo(productInfo1);
        final ExchangeOrderDTO newOrderInfo1 = new ExchangeOrderDTO();
        newOrderInfo1.setOrderSn("orderSn");
        newOrderInfo1.setOrderProductId(0L);
        newOrderInfo1.setProductId(0L);
        newOrderInfo1.setGoodsName("exchangeProductName");
        newOrderInfo1.setProductImage("productImage");
        newOrderInfo1.setGoodsId(0L);
        newOrderInfo1.setSpecValues("specValues");
        newOrderInfo1.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo1.setProductNum(0);
        newOrderInfo1.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo1.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo1.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo1.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo1.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo1.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setNewOrderInfo(newOrderInfo1);
        orderExchangeDetailVO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreVoucherAmount(new BigDecimal("0.00"));
        final OrderExchangeRequest orderExchangeRequest1 = new OrderExchangeRequest();
        orderExchangeRequest1.setOrderSn("orderSn");
        orderExchangeRequest1.setOrderProductId(0L);
        orderExchangeRequest1.setProductNum(0);
        orderExchangeRequest1.setExchangeReason("exchangeReason");
        orderExchangeRequest1.setBuyerConfirmFlag(0);
        orderExchangeRequest1.setProductId(0L);
        orderExchangeRequest1.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest1.setIsTrial(false);
        final UserDTO applicantInfo1 = new UserDTO();
        applicantInfo1.setUserId(0L);
        applicantInfo1.setUserName("operatorName");
        applicantInfo1.setUserRole(0);
        applicantInfo1.setStoreId(0L);
        applicantInfo1.setMobile("mobile");
        orderExchangeRequest1.setApplicantInfo(applicantInfo1);
        orderExchangeRequest1.setChannel("value");
        orderExchangeRequest1.setDealerCode("dealerCode");
        when(mockOrderExchangeService.createExchangeOrder(orderPO2, orderExchangeDetailVO,
                orderExchangeRequest1)).thenReturn(orderPO1);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setGoodsName("goodsName");
        orderProductPO1.setProductImage("productImage");
        orderProductPO1.setSpecValues("specValues");
        orderProductPO1.setProductId(0L);
        orderProductPO1.setFinanceRuleCode("financeRuleCode");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO1.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO1.setReturnNumber(0);
        orderProductPO1.setChannelSkuUnit("channelSkuUnit");
        orderProductPO1.setChannelSkuId("channelSkuId");
        orderProductPO1.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO1);
        when(mockOrderProductModel.getOrderProductListByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure OrderExtendModel.getOrderExtendByOrderSn(...).
        final OrderExtendPO extendPO = new OrderExtendPO();
        extendPO.setOrderSn("orderSn");
        extendPO.setBranch("branch");
        extendPO.setReceiverProvinceCode("province");
        extendPO.setReceiverAddress("detailAddress");
        extendPO.setReceiverMobile("receiverMobile");
        extendPO.setReceiverCityCode("city");
        extendPO.setReceiverDistrictCode("district");
        extendPO.setReceiverTownCode("receiverTownCode");
        extendPO.setReceiverName("receiverName");
        extendPO.setReceiveBranchCode("receiveBranchCode");
        extendPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        extendPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        extendPO.setWarehouseCode("warehouseCode");
        when(mockOrderExtendModel.getOrderExtendByOrderSn("orderSn")).thenReturn(extendPO);

        // Configure OrderExchangeMapper.getOrderExchange(...).
        final OrderExchangePO orderExchangePO = new OrderExchangePO();
        orderExchangePO.setExchangeSn("exchangeSn");
        orderExchangePO.setMemberId(0);
        orderExchangePO.setStoreId(0L);
        orderExchangePO.setExchangeReason("exchangeReason");
        orderExchangePO.setBuyerConfirmFlag(0);
        orderExchangePO.setExchangeOrderState(0);
        orderExchangePO.setApplicantId(0L);
        orderExchangePO.setApplicantName("operatorName");
        orderExchangePO.setApplicantRole(0);
        orderExchangePO.setApproverId(0L);
        orderExchangePO.setApproverName("approverName");
        orderExchangePO.setApproverRole(0);
        orderExchangePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExchangePO.setCreateBy("operatorName");
        orderExchangePO.setUpdateBy("updateBy");
        final OrderExchangeExample example1 = new OrderExchangeExample();
        example1.setExchangeOrderId(0);
        example1.setExchangeSn("exchangeSn");
        example1.setMemberId(0);
        example1.setStoreId(0L);
        example1.setExchangeReason("exchangeReason");
        when(mockOrderExchangeMapper.getOrderExchange(example1)).thenReturn(orderExchangePO);

        // Run the test
        final OrderExchangeDetailVO result = orderExchangeServiceImplUnderTest.applyExchange(orderExchangeRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockOrderExchangeService).saveExchangeApplyOrder("exchangeSn", 0L, 0,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"), 0, "exchangeReason");
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "operatorName", "orderSn", 0, 0, 0, "创建换货申请",
                OrderCreateChannel.H5);

        // Confirm IOrderExchangeService.saveExchangeOrderDetail(...).
        final OrderExchangeDetailVO orderExchangeDetailVO1 = new OrderExchangeDetailVO();
        orderExchangeDetailVO1.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo2 = new ExchangeOrderDTO();
        productInfo2.setOrderSn("orderSn");
        productInfo2.setOrderProductId(0L);
        productInfo2.setProductId(0L);
        productInfo2.setGoodsName("exchangeProductName");
        productInfo2.setProductImage("productImage");
        productInfo2.setGoodsId(0L);
        productInfo2.setSpecValues("specValues");
        productInfo2.setProductShowPrice(new BigDecimal("0.00"));
        productInfo2.setProductNum(0);
        productInfo2.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo2.setMoneyAmount(new BigDecimal("0.00"));
        productInfo2.setXzCardAmount(new BigDecimal("0.00"));
        productInfo2.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo2.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo2.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setProductInfo(productInfo2);
        final ExchangeOrderDTO newOrderInfo2 = new ExchangeOrderDTO();
        newOrderInfo2.setOrderSn("orderSn");
        newOrderInfo2.setOrderProductId(0L);
        newOrderInfo2.setProductId(0L);
        newOrderInfo2.setGoodsName("exchangeProductName");
        newOrderInfo2.setProductImage("productImage");
        newOrderInfo2.setGoodsId(0L);
        newOrderInfo2.setSpecValues("specValues");
        newOrderInfo2.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo2.setProductNum(0);
        newOrderInfo2.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo2.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo2.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo2.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo2.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo2.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setNewOrderInfo(newOrderInfo2);
        orderExchangeDetailVO1.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).saveExchangeOrderDetail(orderExchangeDetailVO1, "operatorName");
        verify(mockOrderExchangeService).updateOrderToExchange("orderSn", 0);

        // Confirm IOrderExchangeService.updateOrderAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO2 = new OrderExchangeDetailVO();
        orderExchangeDetailVO2.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo3 = new ExchangeOrderDTO();
        productInfo3.setOrderSn("orderSn");
        productInfo3.setOrderProductId(0L);
        productInfo3.setProductId(0L);
        productInfo3.setGoodsName("exchangeProductName");
        productInfo3.setProductImage("productImage");
        productInfo3.setGoodsId(0L);
        productInfo3.setSpecValues("specValues");
        productInfo3.setProductShowPrice(new BigDecimal("0.00"));
        productInfo3.setProductNum(0);
        productInfo3.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo3.setMoneyAmount(new BigDecimal("0.00"));
        productInfo3.setXzCardAmount(new BigDecimal("0.00"));
        productInfo3.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo3.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo3.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setProductInfo(productInfo3);
        final ExchangeOrderDTO newOrderInfo3 = new ExchangeOrderDTO();
        newOrderInfo3.setOrderSn("orderSn");
        newOrderInfo3.setOrderProductId(0L);
        newOrderInfo3.setProductId(0L);
        newOrderInfo3.setGoodsName("exchangeProductName");
        newOrderInfo3.setProductImage("productImage");
        newOrderInfo3.setGoodsId(0L);
        newOrderInfo3.setSpecValues("specValues");
        newOrderInfo3.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo3.setProductNum(0);
        newOrderInfo3.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo3.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo3.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo3.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo3.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo3.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setNewOrderInfo(newOrderInfo3);
        orderExchangeDetailVO2.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateOrderAmount("orderSn", orderExchangeDetailVO2);

        // Confirm IOrderExchangeService.updateOrderProductAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO3 = new OrderExchangeDetailVO();
        orderExchangeDetailVO3.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo4 = new ExchangeOrderDTO();
        productInfo4.setOrderSn("orderSn");
        productInfo4.setOrderProductId(0L);
        productInfo4.setProductId(0L);
        productInfo4.setGoodsName("exchangeProductName");
        productInfo4.setProductImage("productImage");
        productInfo4.setGoodsId(0L);
        productInfo4.setSpecValues("specValues");
        productInfo4.setProductShowPrice(new BigDecimal("0.00"));
        productInfo4.setProductNum(0);
        productInfo4.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo4.setMoneyAmount(new BigDecimal("0.00"));
        productInfo4.setXzCardAmount(new BigDecimal("0.00"));
        productInfo4.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo4.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo4.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setProductInfo(productInfo4);
        final ExchangeOrderDTO newOrderInfo4 = new ExchangeOrderDTO();
        newOrderInfo4.setOrderSn("orderSn");
        newOrderInfo4.setOrderProductId(0L);
        newOrderInfo4.setProductId(0L);
        newOrderInfo4.setGoodsName("exchangeProductName");
        newOrderInfo4.setProductImage("productImage");
        newOrderInfo4.setGoodsId(0L);
        newOrderInfo4.setSpecValues("specValues");
        newOrderInfo4.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo4.setProductNum(0);
        newOrderInfo4.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo4.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo4.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo4.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo4.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo4.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setNewOrderInfo(newOrderInfo4);
        orderExchangeDetailVO3.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateOrderProductAmount(orderExchangeDetailVO3);
        verify(mockOrderExchangeService).updatePayAmount("paySn", new BigDecimal("0.00"));

        // Confirm IOrderExchangeService.updateExtendAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO4 = new OrderExchangeDetailVO();
        orderExchangeDetailVO4.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo5 = new ExchangeOrderDTO();
        productInfo5.setOrderSn("orderSn");
        productInfo5.setOrderProductId(0L);
        productInfo5.setProductId(0L);
        productInfo5.setGoodsName("exchangeProductName");
        productInfo5.setProductImage("productImage");
        productInfo5.setGoodsId(0L);
        productInfo5.setSpecValues("specValues");
        productInfo5.setProductShowPrice(new BigDecimal("0.00"));
        productInfo5.setProductNum(0);
        productInfo5.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo5.setMoneyAmount(new BigDecimal("0.00"));
        productInfo5.setXzCardAmount(new BigDecimal("0.00"));
        productInfo5.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo5.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo5.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo5.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setProductInfo(productInfo5);
        final ExchangeOrderDTO newOrderInfo5 = new ExchangeOrderDTO();
        newOrderInfo5.setOrderSn("orderSn");
        newOrderInfo5.setOrderProductId(0L);
        newOrderInfo5.setProductId(0L);
        newOrderInfo5.setGoodsName("exchangeProductName");
        newOrderInfo5.setProductImage("productImage");
        newOrderInfo5.setGoodsId(0L);
        newOrderInfo5.setSpecValues("specValues");
        newOrderInfo5.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo5.setProductNum(0);
        newOrderInfo5.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo5.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo5.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo5.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo5.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo5.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo5.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setNewOrderInfo(newOrderInfo5);
        orderExchangeDetailVO4.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateExtendAmount("orderSn", orderExchangeDetailVO4);

        // Confirm GoodsStockService.goodsStock(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderSn("orderSn");
        orderPO3.setUserNo("userNo");
        orderPO3.setUserMobile("userMobile");
        orderPO3.setPaySn("paySn");
        orderPO3.setStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setOrderState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setPerformanceModes("performanceModes");
        orderPO3.setExchangeFlag(0);
        /*verify(mockGoodsStockService).goodsStock("orderSn", "exchangeSn", 0L, "areaCode", 0, "financeRuleCode",
                "batchNo", "channelSkuId", "branch", "receiveBranchCode", "warehouseCode",
                EventStockTypeEnum.ORDER_EXCHANGE_CREATE_IN_STOCK, BizTypeEnum.EXCHANGE_ORDER_INCREASE_STOCK,
                "operatorName", orderPO3, "channelSkuUnit", "userMobile");*/

        // Confirm IOrderExchangeService.exchangeOrderAuditDeal(...).
        final OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO.setExchangeAuditState(0);
        orderExchangeAuditDTO.setChannel("value");
        verify(mockOrderExchangeService).exchangeOrderAuditDeal(orderExchangeAuditDTO,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"));

        // Confirm RabbitTemplate.convertAndSend(...).
        final MessageSendVO object = new MessageSendVO();
        object.setReceiveId(0L);
        object.setTplType("tplType");
        object.setMsgLinkInfo("msgLinkInfo");
        final MessageSendProperty messageSendProperty = new MessageSendProperty();
        messageSendProperty.setPropertyName("first");
        messageSendProperty.setPropertyValue("memberName");
        object.setPropertyList(Arrays.asList(messageSendProperty));
        final MessageSendProperty messageSendProperty1 = new MessageSendProperty();
        messageSendProperty1.setPropertyName("first");
        messageSendProperty1.setPropertyValue("memberName");
        object.setWxPropertyList(Arrays.asList(messageSendProperty1));
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg", object);
    }

    @Test
    public void testApplyExchange_IOrderExchangeServiceSaveExchangeApplyOrderThrowsException() throws Exception {
        // Setup
        final OrderExchangeRequest orderExchangeRequest = new OrderExchangeRequest();
        orderExchangeRequest.setOrderSn("orderSn");
        orderExchangeRequest.setOrderProductId(0L);
        orderExchangeRequest.setProductNum(0);
        orderExchangeRequest.setExchangeReason("exchangeReason");
        orderExchangeRequest.setBuyerConfirmFlag(0);
        orderExchangeRequest.setProductId(0L);
        orderExchangeRequest.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest.setIsTrial(false);
        final UserDTO applicantInfo = new UserDTO();
        applicantInfo.setUserId(0L);
        applicantInfo.setUserName("operatorName");
        applicantInfo.setUserRole(0);
        applicantInfo.setStoreId(0L);
        applicantInfo.setMobile("mobile");
        orderExchangeRequest.setApplicantInfo(applicantInfo);
        orderExchangeRequest.setChannel("value");
        orderExchangeRequest.setDealerCode("dealerCode");

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserNo("userNo");
        orderPO.setUserMobile("userMobile");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setPerformanceModes("performanceModes");
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure OrderProductModel.getOrderProductByOrderProductId(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsName("goodsName");
        orderProductPO.setProductImage("productImage");
        orderProductPO.setSpecValues("specValues");
        orderProductPO.setProductId(0L);
        orderProductPO.setFinanceRuleCode("financeRuleCode");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        when(mockOrderProductModel.getOrderProductByOrderProductId(0L)).thenReturn(orderProductPO);

        // Configure OrderExchangeMapper.getExchangeOrderList(...).
        final ExchangeOrderDetailDTO exchangeOrderDetailDTO = new ExchangeOrderDetailDTO();
        exchangeOrderDetailDTO.setMemberId(0);
        exchangeOrderDetailDTO.setStoreId(0L);
        exchangeOrderDetailDTO.setOrderSn("orderSn");
        exchangeOrderDetailDTO.setOrderProductId(0L);
        exchangeOrderDetailDTO.setProductNum(0);
        exchangeOrderDetailDTO.setExchangeOrderSn("exchangeOrderSn");
        exchangeOrderDetailDTO.setExchangeOrderProductId(0L);
        exchangeOrderDetailDTO.setExchangeOrderState(0);
        exchangeOrderDetailDTO.setExchangeOrderStateList(Arrays.asList(0));
        exchangeOrderDetailDTO.setAfsSn("afsSn");
        final List<ExchangeOrderDetailDTO> exchangeOrderDetailDTOList = Arrays.asList(exchangeOrderDetailDTO);
        final ExchangeOrderDetailDTO example = new ExchangeOrderDetailDTO();
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setOrderSn("orderSn");
        example.setOrderProductId(0L);
        example.setProductNum(0);
        example.setExchangeOrderSn("exchangeOrderSn");
        example.setExchangeOrderProductId(0L);
        example.setExchangeOrderState(0);
        example.setExchangeOrderStateList(Arrays.asList(0));
        example.setAfsSn("afsSn");
        when(mockOrderExchangeMapper.getExchangeOrderList(example)).thenReturn(exchangeOrderDetailDTOList);

        // Configure OrderLocalUtils.getProductPrice(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setSpecValues("specValues");
        product.setProductPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setMainImage("productImage");
        productPriceVO.setProduct(product);
        final ProductPriceBranchRange productPriceBranchRange = new ProductPriceBranchRange();
        productPriceBranchRange.setTaxPrice(new BigDecimal("0.00"));
        productPriceVO.setProductPriceBranchRange(productPriceBranchRange);
        final Goods goods = new Goods();
        goods.setGoodsId(0L);
        goods.setGoodsName("exchangeProductName");
        productPriceVO.setGoods(goods);
        when(mockOrderLocalUtils.getProductPrice(0L, "areaCode", "financeRuleCode")).thenReturn(productPriceVO);

        when(mockShardingId.next("name", "prefix", "memberId",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0L);
        when(mockOrderExchangeService.saveExchangeApplyOrder("exchangeSn", 0L, 0,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"), 0, "exchangeReason")).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> orderExchangeServiceImplUnderTest.applyExchange(orderExchangeRequest))
                .isInstanceOf(Exception.class);
    }

    @Test
    public void testApplyExchange_OrderProductModelGetOrderProductListByOrderSnReturnsNoItems() throws Exception {
        // Setup
        final OrderExchangeRequest orderExchangeRequest = new OrderExchangeRequest();
        orderExchangeRequest.setOrderSn("orderSn");
        orderExchangeRequest.setOrderProductId(0L);
        orderExchangeRequest.setProductNum(0);
        orderExchangeRequest.setExchangeReason("exchangeReason");
        orderExchangeRequest.setBuyerConfirmFlag(0);
        orderExchangeRequest.setProductId(0L);
        orderExchangeRequest.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest.setIsTrial(false);
        final UserDTO applicantInfo = new UserDTO();
        applicantInfo.setUserId(0L);
        applicantInfo.setUserName("operatorName");
        applicantInfo.setUserRole(0);
        applicantInfo.setStoreId(0L);
        applicantInfo.setMobile("mobile");
        orderExchangeRequest.setApplicantInfo(applicantInfo);
        orderExchangeRequest.setChannel("value");
        orderExchangeRequest.setDealerCode("dealerCode");

        final OrderExchangeDetailVO expectedResult = new OrderExchangeDetailVO();
        expectedResult.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo = new ExchangeOrderDTO();
        productInfo.setOrderSn("orderSn");
        productInfo.setOrderProductId(0L);
        productInfo.setProductId(0L);
        productInfo.setGoodsName("exchangeProductName");
        productInfo.setProductImage("productImage");
        productInfo.setGoodsId(0L);
        productInfo.setSpecValues("specValues");
        productInfo.setProductShowPrice(new BigDecimal("0.00"));
        productInfo.setProductNum(0);
        productInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo.setMoneyAmount(new BigDecimal("0.00"));
        productInfo.setXzCardAmount(new BigDecimal("0.00"));
        productInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setProductInfo(productInfo);
        final ExchangeOrderDTO newOrderInfo = new ExchangeOrderDTO();
        newOrderInfo.setOrderSn("orderSn");
        newOrderInfo.setOrderProductId(0L);
        newOrderInfo.setProductId(0L);
        newOrderInfo.setGoodsName("exchangeProductName");
        newOrderInfo.setProductImage("productImage");
        newOrderInfo.setGoodsId(0L);
        newOrderInfo.setSpecValues("specValues");
        newOrderInfo.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo.setProductNum(0);
        newOrderInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setNewOrderInfo(newOrderInfo);
        expectedResult.setRefundAmount(new BigDecimal("0.00"));
        expectedResult.setXzCardAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformActivityAmount(new BigDecimal("0.00"));
        expectedResult.setStoreActivityAmount(new BigDecimal("0.00"));
        expectedResult.setStoreVoucherAmount(new BigDecimal("0.00"));

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserNo("userNo");
        orderPO.setUserMobile("userMobile");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setPerformanceModes("performanceModes");
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure OrderProductModel.getOrderProductByOrderProductId(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsName("goodsName");
        orderProductPO.setProductImage("productImage");
        orderProductPO.setSpecValues("specValues");
        orderProductPO.setProductId(0L);
        orderProductPO.setFinanceRuleCode("financeRuleCode");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        when(mockOrderProductModel.getOrderProductByOrderProductId(0L)).thenReturn(orderProductPO);

        // Configure OrderExchangeMapper.getExchangeOrderList(...).
        final ExchangeOrderDetailDTO exchangeOrderDetailDTO = new ExchangeOrderDetailDTO();
        exchangeOrderDetailDTO.setMemberId(0);
        exchangeOrderDetailDTO.setStoreId(0L);
        exchangeOrderDetailDTO.setOrderSn("orderSn");
        exchangeOrderDetailDTO.setOrderProductId(0L);
        exchangeOrderDetailDTO.setProductNum(0);
        exchangeOrderDetailDTO.setExchangeOrderSn("exchangeOrderSn");
        exchangeOrderDetailDTO.setExchangeOrderProductId(0L);
        exchangeOrderDetailDTO.setExchangeOrderState(0);
        exchangeOrderDetailDTO.setExchangeOrderStateList(Arrays.asList(0));
        exchangeOrderDetailDTO.setAfsSn("afsSn");
        final List<ExchangeOrderDetailDTO> exchangeOrderDetailDTOList = Arrays.asList(exchangeOrderDetailDTO);
        final ExchangeOrderDetailDTO example = new ExchangeOrderDetailDTO();
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setOrderSn("orderSn");
        example.setOrderProductId(0L);
        example.setProductNum(0);
        example.setExchangeOrderSn("exchangeOrderSn");
        example.setExchangeOrderProductId(0L);
        example.setExchangeOrderState(0);
        example.setExchangeOrderStateList(Arrays.asList(0));
        example.setAfsSn("afsSn");
        when(mockOrderExchangeMapper.getExchangeOrderList(example)).thenReturn(exchangeOrderDetailDTOList);

        // Configure OrderLocalUtils.getProductPrice(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setSpecValues("specValues");
        product.setProductPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setMainImage("productImage");
        productPriceVO.setProduct(product);
        final ProductPriceBranchRange productPriceBranchRange = new ProductPriceBranchRange();
        productPriceBranchRange.setTaxPrice(new BigDecimal("0.00"));
        productPriceVO.setProductPriceBranchRange(productPriceBranchRange);
        final Goods goods = new Goods();
        goods.setGoodsId(0L);
        goods.setGoodsName("exchangeProductName");
        productPriceVO.setGoods(goods);
        when(mockOrderLocalUtils.getProductPrice(0L, "areaCode", "financeRuleCode")).thenReturn(productPriceVO);

        when(mockShardingId.next("name", "prefix", "memberId",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0L);

        // Configure IOrderExchangeService.createExchangeOrder(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderSn("orderSn");
        orderPO1.setUserNo("userNo");
        orderPO1.setUserMobile("userMobile");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setOrderState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setPerformanceModes("performanceModes");
        orderPO1.setExchangeFlag(0);
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderSn("orderSn");
        orderPO2.setUserNo("userNo");
        orderPO2.setUserMobile("userMobile");
        orderPO2.setPaySn("paySn");
        orderPO2.setStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setOrderState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setPerformanceModes("performanceModes");
        orderPO2.setExchangeFlag(0);
        final OrderExchangeDetailVO orderExchangeDetailVO = new OrderExchangeDetailVO();
        orderExchangeDetailVO.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo1 = new ExchangeOrderDTO();
        productInfo1.setOrderSn("orderSn");
        productInfo1.setOrderProductId(0L);
        productInfo1.setProductId(0L);
        productInfo1.setGoodsName("exchangeProductName");
        productInfo1.setProductImage("productImage");
        productInfo1.setGoodsId(0L);
        productInfo1.setSpecValues("specValues");
        productInfo1.setProductShowPrice(new BigDecimal("0.00"));
        productInfo1.setProductNum(0);
        productInfo1.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo1.setMoneyAmount(new BigDecimal("0.00"));
        productInfo1.setXzCardAmount(new BigDecimal("0.00"));
        productInfo1.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo1.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo1.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setProductInfo(productInfo1);
        final ExchangeOrderDTO newOrderInfo1 = new ExchangeOrderDTO();
        newOrderInfo1.setOrderSn("orderSn");
        newOrderInfo1.setOrderProductId(0L);
        newOrderInfo1.setProductId(0L);
        newOrderInfo1.setGoodsName("exchangeProductName");
        newOrderInfo1.setProductImage("productImage");
        newOrderInfo1.setGoodsId(0L);
        newOrderInfo1.setSpecValues("specValues");
        newOrderInfo1.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo1.setProductNum(0);
        newOrderInfo1.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo1.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo1.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo1.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo1.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo1.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setNewOrderInfo(newOrderInfo1);
        orderExchangeDetailVO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreVoucherAmount(new BigDecimal("0.00"));
        final OrderExchangeRequest orderExchangeRequest1 = new OrderExchangeRequest();
        orderExchangeRequest1.setOrderSn("orderSn");
        orderExchangeRequest1.setOrderProductId(0L);
        orderExchangeRequest1.setProductNum(0);
        orderExchangeRequest1.setExchangeReason("exchangeReason");
        orderExchangeRequest1.setBuyerConfirmFlag(0);
        orderExchangeRequest1.setProductId(0L);
        orderExchangeRequest1.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest1.setIsTrial(false);
        final UserDTO applicantInfo1 = new UserDTO();
        applicantInfo1.setUserId(0L);
        applicantInfo1.setUserName("operatorName");
        applicantInfo1.setUserRole(0);
        applicantInfo1.setStoreId(0L);
        applicantInfo1.setMobile("mobile");
        orderExchangeRequest1.setApplicantInfo(applicantInfo1);
        orderExchangeRequest1.setChannel("value");
        orderExchangeRequest1.setDealerCode("dealerCode");
        when(mockOrderExchangeService.createExchangeOrder(orderPO2, orderExchangeDetailVO,
                orderExchangeRequest1)).thenReturn(orderPO1);

        when(mockOrderProductModel.getOrderProductListByOrderSn("orderSn")).thenReturn(Collections.emptyList());

        // Configure OrderExtendModel.getOrderExtendByOrderSn(...).
        final OrderExtendPO extendPO = new OrderExtendPO();
        extendPO.setOrderSn("orderSn");
        extendPO.setBranch("branch");
        extendPO.setReceiverProvinceCode("province");
        extendPO.setReceiverAddress("detailAddress");
        extendPO.setReceiverMobile("receiverMobile");
        extendPO.setReceiverCityCode("city");
        extendPO.setReceiverDistrictCode("district");
        extendPO.setReceiverTownCode("receiverTownCode");
        extendPO.setReceiverName("receiverName");
        extendPO.setReceiveBranchCode("receiveBranchCode");
        extendPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        extendPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        extendPO.setWarehouseCode("warehouseCode");
        when(mockOrderExtendModel.getOrderExtendByOrderSn("orderSn")).thenReturn(extendPO);

        // Configure OrderExchangeMapper.getOrderExchange(...).
        final OrderExchangePO orderExchangePO = new OrderExchangePO();
        orderExchangePO.setExchangeSn("exchangeSn");
        orderExchangePO.setMemberId(0);
        orderExchangePO.setStoreId(0L);
        orderExchangePO.setExchangeReason("exchangeReason");
        orderExchangePO.setBuyerConfirmFlag(0);
        orderExchangePO.setExchangeOrderState(0);
        orderExchangePO.setApplicantId(0L);
        orderExchangePO.setApplicantName("operatorName");
        orderExchangePO.setApplicantRole(0);
        orderExchangePO.setApproverId(0L);
        orderExchangePO.setApproverName("approverName");
        orderExchangePO.setApproverRole(0);
        orderExchangePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExchangePO.setCreateBy("operatorName");
        orderExchangePO.setUpdateBy("updateBy");
        final OrderExchangeExample example1 = new OrderExchangeExample();
        example1.setExchangeOrderId(0);
        example1.setExchangeSn("exchangeSn");
        example1.setMemberId(0);
        example1.setStoreId(0L);
        example1.setExchangeReason("exchangeReason");
        when(mockOrderExchangeMapper.getOrderExchange(example1)).thenReturn(orderExchangePO);

        // Run the test
        final OrderExchangeDetailVO result = orderExchangeServiceImplUnderTest.applyExchange(orderExchangeRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockOrderExchangeService).saveExchangeApplyOrder("exchangeSn", 0L, 0,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"), 0, "exchangeReason");
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "operatorName", "orderSn", 0, 0, 0, "创建换货申请",
                OrderCreateChannel.H5);

        // Confirm IOrderExchangeService.saveExchangeOrderDetail(...).
        final OrderExchangeDetailVO orderExchangeDetailVO1 = new OrderExchangeDetailVO();
        orderExchangeDetailVO1.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo2 = new ExchangeOrderDTO();
        productInfo2.setOrderSn("orderSn");
        productInfo2.setOrderProductId(0L);
        productInfo2.setProductId(0L);
        productInfo2.setGoodsName("exchangeProductName");
        productInfo2.setProductImage("productImage");
        productInfo2.setGoodsId(0L);
        productInfo2.setSpecValues("specValues");
        productInfo2.setProductShowPrice(new BigDecimal("0.00"));
        productInfo2.setProductNum(0);
        productInfo2.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo2.setMoneyAmount(new BigDecimal("0.00"));
        productInfo2.setXzCardAmount(new BigDecimal("0.00"));
        productInfo2.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo2.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo2.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setProductInfo(productInfo2);
        final ExchangeOrderDTO newOrderInfo2 = new ExchangeOrderDTO();
        newOrderInfo2.setOrderSn("orderSn");
        newOrderInfo2.setOrderProductId(0L);
        newOrderInfo2.setProductId(0L);
        newOrderInfo2.setGoodsName("exchangeProductName");
        newOrderInfo2.setProductImage("productImage");
        newOrderInfo2.setGoodsId(0L);
        newOrderInfo2.setSpecValues("specValues");
        newOrderInfo2.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo2.setProductNum(0);
        newOrderInfo2.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo2.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo2.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo2.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo2.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo2.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setNewOrderInfo(newOrderInfo2);
        orderExchangeDetailVO1.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).saveExchangeOrderDetail(orderExchangeDetailVO1, "operatorName");
        verify(mockOrderExchangeService).updateOrderToExchange("orderSn", 0);

        // Confirm IOrderExchangeService.updateOrderAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO2 = new OrderExchangeDetailVO();
        orderExchangeDetailVO2.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo3 = new ExchangeOrderDTO();
        productInfo3.setOrderSn("orderSn");
        productInfo3.setOrderProductId(0L);
        productInfo3.setProductId(0L);
        productInfo3.setGoodsName("exchangeProductName");
        productInfo3.setProductImage("productImage");
        productInfo3.setGoodsId(0L);
        productInfo3.setSpecValues("specValues");
        productInfo3.setProductShowPrice(new BigDecimal("0.00"));
        productInfo3.setProductNum(0);
        productInfo3.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo3.setMoneyAmount(new BigDecimal("0.00"));
        productInfo3.setXzCardAmount(new BigDecimal("0.00"));
        productInfo3.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo3.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo3.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setProductInfo(productInfo3);
        final ExchangeOrderDTO newOrderInfo3 = new ExchangeOrderDTO();
        newOrderInfo3.setOrderSn("orderSn");
        newOrderInfo3.setOrderProductId(0L);
        newOrderInfo3.setProductId(0L);
        newOrderInfo3.setGoodsName("exchangeProductName");
        newOrderInfo3.setProductImage("productImage");
        newOrderInfo3.setGoodsId(0L);
        newOrderInfo3.setSpecValues("specValues");
        newOrderInfo3.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo3.setProductNum(0);
        newOrderInfo3.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo3.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo3.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo3.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo3.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo3.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setNewOrderInfo(newOrderInfo3);
        orderExchangeDetailVO2.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateOrderAmount("orderSn", orderExchangeDetailVO2);

        // Confirm IOrderExchangeService.updateOrderProductAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO3 = new OrderExchangeDetailVO();
        orderExchangeDetailVO3.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo4 = new ExchangeOrderDTO();
        productInfo4.setOrderSn("orderSn");
        productInfo4.setOrderProductId(0L);
        productInfo4.setProductId(0L);
        productInfo4.setGoodsName("exchangeProductName");
        productInfo4.setProductImage("productImage");
        productInfo4.setGoodsId(0L);
        productInfo4.setSpecValues("specValues");
        productInfo4.setProductShowPrice(new BigDecimal("0.00"));
        productInfo4.setProductNum(0);
        productInfo4.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo4.setMoneyAmount(new BigDecimal("0.00"));
        productInfo4.setXzCardAmount(new BigDecimal("0.00"));
        productInfo4.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo4.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo4.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setProductInfo(productInfo4);
        final ExchangeOrderDTO newOrderInfo4 = new ExchangeOrderDTO();
        newOrderInfo4.setOrderSn("orderSn");
        newOrderInfo4.setOrderProductId(0L);
        newOrderInfo4.setProductId(0L);
        newOrderInfo4.setGoodsName("exchangeProductName");
        newOrderInfo4.setProductImage("productImage");
        newOrderInfo4.setGoodsId(0L);
        newOrderInfo4.setSpecValues("specValues");
        newOrderInfo4.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo4.setProductNum(0);
        newOrderInfo4.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo4.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo4.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo4.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo4.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo4.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setNewOrderInfo(newOrderInfo4);
        orderExchangeDetailVO3.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateOrderProductAmount(orderExchangeDetailVO3);
        verify(mockOrderExchangeService).updatePayAmount("paySn", new BigDecimal("0.00"));

        // Confirm IOrderExchangeService.updateExtendAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO4 = new OrderExchangeDetailVO();
        orderExchangeDetailVO4.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo5 = new ExchangeOrderDTO();
        productInfo5.setOrderSn("orderSn");
        productInfo5.setOrderProductId(0L);
        productInfo5.setProductId(0L);
        productInfo5.setGoodsName("exchangeProductName");
        productInfo5.setProductImage("productImage");
        productInfo5.setGoodsId(0L);
        productInfo5.setSpecValues("specValues");
        productInfo5.setProductShowPrice(new BigDecimal("0.00"));
        productInfo5.setProductNum(0);
        productInfo5.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo5.setMoneyAmount(new BigDecimal("0.00"));
        productInfo5.setXzCardAmount(new BigDecimal("0.00"));
        productInfo5.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo5.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo5.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo5.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setProductInfo(productInfo5);
        final ExchangeOrderDTO newOrderInfo5 = new ExchangeOrderDTO();
        newOrderInfo5.setOrderSn("orderSn");
        newOrderInfo5.setOrderProductId(0L);
        newOrderInfo5.setProductId(0L);
        newOrderInfo5.setGoodsName("exchangeProductName");
        newOrderInfo5.setProductImage("productImage");
        newOrderInfo5.setGoodsId(0L);
        newOrderInfo5.setSpecValues("specValues");
        newOrderInfo5.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo5.setProductNum(0);
        newOrderInfo5.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo5.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo5.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo5.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo5.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo5.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo5.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setNewOrderInfo(newOrderInfo5);
        orderExchangeDetailVO4.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateExtendAmount("orderSn", orderExchangeDetailVO4);

        // Confirm GoodsStockService.goodsStock(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderSn("orderSn");
        orderPO3.setUserNo("userNo");
        orderPO3.setUserMobile("userMobile");
        orderPO3.setPaySn("paySn");
        orderPO3.setStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setOrderState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setPerformanceModes("performanceModes");
        orderPO3.setExchangeFlag(0);
        /*verify(mockGoodsStockService).goodsStock("orderSn", "exchangeSn", 0L, "areaCode", 0, "financeRuleCode",
                "batchNo", "channelSkuId", "branch", "receiveBranchCode", "warehouseCode",
                EventStockTypeEnum.ORDER_EXCHANGE_CREATE_IN_STOCK, BizTypeEnum.EXCHANGE_ORDER_INCREASE_STOCK,
                "operatorName", orderPO3, "channelSkuUnit", "userMobile");*/

        // Confirm IOrderExchangeService.exchangeOrderAuditDeal(...).
        final OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO.setExchangeAuditState(0);
        orderExchangeAuditDTO.setChannel("value");
        verify(mockOrderExchangeService).exchangeOrderAuditDeal(orderExchangeAuditDTO,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"));

        // Confirm RabbitTemplate.convertAndSend(...).
        final MessageSendVO object = new MessageSendVO();
        object.setReceiveId(0L);
        object.setTplType("tplType");
        object.setMsgLinkInfo("msgLinkInfo");
        final MessageSendProperty messageSendProperty = new MessageSendProperty();
        messageSendProperty.setPropertyName("first");
        messageSendProperty.setPropertyValue("memberName");
        object.setPropertyList(Arrays.asList(messageSendProperty));
        final MessageSendProperty messageSendProperty1 = new MessageSendProperty();
        messageSendProperty1.setPropertyName("first");
        messageSendProperty1.setPropertyValue("memberName");
        object.setWxPropertyList(Arrays.asList(messageSendProperty1));
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg", object);
    }

    @Test
    public void testApplyExchange_OrderExchangeMapperGetOrderExchangeReturnsNull() throws Exception {
        // Setup
        final OrderExchangeRequest orderExchangeRequest = new OrderExchangeRequest();
        orderExchangeRequest.setOrderSn("orderSn");
        orderExchangeRequest.setOrderProductId(0L);
        orderExchangeRequest.setProductNum(0);
        orderExchangeRequest.setExchangeReason("exchangeReason");
        orderExchangeRequest.setBuyerConfirmFlag(0);
        orderExchangeRequest.setProductId(0L);
        orderExchangeRequest.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest.setIsTrial(false);
        final UserDTO applicantInfo = new UserDTO();
        applicantInfo.setUserId(0L);
        applicantInfo.setUserName("operatorName");
        applicantInfo.setUserRole(0);
        applicantInfo.setStoreId(0L);
        applicantInfo.setMobile("mobile");
        orderExchangeRequest.setApplicantInfo(applicantInfo);
        orderExchangeRequest.setChannel("value");
        orderExchangeRequest.setDealerCode("dealerCode");

        final OrderExchangeDetailVO expectedResult = new OrderExchangeDetailVO();
        expectedResult.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo = new ExchangeOrderDTO();
        productInfo.setOrderSn("orderSn");
        productInfo.setOrderProductId(0L);
        productInfo.setProductId(0L);
        productInfo.setGoodsName("exchangeProductName");
        productInfo.setProductImage("productImage");
        productInfo.setGoodsId(0L);
        productInfo.setSpecValues("specValues");
        productInfo.setProductShowPrice(new BigDecimal("0.00"));
        productInfo.setProductNum(0);
        productInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo.setMoneyAmount(new BigDecimal("0.00"));
        productInfo.setXzCardAmount(new BigDecimal("0.00"));
        productInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setProductInfo(productInfo);
        final ExchangeOrderDTO newOrderInfo = new ExchangeOrderDTO();
        newOrderInfo.setOrderSn("orderSn");
        newOrderInfo.setOrderProductId(0L);
        newOrderInfo.setProductId(0L);
        newOrderInfo.setGoodsName("exchangeProductName");
        newOrderInfo.setProductImage("productImage");
        newOrderInfo.setGoodsId(0L);
        newOrderInfo.setSpecValues("specValues");
        newOrderInfo.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo.setProductNum(0);
        newOrderInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setNewOrderInfo(newOrderInfo);
        expectedResult.setRefundAmount(new BigDecimal("0.00"));
        expectedResult.setXzCardAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformActivityAmount(new BigDecimal("0.00"));
        expectedResult.setStoreActivityAmount(new BigDecimal("0.00"));
        expectedResult.setStoreVoucherAmount(new BigDecimal("0.00"));

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserNo("userNo");
        orderPO.setUserMobile("userMobile");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setPerformanceModes("performanceModes");
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure OrderProductModel.getOrderProductByOrderProductId(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsName("goodsName");
        orderProductPO.setProductImage("productImage");
        orderProductPO.setSpecValues("specValues");
        orderProductPO.setProductId(0L);
        orderProductPO.setFinanceRuleCode("financeRuleCode");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        when(mockOrderProductModel.getOrderProductByOrderProductId(0L)).thenReturn(orderProductPO);

        // Configure OrderExchangeMapper.getExchangeOrderList(...).
        final ExchangeOrderDetailDTO exchangeOrderDetailDTO = new ExchangeOrderDetailDTO();
        exchangeOrderDetailDTO.setMemberId(0);
        exchangeOrderDetailDTO.setStoreId(0L);
        exchangeOrderDetailDTO.setOrderSn("orderSn");
        exchangeOrderDetailDTO.setOrderProductId(0L);
        exchangeOrderDetailDTO.setProductNum(0);
        exchangeOrderDetailDTO.setExchangeOrderSn("exchangeOrderSn");
        exchangeOrderDetailDTO.setExchangeOrderProductId(0L);
        exchangeOrderDetailDTO.setExchangeOrderState(0);
        exchangeOrderDetailDTO.setExchangeOrderStateList(Arrays.asList(0));
        exchangeOrderDetailDTO.setAfsSn("afsSn");
        final List<ExchangeOrderDetailDTO> exchangeOrderDetailDTOList = Arrays.asList(exchangeOrderDetailDTO);
        final ExchangeOrderDetailDTO example = new ExchangeOrderDetailDTO();
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setOrderSn("orderSn");
        example.setOrderProductId(0L);
        example.setProductNum(0);
        example.setExchangeOrderSn("exchangeOrderSn");
        example.setExchangeOrderProductId(0L);
        example.setExchangeOrderState(0);
        example.setExchangeOrderStateList(Arrays.asList(0));
        example.setAfsSn("afsSn");
        when(mockOrderExchangeMapper.getExchangeOrderList(example)).thenReturn(exchangeOrderDetailDTOList);

        // Configure OrderLocalUtils.getProductPrice(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setSpecValues("specValues");
        product.setProductPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setMainImage("productImage");
        productPriceVO.setProduct(product);
        final ProductPriceBranchRange productPriceBranchRange = new ProductPriceBranchRange();
        productPriceBranchRange.setTaxPrice(new BigDecimal("0.00"));
        productPriceVO.setProductPriceBranchRange(productPriceBranchRange);
        final Goods goods = new Goods();
        goods.setGoodsId(0L);
        goods.setGoodsName("exchangeProductName");
        productPriceVO.setGoods(goods);
        when(mockOrderLocalUtils.getProductPrice(0L, "areaCode", "financeRuleCode")).thenReturn(productPriceVO);

        when(mockShardingId.next("name", "prefix", "memberId",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0L);

        // Configure IOrderExchangeService.createExchangeOrder(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderSn("orderSn");
        orderPO1.setUserNo("userNo");
        orderPO1.setUserMobile("userMobile");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setOrderState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setPerformanceModes("performanceModes");
        orderPO1.setExchangeFlag(0);
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderSn("orderSn");
        orderPO2.setUserNo("userNo");
        orderPO2.setUserMobile("userMobile");
        orderPO2.setPaySn("paySn");
        orderPO2.setStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setOrderState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setPerformanceModes("performanceModes");
        orderPO2.setExchangeFlag(0);
        final OrderExchangeDetailVO orderExchangeDetailVO = new OrderExchangeDetailVO();
        orderExchangeDetailVO.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo1 = new ExchangeOrderDTO();
        productInfo1.setOrderSn("orderSn");
        productInfo1.setOrderProductId(0L);
        productInfo1.setProductId(0L);
        productInfo1.setGoodsName("exchangeProductName");
        productInfo1.setProductImage("productImage");
        productInfo1.setGoodsId(0L);
        productInfo1.setSpecValues("specValues");
        productInfo1.setProductShowPrice(new BigDecimal("0.00"));
        productInfo1.setProductNum(0);
        productInfo1.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo1.setMoneyAmount(new BigDecimal("0.00"));
        productInfo1.setXzCardAmount(new BigDecimal("0.00"));
        productInfo1.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo1.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo1.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setProductInfo(productInfo1);
        final ExchangeOrderDTO newOrderInfo1 = new ExchangeOrderDTO();
        newOrderInfo1.setOrderSn("orderSn");
        newOrderInfo1.setOrderProductId(0L);
        newOrderInfo1.setProductId(0L);
        newOrderInfo1.setGoodsName("exchangeProductName");
        newOrderInfo1.setProductImage("productImage");
        newOrderInfo1.setGoodsId(0L);
        newOrderInfo1.setSpecValues("specValues");
        newOrderInfo1.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo1.setProductNum(0);
        newOrderInfo1.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo1.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo1.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo1.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo1.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo1.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setNewOrderInfo(newOrderInfo1);
        orderExchangeDetailVO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreVoucherAmount(new BigDecimal("0.00"));
        final OrderExchangeRequest orderExchangeRequest1 = new OrderExchangeRequest();
        orderExchangeRequest1.setOrderSn("orderSn");
        orderExchangeRequest1.setOrderProductId(0L);
        orderExchangeRequest1.setProductNum(0);
        orderExchangeRequest1.setExchangeReason("exchangeReason");
        orderExchangeRequest1.setBuyerConfirmFlag(0);
        orderExchangeRequest1.setProductId(0L);
        orderExchangeRequest1.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest1.setIsTrial(false);
        final UserDTO applicantInfo1 = new UserDTO();
        applicantInfo1.setUserId(0L);
        applicantInfo1.setUserName("operatorName");
        applicantInfo1.setUserRole(0);
        applicantInfo1.setStoreId(0L);
        applicantInfo1.setMobile("mobile");
        orderExchangeRequest1.setApplicantInfo(applicantInfo1);
        orderExchangeRequest1.setChannel("value");
        orderExchangeRequest1.setDealerCode("dealerCode");
        when(mockOrderExchangeService.createExchangeOrder(orderPO2, orderExchangeDetailVO,
                orderExchangeRequest1)).thenReturn(orderPO1);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setGoodsName("goodsName");
        orderProductPO1.setProductImage("productImage");
        orderProductPO1.setSpecValues("specValues");
        orderProductPO1.setProductId(0L);
        orderProductPO1.setFinanceRuleCode("financeRuleCode");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO1.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO1.setReturnNumber(0);
        orderProductPO1.setChannelSkuUnit("channelSkuUnit");
        orderProductPO1.setChannelSkuId("channelSkuId");
        orderProductPO1.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO1);
        when(mockOrderProductModel.getOrderProductListByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure OrderExtendModel.getOrderExtendByOrderSn(...).
        final OrderExtendPO extendPO = new OrderExtendPO();
        extendPO.setOrderSn("orderSn");
        extendPO.setBranch("branch");
        extendPO.setReceiverProvinceCode("province");
        extendPO.setReceiverAddress("detailAddress");
        extendPO.setReceiverMobile("receiverMobile");
        extendPO.setReceiverCityCode("city");
        extendPO.setReceiverDistrictCode("district");
        extendPO.setReceiverTownCode("receiverTownCode");
        extendPO.setReceiverName("receiverName");
        extendPO.setReceiveBranchCode("receiveBranchCode");
        extendPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        extendPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        extendPO.setWarehouseCode("warehouseCode");
        when(mockOrderExtendModel.getOrderExtendByOrderSn("orderSn")).thenReturn(extendPO);

        // Configure OrderExchangeMapper.getOrderExchange(...).
        final OrderExchangeExample example1 = new OrderExchangeExample();
        example1.setExchangeOrderId(0);
        example1.setExchangeSn("exchangeSn");
        example1.setMemberId(0);
        example1.setStoreId(0L);
        example1.setExchangeReason("exchangeReason");
        when(mockOrderExchangeMapper.getOrderExchange(example1)).thenReturn(null);

        // Run the test
        final OrderExchangeDetailVO result = orderExchangeServiceImplUnderTest.applyExchange(orderExchangeRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockOrderExchangeService).saveExchangeApplyOrder("exchangeSn", 0L, 0,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"), 0, "exchangeReason");
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "operatorName", "orderSn", 0, 0, 0, "创建换货申请",
                OrderCreateChannel.H5);

        // Confirm IOrderExchangeService.saveExchangeOrderDetail(...).
        final OrderExchangeDetailVO orderExchangeDetailVO1 = new OrderExchangeDetailVO();
        orderExchangeDetailVO1.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo2 = new ExchangeOrderDTO();
        productInfo2.setOrderSn("orderSn");
        productInfo2.setOrderProductId(0L);
        productInfo2.setProductId(0L);
        productInfo2.setGoodsName("exchangeProductName");
        productInfo2.setProductImage("productImage");
        productInfo2.setGoodsId(0L);
        productInfo2.setSpecValues("specValues");
        productInfo2.setProductShowPrice(new BigDecimal("0.00"));
        productInfo2.setProductNum(0);
        productInfo2.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo2.setMoneyAmount(new BigDecimal("0.00"));
        productInfo2.setXzCardAmount(new BigDecimal("0.00"));
        productInfo2.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo2.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo2.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setProductInfo(productInfo2);
        final ExchangeOrderDTO newOrderInfo2 = new ExchangeOrderDTO();
        newOrderInfo2.setOrderSn("orderSn");
        newOrderInfo2.setOrderProductId(0L);
        newOrderInfo2.setProductId(0L);
        newOrderInfo2.setGoodsName("exchangeProductName");
        newOrderInfo2.setProductImage("productImage");
        newOrderInfo2.setGoodsId(0L);
        newOrderInfo2.setSpecValues("specValues");
        newOrderInfo2.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo2.setProductNum(0);
        newOrderInfo2.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo2.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo2.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo2.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo2.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo2.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setNewOrderInfo(newOrderInfo2);
        orderExchangeDetailVO1.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).saveExchangeOrderDetail(orderExchangeDetailVO1, "operatorName");
        verify(mockOrderExchangeService).updateOrderToExchange("orderSn", 0);

        // Confirm IOrderExchangeService.updateOrderAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO2 = new OrderExchangeDetailVO();
        orderExchangeDetailVO2.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo3 = new ExchangeOrderDTO();
        productInfo3.setOrderSn("orderSn");
        productInfo3.setOrderProductId(0L);
        productInfo3.setProductId(0L);
        productInfo3.setGoodsName("exchangeProductName");
        productInfo3.setProductImage("productImage");
        productInfo3.setGoodsId(0L);
        productInfo3.setSpecValues("specValues");
        productInfo3.setProductShowPrice(new BigDecimal("0.00"));
        productInfo3.setProductNum(0);
        productInfo3.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo3.setMoneyAmount(new BigDecimal("0.00"));
        productInfo3.setXzCardAmount(new BigDecimal("0.00"));
        productInfo3.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo3.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo3.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setProductInfo(productInfo3);
        final ExchangeOrderDTO newOrderInfo3 = new ExchangeOrderDTO();
        newOrderInfo3.setOrderSn("orderSn");
        newOrderInfo3.setOrderProductId(0L);
        newOrderInfo3.setProductId(0L);
        newOrderInfo3.setGoodsName("exchangeProductName");
        newOrderInfo3.setProductImage("productImage");
        newOrderInfo3.setGoodsId(0L);
        newOrderInfo3.setSpecValues("specValues");
        newOrderInfo3.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo3.setProductNum(0);
        newOrderInfo3.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo3.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo3.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo3.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo3.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo3.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setNewOrderInfo(newOrderInfo3);
        orderExchangeDetailVO2.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateOrderAmount("orderSn", orderExchangeDetailVO2);

        // Confirm IOrderExchangeService.updateOrderProductAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO3 = new OrderExchangeDetailVO();
        orderExchangeDetailVO3.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo4 = new ExchangeOrderDTO();
        productInfo4.setOrderSn("orderSn");
        productInfo4.setOrderProductId(0L);
        productInfo4.setProductId(0L);
        productInfo4.setGoodsName("exchangeProductName");
        productInfo4.setProductImage("productImage");
        productInfo4.setGoodsId(0L);
        productInfo4.setSpecValues("specValues");
        productInfo4.setProductShowPrice(new BigDecimal("0.00"));
        productInfo4.setProductNum(0);
        productInfo4.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo4.setMoneyAmount(new BigDecimal("0.00"));
        productInfo4.setXzCardAmount(new BigDecimal("0.00"));
        productInfo4.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo4.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo4.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setProductInfo(productInfo4);
        final ExchangeOrderDTO newOrderInfo4 = new ExchangeOrderDTO();
        newOrderInfo4.setOrderSn("orderSn");
        newOrderInfo4.setOrderProductId(0L);
        newOrderInfo4.setProductId(0L);
        newOrderInfo4.setGoodsName("exchangeProductName");
        newOrderInfo4.setProductImage("productImage");
        newOrderInfo4.setGoodsId(0L);
        newOrderInfo4.setSpecValues("specValues");
        newOrderInfo4.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo4.setProductNum(0);
        newOrderInfo4.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo4.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo4.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo4.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo4.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo4.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setNewOrderInfo(newOrderInfo4);
        orderExchangeDetailVO3.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateOrderProductAmount(orderExchangeDetailVO3);
        verify(mockOrderExchangeService).updatePayAmount("paySn", new BigDecimal("0.00"));

        // Confirm IOrderExchangeService.updateExtendAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO4 = new OrderExchangeDetailVO();
        orderExchangeDetailVO4.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo5 = new ExchangeOrderDTO();
        productInfo5.setOrderSn("orderSn");
        productInfo5.setOrderProductId(0L);
        productInfo5.setProductId(0L);
        productInfo5.setGoodsName("exchangeProductName");
        productInfo5.setProductImage("productImage");
        productInfo5.setGoodsId(0L);
        productInfo5.setSpecValues("specValues");
        productInfo5.setProductShowPrice(new BigDecimal("0.00"));
        productInfo5.setProductNum(0);
        productInfo5.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo5.setMoneyAmount(new BigDecimal("0.00"));
        productInfo5.setXzCardAmount(new BigDecimal("0.00"));
        productInfo5.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo5.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo5.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo5.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setProductInfo(productInfo5);
        final ExchangeOrderDTO newOrderInfo5 = new ExchangeOrderDTO();
        newOrderInfo5.setOrderSn("orderSn");
        newOrderInfo5.setOrderProductId(0L);
        newOrderInfo5.setProductId(0L);
        newOrderInfo5.setGoodsName("exchangeProductName");
        newOrderInfo5.setProductImage("productImage");
        newOrderInfo5.setGoodsId(0L);
        newOrderInfo5.setSpecValues("specValues");
        newOrderInfo5.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo5.setProductNum(0);
        newOrderInfo5.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo5.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo5.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo5.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo5.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo5.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo5.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setNewOrderInfo(newOrderInfo5);
        orderExchangeDetailVO4.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateExtendAmount("orderSn", orderExchangeDetailVO4);

        // Confirm GoodsStockService.goodsStock(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderSn("orderSn");
        orderPO3.setUserNo("userNo");
        orderPO3.setUserMobile("userMobile");
        orderPO3.setPaySn("paySn");
        orderPO3.setStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setOrderState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setPerformanceModes("performanceModes");
        orderPO3.setExchangeFlag(0);
        /*verify(mockGoodsStockService).goodsStock("orderSn", "exchangeSn", 0L, "areaCode", 0, "financeRuleCode",
                "batchNo", "channelSkuId", "branch", "receiveBranchCode", "warehouseCode",
                EventStockTypeEnum.ORDER_EXCHANGE_CREATE_IN_STOCK, BizTypeEnum.EXCHANGE_ORDER_INCREASE_STOCK,
                "operatorName", orderPO3, "channelSkuUnit", "userMobile");*/

        // Confirm IOrderExchangeService.exchangeOrderAuditDeal(...).
        final OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO.setExchangeAuditState(0);
        orderExchangeAuditDTO.setChannel("value");
        verify(mockOrderExchangeService).exchangeOrderAuditDeal(orderExchangeAuditDTO,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"));

        // Confirm RabbitTemplate.convertAndSend(...).
        final MessageSendVO object = new MessageSendVO();
        object.setReceiveId(0L);
        object.setTplType("tplType");
        object.setMsgLinkInfo("msgLinkInfo");
        final MessageSendProperty messageSendProperty = new MessageSendProperty();
        messageSendProperty.setPropertyName("first");
        messageSendProperty.setPropertyValue("memberName");
        object.setPropertyList(Arrays.asList(messageSendProperty));
        final MessageSendProperty messageSendProperty1 = new MessageSendProperty();
        messageSendProperty1.setPropertyName("first");
        messageSendProperty1.setPropertyValue("memberName");
        object.setWxPropertyList(Arrays.asList(messageSendProperty1));
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg", object);
    }

    @Test
    public void testApplyExchange_IOrderExchangeServiceExchangeOrderAuditDealThrowsException() throws Exception {
        // Setup
        final OrderExchangeRequest orderExchangeRequest = new OrderExchangeRequest();
        orderExchangeRequest.setOrderSn("orderSn");
        orderExchangeRequest.setOrderProductId(0L);
        orderExchangeRequest.setProductNum(0);
        orderExchangeRequest.setExchangeReason("exchangeReason");
        orderExchangeRequest.setBuyerConfirmFlag(0);
        orderExchangeRequest.setProductId(0L);
        orderExchangeRequest.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest.setIsTrial(false);
        final UserDTO applicantInfo = new UserDTO();
        applicantInfo.setUserId(0L);
        applicantInfo.setUserName("operatorName");
        applicantInfo.setUserRole(0);
        applicantInfo.setStoreId(0L);
        applicantInfo.setMobile("mobile");
        orderExchangeRequest.setApplicantInfo(applicantInfo);
        orderExchangeRequest.setChannel("value");
        orderExchangeRequest.setDealerCode("dealerCode");

        final OrderExchangeDetailVO expectedResult = new OrderExchangeDetailVO();
        expectedResult.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo = new ExchangeOrderDTO();
        productInfo.setOrderSn("orderSn");
        productInfo.setOrderProductId(0L);
        productInfo.setProductId(0L);
        productInfo.setGoodsName("exchangeProductName");
        productInfo.setProductImage("productImage");
        productInfo.setGoodsId(0L);
        productInfo.setSpecValues("specValues");
        productInfo.setProductShowPrice(new BigDecimal("0.00"));
        productInfo.setProductNum(0);
        productInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo.setMoneyAmount(new BigDecimal("0.00"));
        productInfo.setXzCardAmount(new BigDecimal("0.00"));
        productInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setProductInfo(productInfo);
        final ExchangeOrderDTO newOrderInfo = new ExchangeOrderDTO();
        newOrderInfo.setOrderSn("orderSn");
        newOrderInfo.setOrderProductId(0L);
        newOrderInfo.setProductId(0L);
        newOrderInfo.setGoodsName("exchangeProductName");
        newOrderInfo.setProductImage("productImage");
        newOrderInfo.setGoodsId(0L);
        newOrderInfo.setSpecValues("specValues");
        newOrderInfo.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo.setProductNum(0);
        newOrderInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setNewOrderInfo(newOrderInfo);
        expectedResult.setRefundAmount(new BigDecimal("0.00"));
        expectedResult.setXzCardAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformActivityAmount(new BigDecimal("0.00"));
        expectedResult.setStoreActivityAmount(new BigDecimal("0.00"));
        expectedResult.setStoreVoucherAmount(new BigDecimal("0.00"));

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserNo("userNo");
        orderPO.setUserMobile("userMobile");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setPerformanceModes("performanceModes");
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure OrderProductModel.getOrderProductByOrderProductId(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsName("goodsName");
        orderProductPO.setProductImage("productImage");
        orderProductPO.setSpecValues("specValues");
        orderProductPO.setProductId(0L);
        orderProductPO.setFinanceRuleCode("financeRuleCode");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        when(mockOrderProductModel.getOrderProductByOrderProductId(0L)).thenReturn(orderProductPO);

        // Configure OrderExchangeMapper.getExchangeOrderList(...).
        final ExchangeOrderDetailDTO exchangeOrderDetailDTO = new ExchangeOrderDetailDTO();
        exchangeOrderDetailDTO.setMemberId(0);
        exchangeOrderDetailDTO.setStoreId(0L);
        exchangeOrderDetailDTO.setOrderSn("orderSn");
        exchangeOrderDetailDTO.setOrderProductId(0L);
        exchangeOrderDetailDTO.setProductNum(0);
        exchangeOrderDetailDTO.setExchangeOrderSn("exchangeOrderSn");
        exchangeOrderDetailDTO.setExchangeOrderProductId(0L);
        exchangeOrderDetailDTO.setExchangeOrderState(0);
        exchangeOrderDetailDTO.setExchangeOrderStateList(Arrays.asList(0));
        exchangeOrderDetailDTO.setAfsSn("afsSn");
        final List<ExchangeOrderDetailDTO> exchangeOrderDetailDTOList = Arrays.asList(exchangeOrderDetailDTO);
        final ExchangeOrderDetailDTO example = new ExchangeOrderDetailDTO();
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setOrderSn("orderSn");
        example.setOrderProductId(0L);
        example.setProductNum(0);
        example.setExchangeOrderSn("exchangeOrderSn");
        example.setExchangeOrderProductId(0L);
        example.setExchangeOrderState(0);
        example.setExchangeOrderStateList(Arrays.asList(0));
        example.setAfsSn("afsSn");
        when(mockOrderExchangeMapper.getExchangeOrderList(example)).thenReturn(exchangeOrderDetailDTOList);

        // Configure OrderLocalUtils.getProductPrice(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setSpecValues("specValues");
        product.setProductPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setMainImage("productImage");
        productPriceVO.setProduct(product);
        final ProductPriceBranchRange productPriceBranchRange = new ProductPriceBranchRange();
        productPriceBranchRange.setTaxPrice(new BigDecimal("0.00"));
        productPriceVO.setProductPriceBranchRange(productPriceBranchRange);
        final Goods goods = new Goods();
        goods.setGoodsId(0L);
        goods.setGoodsName("exchangeProductName");
        productPriceVO.setGoods(goods);
        when(mockOrderLocalUtils.getProductPrice(0L, "areaCode", "financeRuleCode")).thenReturn(productPriceVO);

        when(mockShardingId.next("name", "prefix", "memberId",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0L);

        // Configure IOrderExchangeService.createExchangeOrder(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderSn("orderSn");
        orderPO1.setUserNo("userNo");
        orderPO1.setUserMobile("userMobile");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setOrderState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setPerformanceModes("performanceModes");
        orderPO1.setExchangeFlag(0);
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderSn("orderSn");
        orderPO2.setUserNo("userNo");
        orderPO2.setUserMobile("userMobile");
        orderPO2.setPaySn("paySn");
        orderPO2.setStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setOrderState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setPerformanceModes("performanceModes");
        orderPO2.setExchangeFlag(0);
        final OrderExchangeDetailVO orderExchangeDetailVO = new OrderExchangeDetailVO();
        orderExchangeDetailVO.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo1 = new ExchangeOrderDTO();
        productInfo1.setOrderSn("orderSn");
        productInfo1.setOrderProductId(0L);
        productInfo1.setProductId(0L);
        productInfo1.setGoodsName("exchangeProductName");
        productInfo1.setProductImage("productImage");
        productInfo1.setGoodsId(0L);
        productInfo1.setSpecValues("specValues");
        productInfo1.setProductShowPrice(new BigDecimal("0.00"));
        productInfo1.setProductNum(0);
        productInfo1.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo1.setMoneyAmount(new BigDecimal("0.00"));
        productInfo1.setXzCardAmount(new BigDecimal("0.00"));
        productInfo1.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo1.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo1.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setProductInfo(productInfo1);
        final ExchangeOrderDTO newOrderInfo1 = new ExchangeOrderDTO();
        newOrderInfo1.setOrderSn("orderSn");
        newOrderInfo1.setOrderProductId(0L);
        newOrderInfo1.setProductId(0L);
        newOrderInfo1.setGoodsName("exchangeProductName");
        newOrderInfo1.setProductImage("productImage");
        newOrderInfo1.setGoodsId(0L);
        newOrderInfo1.setSpecValues("specValues");
        newOrderInfo1.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo1.setProductNum(0);
        newOrderInfo1.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo1.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo1.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo1.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo1.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo1.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setNewOrderInfo(newOrderInfo1);
        orderExchangeDetailVO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreVoucherAmount(new BigDecimal("0.00"));
        final OrderExchangeRequest orderExchangeRequest1 = new OrderExchangeRequest();
        orderExchangeRequest1.setOrderSn("orderSn");
        orderExchangeRequest1.setOrderProductId(0L);
        orderExchangeRequest1.setProductNum(0);
        orderExchangeRequest1.setExchangeReason("exchangeReason");
        orderExchangeRequest1.setBuyerConfirmFlag(0);
        orderExchangeRequest1.setProductId(0L);
        orderExchangeRequest1.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest1.setIsTrial(false);
        final UserDTO applicantInfo1 = new UserDTO();
        applicantInfo1.setUserId(0L);
        applicantInfo1.setUserName("operatorName");
        applicantInfo1.setUserRole(0);
        applicantInfo1.setStoreId(0L);
        applicantInfo1.setMobile("mobile");
        orderExchangeRequest1.setApplicantInfo(applicantInfo1);
        orderExchangeRequest1.setChannel("value");
        orderExchangeRequest1.setDealerCode("dealerCode");
        when(mockOrderExchangeService.createExchangeOrder(orderPO2, orderExchangeDetailVO,
                orderExchangeRequest1)).thenReturn(orderPO1);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setGoodsName("goodsName");
        orderProductPO1.setProductImage("productImage");
        orderProductPO1.setSpecValues("specValues");
        orderProductPO1.setProductId(0L);
        orderProductPO1.setFinanceRuleCode("financeRuleCode");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO1.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO1.setReturnNumber(0);
        orderProductPO1.setChannelSkuUnit("channelSkuUnit");
        orderProductPO1.setChannelSkuId("channelSkuId");
        orderProductPO1.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO1);
        when(mockOrderProductModel.getOrderProductListByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure OrderExtendModel.getOrderExtendByOrderSn(...).
        final OrderExtendPO extendPO = new OrderExtendPO();
        extendPO.setOrderSn("orderSn");
        extendPO.setBranch("branch");
        extendPO.setReceiverProvinceCode("province");
        extendPO.setReceiverAddress("detailAddress");
        extendPO.setReceiverMobile("receiverMobile");
        extendPO.setReceiverCityCode("city");
        extendPO.setReceiverDistrictCode("district");
        extendPO.setReceiverTownCode("receiverTownCode");
        extendPO.setReceiverName("receiverName");
        extendPO.setReceiveBranchCode("receiveBranchCode");
        extendPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        extendPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        extendPO.setWarehouseCode("warehouseCode");
        when(mockOrderExtendModel.getOrderExtendByOrderSn("orderSn")).thenReturn(extendPO);

        // Configure OrderExchangeMapper.getOrderExchange(...).
        final OrderExchangePO orderExchangePO = new OrderExchangePO();
        orderExchangePO.setExchangeSn("exchangeSn");
        orderExchangePO.setMemberId(0);
        orderExchangePO.setStoreId(0L);
        orderExchangePO.setExchangeReason("exchangeReason");
        orderExchangePO.setBuyerConfirmFlag(0);
        orderExchangePO.setExchangeOrderState(0);
        orderExchangePO.setApplicantId(0L);
        orderExchangePO.setApplicantName("operatorName");
        orderExchangePO.setApplicantRole(0);
        orderExchangePO.setApproverId(0L);
        orderExchangePO.setApproverName("approverName");
        orderExchangePO.setApproverRole(0);
        orderExchangePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExchangePO.setCreateBy("operatorName");
        orderExchangePO.setUpdateBy("updateBy");
        final OrderExchangeExample example1 = new OrderExchangeExample();
        example1.setExchangeOrderId(0);
        example1.setExchangeSn("exchangeSn");
        example1.setMemberId(0);
        example1.setStoreId(0L);
        example1.setExchangeReason("exchangeReason");
        when(mockOrderExchangeMapper.getOrderExchange(example1)).thenReturn(orderExchangePO);

        // Configure IOrderExchangeService.exchangeOrderAuditDeal(...).
        final OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO.setExchangeAuditState(0);
        orderExchangeAuditDTO.setChannel("value");
        doThrow(Exception.class).when(mockOrderExchangeService).exchangeOrderAuditDeal(orderExchangeAuditDTO,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"));

        // Run the test
        final OrderExchangeDetailVO result = orderExchangeServiceImplUnderTest.applyExchange(orderExchangeRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockOrderExchangeService).saveExchangeApplyOrder("exchangeSn", 0L, 0,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"), 0, "exchangeReason");
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "operatorName", "orderSn", 0, 0, 0, "创建换货申请",
                OrderCreateChannel.H5);

        // Confirm IOrderExchangeService.saveExchangeOrderDetail(...).
        final OrderExchangeDetailVO orderExchangeDetailVO1 = new OrderExchangeDetailVO();
        orderExchangeDetailVO1.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo2 = new ExchangeOrderDTO();
        productInfo2.setOrderSn("orderSn");
        productInfo2.setOrderProductId(0L);
        productInfo2.setProductId(0L);
        productInfo2.setGoodsName("exchangeProductName");
        productInfo2.setProductImage("productImage");
        productInfo2.setGoodsId(0L);
        productInfo2.setSpecValues("specValues");
        productInfo2.setProductShowPrice(new BigDecimal("0.00"));
        productInfo2.setProductNum(0);
        productInfo2.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo2.setMoneyAmount(new BigDecimal("0.00"));
        productInfo2.setXzCardAmount(new BigDecimal("0.00"));
        productInfo2.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo2.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo2.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setProductInfo(productInfo2);
        final ExchangeOrderDTO newOrderInfo2 = new ExchangeOrderDTO();
        newOrderInfo2.setOrderSn("orderSn");
        newOrderInfo2.setOrderProductId(0L);
        newOrderInfo2.setProductId(0L);
        newOrderInfo2.setGoodsName("exchangeProductName");
        newOrderInfo2.setProductImage("productImage");
        newOrderInfo2.setGoodsId(0L);
        newOrderInfo2.setSpecValues("specValues");
        newOrderInfo2.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo2.setProductNum(0);
        newOrderInfo2.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo2.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo2.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo2.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo2.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo2.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setNewOrderInfo(newOrderInfo2);
        orderExchangeDetailVO1.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).saveExchangeOrderDetail(orderExchangeDetailVO1, "operatorName");
        verify(mockOrderExchangeService).updateOrderToExchange("orderSn", 0);

        // Confirm IOrderExchangeService.updateOrderAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO2 = new OrderExchangeDetailVO();
        orderExchangeDetailVO2.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo3 = new ExchangeOrderDTO();
        productInfo3.setOrderSn("orderSn");
        productInfo3.setOrderProductId(0L);
        productInfo3.setProductId(0L);
        productInfo3.setGoodsName("exchangeProductName");
        productInfo3.setProductImage("productImage");
        productInfo3.setGoodsId(0L);
        productInfo3.setSpecValues("specValues");
        productInfo3.setProductShowPrice(new BigDecimal("0.00"));
        productInfo3.setProductNum(0);
        productInfo3.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo3.setMoneyAmount(new BigDecimal("0.00"));
        productInfo3.setXzCardAmount(new BigDecimal("0.00"));
        productInfo3.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo3.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo3.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setProductInfo(productInfo3);
        final ExchangeOrderDTO newOrderInfo3 = new ExchangeOrderDTO();
        newOrderInfo3.setOrderSn("orderSn");
        newOrderInfo3.setOrderProductId(0L);
        newOrderInfo3.setProductId(0L);
        newOrderInfo3.setGoodsName("exchangeProductName");
        newOrderInfo3.setProductImage("productImage");
        newOrderInfo3.setGoodsId(0L);
        newOrderInfo3.setSpecValues("specValues");
        newOrderInfo3.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo3.setProductNum(0);
        newOrderInfo3.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo3.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo3.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo3.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo3.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo3.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setNewOrderInfo(newOrderInfo3);
        orderExchangeDetailVO2.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateOrderAmount("orderSn", orderExchangeDetailVO2);

        // Confirm IOrderExchangeService.updateOrderProductAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO3 = new OrderExchangeDetailVO();
        orderExchangeDetailVO3.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo4 = new ExchangeOrderDTO();
        productInfo4.setOrderSn("orderSn");
        productInfo4.setOrderProductId(0L);
        productInfo4.setProductId(0L);
        productInfo4.setGoodsName("exchangeProductName");
        productInfo4.setProductImage("productImage");
        productInfo4.setGoodsId(0L);
        productInfo4.setSpecValues("specValues");
        productInfo4.setProductShowPrice(new BigDecimal("0.00"));
        productInfo4.setProductNum(0);
        productInfo4.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo4.setMoneyAmount(new BigDecimal("0.00"));
        productInfo4.setXzCardAmount(new BigDecimal("0.00"));
        productInfo4.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo4.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo4.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setProductInfo(productInfo4);
        final ExchangeOrderDTO newOrderInfo4 = new ExchangeOrderDTO();
        newOrderInfo4.setOrderSn("orderSn");
        newOrderInfo4.setOrderProductId(0L);
        newOrderInfo4.setProductId(0L);
        newOrderInfo4.setGoodsName("exchangeProductName");
        newOrderInfo4.setProductImage("productImage");
        newOrderInfo4.setGoodsId(0L);
        newOrderInfo4.setSpecValues("specValues");
        newOrderInfo4.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo4.setProductNum(0);
        newOrderInfo4.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo4.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo4.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo4.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo4.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo4.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setNewOrderInfo(newOrderInfo4);
        orderExchangeDetailVO3.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateOrderProductAmount(orderExchangeDetailVO3);
        verify(mockOrderExchangeService).updatePayAmount("paySn", new BigDecimal("0.00"));

        // Confirm IOrderExchangeService.updateExtendAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO4 = new OrderExchangeDetailVO();
        orderExchangeDetailVO4.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo5 = new ExchangeOrderDTO();
        productInfo5.setOrderSn("orderSn");
        productInfo5.setOrderProductId(0L);
        productInfo5.setProductId(0L);
        productInfo5.setGoodsName("exchangeProductName");
        productInfo5.setProductImage("productImage");
        productInfo5.setGoodsId(0L);
        productInfo5.setSpecValues("specValues");
        productInfo5.setProductShowPrice(new BigDecimal("0.00"));
        productInfo5.setProductNum(0);
        productInfo5.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo5.setMoneyAmount(new BigDecimal("0.00"));
        productInfo5.setXzCardAmount(new BigDecimal("0.00"));
        productInfo5.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo5.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo5.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo5.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setProductInfo(productInfo5);
        final ExchangeOrderDTO newOrderInfo5 = new ExchangeOrderDTO();
        newOrderInfo5.setOrderSn("orderSn");
        newOrderInfo5.setOrderProductId(0L);
        newOrderInfo5.setProductId(0L);
        newOrderInfo5.setGoodsName("exchangeProductName");
        newOrderInfo5.setProductImage("productImage");
        newOrderInfo5.setGoodsId(0L);
        newOrderInfo5.setSpecValues("specValues");
        newOrderInfo5.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo5.setProductNum(0);
        newOrderInfo5.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo5.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo5.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo5.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo5.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo5.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo5.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setNewOrderInfo(newOrderInfo5);
        orderExchangeDetailVO4.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateExtendAmount("orderSn", orderExchangeDetailVO4);

        // Confirm GoodsStockService.goodsStock(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderSn("orderSn");
        orderPO3.setUserNo("userNo");
        orderPO3.setUserMobile("userMobile");
        orderPO3.setPaySn("paySn");
        orderPO3.setStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setOrderState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setPerformanceModes("performanceModes");
        orderPO3.setExchangeFlag(0);
        /*verify(mockGoodsStockService).goodsStock("orderSn", "exchangeSn", 0L, "areaCode", 0, "financeRuleCode",
                "batchNo", "channelSkuId", "branch", "receiveBranchCode", "warehouseCode",
                EventStockTypeEnum.ORDER_EXCHANGE_CREATE_IN_STOCK, BizTypeEnum.EXCHANGE_ORDER_INCREASE_STOCK,
                "operatorName", orderPO3, "channelSkuUnit", "userMobile");*/

        // Confirm RabbitTemplate.convertAndSend(...).
        final MessageSendVO object = new MessageSendVO();
        object.setReceiveId(0L);
        object.setTplType("tplType");
        object.setMsgLinkInfo("msgLinkInfo");
        final MessageSendProperty messageSendProperty = new MessageSendProperty();
        messageSendProperty.setPropertyName("first");
        messageSendProperty.setPropertyValue("memberName");
        object.setPropertyList(Arrays.asList(messageSendProperty));
        final MessageSendProperty messageSendProperty1 = new MessageSendProperty();
        messageSendProperty1.setPropertyName("first");
        messageSendProperty1.setPropertyValue("memberName");
        object.setWxPropertyList(Arrays.asList(messageSendProperty1));
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg", object);
    }

    @Test
    public void testApplyExchange_RabbitTemplateThrowsAmqpException() throws Exception {
        // Setup
        final OrderExchangeRequest orderExchangeRequest = new OrderExchangeRequest();
        orderExchangeRequest.setOrderSn("orderSn");
        orderExchangeRequest.setOrderProductId(0L);
        orderExchangeRequest.setProductNum(0);
        orderExchangeRequest.setExchangeReason("exchangeReason");
        orderExchangeRequest.setBuyerConfirmFlag(0);
        orderExchangeRequest.setProductId(0L);
        orderExchangeRequest.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest.setIsTrial(false);
        final UserDTO applicantInfo = new UserDTO();
        applicantInfo.setUserId(0L);
        applicantInfo.setUserName("operatorName");
        applicantInfo.setUserRole(0);
        applicantInfo.setStoreId(0L);
        applicantInfo.setMobile("mobile");
        orderExchangeRequest.setApplicantInfo(applicantInfo);
        orderExchangeRequest.setChannel("value");
        orderExchangeRequest.setDealerCode("dealerCode");

        final OrderExchangeDetailVO expectedResult = new OrderExchangeDetailVO();
        expectedResult.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo = new ExchangeOrderDTO();
        productInfo.setOrderSn("orderSn");
        productInfo.setOrderProductId(0L);
        productInfo.setProductId(0L);
        productInfo.setGoodsName("exchangeProductName");
        productInfo.setProductImage("productImage");
        productInfo.setGoodsId(0L);
        productInfo.setSpecValues("specValues");
        productInfo.setProductShowPrice(new BigDecimal("0.00"));
        productInfo.setProductNum(0);
        productInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo.setMoneyAmount(new BigDecimal("0.00"));
        productInfo.setXzCardAmount(new BigDecimal("0.00"));
        productInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setProductInfo(productInfo);
        final ExchangeOrderDTO newOrderInfo = new ExchangeOrderDTO();
        newOrderInfo.setOrderSn("orderSn");
        newOrderInfo.setOrderProductId(0L);
        newOrderInfo.setProductId(0L);
        newOrderInfo.setGoodsName("exchangeProductName");
        newOrderInfo.setProductImage("productImage");
        newOrderInfo.setGoodsId(0L);
        newOrderInfo.setSpecValues("specValues");
        newOrderInfo.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo.setProductNum(0);
        newOrderInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setNewOrderInfo(newOrderInfo);
        expectedResult.setRefundAmount(new BigDecimal("0.00"));
        expectedResult.setXzCardAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformActivityAmount(new BigDecimal("0.00"));
        expectedResult.setStoreActivityAmount(new BigDecimal("0.00"));
        expectedResult.setStoreVoucherAmount(new BigDecimal("0.00"));

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserNo("userNo");
        orderPO.setUserMobile("userMobile");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setPerformanceModes("performanceModes");
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure OrderProductModel.getOrderProductByOrderProductId(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsName("goodsName");
        orderProductPO.setProductImage("productImage");
        orderProductPO.setSpecValues("specValues");
        orderProductPO.setProductId(0L);
        orderProductPO.setFinanceRuleCode("financeRuleCode");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        when(mockOrderProductModel.getOrderProductByOrderProductId(0L)).thenReturn(orderProductPO);

        // Configure OrderExchangeMapper.getExchangeOrderList(...).
        final ExchangeOrderDetailDTO exchangeOrderDetailDTO = new ExchangeOrderDetailDTO();
        exchangeOrderDetailDTO.setMemberId(0);
        exchangeOrderDetailDTO.setStoreId(0L);
        exchangeOrderDetailDTO.setOrderSn("orderSn");
        exchangeOrderDetailDTO.setOrderProductId(0L);
        exchangeOrderDetailDTO.setProductNum(0);
        exchangeOrderDetailDTO.setExchangeOrderSn("exchangeOrderSn");
        exchangeOrderDetailDTO.setExchangeOrderProductId(0L);
        exchangeOrderDetailDTO.setExchangeOrderState(0);
        exchangeOrderDetailDTO.setExchangeOrderStateList(Arrays.asList(0));
        exchangeOrderDetailDTO.setAfsSn("afsSn");
        final List<ExchangeOrderDetailDTO> exchangeOrderDetailDTOList = Arrays.asList(exchangeOrderDetailDTO);
        final ExchangeOrderDetailDTO example = new ExchangeOrderDetailDTO();
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setOrderSn("orderSn");
        example.setOrderProductId(0L);
        example.setProductNum(0);
        example.setExchangeOrderSn("exchangeOrderSn");
        example.setExchangeOrderProductId(0L);
        example.setExchangeOrderState(0);
        example.setExchangeOrderStateList(Arrays.asList(0));
        example.setAfsSn("afsSn");
        when(mockOrderExchangeMapper.getExchangeOrderList(example)).thenReturn(exchangeOrderDetailDTOList);

        // Configure OrderLocalUtils.getProductPrice(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setSpecValues("specValues");
        product.setProductPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setMainImage("productImage");
        productPriceVO.setProduct(product);
        final ProductPriceBranchRange productPriceBranchRange = new ProductPriceBranchRange();
        productPriceBranchRange.setTaxPrice(new BigDecimal("0.00"));
        productPriceVO.setProductPriceBranchRange(productPriceBranchRange);
        final Goods goods = new Goods();
        goods.setGoodsId(0L);
        goods.setGoodsName("exchangeProductName");
        productPriceVO.setGoods(goods);
        when(mockOrderLocalUtils.getProductPrice(0L, "areaCode", "financeRuleCode")).thenReturn(productPriceVO);

        when(mockShardingId.next("name", "prefix", "memberId",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0L);

        // Configure IOrderExchangeService.createExchangeOrder(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderSn("orderSn");
        orderPO1.setUserNo("userNo");
        orderPO1.setUserMobile("userMobile");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setOrderState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setPerformanceModes("performanceModes");
        orderPO1.setExchangeFlag(0);
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderSn("orderSn");
        orderPO2.setUserNo("userNo");
        orderPO2.setUserMobile("userMobile");
        orderPO2.setPaySn("paySn");
        orderPO2.setStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setOrderState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setPerformanceModes("performanceModes");
        orderPO2.setExchangeFlag(0);
        final OrderExchangeDetailVO orderExchangeDetailVO = new OrderExchangeDetailVO();
        orderExchangeDetailVO.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo1 = new ExchangeOrderDTO();
        productInfo1.setOrderSn("orderSn");
        productInfo1.setOrderProductId(0L);
        productInfo1.setProductId(0L);
        productInfo1.setGoodsName("exchangeProductName");
        productInfo1.setProductImage("productImage");
        productInfo1.setGoodsId(0L);
        productInfo1.setSpecValues("specValues");
        productInfo1.setProductShowPrice(new BigDecimal("0.00"));
        productInfo1.setProductNum(0);
        productInfo1.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo1.setMoneyAmount(new BigDecimal("0.00"));
        productInfo1.setXzCardAmount(new BigDecimal("0.00"));
        productInfo1.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo1.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo1.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setProductInfo(productInfo1);
        final ExchangeOrderDTO newOrderInfo1 = new ExchangeOrderDTO();
        newOrderInfo1.setOrderSn("orderSn");
        newOrderInfo1.setOrderProductId(0L);
        newOrderInfo1.setProductId(0L);
        newOrderInfo1.setGoodsName("exchangeProductName");
        newOrderInfo1.setProductImage("productImage");
        newOrderInfo1.setGoodsId(0L);
        newOrderInfo1.setSpecValues("specValues");
        newOrderInfo1.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo1.setProductNum(0);
        newOrderInfo1.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo1.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo1.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo1.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo1.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo1.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setNewOrderInfo(newOrderInfo1);
        orderExchangeDetailVO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreVoucherAmount(new BigDecimal("0.00"));
        final OrderExchangeRequest orderExchangeRequest1 = new OrderExchangeRequest();
        orderExchangeRequest1.setOrderSn("orderSn");
        orderExchangeRequest1.setOrderProductId(0L);
        orderExchangeRequest1.setProductNum(0);
        orderExchangeRequest1.setExchangeReason("exchangeReason");
        orderExchangeRequest1.setBuyerConfirmFlag(0);
        orderExchangeRequest1.setProductId(0L);
        orderExchangeRequest1.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest1.setIsTrial(false);
        final UserDTO applicantInfo1 = new UserDTO();
        applicantInfo1.setUserId(0L);
        applicantInfo1.setUserName("operatorName");
        applicantInfo1.setUserRole(0);
        applicantInfo1.setStoreId(0L);
        applicantInfo1.setMobile("mobile");
        orderExchangeRequest1.setApplicantInfo(applicantInfo1);
        orderExchangeRequest1.setChannel("value");
        orderExchangeRequest1.setDealerCode("dealerCode");
        when(mockOrderExchangeService.createExchangeOrder(orderPO2, orderExchangeDetailVO,
                orderExchangeRequest1)).thenReturn(orderPO1);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setGoodsName("goodsName");
        orderProductPO1.setProductImage("productImage");
        orderProductPO1.setSpecValues("specValues");
        orderProductPO1.setProductId(0L);
        orderProductPO1.setFinanceRuleCode("financeRuleCode");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO1.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO1.setReturnNumber(0);
        orderProductPO1.setChannelSkuUnit("channelSkuUnit");
        orderProductPO1.setChannelSkuId("channelSkuId");
        orderProductPO1.setBatchNo("batchNo");
        final List<OrderProductPO> orderProductPOList = Arrays.asList(orderProductPO1);
        when(mockOrderProductModel.getOrderProductListByOrderSn("orderSn")).thenReturn(orderProductPOList);

        // Configure OrderExtendModel.getOrderExtendByOrderSn(...).
        final OrderExtendPO extendPO = new OrderExtendPO();
        extendPO.setOrderSn("orderSn");
        extendPO.setBranch("branch");
        extendPO.setReceiverProvinceCode("province");
        extendPO.setReceiverAddress("detailAddress");
        extendPO.setReceiverMobile("receiverMobile");
        extendPO.setReceiverCityCode("city");
        extendPO.setReceiverDistrictCode("district");
        extendPO.setReceiverTownCode("receiverTownCode");
        extendPO.setReceiverName("receiverName");
        extendPO.setReceiveBranchCode("receiveBranchCode");
        extendPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        extendPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        extendPO.setWarehouseCode("warehouseCode");
        when(mockOrderExtendModel.getOrderExtendByOrderSn("orderSn")).thenReturn(extendPO);

        // Configure OrderExchangeMapper.getOrderExchange(...).
        final OrderExchangePO orderExchangePO = new OrderExchangePO();
        orderExchangePO.setExchangeSn("exchangeSn");
        orderExchangePO.setMemberId(0);
        orderExchangePO.setStoreId(0L);
        orderExchangePO.setExchangeReason("exchangeReason");
        orderExchangePO.setBuyerConfirmFlag(0);
        orderExchangePO.setExchangeOrderState(0);
        orderExchangePO.setApplicantId(0L);
        orderExchangePO.setApplicantName("operatorName");
        orderExchangePO.setApplicantRole(0);
        orderExchangePO.setApproverId(0L);
        orderExchangePO.setApproverName("approverName");
        orderExchangePO.setApproverRole(0);
        orderExchangePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExchangePO.setCreateBy("operatorName");
        orderExchangePO.setUpdateBy("updateBy");
        final OrderExchangeExample example1 = new OrderExchangeExample();
        example1.setExchangeOrderId(0);
        example1.setExchangeSn("exchangeSn");
        example1.setMemberId(0);
        example1.setStoreId(0L);
        example1.setExchangeReason("exchangeReason");
        when(mockOrderExchangeMapper.getOrderExchange(example1)).thenReturn(orderExchangePO);

        // Configure RabbitTemplate.convertAndSend(...).
        final MessageSendVO object = new MessageSendVO();
        object.setReceiveId(0L);
        object.setTplType("tplType");
        object.setMsgLinkInfo("msgLinkInfo");
        final MessageSendProperty messageSendProperty = new MessageSendProperty();
        messageSendProperty.setPropertyName("first");
        messageSendProperty.setPropertyValue("memberName");
        object.setPropertyList(Arrays.asList(messageSendProperty));
        final MessageSendProperty messageSendProperty1 = new MessageSendProperty();
        messageSendProperty1.setPropertyName("first");
        messageSendProperty1.setPropertyValue("memberName");
        object.setWxPropertyList(Arrays.asList(messageSendProperty1));
        doThrow(AmqpException.class).when(mockRabbitTemplate).convertAndSend("newmall_exchange",
                "newmall_queue_member_msg", object);

        // Run the test
        final OrderExchangeDetailVO result = orderExchangeServiceImplUnderTest.applyExchange(orderExchangeRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockOrderExchangeService).saveExchangeApplyOrder("exchangeSn", 0L, 0,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"), 0, "exchangeReason");
        verify(mockOrderLogModel).insertOrderLog(2, 0L, "operatorName", "orderSn", 0, 0, 0, "创建换货申请",
                OrderCreateChannel.H5);

        // Confirm IOrderExchangeService.saveExchangeOrderDetail(...).
        final OrderExchangeDetailVO orderExchangeDetailVO1 = new OrderExchangeDetailVO();
        orderExchangeDetailVO1.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo2 = new ExchangeOrderDTO();
        productInfo2.setOrderSn("orderSn");
        productInfo2.setOrderProductId(0L);
        productInfo2.setProductId(0L);
        productInfo2.setGoodsName("exchangeProductName");
        productInfo2.setProductImage("productImage");
        productInfo2.setGoodsId(0L);
        productInfo2.setSpecValues("specValues");
        productInfo2.setProductShowPrice(new BigDecimal("0.00"));
        productInfo2.setProductNum(0);
        productInfo2.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo2.setMoneyAmount(new BigDecimal("0.00"));
        productInfo2.setXzCardAmount(new BigDecimal("0.00"));
        productInfo2.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo2.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo2.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setProductInfo(productInfo2);
        final ExchangeOrderDTO newOrderInfo2 = new ExchangeOrderDTO();
        newOrderInfo2.setOrderSn("orderSn");
        newOrderInfo2.setOrderProductId(0L);
        newOrderInfo2.setProductId(0L);
        newOrderInfo2.setGoodsName("exchangeProductName");
        newOrderInfo2.setProductImage("productImage");
        newOrderInfo2.setGoodsId(0L);
        newOrderInfo2.setSpecValues("specValues");
        newOrderInfo2.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo2.setProductNum(0);
        newOrderInfo2.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo2.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo2.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo2.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo2.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo2.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setNewOrderInfo(newOrderInfo2);
        orderExchangeDetailVO1.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).saveExchangeOrderDetail(orderExchangeDetailVO1, "operatorName");
        verify(mockOrderExchangeService).updateOrderToExchange("orderSn", 0);

        // Confirm IOrderExchangeService.updateOrderAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO2 = new OrderExchangeDetailVO();
        orderExchangeDetailVO2.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo3 = new ExchangeOrderDTO();
        productInfo3.setOrderSn("orderSn");
        productInfo3.setOrderProductId(0L);
        productInfo3.setProductId(0L);
        productInfo3.setGoodsName("exchangeProductName");
        productInfo3.setProductImage("productImage");
        productInfo3.setGoodsId(0L);
        productInfo3.setSpecValues("specValues");
        productInfo3.setProductShowPrice(new BigDecimal("0.00"));
        productInfo3.setProductNum(0);
        productInfo3.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo3.setMoneyAmount(new BigDecimal("0.00"));
        productInfo3.setXzCardAmount(new BigDecimal("0.00"));
        productInfo3.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo3.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo3.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setProductInfo(productInfo3);
        final ExchangeOrderDTO newOrderInfo3 = new ExchangeOrderDTO();
        newOrderInfo3.setOrderSn("orderSn");
        newOrderInfo3.setOrderProductId(0L);
        newOrderInfo3.setProductId(0L);
        newOrderInfo3.setGoodsName("exchangeProductName");
        newOrderInfo3.setProductImage("productImage");
        newOrderInfo3.setGoodsId(0L);
        newOrderInfo3.setSpecValues("specValues");
        newOrderInfo3.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo3.setProductNum(0);
        newOrderInfo3.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo3.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo3.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo3.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo3.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo3.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setNewOrderInfo(newOrderInfo3);
        orderExchangeDetailVO2.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateOrderAmount("orderSn", orderExchangeDetailVO2);

        // Confirm IOrderExchangeService.updateOrderProductAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO3 = new OrderExchangeDetailVO();
        orderExchangeDetailVO3.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo4 = new ExchangeOrderDTO();
        productInfo4.setOrderSn("orderSn");
        productInfo4.setOrderProductId(0L);
        productInfo4.setProductId(0L);
        productInfo4.setGoodsName("exchangeProductName");
        productInfo4.setProductImage("productImage");
        productInfo4.setGoodsId(0L);
        productInfo4.setSpecValues("specValues");
        productInfo4.setProductShowPrice(new BigDecimal("0.00"));
        productInfo4.setProductNum(0);
        productInfo4.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo4.setMoneyAmount(new BigDecimal("0.00"));
        productInfo4.setXzCardAmount(new BigDecimal("0.00"));
        productInfo4.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo4.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo4.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setProductInfo(productInfo4);
        final ExchangeOrderDTO newOrderInfo4 = new ExchangeOrderDTO();
        newOrderInfo4.setOrderSn("orderSn");
        newOrderInfo4.setOrderProductId(0L);
        newOrderInfo4.setProductId(0L);
        newOrderInfo4.setGoodsName("exchangeProductName");
        newOrderInfo4.setProductImage("productImage");
        newOrderInfo4.setGoodsId(0L);
        newOrderInfo4.setSpecValues("specValues");
        newOrderInfo4.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo4.setProductNum(0);
        newOrderInfo4.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo4.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo4.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo4.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo4.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo4.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setNewOrderInfo(newOrderInfo4);
        orderExchangeDetailVO3.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateOrderProductAmount(orderExchangeDetailVO3);
        verify(mockOrderExchangeService).updatePayAmount("paySn", new BigDecimal("0.00"));

        // Confirm IOrderExchangeService.updateExtendAmount(...).
        final OrderExchangeDetailVO orderExchangeDetailVO4 = new OrderExchangeDetailVO();
        orderExchangeDetailVO4.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo5 = new ExchangeOrderDTO();
        productInfo5.setOrderSn("orderSn");
        productInfo5.setOrderProductId(0L);
        productInfo5.setProductId(0L);
        productInfo5.setGoodsName("exchangeProductName");
        productInfo5.setProductImage("productImage");
        productInfo5.setGoodsId(0L);
        productInfo5.setSpecValues("specValues");
        productInfo5.setProductShowPrice(new BigDecimal("0.00"));
        productInfo5.setProductNum(0);
        productInfo5.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo5.setMoneyAmount(new BigDecimal("0.00"));
        productInfo5.setXzCardAmount(new BigDecimal("0.00"));
        productInfo5.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo5.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo5.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo5.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setProductInfo(productInfo5);
        final ExchangeOrderDTO newOrderInfo5 = new ExchangeOrderDTO();
        newOrderInfo5.setOrderSn("orderSn");
        newOrderInfo5.setOrderProductId(0L);
        newOrderInfo5.setProductId(0L);
        newOrderInfo5.setGoodsName("exchangeProductName");
        newOrderInfo5.setProductImage("productImage");
        newOrderInfo5.setGoodsId(0L);
        newOrderInfo5.setSpecValues("specValues");
        newOrderInfo5.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo5.setProductNum(0);
        newOrderInfo5.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo5.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo5.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo5.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo5.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo5.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo5.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setNewOrderInfo(newOrderInfo5);
        orderExchangeDetailVO4.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        verify(mockOrderExchangeService).updateExtendAmount("orderSn", orderExchangeDetailVO4);

        // Confirm GoodsStockService.goodsStock(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderSn("orderSn");
        orderPO3.setUserNo("userNo");
        orderPO3.setUserMobile("userMobile");
        orderPO3.setPaySn("paySn");
        orderPO3.setStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setOrderState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setPerformanceModes("performanceModes");
        orderPO3.setExchangeFlag(0);
        /*verify(mockGoodsStockService).goodsStock("orderSn", "exchangeSn", 0L, "areaCode", 0, "financeRuleCode",
                "batchNo", "channelSkuId", "branch", "receiveBranchCode", "warehouseCode",
                EventStockTypeEnum.ORDER_EXCHANGE_CREATE_IN_STOCK, BizTypeEnum.EXCHANGE_ORDER_INCREASE_STOCK,
                "operatorName", orderPO3, "channelSkuUnit", "userMobile");*/

        // Confirm IOrderExchangeService.exchangeOrderAuditDeal(...).
        final OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO.setExchangeAuditState(0);
        orderExchangeAuditDTO.setChannel("value");
        verify(mockOrderExchangeService).exchangeOrderAuditDeal(orderExchangeAuditDTO,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"));
    }

    @Test
    public void testUpdateOrderAmount() {
        // Setup
        final OrderExchangeDetailVO orderExchangeDetailVO = new OrderExchangeDetailVO();
        orderExchangeDetailVO.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo = new ExchangeOrderDTO();
        productInfo.setOrderSn("orderSn");
        productInfo.setOrderProductId(0L);
        productInfo.setProductId(0L);
        productInfo.setGoodsName("exchangeProductName");
        productInfo.setProductImage("productImage");
        productInfo.setGoodsId(0L);
        productInfo.setSpecValues("specValues");
        productInfo.setProductShowPrice(new BigDecimal("0.00"));
        productInfo.setProductNum(0);
        productInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo.setMoneyAmount(new BigDecimal("0.00"));
        productInfo.setXzCardAmount(new BigDecimal("0.00"));
        productInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setProductInfo(productInfo);
        final ExchangeOrderDTO newOrderInfo = new ExchangeOrderDTO();
        newOrderInfo.setOrderSn("orderSn");
        newOrderInfo.setOrderProductId(0L);
        newOrderInfo.setProductId(0L);
        newOrderInfo.setGoodsName("exchangeProductName");
        newOrderInfo.setProductImage("productImage");
        newOrderInfo.setGoodsId(0L);
        newOrderInfo.setSpecValues("specValues");
        newOrderInfo.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo.setProductNum(0);
        newOrderInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setNewOrderInfo(newOrderInfo);
        orderExchangeDetailVO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreVoucherAmount(new BigDecimal("0.00"));

        // Run the test
        orderExchangeServiceImplUnderTest.updateOrderAmount("orderSn", orderExchangeDetailVO);

        // Verify the results
        verify(mockOrderService).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    public void testUpdateOrderProductAmount() {
        // Setup
        final OrderExchangeDetailVO orderExchangeDetailVO = new OrderExchangeDetailVO();
        orderExchangeDetailVO.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo = new ExchangeOrderDTO();
        productInfo.setOrderSn("orderSn");
        productInfo.setOrderProductId(0L);
        productInfo.setProductId(0L);
        productInfo.setGoodsName("exchangeProductName");
        productInfo.setProductImage("productImage");
        productInfo.setGoodsId(0L);
        productInfo.setSpecValues("specValues");
        productInfo.setProductShowPrice(new BigDecimal("0.00"));
        productInfo.setProductNum(0);
        productInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo.setMoneyAmount(new BigDecimal("0.00"));
        productInfo.setXzCardAmount(new BigDecimal("0.00"));
        productInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setProductInfo(productInfo);
        final ExchangeOrderDTO newOrderInfo = new ExchangeOrderDTO();
        newOrderInfo.setOrderSn("orderSn");
        newOrderInfo.setOrderProductId(0L);
        newOrderInfo.setProductId(0L);
        newOrderInfo.setGoodsName("exchangeProductName");
        newOrderInfo.setProductImage("productImage");
        newOrderInfo.setGoodsId(0L);
        newOrderInfo.setSpecValues("specValues");
        newOrderInfo.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo.setProductNum(0);
        newOrderInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setNewOrderInfo(newOrderInfo);
        orderExchangeDetailVO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreVoucherAmount(new BigDecimal("0.00"));

        // Run the test
        orderExchangeServiceImplUnderTest.updateOrderProductAmount(orderExchangeDetailVO);

        // Verify the results
        verify(mockOrderProductService).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    public void testUpdatePayAmount() {
        // Setup
        // Configure IOrderPayService.getByPaySn(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("orderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        when(mockOrderPayService.getByPaySn("paySn")).thenReturn(orderPayPO);

        // Run the test
        orderExchangeServiceImplUnderTest.updatePayAmount("paySn", new BigDecimal("0.00"));

        // Verify the results
        verify(mockOrderPayService).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    public void testUpdateExtendAmount() {
        // Setup
        final OrderExchangeDetailVO orderExchangeDetailVO = new OrderExchangeDetailVO();
        orderExchangeDetailVO.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo = new ExchangeOrderDTO();
        productInfo.setOrderSn("orderSn");
        productInfo.setOrderProductId(0L);
        productInfo.setProductId(0L);
        productInfo.setGoodsName("exchangeProductName");
        productInfo.setProductImage("productImage");
        productInfo.setGoodsId(0L);
        productInfo.setSpecValues("specValues");
        productInfo.setProductShowPrice(new BigDecimal("0.00"));
        productInfo.setProductNum(0);
        productInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo.setMoneyAmount(new BigDecimal("0.00"));
        productInfo.setXzCardAmount(new BigDecimal("0.00"));
        productInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setProductInfo(productInfo);
        final ExchangeOrderDTO newOrderInfo = new ExchangeOrderDTO();
        newOrderInfo.setOrderSn("orderSn");
        newOrderInfo.setOrderProductId(0L);
        newOrderInfo.setProductId(0L);
        newOrderInfo.setGoodsName("exchangeProductName");
        newOrderInfo.setProductImage("productImage");
        newOrderInfo.setGoodsId(0L);
        newOrderInfo.setSpecValues("specValues");
        newOrderInfo.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo.setProductNum(0);
        newOrderInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setNewOrderInfo(newOrderInfo);
        orderExchangeDetailVO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreVoucherAmount(new BigDecimal("0.00"));

        // Run the test
        orderExchangeServiceImplUnderTest.updateExtendAmount("orderSn", orderExchangeDetailVO);

        // Verify the results
        verify(mockOrderExtendService).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    public void testUpdateOrderToExchange() {
        // Setup
        // Run the test
        orderExchangeServiceImplUnderTest.updateOrderToExchange("orderSn", 0);

        // Verify the results
        verify(mockOrderService).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    public void testSaveExchangeOrderDetail() {
        // Setup
        final OrderExchangeDetailVO orderExchangeDetailVO = new OrderExchangeDetailVO();
        orderExchangeDetailVO.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo = new ExchangeOrderDTO();
        productInfo.setOrderSn("orderSn");
        productInfo.setOrderProductId(0L);
        productInfo.setProductId(0L);
        productInfo.setGoodsName("exchangeProductName");
        productInfo.setProductImage("productImage");
        productInfo.setGoodsId(0L);
        productInfo.setSpecValues("specValues");
        productInfo.setProductShowPrice(new BigDecimal("0.00"));
        productInfo.setProductNum(0);
        productInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo.setMoneyAmount(new BigDecimal("0.00"));
        productInfo.setXzCardAmount(new BigDecimal("0.00"));
        productInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setProductInfo(productInfo);
        final ExchangeOrderDTO newOrderInfo = new ExchangeOrderDTO();
        newOrderInfo.setOrderSn("orderSn");
        newOrderInfo.setOrderProductId(0L);
        newOrderInfo.setProductId(0L);
        newOrderInfo.setGoodsName("exchangeProductName");
        newOrderInfo.setProductImage("productImage");
        newOrderInfo.setGoodsId(0L);
        newOrderInfo.setSpecValues("specValues");
        newOrderInfo.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo.setProductNum(0);
        newOrderInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setNewOrderInfo(newOrderInfo);
        orderExchangeDetailVO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreVoucherAmount(new BigDecimal("0.00"));

        final OrderExchangeDetailPO expectedResult = new OrderExchangeDetailPO();
        expectedResult.setExchangeSn("exchangeSn");
        expectedResult.setOrderSn("orderSn");
        expectedResult.setOrderProductId(0L);
        expectedResult.setProductName("exchangeProductName");
        expectedResult.setProductNum(0);
        expectedResult.setAfsSn("afsSn");
        expectedResult.setExchangeOrderSn("orderSn");
        expectedResult.setExchangeOrderProductId(0L);
        expectedResult.setExchangeProductName("exchangeProductName");
        expectedResult.setExchangeProductNum(0);
        expectedResult.setRefundAmount(new BigDecimal("0.00"));
        expectedResult.setActualRefundAmount(new BigDecimal("0.00"));
        expectedResult.setXzCardAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformActivityAmount(new BigDecimal("0.00"));
        expectedResult.setStoreActivityAmount(new BigDecimal("0.00"));
        expectedResult.setStoreVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setCreateBy("operatorName");

        // Run the test
        final OrderExchangeDetailPO result = orderExchangeServiceImplUnderTest.saveExchangeOrderDetail(
                orderExchangeDetailVO, "operatorName");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm OrderExchangeDetailMapper.insert(...).
        final OrderExchangeDetailPO entity = new OrderExchangeDetailPO();
        entity.setExchangeSn("exchangeSn");
        entity.setOrderSn("orderSn");
        entity.setOrderProductId(0L);
        entity.setProductName("exchangeProductName");
        entity.setProductNum(0);
        entity.setAfsSn("afsSn");
        entity.setExchangeOrderSn("orderSn");
        entity.setExchangeOrderProductId(0L);
        entity.setExchangeProductName("exchangeProductName");
        entity.setExchangeProductNum(0);
        entity.setRefundAmount(new BigDecimal("0.00"));
        entity.setActualRefundAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setPlatformVoucherAmount(new BigDecimal("0.00"));
        entity.setPlatformActivityAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setCreateBy("operatorName");
        verify(mockOrderExchangeDetailMapper).insert(entity);
    }

    @Test
    public void testCreateExchangeOrder() {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserNo("userNo");
        orderPO.setUserMobile("userMobile");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setPerformanceModes("performanceModes");
        orderPO.setExchangeFlag(0);

        final OrderExchangeDetailVO orderExchangeDetailVO = new OrderExchangeDetailVO();
        orderExchangeDetailVO.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo = new ExchangeOrderDTO();
        productInfo.setOrderSn("orderSn");
        productInfo.setOrderProductId(0L);
        productInfo.setProductId(0L);
        productInfo.setGoodsName("exchangeProductName");
        productInfo.setProductImage("productImage");
        productInfo.setGoodsId(0L);
        productInfo.setSpecValues("specValues");
        productInfo.setProductShowPrice(new BigDecimal("0.00"));
        productInfo.setProductNum(0);
        productInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo.setMoneyAmount(new BigDecimal("0.00"));
        productInfo.setXzCardAmount(new BigDecimal("0.00"));
        productInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setProductInfo(productInfo);
        final ExchangeOrderDTO newOrderInfo = new ExchangeOrderDTO();
        newOrderInfo.setOrderSn("orderSn");
        newOrderInfo.setOrderProductId(0L);
        newOrderInfo.setProductId(0L);
        newOrderInfo.setGoodsName("exchangeProductName");
        newOrderInfo.setProductImage("productImage");
        newOrderInfo.setGoodsId(0L);
        newOrderInfo.setSpecValues("specValues");
        newOrderInfo.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo.setProductNum(0);
        newOrderInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setNewOrderInfo(newOrderInfo);
        orderExchangeDetailVO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreVoucherAmount(new BigDecimal("0.00"));

        final OrderExchangeRequest orderExchangeRequest = new OrderExchangeRequest();
        orderExchangeRequest.setOrderSn("orderSn");
        orderExchangeRequest.setOrderProductId(0L);
        orderExchangeRequest.setProductNum(0);
        orderExchangeRequest.setExchangeReason("exchangeReason");
        orderExchangeRequest.setBuyerConfirmFlag(0);
        orderExchangeRequest.setProductId(0L);
        orderExchangeRequest.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest.setIsTrial(false);
        final UserDTO applicantInfo = new UserDTO();
        applicantInfo.setUserId(0L);
        applicantInfo.setUserName("operatorName");
        applicantInfo.setUserRole(0);
        applicantInfo.setStoreId(0L);
        applicantInfo.setMobile("mobile");
        orderExchangeRequest.setApplicantInfo(applicantInfo);
        orderExchangeRequest.setChannel("value");
        orderExchangeRequest.setDealerCode("dealerCode");

        final OrderPO expectedResult = new OrderPO();
        expectedResult.setOrderSn("orderSn");
        expectedResult.setUserNo("userNo");
        expectedResult.setUserMobile("userMobile");
        expectedResult.setPaySn("paySn");
        expectedResult.setStoreId(0L);
        expectedResult.setMemberName("memberName");
        expectedResult.setMemberId(0);
        expectedResult.setOrderState(0);
        expectedResult.setPaymentName("paymentName");
        expectedResult.setPaymentCode("paymentCode");
        expectedResult.setOrderAmount(new BigDecimal("0.00"));
        expectedResult.setActivityDiscountAmount(new BigDecimal("0.00"));
        expectedResult.setStoreVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setStoreActivityAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformActivityAmount(new BigDecimal("0.00"));
        expectedResult.setXzCardAmount(new BigDecimal("0.00"));
        expectedResult.setPayAmount(new BigDecimal("0.00"));
        expectedResult.setAreaCode("areaCode");
        expectedResult.setOrderType(0);
        expectedResult.setPerformanceModes("performanceModes");
        expectedResult.setExchangeFlag(0);

        when(mockShardingId.next(SeqEnum.PNO, "memberId")).thenReturn(0L);

        // Configure OrderExtendModel.getOrderExtendByOrderSn(...).
        final OrderExtendPO extendPO = new OrderExtendPO();
        extendPO.setOrderSn("orderSn");
        extendPO.setBranch("branch");
        extendPO.setReceiverProvinceCode("province");
        extendPO.setReceiverAddress("detailAddress");
        extendPO.setReceiverMobile("receiverMobile");
        extendPO.setReceiverCityCode("city");
        extendPO.setReceiverDistrictCode("district");
        extendPO.setReceiverTownCode("receiverTownCode");
        extendPO.setReceiverName("receiverName");
        extendPO.setReceiveBranchCode("receiveBranchCode");
        extendPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        extendPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        extendPO.setWarehouseCode("warehouseCode");
        when(mockOrderExtendModel.getOrderExtendByOrderSn("orderSn")).thenReturn(extendPO);

        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure MemberFeignClient.getMemberByMemberId(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");
        when(mockMemberFeignClient.getMemberByMemberId(0)).thenReturn(member);

        // Configure OrderModel.submitOrder(...).
        final Cart cart = new Cart();
        cart.setCartId(0);
        cart.setMemberId(0);
        cart.setStoreId(0L);
        cart.setStoreName("storeName");
        cart.setGoodsId(0L);
        final OrderSubmitParamDTO orderSubmitParamDTO = new OrderSubmitParamDTO();
        orderSubmitParamDTO.setChannel("value");
        orderSubmitParamDTO.setSource(0);
        orderSubmitParamDTO.setOrderFrom(0);
        orderSubmitParamDTO.setStoreId("storeId");
        final OrderSubmitParamDTO.StoreInfo storeInfo = new OrderSubmitParamDTO.StoreInfo();
        storeInfo.setStoreId(0L);
        orderSubmitParamDTO.setStoreInfoList(Arrays.asList(storeInfo));
        orderSubmitParamDTO.setAreaCode("areaCode");
        orderSubmitParamDTO.setIsCart(false);
        orderSubmitParamDTO.setOrderPattern(0);
        orderSubmitParamDTO.setDealerCode("dealerCode");
        orderSubmitParamDTO.setProductId(0L);
        orderSubmitParamDTO.setNumber(0);
        orderSubmitParamDTO.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress = new OrderAddressDTO();
        orderAddress.setReceiverName("receiverName");
        orderAddress.setReceiverMobile("receiverMobile");
        orderAddress.setProvince("province");
        orderAddress.setCity("city");
        orderAddress.setCityCode("city");
        orderAddress.setDistrict("district");
        orderAddress.setTown("receiverTownCode");
        orderAddress.setDetailAddress("detailAddress");
        orderSubmitParamDTO.setOrderAddress(orderAddress);
        final OrderSubmitDTO orderSubmitDTO = new OrderSubmitDTO(Arrays.asList(cart), orderSubmitParamDTO);
        final Member member1 = new Member();
        member1.setMemberId(0);
        member1.setUserNo("userNo");
        member1.setCustNo("custNo");
        member1.setCappCustNo("cappCustNo");
        member1.setMemberName("memberName");
        final OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        final OrderSubmitParamDTO paramDTO = new OrderSubmitParamDTO();
        paramDTO.setChannel("value");
        paramDTO.setSource(0);
        paramDTO.setOrderFrom(0);
        paramDTO.setStoreId("storeId");
        final OrderSubmitParamDTO.StoreInfo storeInfo1 = new OrderSubmitParamDTO.StoreInfo();
        storeInfo1.setStoreId(0L);
        paramDTO.setStoreInfoList(Arrays.asList(storeInfo1));
        paramDTO.setAreaCode("areaCode");
        paramDTO.setIsCart(false);
        paramDTO.setOrderPattern(0);
        paramDTO.setDealerCode("dealerCode");
        paramDTO.setProductId(0L);
        paramDTO.setNumber(0);
        paramDTO.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress1 = new OrderAddressDTO();
        orderAddress1.setReceiverName("receiverName");
        orderAddress1.setReceiverMobile("receiverMobile");
        orderAddress1.setProvince("province");
        orderAddress1.setCity("city");
        orderAddress1.setCityCode("city");
        orderAddress1.setDistrict("district");
        orderAddress1.setTown("receiverTownCode");
        orderAddress1.setDetailAddress("detailAddress");
        paramDTO.setOrderAddress(orderAddress1);
        consumerDTO.setParamDTO(paramDTO);
        consumerDTO.setMemberId(0);
        consumerDTO.setUserNo("userNo");
        consumerDTO.setPaySn("paySn");
        consumerDTO.setAreaCode("areaCode");
       /* when(mockOrderModel.submitOrder(orderSubmitDTO, member1, consumerDTO))
                .thenReturn(new HashSet<>(Arrays.asList("value")));*/

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderSn("orderSn");
        orderPO1.setUserNo("userNo");
        orderPO1.setUserMobile("userMobile");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setOrderState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setPerformanceModes("performanceModes");
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderIdIn("orderIdIn");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        // Run the test
        final OrderPO result = orderExchangeServiceImplUnderTest.createExchangeOrder(orderPO, orderExchangeDetailVO,
                orderExchangeRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm IOrderService.dealExpress(...).
        final Cart cart1 = new Cart();
        cart1.setCartId(0);
        cart1.setMemberId(0);
        cart1.setStoreId(0L);
        cart1.setStoreName("storeName");
        cart1.setGoodsId(0L);
        final OrderSubmitParamDTO orderSubmitParamDTO1 = new OrderSubmitParamDTO();
        orderSubmitParamDTO1.setChannel("value");
        orderSubmitParamDTO1.setSource(0);
        orderSubmitParamDTO1.setOrderFrom(0);
        orderSubmitParamDTO1.setStoreId("storeId");
        final OrderSubmitParamDTO.StoreInfo storeInfo2 = new OrderSubmitParamDTO.StoreInfo();
        storeInfo2.setStoreId(0L);
        orderSubmitParamDTO1.setStoreInfoList(Arrays.asList(storeInfo2));
        orderSubmitParamDTO1.setAreaCode("areaCode");
        orderSubmitParamDTO1.setIsCart(false);
        orderSubmitParamDTO1.setOrderPattern(0);
        orderSubmitParamDTO1.setDealerCode("dealerCode");
        orderSubmitParamDTO1.setProductId(0L);
        orderSubmitParamDTO1.setNumber(0);
        orderSubmitParamDTO1.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress2 = new OrderAddressDTO();
        orderAddress2.setReceiverName("receiverName");
        orderAddress2.setReceiverMobile("receiverMobile");
        orderAddress2.setProvince("province");
        orderAddress2.setCity("city");
        orderAddress2.setCityCode("city");
        orderAddress2.setDistrict("district");
        orderAddress2.setTown("receiverTownCode");
        orderAddress2.setDetailAddress("detailAddress");
        orderSubmitParamDTO1.setOrderAddress(orderAddress2);
        final OrderSubmitDTO orderSubmitDTO1 = new OrderSubmitDTO(Arrays.asList(cart1), orderSubmitParamDTO1);
        verify(mockOrderService).dealExpress(orderSubmitDTO1, Arrays.asList(new BigDecimal("0.00")));
        verify(mockSlodonLock).unlock("lockName");
    }

    @Test
    public void testCreateExchangeOrder_OrderModelSubmitOrderReturnsNoItems() {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserNo("userNo");
        orderPO.setUserMobile("userMobile");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setPerformanceModes("performanceModes");
        orderPO.setExchangeFlag(0);

        final OrderExchangeDetailVO orderExchangeDetailVO = new OrderExchangeDetailVO();
        orderExchangeDetailVO.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo = new ExchangeOrderDTO();
        productInfo.setOrderSn("orderSn");
        productInfo.setOrderProductId(0L);
        productInfo.setProductId(0L);
        productInfo.setGoodsName("exchangeProductName");
        productInfo.setProductImage("productImage");
        productInfo.setGoodsId(0L);
        productInfo.setSpecValues("specValues");
        productInfo.setProductShowPrice(new BigDecimal("0.00"));
        productInfo.setProductNum(0);
        productInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo.setMoneyAmount(new BigDecimal("0.00"));
        productInfo.setXzCardAmount(new BigDecimal("0.00"));
        productInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setProductInfo(productInfo);
        final ExchangeOrderDTO newOrderInfo = new ExchangeOrderDTO();
        newOrderInfo.setOrderSn("orderSn");
        newOrderInfo.setOrderProductId(0L);
        newOrderInfo.setProductId(0L);
        newOrderInfo.setGoodsName("exchangeProductName");
        newOrderInfo.setProductImage("productImage");
        newOrderInfo.setGoodsId(0L);
        newOrderInfo.setSpecValues("specValues");
        newOrderInfo.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo.setProductNum(0);
        newOrderInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setNewOrderInfo(newOrderInfo);
        orderExchangeDetailVO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreVoucherAmount(new BigDecimal("0.00"));

        final OrderExchangeRequest orderExchangeRequest = new OrderExchangeRequest();
        orderExchangeRequest.setOrderSn("orderSn");
        orderExchangeRequest.setOrderProductId(0L);
        orderExchangeRequest.setProductNum(0);
        orderExchangeRequest.setExchangeReason("exchangeReason");
        orderExchangeRequest.setBuyerConfirmFlag(0);
        orderExchangeRequest.setProductId(0L);
        orderExchangeRequest.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest.setIsTrial(false);
        final UserDTO applicantInfo = new UserDTO();
        applicantInfo.setUserId(0L);
        applicantInfo.setUserName("operatorName");
        applicantInfo.setUserRole(0);
        applicantInfo.setStoreId(0L);
        applicantInfo.setMobile("mobile");
        orderExchangeRequest.setApplicantInfo(applicantInfo);
        orderExchangeRequest.setChannel("value");
        orderExchangeRequest.setDealerCode("dealerCode");

        final OrderPO expectedResult = new OrderPO();
        expectedResult.setOrderSn("orderSn");
        expectedResult.setUserNo("userNo");
        expectedResult.setUserMobile("userMobile");
        expectedResult.setPaySn("paySn");
        expectedResult.setStoreId(0L);
        expectedResult.setMemberName("memberName");
        expectedResult.setMemberId(0);
        expectedResult.setOrderState(0);
        expectedResult.setPaymentName("paymentName");
        expectedResult.setPaymentCode("paymentCode");
        expectedResult.setOrderAmount(new BigDecimal("0.00"));
        expectedResult.setActivityDiscountAmount(new BigDecimal("0.00"));
        expectedResult.setStoreVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setStoreActivityAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformActivityAmount(new BigDecimal("0.00"));
        expectedResult.setXzCardAmount(new BigDecimal("0.00"));
        expectedResult.setPayAmount(new BigDecimal("0.00"));
        expectedResult.setAreaCode("areaCode");
        expectedResult.setOrderType(0);
        expectedResult.setPerformanceModes("performanceModes");
        expectedResult.setExchangeFlag(0);

        when(mockShardingId.next(SeqEnum.PNO, "memberId")).thenReturn(0L);

        // Configure OrderExtendModel.getOrderExtendByOrderSn(...).
        final OrderExtendPO extendPO = new OrderExtendPO();
        extendPO.setOrderSn("orderSn");
        extendPO.setBranch("branch");
        extendPO.setReceiverProvinceCode("province");
        extendPO.setReceiverAddress("detailAddress");
        extendPO.setReceiverMobile("receiverMobile");
        extendPO.setReceiverCityCode("city");
        extendPO.setReceiverDistrictCode("district");
        extendPO.setReceiverTownCode("receiverTownCode");
        extendPO.setReceiverName("receiverName");
        extendPO.setReceiveBranchCode("receiveBranchCode");
        extendPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        extendPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        extendPO.setWarehouseCode("warehouseCode");
        when(mockOrderExtendModel.getOrderExtendByOrderSn("orderSn")).thenReturn(extendPO);

        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure MemberFeignClient.getMemberByMemberId(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");
        when(mockMemberFeignClient.getMemberByMemberId(0)).thenReturn(member);

        // Configure OrderModel.submitOrder(...).
        final Cart cart = new Cart();
        cart.setCartId(0);
        cart.setMemberId(0);
        cart.setStoreId(0L);
        cart.setStoreName("storeName");
        cart.setGoodsId(0L);
        final OrderSubmitParamDTO orderSubmitParamDTO = new OrderSubmitParamDTO();
        orderSubmitParamDTO.setChannel("value");
        orderSubmitParamDTO.setSource(0);
        orderSubmitParamDTO.setOrderFrom(0);
        orderSubmitParamDTO.setStoreId("storeId");
        final OrderSubmitParamDTO.StoreInfo storeInfo = new OrderSubmitParamDTO.StoreInfo();
        storeInfo.setStoreId(0L);
        orderSubmitParamDTO.setStoreInfoList(Arrays.asList(storeInfo));
        orderSubmitParamDTO.setAreaCode("areaCode");
        orderSubmitParamDTO.setIsCart(false);
        orderSubmitParamDTO.setOrderPattern(0);
        orderSubmitParamDTO.setDealerCode("dealerCode");
        orderSubmitParamDTO.setProductId(0L);
        orderSubmitParamDTO.setNumber(0);
        orderSubmitParamDTO.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress = new OrderAddressDTO();
        orderAddress.setReceiverName("receiverName");
        orderAddress.setReceiverMobile("receiverMobile");
        orderAddress.setProvince("province");
        orderAddress.setCity("city");
        orderAddress.setCityCode("city");
        orderAddress.setDistrict("district");
        orderAddress.setTown("receiverTownCode");
        orderAddress.setDetailAddress("detailAddress");
        orderSubmitParamDTO.setOrderAddress(orderAddress);
        final OrderSubmitDTO orderSubmitDTO = new OrderSubmitDTO(Arrays.asList(cart), orderSubmitParamDTO);
        final Member member1 = new Member();
        member1.setMemberId(0);
        member1.setUserNo("userNo");
        member1.setCustNo("custNo");
        member1.setCappCustNo("cappCustNo");
        member1.setMemberName("memberName");
        final OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        final OrderSubmitParamDTO paramDTO = new OrderSubmitParamDTO();
        paramDTO.setChannel("value");
        paramDTO.setSource(0);
        paramDTO.setOrderFrom(0);
        paramDTO.setStoreId("storeId");
        final OrderSubmitParamDTO.StoreInfo storeInfo1 = new OrderSubmitParamDTO.StoreInfo();
        storeInfo1.setStoreId(0L);
        paramDTO.setStoreInfoList(Arrays.asList(storeInfo1));
        paramDTO.setAreaCode("areaCode");
        paramDTO.setIsCart(false);
        paramDTO.setOrderPattern(0);
        paramDTO.setDealerCode("dealerCode");
        paramDTO.setProductId(0L);
        paramDTO.setNumber(0);
        paramDTO.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress1 = new OrderAddressDTO();
        orderAddress1.setReceiverName("receiverName");
        orderAddress1.setReceiverMobile("receiverMobile");
        orderAddress1.setProvince("province");
        orderAddress1.setCity("city");
        orderAddress1.setCityCode("city");
        orderAddress1.setDistrict("district");
        orderAddress1.setTown("receiverTownCode");
        orderAddress1.setDetailAddress("detailAddress");
        paramDTO.setOrderAddress(orderAddress1);
        consumerDTO.setParamDTO(paramDTO);
        consumerDTO.setMemberId(0);
        consumerDTO.setUserNo("userNo");
        consumerDTO.setPaySn("paySn");
        consumerDTO.setAreaCode("areaCode");
        //when(mockOrderModel.submitOrder(orderSubmitDTO, member1, consumerDTO)).thenReturn(Collections.emptySet());

        // Configure OrderModel.getOrderList(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderSn("orderSn");
        orderPO1.setUserNo("userNo");
        orderPO1.setUserMobile("userMobile");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setOrderState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setPerformanceModes("performanceModes");
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderIdIn("orderIdIn");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(orderPOList);

        // Run the test
        final OrderPO result = orderExchangeServiceImplUnderTest.createExchangeOrder(orderPO, orderExchangeDetailVO,
                orderExchangeRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm IOrderService.dealExpress(...).
        final Cart cart1 = new Cart();
        cart1.setCartId(0);
        cart1.setMemberId(0);
        cart1.setStoreId(0L);
        cart1.setStoreName("storeName");
        cart1.setGoodsId(0L);
        final OrderSubmitParamDTO orderSubmitParamDTO1 = new OrderSubmitParamDTO();
        orderSubmitParamDTO1.setChannel("value");
        orderSubmitParamDTO1.setSource(0);
        orderSubmitParamDTO1.setOrderFrom(0);
        orderSubmitParamDTO1.setStoreId("storeId");
        final OrderSubmitParamDTO.StoreInfo storeInfo2 = new OrderSubmitParamDTO.StoreInfo();
        storeInfo2.setStoreId(0L);
        orderSubmitParamDTO1.setStoreInfoList(Arrays.asList(storeInfo2));
        orderSubmitParamDTO1.setAreaCode("areaCode");
        orderSubmitParamDTO1.setIsCart(false);
        orderSubmitParamDTO1.setOrderPattern(0);
        orderSubmitParamDTO1.setDealerCode("dealerCode");
        orderSubmitParamDTO1.setProductId(0L);
        orderSubmitParamDTO1.setNumber(0);
        orderSubmitParamDTO1.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress2 = new OrderAddressDTO();
        orderAddress2.setReceiverName("receiverName");
        orderAddress2.setReceiverMobile("receiverMobile");
        orderAddress2.setProvince("province");
        orderAddress2.setCity("city");
        orderAddress2.setCityCode("city");
        orderAddress2.setDistrict("district");
        orderAddress2.setTown("receiverTownCode");
        orderAddress2.setDetailAddress("detailAddress");
        orderSubmitParamDTO1.setOrderAddress(orderAddress2);
        final OrderSubmitDTO orderSubmitDTO1 = new OrderSubmitDTO(Arrays.asList(cart1), orderSubmitParamDTO1);
        verify(mockOrderService).dealExpress(orderSubmitDTO1, Arrays.asList(new BigDecimal("0.00")));
    }

    @Test
    public void testCreateExchangeOrder_OrderModelGetOrderListReturnsNoItems() {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserNo("userNo");
        orderPO.setUserMobile("userMobile");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setPerformanceModes("performanceModes");
        orderPO.setExchangeFlag(0);

        final OrderExchangeDetailVO orderExchangeDetailVO = new OrderExchangeDetailVO();
        orderExchangeDetailVO.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo = new ExchangeOrderDTO();
        productInfo.setOrderSn("orderSn");
        productInfo.setOrderProductId(0L);
        productInfo.setProductId(0L);
        productInfo.setGoodsName("exchangeProductName");
        productInfo.setProductImage("productImage");
        productInfo.setGoodsId(0L);
        productInfo.setSpecValues("specValues");
        productInfo.setProductShowPrice(new BigDecimal("0.00"));
        productInfo.setProductNum(0);
        productInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo.setMoneyAmount(new BigDecimal("0.00"));
        productInfo.setXzCardAmount(new BigDecimal("0.00"));
        productInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setProductInfo(productInfo);
        final ExchangeOrderDTO newOrderInfo = new ExchangeOrderDTO();
        newOrderInfo.setOrderSn("orderSn");
        newOrderInfo.setOrderProductId(0L);
        newOrderInfo.setProductId(0L);
        newOrderInfo.setGoodsName("exchangeProductName");
        newOrderInfo.setProductImage("productImage");
        newOrderInfo.setGoodsId(0L);
        newOrderInfo.setSpecValues("specValues");
        newOrderInfo.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo.setProductNum(0);
        newOrderInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setNewOrderInfo(newOrderInfo);
        orderExchangeDetailVO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailVO.setStoreVoucherAmount(new BigDecimal("0.00"));

        final OrderExchangeRequest orderExchangeRequest = new OrderExchangeRequest();
        orderExchangeRequest.setOrderSn("orderSn");
        orderExchangeRequest.setOrderProductId(0L);
        orderExchangeRequest.setProductNum(0);
        orderExchangeRequest.setExchangeReason("exchangeReason");
        orderExchangeRequest.setBuyerConfirmFlag(0);
        orderExchangeRequest.setProductId(0L);
        orderExchangeRequest.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest.setIsTrial(false);
        final UserDTO applicantInfo = new UserDTO();
        applicantInfo.setUserId(0L);
        applicantInfo.setUserName("operatorName");
        applicantInfo.setUserRole(0);
        applicantInfo.setStoreId(0L);
        applicantInfo.setMobile("mobile");
        orderExchangeRequest.setApplicantInfo(applicantInfo);
        orderExchangeRequest.setChannel("value");
        orderExchangeRequest.setDealerCode("dealerCode");

        final OrderPO expectedResult = new OrderPO();
        expectedResult.setOrderSn("orderSn");
        expectedResult.setUserNo("userNo");
        expectedResult.setUserMobile("userMobile");
        expectedResult.setPaySn("paySn");
        expectedResult.setStoreId(0L);
        expectedResult.setMemberName("memberName");
        expectedResult.setMemberId(0);
        expectedResult.setOrderState(0);
        expectedResult.setPaymentName("paymentName");
        expectedResult.setPaymentCode("paymentCode");
        expectedResult.setOrderAmount(new BigDecimal("0.00"));
        expectedResult.setActivityDiscountAmount(new BigDecimal("0.00"));
        expectedResult.setStoreVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setStoreActivityAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformActivityAmount(new BigDecimal("0.00"));
        expectedResult.setXzCardAmount(new BigDecimal("0.00"));
        expectedResult.setPayAmount(new BigDecimal("0.00"));
        expectedResult.setAreaCode("areaCode");
        expectedResult.setOrderType(0);
        expectedResult.setPerformanceModes("performanceModes");
        expectedResult.setExchangeFlag(0);

        when(mockShardingId.next(SeqEnum.PNO, "memberId")).thenReturn(0L);

        // Configure OrderExtendModel.getOrderExtendByOrderSn(...).
        final OrderExtendPO extendPO = new OrderExtendPO();
        extendPO.setOrderSn("orderSn");
        extendPO.setBranch("branch");
        extendPO.setReceiverProvinceCode("province");
        extendPO.setReceiverAddress("detailAddress");
        extendPO.setReceiverMobile("receiverMobile");
        extendPO.setReceiverCityCode("city");
        extendPO.setReceiverDistrictCode("district");
        extendPO.setReceiverTownCode("receiverTownCode");
        extendPO.setReceiverName("receiverName");
        extendPO.setReceiveBranchCode("receiveBranchCode");
        extendPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        extendPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        extendPO.setWarehouseCode("warehouseCode");
        when(mockOrderExtendModel.getOrderExtendByOrderSn("orderSn")).thenReturn(extendPO);

        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure MemberFeignClient.getMemberByMemberId(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");
        when(mockMemberFeignClient.getMemberByMemberId(0)).thenReturn(member);

        // Configure OrderModel.submitOrder(...).
        final Cart cart = new Cart();
        cart.setCartId(0);
        cart.setMemberId(0);
        cart.setStoreId(0L);
        cart.setStoreName("storeName");
        cart.setGoodsId(0L);
        final OrderSubmitParamDTO orderSubmitParamDTO = new OrderSubmitParamDTO();
        orderSubmitParamDTO.setChannel("value");
        orderSubmitParamDTO.setSource(0);
        orderSubmitParamDTO.setOrderFrom(0);
        orderSubmitParamDTO.setStoreId("storeId");
        final OrderSubmitParamDTO.StoreInfo storeInfo = new OrderSubmitParamDTO.StoreInfo();
        storeInfo.setStoreId(0L);
        orderSubmitParamDTO.setStoreInfoList(Arrays.asList(storeInfo));
        orderSubmitParamDTO.setAreaCode("areaCode");
        orderSubmitParamDTO.setIsCart(false);
        orderSubmitParamDTO.setOrderPattern(0);
        orderSubmitParamDTO.setDealerCode("dealerCode");
        orderSubmitParamDTO.setProductId(0L);
        orderSubmitParamDTO.setNumber(0);
        orderSubmitParamDTO.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress = new OrderAddressDTO();
        orderAddress.setReceiverName("receiverName");
        orderAddress.setReceiverMobile("receiverMobile");
        orderAddress.setProvince("province");
        orderAddress.setCity("city");
        orderAddress.setCityCode("city");
        orderAddress.setDistrict("district");
        orderAddress.setTown("receiverTownCode");
        orderAddress.setDetailAddress("detailAddress");
        orderSubmitParamDTO.setOrderAddress(orderAddress);
        final OrderSubmitDTO orderSubmitDTO = new OrderSubmitDTO(Arrays.asList(cart), orderSubmitParamDTO);
        final Member member1 = new Member();
        member1.setMemberId(0);
        member1.setUserNo("userNo");
        member1.setCustNo("custNo");
        member1.setCappCustNo("cappCustNo");
        member1.setMemberName("memberName");
        final OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        final OrderSubmitParamDTO paramDTO = new OrderSubmitParamDTO();
        paramDTO.setChannel("value");
        paramDTO.setSource(0);
        paramDTO.setOrderFrom(0);
        paramDTO.setStoreId("storeId");
        final OrderSubmitParamDTO.StoreInfo storeInfo1 = new OrderSubmitParamDTO.StoreInfo();
        storeInfo1.setStoreId(0L);
        paramDTO.setStoreInfoList(Arrays.asList(storeInfo1));
        paramDTO.setAreaCode("areaCode");
        paramDTO.setIsCart(false);
        paramDTO.setOrderPattern(0);
        paramDTO.setDealerCode("dealerCode");
        paramDTO.setProductId(0L);
        paramDTO.setNumber(0);
        paramDTO.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress1 = new OrderAddressDTO();
        orderAddress1.setReceiverName("receiverName");
        orderAddress1.setReceiverMobile("receiverMobile");
        orderAddress1.setProvince("province");
        orderAddress1.setCity("city");
        orderAddress1.setCityCode("city");
        orderAddress1.setDistrict("district");
        orderAddress1.setTown("receiverTownCode");
        orderAddress1.setDetailAddress("detailAddress");
        paramDTO.setOrderAddress(orderAddress1);
        consumerDTO.setParamDTO(paramDTO);
        consumerDTO.setMemberId(0);
        consumerDTO.setUserNo("userNo");
        consumerDTO.setPaySn("paySn");
        consumerDTO.setAreaCode("areaCode");
        /*when(mockOrderModel.submitOrder(orderSubmitDTO, member1, consumerDTO))
                .thenReturn(new HashSet<>(Arrays.asList("value")));*/

        // Configure OrderModel.getOrderList(...).
        final OrderExample example = new OrderExample();
        example.setOrderIdNotEquals(0);
        example.setUserNo("userNo");
        example.setUserMobile("userMobile");
        example.setOrderIdIn("orderIdIn");
        example.setPaySn("paySn");
        when(mockOrderModel.getOrderList(eq(example), any(PagerInfo.class))).thenReturn(Collections.emptyList());

        // Run the test
        final OrderPO result = orderExchangeServiceImplUnderTest.createExchangeOrder(orderPO, orderExchangeDetailVO,
                orderExchangeRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm IOrderService.dealExpress(...).
        final Cart cart1 = new Cart();
        cart1.setCartId(0);
        cart1.setMemberId(0);
        cart1.setStoreId(0L);
        cart1.setStoreName("storeName");
        cart1.setGoodsId(0L);
        final OrderSubmitParamDTO orderSubmitParamDTO1 = new OrderSubmitParamDTO();
        orderSubmitParamDTO1.setChannel("value");
        orderSubmitParamDTO1.setSource(0);
        orderSubmitParamDTO1.setOrderFrom(0);
        orderSubmitParamDTO1.setStoreId("storeId");
        final OrderSubmitParamDTO.StoreInfo storeInfo2 = new OrderSubmitParamDTO.StoreInfo();
        storeInfo2.setStoreId(0L);
        orderSubmitParamDTO1.setStoreInfoList(Arrays.asList(storeInfo2));
        orderSubmitParamDTO1.setAreaCode("areaCode");
        orderSubmitParamDTO1.setIsCart(false);
        orderSubmitParamDTO1.setOrderPattern(0);
        orderSubmitParamDTO1.setDealerCode("dealerCode");
        orderSubmitParamDTO1.setProductId(0L);
        orderSubmitParamDTO1.setNumber(0);
        orderSubmitParamDTO1.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress2 = new OrderAddressDTO();
        orderAddress2.setReceiverName("receiverName");
        orderAddress2.setReceiverMobile("receiverMobile");
        orderAddress2.setProvince("province");
        orderAddress2.setCity("city");
        orderAddress2.setCityCode("city");
        orderAddress2.setDistrict("district");
        orderAddress2.setTown("receiverTownCode");
        orderAddress2.setDetailAddress("detailAddress");
        orderSubmitParamDTO1.setOrderAddress(orderAddress2);
        final OrderSubmitDTO orderSubmitDTO1 = new OrderSubmitDTO(Arrays.asList(cart1), orderSubmitParamDTO1);
        verify(mockOrderService).dealExpress(orderSubmitDTO1, Arrays.asList(new BigDecimal("0.00")));
        verify(mockSlodonLock).unlock("lockName");
    }

    @Test
    public void testSaveExchangeApplyOrder() throws Exception {
        // Setup
        final UserDTO userDTO = new UserDTO(0L, "operatorName", 0, 0L, "mobile");
        final OrderExchangePO expectedResult = new OrderExchangePO();
        expectedResult.setExchangeSn("exchangeSn");
        expectedResult.setMemberId(0);
        expectedResult.setStoreId(0L);
        expectedResult.setExchangeReason("exchangeReason");
        expectedResult.setBuyerConfirmFlag(0);
        expectedResult.setExchangeOrderState(0);
        expectedResult.setApplicantId(0L);
        expectedResult.setApplicantName("operatorName");
        expectedResult.setApplicantRole(0);
        expectedResult.setApproverId(0L);
        expectedResult.setApproverName("approverName");
        expectedResult.setApproverRole(0);
        expectedResult.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setCreateBy("operatorName");
        expectedResult.setUpdateBy("updateBy");

        // Run the test
        final OrderExchangePO result = orderExchangeServiceImplUnderTest.saveExchangeApplyOrder("exchangeSn", 0L, 0,
                userDTO, 0, "exchangeReason");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm OrderExchangeMapper.insert(...).
        final OrderExchangePO entity = new OrderExchangePO();
        entity.setExchangeSn("exchangeSn");
        entity.setMemberId(0);
        entity.setStoreId(0L);
        entity.setExchangeReason("exchangeReason");
        entity.setBuyerConfirmFlag(0);
        entity.setExchangeOrderState(0);
        entity.setApplicantId(0L);
        entity.setApplicantName("operatorName");
        entity.setApplicantRole(0);
        entity.setApproverId(0L);
        entity.setApproverName("approverName");
        entity.setApproverRole(0);
        entity.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setCreateBy("operatorName");
        entity.setUpdateBy("updateBy");
        verify(mockOrderExchangeMapper).insert(entity);
    }

    @Test
    public void testOrderExchangeTrial() {
        // Setup
        final OrderExchangeRequest orderExchangeRequest = new OrderExchangeRequest();
        orderExchangeRequest.setOrderSn("orderSn");
        orderExchangeRequest.setOrderProductId(0L);
        orderExchangeRequest.setProductNum(0);
        orderExchangeRequest.setExchangeReason("exchangeReason");
        orderExchangeRequest.setBuyerConfirmFlag(0);
        orderExchangeRequest.setProductId(0L);
        orderExchangeRequest.setFinanceRuleCode("financeRuleCode");
        orderExchangeRequest.setIsTrial(false);
        final UserDTO applicantInfo = new UserDTO();
        applicantInfo.setUserId(0L);
        applicantInfo.setUserName("operatorName");
        applicantInfo.setUserRole(0);
        applicantInfo.setStoreId(0L);
        applicantInfo.setMobile("mobile");
        orderExchangeRequest.setApplicantInfo(applicantInfo);
        orderExchangeRequest.setChannel("value");
        orderExchangeRequest.setDealerCode("dealerCode");

        final OrderExchangeDetailVO expectedResult = new OrderExchangeDetailVO();
        expectedResult.setExchangeSn("exchangeSn");
        final ExchangeOrderDTO productInfo = new ExchangeOrderDTO();
        productInfo.setOrderSn("orderSn");
        productInfo.setOrderProductId(0L);
        productInfo.setProductId(0L);
        productInfo.setGoodsName("exchangeProductName");
        productInfo.setProductImage("productImage");
        productInfo.setGoodsId(0L);
        productInfo.setSpecValues("specValues");
        productInfo.setProductShowPrice(new BigDecimal("0.00"));
        productInfo.setProductNum(0);
        productInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        productInfo.setMoneyAmount(new BigDecimal("0.00"));
        productInfo.setXzCardAmount(new BigDecimal("0.00"));
        productInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        productInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        productInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        productInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setProductInfo(productInfo);
        final ExchangeOrderDTO newOrderInfo = new ExchangeOrderDTO();
        newOrderInfo.setOrderSn("orderSn");
        newOrderInfo.setOrderProductId(0L);
        newOrderInfo.setProductId(0L);
        newOrderInfo.setGoodsName("exchangeProductName");
        newOrderInfo.setProductImage("productImage");
        newOrderInfo.setGoodsId(0L);
        newOrderInfo.setSpecValues("specValues");
        newOrderInfo.setProductShowPrice(new BigDecimal("0.00"));
        newOrderInfo.setProductNum(0);
        newOrderInfo.setGoodsAmountTotal(new BigDecimal("0.00"));
        newOrderInfo.setMoneyAmount(new BigDecimal("0.00"));
        newOrderInfo.setXzCardAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformActivityAmount(new BigDecimal("0.00"));
        newOrderInfo.setStoreVoucherAmount(new BigDecimal("0.00"));
        newOrderInfo.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setNewOrderInfo(newOrderInfo);
        expectedResult.setRefundAmount(new BigDecimal("0.00"));
        expectedResult.setXzCardAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformVoucherAmount(new BigDecimal("0.00"));
        expectedResult.setPlatformActivityAmount(new BigDecimal("0.00"));
        expectedResult.setStoreActivityAmount(new BigDecimal("0.00"));
        expectedResult.setStoreVoucherAmount(new BigDecimal("0.00"));

        // Configure OrderProductModel.getOrderProductByOrderProductId(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsName("goodsName");
        orderProductPO.setProductImage("productImage");
        orderProductPO.setSpecValues("specValues");
        orderProductPO.setProductId(0L);
        orderProductPO.setFinanceRuleCode("financeRuleCode");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        when(mockOrderProductModel.getOrderProductByOrderProductId(0L)).thenReturn(orderProductPO);

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserNo("userNo");
        orderPO.setUserMobile("userMobile");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setPerformanceModes("performanceModes");
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure OrderLocalUtils.getProductPrice(...).
        final ProductPriceVO productPriceVO = new ProductPriceVO();
        final Product product = new Product();
        product.setProductId(0L);
        product.setSpecValues("specValues");
        product.setProductPrice(new BigDecimal("0.00"));
        product.setProductStock(0);
        product.setMainImage("productImage");
        productPriceVO.setProduct(product);
        final ProductPriceBranchRange productPriceBranchRange = new ProductPriceBranchRange();
        productPriceBranchRange.setTaxPrice(new BigDecimal("0.00"));
        productPriceVO.setProductPriceBranchRange(productPriceBranchRange);
        final Goods goods = new Goods();
        goods.setGoodsId(0L);
        goods.setGoodsName("exchangeProductName");
        productPriceVO.setGoods(goods);
        when(mockOrderLocalUtils.getProductPrice(0L, "areaCode", "financeRuleCode")).thenReturn(productPriceVO);

        // Run the test
        final OrderExchangeDetailVO result = orderExchangeServiceImplUnderTest.orderExchangeTrial(orderExchangeRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDealProductExchangeFlag() {
        // Setup
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsName("goodsName");
        orderProductPO.setProductImage("productImage");
        orderProductPO.setSpecValues("specValues");
        orderProductPO.setProductId(0L);
        orderProductPO.setFinanceRuleCode("financeRuleCode");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductListVO> orderProductList = Arrays.asList(new OrderProductListVO(orderProductPO));

        // Configure OrderExchangeDetailMapper.getOrderExchangeDetailList(...).
        final OrderExchangeDetailPO orderExchangeDetailPO = new OrderExchangeDetailPO();
        orderExchangeDetailPO.setExchangeSn("exchangeSn");
        orderExchangeDetailPO.setOrderSn("orderSn");
        orderExchangeDetailPO.setOrderProductId(0L);
        orderExchangeDetailPO.setProductName("exchangeProductName");
        orderExchangeDetailPO.setProductNum(0);
        orderExchangeDetailPO.setAfsSn("afsSn");
        orderExchangeDetailPO.setExchangeOrderSn("orderSn");
        orderExchangeDetailPO.setExchangeOrderProductId(0L);
        orderExchangeDetailPO.setExchangeProductName("exchangeProductName");
        orderExchangeDetailPO.setExchangeProductNum(0);
        orderExchangeDetailPO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setActualRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setCreateBy("operatorName");
        final List<OrderExchangeDetailPO> orderExchangeDetailPOS = Arrays.asList(orderExchangeDetailPO);
        final OrderExchangeDetailExample example = new OrderExchangeDetailExample();
        example.setExchangeDetailId(0);
        example.setExchangeSn("exchangeSn");
        example.setOrderProductIdIn(Arrays.asList(0L));
        example.setAfsSn("afsSn");
        example.setExchangeOrderSn("orderSn");
        when(mockOrderExchangeDetailMapper.getOrderExchangeDetailList(example)).thenReturn(orderExchangeDetailPOS);

        // Run the test
        orderExchangeServiceImplUnderTest.dealProductExchangeFlag(orderProductList);

        // Verify the results
    }

    @Test
    public void testDealProductExchangeFlag_OrderExchangeDetailMapperReturnsNoItems() {
        // Setup
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsName("goodsName");
        orderProductPO.setProductImage("productImage");
        orderProductPO.setSpecValues("specValues");
        orderProductPO.setProductId(0L);
        orderProductPO.setFinanceRuleCode("financeRuleCode");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final List<OrderProductListVO> orderProductList = Arrays.asList(new OrderProductListVO(orderProductPO));

        // Configure OrderExchangeDetailMapper.getOrderExchangeDetailList(...).
        final OrderExchangeDetailExample example = new OrderExchangeDetailExample();
        example.setExchangeDetailId(0);
        example.setExchangeSn("exchangeSn");
        example.setOrderProductIdIn(Arrays.asList(0L));
        example.setAfsSn("afsSn");
        example.setExchangeOrderSn("orderSn");
        when(mockOrderExchangeDetailMapper.getOrderExchangeDetailList(example)).thenReturn(Collections.emptyList());

        // Run the test
        orderExchangeServiceImplUnderTest.dealProductExchangeFlag(orderProductList);

        // Verify the results
    }

    @Test
    public void testExchangeOrderAudit() throws Exception {
        // Setup
        final OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO.setExchangeAuditState(0);
        orderExchangeAuditDTO.setChannel("value");

        final UserDTO userDTO = new UserDTO(0L, "operatorName", 0, 0L, "mobile");

        // Configure OrderExchangeMapper.getOrderExchange(...).
        final OrderExchangePO orderExchangePO = new OrderExchangePO();
        orderExchangePO.setExchangeSn("exchangeSn");
        orderExchangePO.setMemberId(0);
        orderExchangePO.setStoreId(0L);
        orderExchangePO.setExchangeReason("exchangeReason");
        orderExchangePO.setBuyerConfirmFlag(0);
        orderExchangePO.setExchangeOrderState(0);
        orderExchangePO.setApplicantId(0L);
        orderExchangePO.setApplicantName("operatorName");
        orderExchangePO.setApplicantRole(0);
        orderExchangePO.setApproverId(0L);
        orderExchangePO.setApproverName("approverName");
        orderExchangePO.setApproverRole(0);
        orderExchangePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExchangePO.setCreateBy("operatorName");
        orderExchangePO.setUpdateBy("updateBy");
        final OrderExchangeExample example = new OrderExchangeExample();
        example.setExchangeOrderId(0);
        example.setExchangeSn("exchangeSn");
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setExchangeReason("exchangeReason");
        when(mockOrderExchangeMapper.getOrderExchange(example)).thenReturn(orderExchangePO);

        // Run the test
        orderExchangeServiceImplUnderTest.exchangeOrderAudit(orderExchangeAuditDTO, userDTO);

        // Verify the results
        // Confirm IOrderExchangeService.exchangeOrderAuditDeal(...).
        final OrderExchangeAuditDTO orderExchangeAuditDTO1 = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO1.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO1.setExchangeAuditState(0);
        orderExchangeAuditDTO1.setChannel("value");
        verify(mockOrderExchangeService).exchangeOrderAuditDeal(orderExchangeAuditDTO1,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"));
    }

    @Test
    public void testExchangeOrderAudit_OrderExchangeMapperReturnsNull() throws Exception {
        // Setup
        final OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO.setExchangeAuditState(0);
        orderExchangeAuditDTO.setChannel("value");

        final UserDTO userDTO = new UserDTO(0L, "operatorName", 0, 0L, "mobile");

        // Configure OrderExchangeMapper.getOrderExchange(...).
        final OrderExchangeExample example = new OrderExchangeExample();
        example.setExchangeOrderId(0);
        example.setExchangeSn("exchangeSn");
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setExchangeReason("exchangeReason");
        when(mockOrderExchangeMapper.getOrderExchange(example)).thenReturn(null);

        // Run the test
        orderExchangeServiceImplUnderTest.exchangeOrderAudit(orderExchangeAuditDTO, userDTO);

        // Verify the results
        // Confirm IOrderExchangeService.exchangeOrderAuditDeal(...).
        final OrderExchangeAuditDTO orderExchangeAuditDTO1 = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO1.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO1.setExchangeAuditState(0);
        orderExchangeAuditDTO1.setChannel("value");
        verify(mockOrderExchangeService).exchangeOrderAuditDeal(orderExchangeAuditDTO1,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"));
    }

    @Test
    public void testExchangeOrderAudit_IOrderExchangeServiceThrowsException() throws Exception {
        // Setup
        final OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO.setExchangeAuditState(0);
        orderExchangeAuditDTO.setChannel("value");

        final UserDTO userDTO = new UserDTO(0L, "operatorName", 0, 0L, "mobile");

        // Configure OrderExchangeMapper.getOrderExchange(...).
        final OrderExchangePO orderExchangePO = new OrderExchangePO();
        orderExchangePO.setExchangeSn("exchangeSn");
        orderExchangePO.setMemberId(0);
        orderExchangePO.setStoreId(0L);
        orderExchangePO.setExchangeReason("exchangeReason");
        orderExchangePO.setBuyerConfirmFlag(0);
        orderExchangePO.setExchangeOrderState(0);
        orderExchangePO.setApplicantId(0L);
        orderExchangePO.setApplicantName("operatorName");
        orderExchangePO.setApplicantRole(0);
        orderExchangePO.setApproverId(0L);
        orderExchangePO.setApproverName("approverName");
        orderExchangePO.setApproverRole(0);
        orderExchangePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExchangePO.setCreateBy("operatorName");
        orderExchangePO.setUpdateBy("updateBy");
        final OrderExchangeExample example = new OrderExchangeExample();
        example.setExchangeOrderId(0);
        example.setExchangeSn("exchangeSn");
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setExchangeReason("exchangeReason");
        when(mockOrderExchangeMapper.getOrderExchange(example)).thenReturn(orderExchangePO);

        // Configure IOrderExchangeService.exchangeOrderAuditDeal(...).
        final OrderExchangeAuditDTO orderExchangeAuditDTO1 = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO1.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO1.setExchangeAuditState(0);
        orderExchangeAuditDTO1.setChannel("value");
        doThrow(Exception.class).when(mockOrderExchangeService).exchangeOrderAuditDeal(orderExchangeAuditDTO1,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"));

        // Run the test
        assertThatThrownBy(() -> orderExchangeServiceImplUnderTest.exchangeOrderAudit(orderExchangeAuditDTO,
                userDTO)).isInstanceOf(Exception.class);
    }

    @Test
    public void testExchangeOrderAuditDeal() throws Exception {
        // Setup
        final OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO.setExchangeAuditState(0);
        orderExchangeAuditDTO.setChannel("value");

        final UserDTO userDTO = new UserDTO(0L, "operatorName", 0, 0L, "mobile");

        // Configure OrderExchangeMapper.getOrderExchange(...).
        final OrderExchangePO orderExchangePO = new OrderExchangePO();
        orderExchangePO.setExchangeSn("exchangeSn");
        orderExchangePO.setMemberId(0);
        orderExchangePO.setStoreId(0L);
        orderExchangePO.setExchangeReason("exchangeReason");
        orderExchangePO.setBuyerConfirmFlag(0);
        orderExchangePO.setExchangeOrderState(0);
        orderExchangePO.setApplicantId(0L);
        orderExchangePO.setApplicantName("operatorName");
        orderExchangePO.setApplicantRole(0);
        orderExchangePO.setApproverId(0L);
        orderExchangePO.setApproverName("approverName");
        orderExchangePO.setApproverRole(0);
        orderExchangePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExchangePO.setCreateBy("operatorName");
        orderExchangePO.setUpdateBy("updateBy");
        final OrderExchangeExample example = new OrderExchangeExample();
        example.setExchangeOrderId(0);
        example.setExchangeSn("exchangeSn");
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setExchangeReason("exchangeReason");
        when(mockOrderExchangeMapper.getOrderExchange(example)).thenReturn(orderExchangePO);

        // Configure OrderExchangeDetailMapper.getExchangeDetailByExchangeSn(...).
        final OrderExchangeDetailPO orderExchangeDetailPO = new OrderExchangeDetailPO();
        orderExchangeDetailPO.setExchangeSn("exchangeSn");
        orderExchangeDetailPO.setOrderSn("orderSn");
        orderExchangeDetailPO.setOrderProductId(0L);
        orderExchangeDetailPO.setProductName("exchangeProductName");
        orderExchangeDetailPO.setProductNum(0);
        orderExchangeDetailPO.setAfsSn("afsSn");
        orderExchangeDetailPO.setExchangeOrderSn("orderSn");
        orderExchangeDetailPO.setExchangeOrderProductId(0L);
        orderExchangeDetailPO.setExchangeProductName("exchangeProductName");
        orderExchangeDetailPO.setExchangeProductNum(0);
        orderExchangeDetailPO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setActualRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setCreateBy("operatorName");
        when(mockOrderExchangeDetailMapper.getExchangeDetailByExchangeSn("exchangeSn"))
                .thenReturn(orderExchangeDetailPO);

        // Configure OrderProductModel.getOrderProductByOrderProductId(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsName("goodsName");
        orderProductPO.setProductImage("productImage");
        orderProductPO.setSpecValues("specValues");
        orderProductPO.setProductId(0L);
        orderProductPO.setFinanceRuleCode("financeRuleCode");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        when(mockOrderProductModel.getOrderProductByOrderProductId(0L)).thenReturn(orderProductPO);

        when(mockOrderExchangeService.update(any(LambdaUpdateWrapper.class))).thenReturn(false);

        // Run the test
        orderExchangeServiceImplUnderTest.exchangeOrderAuditDeal(orderExchangeAuditDTO, userDTO);

        // Verify the results
        // Confirm IOrderExchangeService.dealCloseExchangeOrder(...).
        final OrderExchangeDetailPO exchangeDetailPO = new OrderExchangeDetailPO();
        exchangeDetailPO.setExchangeSn("exchangeSn");
        exchangeDetailPO.setOrderSn("orderSn");
        exchangeDetailPO.setOrderProductId(0L);
        exchangeDetailPO.setProductName("exchangeProductName");
        exchangeDetailPO.setProductNum(0);
        exchangeDetailPO.setAfsSn("afsSn");
        exchangeDetailPO.setExchangeOrderSn("orderSn");
        exchangeDetailPO.setExchangeOrderProductId(0L);
        exchangeDetailPO.setExchangeProductName("exchangeProductName");
        exchangeDetailPO.setExchangeProductNum(0);
        exchangeDetailPO.setRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setActualRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setXzCardAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setStoreActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setCreateBy("operatorName");
        verify(mockOrderExchangeService).dealCloseExchangeOrder(exchangeDetailPO,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"), 0, "content");

        // Confirm IOrderExchangeService.dealAgreeExchangeOrder(...).
        final OrderExchangeDetailPO exchangeDetailPO1 = new OrderExchangeDetailPO();
        exchangeDetailPO1.setExchangeSn("exchangeSn");
        exchangeDetailPO1.setOrderSn("orderSn");
        exchangeDetailPO1.setOrderProductId(0L);
        exchangeDetailPO1.setProductName("exchangeProductName");
        exchangeDetailPO1.setProductNum(0);
        exchangeDetailPO1.setAfsSn("afsSn");
        exchangeDetailPO1.setExchangeOrderSn("orderSn");
        exchangeDetailPO1.setExchangeOrderProductId(0L);
        exchangeDetailPO1.setExchangeProductName("exchangeProductName");
        exchangeDetailPO1.setExchangeProductNum(0);
        exchangeDetailPO1.setRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setActualRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setXzCardAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setCreateBy("operatorName");
        verify(mockOrderExchangeService).dealAgreeExchangeOrder(exchangeDetailPO1,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"));
        verify(mockOrderLogModel).insertOrderLog(0, 0L, "operatorName", "exchangeSn", 0, 0, 0, "content",
                OrderCreateChannel.H5);
    }

    @Test
    public void testExchangeOrderAuditDeal_OrderExchangeMapperReturnsNull() throws Exception {
        // Setup
        final OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO.setExchangeAuditState(0);
        orderExchangeAuditDTO.setChannel("value");

        final UserDTO userDTO = new UserDTO(0L, "operatorName", 0, 0L, "mobile");

        // Configure OrderExchangeMapper.getOrderExchange(...).
        final OrderExchangeExample example = new OrderExchangeExample();
        example.setExchangeOrderId(0);
        example.setExchangeSn("exchangeSn");
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setExchangeReason("exchangeReason");
        when(mockOrderExchangeMapper.getOrderExchange(example)).thenReturn(null);

        // Configure OrderExchangeDetailMapper.getExchangeDetailByExchangeSn(...).
        final OrderExchangeDetailPO orderExchangeDetailPO = new OrderExchangeDetailPO();
        orderExchangeDetailPO.setExchangeSn("exchangeSn");
        orderExchangeDetailPO.setOrderSn("orderSn");
        orderExchangeDetailPO.setOrderProductId(0L);
        orderExchangeDetailPO.setProductName("exchangeProductName");
        orderExchangeDetailPO.setProductNum(0);
        orderExchangeDetailPO.setAfsSn("afsSn");
        orderExchangeDetailPO.setExchangeOrderSn("orderSn");
        orderExchangeDetailPO.setExchangeOrderProductId(0L);
        orderExchangeDetailPO.setExchangeProductName("exchangeProductName");
        orderExchangeDetailPO.setExchangeProductNum(0);
        orderExchangeDetailPO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setActualRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setCreateBy("operatorName");
        when(mockOrderExchangeDetailMapper.getExchangeDetailByExchangeSn("exchangeSn"))
                .thenReturn(orderExchangeDetailPO);

        // Configure OrderProductModel.getOrderProductByOrderProductId(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsName("goodsName");
        orderProductPO.setProductImage("productImage");
        orderProductPO.setSpecValues("specValues");
        orderProductPO.setProductId(0L);
        orderProductPO.setFinanceRuleCode("financeRuleCode");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        when(mockOrderProductModel.getOrderProductByOrderProductId(0L)).thenReturn(orderProductPO);

        when(mockOrderExchangeService.update(any(LambdaUpdateWrapper.class))).thenReturn(false);

        // Run the test
        orderExchangeServiceImplUnderTest.exchangeOrderAuditDeal(orderExchangeAuditDTO, userDTO);

        // Verify the results
        // Confirm IOrderExchangeService.dealCloseExchangeOrder(...).
        final OrderExchangeDetailPO exchangeDetailPO = new OrderExchangeDetailPO();
        exchangeDetailPO.setExchangeSn("exchangeSn");
        exchangeDetailPO.setOrderSn("orderSn");
        exchangeDetailPO.setOrderProductId(0L);
        exchangeDetailPO.setProductName("exchangeProductName");
        exchangeDetailPO.setProductNum(0);
        exchangeDetailPO.setAfsSn("afsSn");
        exchangeDetailPO.setExchangeOrderSn("orderSn");
        exchangeDetailPO.setExchangeOrderProductId(0L);
        exchangeDetailPO.setExchangeProductName("exchangeProductName");
        exchangeDetailPO.setExchangeProductNum(0);
        exchangeDetailPO.setRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setActualRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setXzCardAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setStoreActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setCreateBy("operatorName");
        verify(mockOrderExchangeService).dealCloseExchangeOrder(exchangeDetailPO,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"), 0, "content");

        // Confirm IOrderExchangeService.dealAgreeExchangeOrder(...).
        final OrderExchangeDetailPO exchangeDetailPO1 = new OrderExchangeDetailPO();
        exchangeDetailPO1.setExchangeSn("exchangeSn");
        exchangeDetailPO1.setOrderSn("orderSn");
        exchangeDetailPO1.setOrderProductId(0L);
        exchangeDetailPO1.setProductName("exchangeProductName");
        exchangeDetailPO1.setProductNum(0);
        exchangeDetailPO1.setAfsSn("afsSn");
        exchangeDetailPO1.setExchangeOrderSn("orderSn");
        exchangeDetailPO1.setExchangeOrderProductId(0L);
        exchangeDetailPO1.setExchangeProductName("exchangeProductName");
        exchangeDetailPO1.setExchangeProductNum(0);
        exchangeDetailPO1.setRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setActualRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setXzCardAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setCreateBy("operatorName");
        verify(mockOrderExchangeService).dealAgreeExchangeOrder(exchangeDetailPO1,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"));
        verify(mockOrderLogModel).insertOrderLog(0, 0L, "operatorName", "exchangeSn", 0, 0, 0, "content",
                OrderCreateChannel.H5);
    }

    @Test
    public void testExchangeOrderAuditDeal_IOrderExchangeServiceDealCloseExchangeOrderThrowsException() throws Exception {
        // Setup
        final OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO.setExchangeAuditState(0);
        orderExchangeAuditDTO.setChannel("value");

        final UserDTO userDTO = new UserDTO(0L, "operatorName", 0, 0L, "mobile");

        // Configure OrderExchangeMapper.getOrderExchange(...).
        final OrderExchangePO orderExchangePO = new OrderExchangePO();
        orderExchangePO.setExchangeSn("exchangeSn");
        orderExchangePO.setMemberId(0);
        orderExchangePO.setStoreId(0L);
        orderExchangePO.setExchangeReason("exchangeReason");
        orderExchangePO.setBuyerConfirmFlag(0);
        orderExchangePO.setExchangeOrderState(0);
        orderExchangePO.setApplicantId(0L);
        orderExchangePO.setApplicantName("operatorName");
        orderExchangePO.setApplicantRole(0);
        orderExchangePO.setApproverId(0L);
        orderExchangePO.setApproverName("approverName");
        orderExchangePO.setApproverRole(0);
        orderExchangePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExchangePO.setCreateBy("operatorName");
        orderExchangePO.setUpdateBy("updateBy");
        final OrderExchangeExample example = new OrderExchangeExample();
        example.setExchangeOrderId(0);
        example.setExchangeSn("exchangeSn");
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setExchangeReason("exchangeReason");
        when(mockOrderExchangeMapper.getOrderExchange(example)).thenReturn(orderExchangePO);

        // Configure OrderExchangeDetailMapper.getExchangeDetailByExchangeSn(...).
        final OrderExchangeDetailPO orderExchangeDetailPO = new OrderExchangeDetailPO();
        orderExchangeDetailPO.setExchangeSn("exchangeSn");
        orderExchangeDetailPO.setOrderSn("orderSn");
        orderExchangeDetailPO.setOrderProductId(0L);
        orderExchangeDetailPO.setProductName("exchangeProductName");
        orderExchangeDetailPO.setProductNum(0);
        orderExchangeDetailPO.setAfsSn("afsSn");
        orderExchangeDetailPO.setExchangeOrderSn("orderSn");
        orderExchangeDetailPO.setExchangeOrderProductId(0L);
        orderExchangeDetailPO.setExchangeProductName("exchangeProductName");
        orderExchangeDetailPO.setExchangeProductNum(0);
        orderExchangeDetailPO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setActualRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setCreateBy("operatorName");
        when(mockOrderExchangeDetailMapper.getExchangeDetailByExchangeSn("exchangeSn"))
                .thenReturn(orderExchangeDetailPO);

        // Configure IOrderExchangeService.dealCloseExchangeOrder(...).
        final OrderExchangeDetailPO exchangeDetailPO = new OrderExchangeDetailPO();
        exchangeDetailPO.setExchangeSn("exchangeSn");
        exchangeDetailPO.setOrderSn("orderSn");
        exchangeDetailPO.setOrderProductId(0L);
        exchangeDetailPO.setProductName("exchangeProductName");
        exchangeDetailPO.setProductNum(0);
        exchangeDetailPO.setAfsSn("afsSn");
        exchangeDetailPO.setExchangeOrderSn("orderSn");
        exchangeDetailPO.setExchangeOrderProductId(0L);
        exchangeDetailPO.setExchangeProductName("exchangeProductName");
        exchangeDetailPO.setExchangeProductNum(0);
        exchangeDetailPO.setRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setActualRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setXzCardAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setStoreActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setCreateBy("operatorName");
        doThrow(Exception.class).when(mockOrderExchangeService).dealCloseExchangeOrder(exchangeDetailPO,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"), 0, "content");

        // Run the test
        assertThatThrownBy(() -> orderExchangeServiceImplUnderTest.exchangeOrderAuditDeal(orderExchangeAuditDTO,
                userDTO)).isInstanceOf(Exception.class);
    }

    @Test
    public void testDealAgreeExchangeOrder() {
        // Setup
        final OrderExchangeDetailPO exchangeDetailPO = new OrderExchangeDetailPO();
        exchangeDetailPO.setExchangeSn("exchangeSn");
        exchangeDetailPO.setOrderSn("orderSn");
        exchangeDetailPO.setOrderProductId(0L);
        exchangeDetailPO.setProductName("exchangeProductName");
        exchangeDetailPO.setProductNum(0);
        exchangeDetailPO.setAfsSn("afsSn");
        exchangeDetailPO.setExchangeOrderSn("orderSn");
        exchangeDetailPO.setExchangeOrderProductId(0L);
        exchangeDetailPO.setExchangeProductName("exchangeProductName");
        exchangeDetailPO.setExchangeProductNum(0);
        exchangeDetailPO.setRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setActualRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setXzCardAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setStoreActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setCreateBy("operatorName");

        final UserDTO userDTO = new UserDTO(0L, "operatorName", 0, 0L, "mobile");

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserNo("userNo");
        orderPO.setUserMobile("userMobile");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setPerformanceModes("performanceModes");
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure OrderExchangeMapper.getOrderExchangeByExchangeSn(...).
        final OrderExchangePO orderExchangePO = new OrderExchangePO();
        orderExchangePO.setExchangeSn("exchangeSn");
        orderExchangePO.setMemberId(0);
        orderExchangePO.setStoreId(0L);
        orderExchangePO.setExchangeReason("exchangeReason");
        orderExchangePO.setBuyerConfirmFlag(0);
        orderExchangePO.setExchangeOrderState(0);
        orderExchangePO.setApplicantId(0L);
        orderExchangePO.setApplicantName("operatorName");
        orderExchangePO.setApplicantRole(0);
        orderExchangePO.setApproverId(0L);
        orderExchangePO.setApproverName("approverName");
        orderExchangePO.setApproverRole(0);
        orderExchangePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExchangePO.setCreateBy("operatorName");
        orderExchangePO.setUpdateBy("updateBy");
        when(mockOrderExchangeMapper.getOrderExchangeByExchangeSn("exchangeSn")).thenReturn(orderExchangePO);

        // Configure OrderAfterServiceModel.createAfsProductVO(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsName("goodsName");
        orderProductPO.setProductImage("productImage");
        orderProductPO.setSpecValues("specValues");
        orderProductPO.setProductId(0L);
        orderProductPO.setFinanceRuleCode("financeRuleCode");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        final AfsProductVO afsProductVO = new AfsProductVO(orderProductPO);
        when(mockOrderAfterServiceModel.createAfsProductVO(0L, new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), new BigDecimal("0.00"), 0, 2, 0)).thenReturn(afsProductVO);

        // Configure OrderAfterServiceModel.createAfterSaleOrder(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderSn("orderSn");
        orderPO1.setUserNo("userNo");
        orderPO1.setUserMobile("userMobile");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setOrderState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setPerformanceModes("performanceModes");
        orderPO1.setExchangeFlag(0);
        final OrderAfterDTO orderAfterDTO = new OrderAfterDTO();
        orderAfterDTO.setOrderSn("orderSn");
        orderAfterDTO.setAfsType(0);
        orderAfterDTO.setGoodsState(0);
        orderAfterDTO.setApplyReasonContent("applyReasonContent");
        orderAfterDTO.setAfsDescription("applyReasonContent");
        orderAfterDTO.setReturnBy(0);
        orderAfterDTO.setChannel("value");
        final OrderAfterDTO.AfterProduct afterProduct = new OrderAfterDTO.AfterProduct();
        afterProduct.setOrderProductId(0L);
        afterProduct.setAfsNum(0);
        orderAfterDTO.setProductList(Arrays.asList(afterProduct));
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setGoodsName("goodsName");
        orderProductPO1.setProductImage("productImage");
        orderProductPO1.setSpecValues("specValues");
        orderProductPO1.setProductId(0L);
        orderProductPO1.setFinanceRuleCode("financeRuleCode");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO1.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO1.setReturnNumber(0);
        orderProductPO1.setChannelSkuUnit("channelSkuUnit");
        orderProductPO1.setChannelSkuId("channelSkuId");
        orderProductPO1.setBatchNo("batchNo");
        final AfsProductVO afsProductVO1 = new AfsProductVO(orderProductPO1);
        when(mockOrderAfterServiceModel.createAfterSaleOrder(orderPO1, orderAfterDTO, afsProductVO1,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"))).thenReturn("result");

        // Run the test
        orderExchangeServiceImplUnderTest.dealAgreeExchangeOrder(exchangeDetailPO, userDTO);

        // Verify the results
        // Confirm OrderPayModel.orderPaySuccess(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderSn("orderSn");
        orderPO2.setUserNo("userNo");
        orderPO2.setUserMobile("userMobile");
        orderPO2.setPaySn("paySn");
        orderPO2.setStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setOrderState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setPerformanceModes("performanceModes");
        orderPO2.setExchangeFlag(0);
        verify(mockOrderPayModel).orderPaySuccess(orderPO2, "tradeSn", "paymentCode", "paymentName",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "payNo");
        verify(mockOrderExchangeDetailService).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    public void testDealCloseExchangeOrder() throws Exception {
        // Setup
        final OrderExchangeDetailPO exchangeDetailPO = new OrderExchangeDetailPO();
        exchangeDetailPO.setExchangeSn("exchangeSn");
        exchangeDetailPO.setOrderSn("orderSn");
        exchangeDetailPO.setOrderProductId(0L);
        exchangeDetailPO.setProductName("exchangeProductName");
        exchangeDetailPO.setProductNum(0);
        exchangeDetailPO.setAfsSn("afsSn");
        exchangeDetailPO.setExchangeOrderSn("orderSn");
        exchangeDetailPO.setExchangeOrderProductId(0L);
        exchangeDetailPO.setExchangeProductName("exchangeProductName");
        exchangeDetailPO.setExchangeProductNum(0);
        exchangeDetailPO.setRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setActualRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setXzCardAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setStoreActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setCreateBy("operatorName");

        final UserDTO userDTO = new UserDTO(0L, "operatorName", 0, 0L, "mobile");

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserNo("userNo");
        orderPO.setUserMobile("userMobile");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setPerformanceModes("performanceModes");
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure OrderExtendModel.getOrderExtendByOrderSn(...).
        final OrderExtendPO extendPO = new OrderExtendPO();
        extendPO.setOrderSn("orderSn");
        extendPO.setBranch("branch");
        extendPO.setReceiverProvinceCode("province");
        extendPO.setReceiverAddress("detailAddress");
        extendPO.setReceiverMobile("receiverMobile");
        extendPO.setReceiverCityCode("city");
        extendPO.setReceiverDistrictCode("district");
        extendPO.setReceiverTownCode("receiverTownCode");
        extendPO.setReceiverName("receiverName");
        extendPO.setReceiveBranchCode("receiveBranchCode");
        extendPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        extendPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        extendPO.setWarehouseCode("warehouseCode");
        when(mockOrderExtendModel.getOrderExtendByOrderSn("orderSn")).thenReturn(extendPO);

        // Configure OrderProductModel.getOrderProductByOrderProductId(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsName("goodsName");
        orderProductPO.setProductImage("productImage");
        orderProductPO.setSpecValues("specValues");
        orderProductPO.setProductId(0L);
        orderProductPO.setFinanceRuleCode("financeRuleCode");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        when(mockOrderProductModel.getOrderProductByOrderProductId(0L)).thenReturn(orderProductPO);

        // Configure OrderExchangeMapper.getExchangeOrderList(...).
        final ExchangeOrderDetailDTO exchangeOrderDetailDTO = new ExchangeOrderDetailDTO();
        exchangeOrderDetailDTO.setMemberId(0);
        exchangeOrderDetailDTO.setStoreId(0L);
        exchangeOrderDetailDTO.setOrderSn("orderSn");
        exchangeOrderDetailDTO.setOrderProductId(0L);
        exchangeOrderDetailDTO.setProductNum(0);
        exchangeOrderDetailDTO.setExchangeOrderSn("exchangeOrderSn");
        exchangeOrderDetailDTO.setExchangeOrderProductId(0L);
        exchangeOrderDetailDTO.setExchangeOrderState(0);
        exchangeOrderDetailDTO.setExchangeOrderStateList(Arrays.asList(0));
        exchangeOrderDetailDTO.setAfsSn("afsSn");
        final List<ExchangeOrderDetailDTO> exchangeOrderDetailDTOList = Arrays.asList(exchangeOrderDetailDTO);
        final ExchangeOrderDetailDTO example = new ExchangeOrderDetailDTO();
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setOrderSn("orderSn");
        example.setOrderProductId(0L);
        example.setProductNum(0);
        example.setExchangeOrderSn("exchangeOrderSn");
        example.setExchangeOrderProductId(0L);
        example.setExchangeOrderState(0);
        example.setExchangeOrderStateList(Arrays.asList(0));
        example.setAfsSn("afsSn");
        when(mockOrderExchangeMapper.getExchangeOrderList(example)).thenReturn(exchangeOrderDetailDTOList);

        // Run the test
        orderExchangeServiceImplUnderTest.dealCloseExchangeOrder(exchangeDetailPO, userDTO, 0, "content");

        // Verify the results
        // Confirm GoodsStockService.goodsStock(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderSn("orderSn");
        orderPO1.setUserNo("userNo");
        orderPO1.setUserMobile("userMobile");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setOrderState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setPerformanceModes("performanceModes");
        orderPO1.setExchangeFlag(0);
        /*verify(mockGoodsStockService).goodsStock("orderSn", "exchangeSn", 0L, "areaCode", 0, "financeRuleCode",
                "batchNo", "channelSkuId", "branch", "receiveBranchCode", "warehouseCode",
                EventStockTypeEnum.ORDER_EXCHANGE_REVOKE_OUT_STOCK, BizTypeEnum.EXCHANGE_ORDER_REDUCE_STOCK,
                "operatorName", orderPO1, "channelSkuUnit", "userMobile");*/

        // Confirm OrderModel.cancelOrder(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderSn("orderSn");
        orderPO2.setUserNo("userNo");
        orderPO2.setUserMobile("userMobile");
        orderPO2.setPaySn("paySn");
        orderPO2.setStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setOrderState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setPerformanceModes("performanceModes");
        orderPO2.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO2);
        verify(mockOrderModel).cancelOrder(orderPOList, "content", "cancelRemark", 0, 0L, "operatorName", "content", 0);

        // Confirm IOrderExchangeService.cancelPaidOrder(...).
        final OrderExchangeDetailPO exchangeDetailPO1 = new OrderExchangeDetailPO();
        exchangeDetailPO1.setExchangeSn("exchangeSn");
        exchangeDetailPO1.setOrderSn("orderSn");
        exchangeDetailPO1.setOrderProductId(0L);
        exchangeDetailPO1.setProductName("exchangeProductName");
        exchangeDetailPO1.setProductNum(0);
        exchangeDetailPO1.setAfsSn("afsSn");
        exchangeDetailPO1.setExchangeOrderSn("orderSn");
        exchangeDetailPO1.setExchangeOrderProductId(0L);
        exchangeDetailPO1.setExchangeProductName("exchangeProductName");
        exchangeDetailPO1.setExchangeProductNum(0);
        exchangeDetailPO1.setRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setActualRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setXzCardAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO1.setCreateBy("operatorName");
        verify(mockOrderExchangeService).cancelPaidOrder(exchangeDetailPO1,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"));
    }

    @Test
    public void testDealCloseExchangeOrder_OrderExchangeMapperReturnsNoItems() throws Exception {
        // Setup
        final OrderExchangeDetailPO exchangeDetailPO = new OrderExchangeDetailPO();
        exchangeDetailPO.setExchangeSn("exchangeSn");
        exchangeDetailPO.setOrderSn("orderSn");
        exchangeDetailPO.setOrderProductId(0L);
        exchangeDetailPO.setProductName("exchangeProductName");
        exchangeDetailPO.setProductNum(0);
        exchangeDetailPO.setAfsSn("afsSn");
        exchangeDetailPO.setExchangeOrderSn("orderSn");
        exchangeDetailPO.setExchangeOrderProductId(0L);
        exchangeDetailPO.setExchangeProductName("exchangeProductName");
        exchangeDetailPO.setExchangeProductNum(0);
        exchangeDetailPO.setRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setActualRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setXzCardAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setStoreActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setCreateBy("operatorName");

        final UserDTO userDTO = new UserDTO(0L, "operatorName", 0, 0L, "mobile");

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserNo("userNo");
        orderPO.setUserMobile("userMobile");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setPerformanceModes("performanceModes");
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure OrderExtendModel.getOrderExtendByOrderSn(...).
        final OrderExtendPO extendPO = new OrderExtendPO();
        extendPO.setOrderSn("orderSn");
        extendPO.setBranch("branch");
        extendPO.setReceiverProvinceCode("province");
        extendPO.setReceiverAddress("detailAddress");
        extendPO.setReceiverMobile("receiverMobile");
        extendPO.setReceiverCityCode("city");
        extendPO.setReceiverDistrictCode("district");
        extendPO.setReceiverTownCode("receiverTownCode");
        extendPO.setReceiverName("receiverName");
        extendPO.setReceiveBranchCode("receiveBranchCode");
        extendPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        extendPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        extendPO.setWarehouseCode("warehouseCode");
        when(mockOrderExtendModel.getOrderExtendByOrderSn("orderSn")).thenReturn(extendPO);

        // Configure OrderProductModel.getOrderProductByOrderProductId(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsName("goodsName");
        orderProductPO.setProductImage("productImage");
        orderProductPO.setSpecValues("specValues");
        orderProductPO.setProductId(0L);
        orderProductPO.setFinanceRuleCode("financeRuleCode");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        when(mockOrderProductModel.getOrderProductByOrderProductId(0L)).thenReturn(orderProductPO);

        // Configure OrderExchangeMapper.getExchangeOrderList(...).
        final ExchangeOrderDetailDTO example = new ExchangeOrderDetailDTO();
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setOrderSn("orderSn");
        example.setOrderProductId(0L);
        example.setProductNum(0);
        example.setExchangeOrderSn("exchangeOrderSn");
        example.setExchangeOrderProductId(0L);
        example.setExchangeOrderState(0);
        example.setExchangeOrderStateList(Arrays.asList(0));
        example.setAfsSn("afsSn");
        when(mockOrderExchangeMapper.getExchangeOrderList(example)).thenReturn(Collections.emptyList());

        // Run the test
        orderExchangeServiceImplUnderTest.dealCloseExchangeOrder(exchangeDetailPO, userDTO, 0, "content");

        // Verify the results
        // Confirm GoodsStockService.goodsStock(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderSn("orderSn");
        orderPO1.setUserNo("userNo");
        orderPO1.setUserMobile("userMobile");
        orderPO1.setPaySn("paySn");
        orderPO1.setStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setOrderState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setPerformanceModes("performanceModes");
        orderPO1.setExchangeFlag(0);
       /* verify(mockGoodsStockService).goodsStock("orderSn", "exchangeSn", 0L, "areaCode", 0, "financeRuleCode",
                "batchNo", "channelSkuId", "branch", "receiveBranchCode", "warehouseCode",
                EventStockTypeEnum.ORDER_EXCHANGE_REVOKE_OUT_STOCK, BizTypeEnum.EXCHANGE_ORDER_REDUCE_STOCK,
                "operatorName", orderPO1, "channelSkuUnit", "userMobile");*/

        // Confirm OrderModel.cancelOrder(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderSn("orderSn");
        orderPO2.setUserNo("userNo");
        orderPO2.setUserMobile("userMobile");
        orderPO2.setPaySn("paySn");
        orderPO2.setStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setOrderState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setPerformanceModes("performanceModes");
        orderPO2.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO2);
        verify(mockOrderModel).cancelOrder(orderPOList, "content", "cancelRemark", 0, 0L, "operatorName", "content", 0);
        verify(mockOrderExchangeService).updateOrderToExchange("orderSn", 0);
    }

    @Test
    public void testCancelPaidOrder() {
        // Setup
        final OrderExchangeDetailPO exchangeDetailPO = new OrderExchangeDetailPO();
        exchangeDetailPO.setExchangeSn("exchangeSn");
        exchangeDetailPO.setOrderSn("orderSn");
        exchangeDetailPO.setOrderProductId(0L);
        exchangeDetailPO.setProductName("exchangeProductName");
        exchangeDetailPO.setProductNum(0);
        exchangeDetailPO.setAfsSn("afsSn");
        exchangeDetailPO.setExchangeOrderSn("orderSn");
        exchangeDetailPO.setExchangeOrderProductId(0L);
        exchangeDetailPO.setExchangeProductName("exchangeProductName");
        exchangeDetailPO.setExchangeProductNum(0);
        exchangeDetailPO.setRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setActualRefundAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setXzCardAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setStoreActivityAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        exchangeDetailPO.setCreateBy("operatorName");

        final UserDTO userDTO = new UserDTO(0L, "operatorName", 0, 0L, "mobile");

        // Configure IOrderAdminReturnService.adminForceRefund(...).
        final Admin admin = new Admin();
        admin.setAdminId(0);
        admin.setAdminName("operatorName");
        admin.setPhone("mobile");
        admin.setEmail("email");
        admin.setEmployeeNumber("employeeNumber");
        final AdminForceRefundRequest forceRefundReq = new AdminForceRefundRequest();
        forceRefundReq.setOrderSns("orderSn");
        forceRefundReq.setRemark("remark");
        forceRefundReq.setReasonId(0);
        forceRefundReq.setReason("换货撤销");
        forceRefundReq.setProductIds("productIds");
        when(mockOrderAdminReturnService.adminForceRefund(admin, forceRefundReq)).thenReturn("result");

        // Configure OrderAfterMapper.selectOne(...).
        final OrderAfterPO orderAfterPO = new OrderAfterPO();
        orderAfterPO.setAfsId(0);
        orderAfterPO.setAfsSn("afsSn");
        orderAfterPO.setStoreId(0L);
        orderAfterPO.setOrderProductId(0L);
        orderAfterPO.setEnabledFlag(0);
        when(mockOrderAfterMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderAfterPO);

        // Run the test
        orderExchangeServiceImplUnderTest.cancelPaidOrder(exchangeDetailPO, userDTO);

        // Verify the results
        // Confirm IOrderReturnService.revokeRefund(...).
        final RevokeRefundBaseRequest revokeRequest = new RevokeRefundBaseRequest();
        revokeRequest.setAfsSn("afsSn");
        revokeRequest.setChannel("WEB");
        revokeRequest.setRevokeReason(0);
        revokeRequest.setRemark("换货撤销");
        verify(mockOrderReturnService).revokeRefund(revokeRequest, OrderRefundRevokingPartyEnum.PLATFORM_REJECT,
                new OperatorDTO(0, "operatorName", 0, "mobile"));

        // Confirm OrderReturnModel.adminRefundOperation(...).
        final Admin admin1 = new Admin();
        admin1.setAdminId(0);
        admin1.setAdminName("operatorName");
        admin1.setPhone("mobile");
        admin1.setEmail("email");
        admin1.setEmployeeNumber("employeeNumber");
        verify(mockOrderReturnModel).adminRefundOperation(admin1, "afsSn", "换货后的商品售后", "refuseReason", true, "WEB",
                new BigDecimal("0.00"));
    }

    @Test
    public void testExchangeOrderAutoAuditJob() throws Exception {
        // Setup
        // Configure OrderExchangeMapper.getExchangeOrderAutoAuditData(...).
        final OrderExchangePO orderExchangePO = new OrderExchangePO();
        orderExchangePO.setExchangeSn("exchangeSn");
        orderExchangePO.setMemberId(0);
        orderExchangePO.setStoreId(0L);
        orderExchangePO.setExchangeReason("exchangeReason");
        orderExchangePO.setBuyerConfirmFlag(0);
        orderExchangePO.setExchangeOrderState(0);
        orderExchangePO.setApplicantId(0L);
        orderExchangePO.setApplicantName("operatorName");
        orderExchangePO.setApplicantRole(0);
        orderExchangePO.setApproverId(0L);
        orderExchangePO.setApproverName("approverName");
        orderExchangePO.setApproverRole(0);
        orderExchangePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExchangePO.setCreateBy("operatorName");
        orderExchangePO.setUpdateBy("updateBy");
        final List<OrderExchangePO> orderExchangePOList = Arrays.asList(orderExchangePO);
        when(mockOrderExchangeMapper.getExchangeOrderAutoAuditData()).thenReturn(orderExchangePOList);

        // Run the test
        orderExchangeServiceImplUnderTest.exchangeOrderAutoAuditJob();

        // Verify the results
        // Confirm IOrderExchangeService.exchangeOrderAudit(...).
        final OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO.setExchangeAuditState(0);
        orderExchangeAuditDTO.setChannel("value");
        verify(mockOrderExchangeService).exchangeOrderAudit(orderExchangeAuditDTO,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"));
    }

    @Test
    public void testExchangeOrderAutoAuditJob_OrderExchangeMapperReturnsNoItems() throws Exception {
        // Setup
        when(mockOrderExchangeMapper.getExchangeOrderAutoAuditData()).thenReturn(Collections.emptyList());

        // Run the test
        orderExchangeServiceImplUnderTest.exchangeOrderAutoAuditJob();

        // Verify the results
    }

    @Test
    public void testExchangeOrderAutoAuditJob_IOrderExchangeServiceThrowsException() throws Exception {
        // Setup
        // Configure OrderExchangeMapper.getExchangeOrderAutoAuditData(...).
        final OrderExchangePO orderExchangePO = new OrderExchangePO();
        orderExchangePO.setExchangeSn("exchangeSn");
        orderExchangePO.setMemberId(0);
        orderExchangePO.setStoreId(0L);
        orderExchangePO.setExchangeReason("exchangeReason");
        orderExchangePO.setBuyerConfirmFlag(0);
        orderExchangePO.setExchangeOrderState(0);
        orderExchangePO.setApplicantId(0L);
        orderExchangePO.setApplicantName("operatorName");
        orderExchangePO.setApplicantRole(0);
        orderExchangePO.setApproverId(0L);
        orderExchangePO.setApproverName("approverName");
        orderExchangePO.setApproverRole(0);
        orderExchangePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExchangePO.setCreateBy("operatorName");
        orderExchangePO.setUpdateBy("updateBy");
        final List<OrderExchangePO> orderExchangePOList = Arrays.asList(orderExchangePO);
        when(mockOrderExchangeMapper.getExchangeOrderAutoAuditData()).thenReturn(orderExchangePOList);

        // Configure IOrderExchangeService.exchangeOrderAudit(...).
        final OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO.setExchangeAuditState(0);
        orderExchangeAuditDTO.setChannel("value");
        doThrow(Exception.class).when(mockOrderExchangeService).exchangeOrderAudit(orderExchangeAuditDTO,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"));

        // Run the test
        assertThatThrownBy(() -> orderExchangeServiceImplUnderTest.exchangeOrderAutoAuditJob())
                .isInstanceOf(Exception.class);
    }

    @Test
    public void testDealExchangeOrderFinish() {
        // Setup
        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserNo("userNo");
        orderPO.setUserMobile("userMobile");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setPerformanceModes("performanceModes");
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Configure IOrderExchangeDetailService.getExchangeOrderDetailByExample(...).
        final OrderExchangeDetailPO orderExchangeDetailPO = new OrderExchangeDetailPO();
        orderExchangeDetailPO.setExchangeSn("exchangeSn");
        orderExchangeDetailPO.setOrderSn("orderSn");
        orderExchangeDetailPO.setOrderProductId(0L);
        orderExchangeDetailPO.setProductName("exchangeProductName");
        orderExchangeDetailPO.setProductNum(0);
        orderExchangeDetailPO.setAfsSn("afsSn");
        orderExchangeDetailPO.setExchangeOrderSn("orderSn");
        orderExchangeDetailPO.setExchangeOrderProductId(0L);
        orderExchangeDetailPO.setExchangeProductName("exchangeProductName");
        orderExchangeDetailPO.setExchangeProductNum(0);
        orderExchangeDetailPO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setActualRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setCreateBy("operatorName");
        final OrderExchangeDetailExample example = new OrderExchangeDetailExample();
        example.setExchangeDetailId(0);
        example.setExchangeSn("exchangeSn");
        example.setOrderProductIdIn(Arrays.asList(0L));
        example.setAfsSn("afsSn");
        example.setExchangeOrderSn("orderSn");
        when(mockOrderExchangeDetailService.getExchangeOrderDetailByExample(example)).thenReturn(orderExchangeDetailPO);

        // Run the test
        orderExchangeServiceImplUnderTest.dealExchangeOrderFinish("orderSn");

        // Verify the results
        verify(mockOrderLogModel).insertOrderLog(0, 0L, "system", "exchangeSn", 0, 0, 0, "换货完成",
                OrderCreateChannel.WEB);
    }

    @Test
    public void testDealExchangeOrderFinish_OrderModelReturnsNull() {
        // Setup
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(null);

        // Configure IOrderExchangeDetailService.getExchangeOrderDetailByExample(...).
        final OrderExchangeDetailPO orderExchangeDetailPO = new OrderExchangeDetailPO();
        orderExchangeDetailPO.setExchangeSn("exchangeSn");
        orderExchangeDetailPO.setOrderSn("orderSn");
        orderExchangeDetailPO.setOrderProductId(0L);
        orderExchangeDetailPO.setProductName("exchangeProductName");
        orderExchangeDetailPO.setProductNum(0);
        orderExchangeDetailPO.setAfsSn("afsSn");
        orderExchangeDetailPO.setExchangeOrderSn("orderSn");
        orderExchangeDetailPO.setExchangeOrderProductId(0L);
        orderExchangeDetailPO.setExchangeProductName("exchangeProductName");
        orderExchangeDetailPO.setExchangeProductNum(0);
        orderExchangeDetailPO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setActualRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setCreateBy("operatorName");
        final OrderExchangeDetailExample example = new OrderExchangeDetailExample();
        example.setExchangeDetailId(0);
        example.setExchangeSn("exchangeSn");
        example.setOrderProductIdIn(Arrays.asList(0L));
        example.setAfsSn("afsSn");
        example.setExchangeOrderSn("orderSn");
        when(mockOrderExchangeDetailService.getExchangeOrderDetailByExample(example)).thenReturn(orderExchangeDetailPO);

        // Run the test
        orderExchangeServiceImplUnderTest.dealExchangeOrderFinish("orderSn");

        // Verify the results
        verify(mockOrderLogModel).insertOrderLog(0, 0L, "system", "exchangeSn", 0, 0, 0, "换货完成",
                OrderCreateChannel.WEB);
    }

    @Test
    public void testUpdateExchangeApplyStatus() {
        // Setup
        // Configure OrderReturnModel.getOrderReturnByAfsSn(...).
        final OrderReturnPO orderReturnPO = new OrderReturnPO();
        orderReturnPO.setReturnId(0);
        orderReturnPO.setAfsSn("afsSn");
        orderReturnPO.setOrderSn("orderSn");
        orderReturnPO.setStoreId(0L);
        orderReturnPO.setReturnType(0);
        when(mockOrderReturnModel.getOrderReturnByAfsSn("afsSn")).thenReturn(orderReturnPO);

        // Configure IOrderExchangeDetailService.getExchangeOrderDetailByExample(...).
        final OrderExchangeDetailPO orderExchangeDetailPO = new OrderExchangeDetailPO();
        orderExchangeDetailPO.setExchangeSn("exchangeSn");
        orderExchangeDetailPO.setOrderSn("orderSn");
        orderExchangeDetailPO.setOrderProductId(0L);
        orderExchangeDetailPO.setProductName("exchangeProductName");
        orderExchangeDetailPO.setProductNum(0);
        orderExchangeDetailPO.setAfsSn("afsSn");
        orderExchangeDetailPO.setExchangeOrderSn("orderSn");
        orderExchangeDetailPO.setExchangeOrderProductId(0L);
        orderExchangeDetailPO.setExchangeProductName("exchangeProductName");
        orderExchangeDetailPO.setExchangeProductNum(0);
        orderExchangeDetailPO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setActualRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setCreateBy("operatorName");
        final OrderExchangeDetailExample example = new OrderExchangeDetailExample();
        example.setExchangeDetailId(0);
        example.setExchangeSn("exchangeSn");
        example.setOrderProductIdIn(Arrays.asList(0L));
        example.setAfsSn("afsSn");
        example.setExchangeOrderSn("orderSn");
        when(mockOrderExchangeDetailService.getExchangeOrderDetailByExample(example)).thenReturn(orderExchangeDetailPO);

        // Run the test
        orderExchangeServiceImplUnderTest.updateExchangeApplyStatus("afsSn");

        // Verify the results
    }

    @Test
    public void testUpdateExchangeApplyStatus_OrderReturnModelReturnsNull() {
        // Setup
        when(mockOrderReturnModel.getOrderReturnByAfsSn("afsSn")).thenReturn(null);

        // Configure IOrderExchangeDetailService.getExchangeOrderDetailByExample(...).
        final OrderExchangeDetailPO orderExchangeDetailPO = new OrderExchangeDetailPO();
        orderExchangeDetailPO.setExchangeSn("exchangeSn");
        orderExchangeDetailPO.setOrderSn("orderSn");
        orderExchangeDetailPO.setOrderProductId(0L);
        orderExchangeDetailPO.setProductName("exchangeProductName");
        orderExchangeDetailPO.setProductNum(0);
        orderExchangeDetailPO.setAfsSn("afsSn");
        orderExchangeDetailPO.setExchangeOrderSn("orderSn");
        orderExchangeDetailPO.setExchangeOrderProductId(0L);
        orderExchangeDetailPO.setExchangeProductName("exchangeProductName");
        orderExchangeDetailPO.setExchangeProductNum(0);
        orderExchangeDetailPO.setRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setActualRefundAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setXzCardAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderExchangeDetailPO.setCreateBy("operatorName");
        final OrderExchangeDetailExample example = new OrderExchangeDetailExample();
        example.setExchangeDetailId(0);
        example.setExchangeSn("exchangeSn");
        example.setOrderProductIdIn(Arrays.asList(0L));
        example.setAfsSn("afsSn");
        example.setExchangeOrderSn("orderSn");
        when(mockOrderExchangeDetailService.getExchangeOrderDetailByExample(example)).thenReturn(orderExchangeDetailPO);

        // Run the test
        orderExchangeServiceImplUnderTest.updateExchangeApplyStatus("afsSn");

        // Verify the results
    }

    @Test
    public void testUpdateExchangeApplyStatus_IOrderExchangeDetailServiceReturnsNull() {
        // Setup
        // Configure OrderReturnModel.getOrderReturnByAfsSn(...).
        final OrderReturnPO orderReturnPO = new OrderReturnPO();
        orderReturnPO.setReturnId(0);
        orderReturnPO.setAfsSn("afsSn");
        orderReturnPO.setOrderSn("orderSn");
        orderReturnPO.setStoreId(0L);
        orderReturnPO.setReturnType(0);
        when(mockOrderReturnModel.getOrderReturnByAfsSn("afsSn")).thenReturn(orderReturnPO);

        // Configure IOrderExchangeDetailService.getExchangeOrderDetailByExample(...).
        final OrderExchangeDetailExample example = new OrderExchangeDetailExample();
        example.setExchangeDetailId(0);
        example.setExchangeSn("exchangeSn");
        example.setOrderProductIdIn(Arrays.asList(0L));
        example.setAfsSn("afsSn");
        example.setExchangeOrderSn("orderSn");
        when(mockOrderExchangeDetailService.getExchangeOrderDetailByExample(example)).thenReturn(null);

        // Run the test
        orderExchangeServiceImplUnderTest.updateExchangeApplyStatus("afsSn");

        // Verify the results
    }

    @Test
    public void testGetOrderExchangeReturnList() {
        // Setup
        final ExchangeApplyListReq exchangeApplyListReq = new ExchangeApplyListReq();
        final UserDTO userDTO = new UserDTO();
        userDTO.setUserId(0L);
        userDTO.setUserName("operatorName");
        userDTO.setUserRole(0);
        userDTO.setStoreId(0L);
        userDTO.setMobile("mobile");
        exchangeApplyListReq.setUserDTO(userDTO);

        final PagerInfo pager = new PagerInfo(0, 0);
        final ExchangeOrderDetailDTO exchangeOrderDetailDTO = new ExchangeOrderDetailDTO();
        exchangeOrderDetailDTO.setMemberId(0);
        exchangeOrderDetailDTO.setStoreId(0L);
        exchangeOrderDetailDTO.setOrderSn("orderSn");
        exchangeOrderDetailDTO.setOrderProductId(0L);
        exchangeOrderDetailDTO.setProductNum(0);
        exchangeOrderDetailDTO.setExchangeOrderSn("exchangeOrderSn");
        exchangeOrderDetailDTO.setExchangeOrderProductId(0L);
        exchangeOrderDetailDTO.setExchangeOrderState(0);
        exchangeOrderDetailDTO.setExchangeOrderStateList(Arrays.asList(0));
        exchangeOrderDetailDTO.setAfsSn("afsSn");
        final List<OrderExchangeReturnListVO> expectedResult = Arrays.asList(
                new OrderExchangeReturnListVO(exchangeOrderDetailDTO));

        // Configure OrderExchangeMapper.getExchangeOrderListByPage(...).
        final ExchangeOrderDetailDTO exchangeOrderDetailDTO1 = new ExchangeOrderDetailDTO();
        exchangeOrderDetailDTO1.setMemberId(0);
        exchangeOrderDetailDTO1.setStoreId(0L);
        exchangeOrderDetailDTO1.setOrderSn("orderSn");
        exchangeOrderDetailDTO1.setOrderProductId(0L);
        exchangeOrderDetailDTO1.setProductNum(0);
        exchangeOrderDetailDTO1.setExchangeOrderSn("exchangeOrderSn");
        exchangeOrderDetailDTO1.setExchangeOrderProductId(0L);
        exchangeOrderDetailDTO1.setExchangeOrderState(0);
        exchangeOrderDetailDTO1.setExchangeOrderStateList(Arrays.asList(0));
        exchangeOrderDetailDTO1.setAfsSn("afsSn");
        final List<ExchangeOrderDetailDTO> exchangeOrderDetailDTOList = Arrays.asList(exchangeOrderDetailDTO1);
        final ExchangeOrderDetailDTO example = new ExchangeOrderDetailDTO();
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setOrderSn("orderSn");
        example.setOrderProductId(0L);
        example.setProductNum(0);
        example.setExchangeOrderSn("exchangeOrderSn");
        example.setExchangeOrderProductId(0L);
        example.setExchangeOrderState(0);
        example.setExchangeOrderStateList(Arrays.asList(0));
        example.setAfsSn("afsSn");
        when(mockOrderExchangeMapper.getExchangeOrderListByPage(example, 0, 0)).thenReturn(exchangeOrderDetailDTOList);

        // Configure OrderExchangeMapper.getExchangeOrderListCount(...).
        final ExchangeOrderDetailDTO example1 = new ExchangeOrderDetailDTO();
        example1.setMemberId(0);
        example1.setStoreId(0L);
        example1.setOrderSn("orderSn");
        example1.setOrderProductId(0L);
        example1.setProductNum(0);
        example1.setExchangeOrderSn("exchangeOrderSn");
        example1.setExchangeOrderProductId(0L);
        example1.setExchangeOrderState(0);
        example1.setExchangeOrderStateList(Arrays.asList(0));
        example1.setAfsSn("afsSn");
        when(mockOrderExchangeMapper.getExchangeOrderListCount(example1)).thenReturn(0);

        // Configure OrderProductModel.getOrderProductById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setGoodsName("goodsName");
        orderProductPO.setProductImage("productImage");
        orderProductPO.setSpecValues("specValues");
        orderProductPO.setProductId(0L);
        orderProductPO.setFinanceRuleCode("financeRuleCode");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setGoodsAmountTotal(new BigDecimal("0.00"));
        orderProductPO.setMoneyAmount(new BigDecimal("0.00"));
        orderProductPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setXzCardAmount(new BigDecimal("0.00"));
        orderProductPO.setReturnNumber(0);
        orderProductPO.setChannelSkuUnit("channelSkuUnit");
        orderProductPO.setChannelSkuId("channelSkuId");
        orderProductPO.setBatchNo("batchNo");
        when(mockOrderProductModel.getOrderProductById(0L)).thenReturn(orderProductPO);

        // Configure OrderModel.getOrderByOrderSn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderSn("orderSn");
        orderPO.setUserNo("userNo");
        orderPO.setUserMobile("userMobile");
        orderPO.setPaySn("paySn");
        orderPO.setStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setOrderState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setActivityDiscountAmount(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setPlatformVoucherAmount(new BigDecimal("0.00"));
        orderPO.setPlatformActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setPerformanceModes("performanceModes");
        orderPO.setExchangeFlag(0);
        when(mockOrderModel.getOrderByOrderSn("orderSn")).thenReturn(orderPO);

        // Run the test
        final List<OrderExchangeReturnListVO> result = orderExchangeServiceImplUnderTest.getOrderExchangeReturnList(
                exchangeApplyListReq, pager);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOrderExchangeReturnList_OrderExchangeMapperGetExchangeOrderListByPageReturnsNoItems() {
        // Setup
        final ExchangeApplyListReq exchangeApplyListReq = new ExchangeApplyListReq();
        final UserDTO userDTO = new UserDTO();
        userDTO.setUserId(0L);
        userDTO.setUserName("operatorName");
        userDTO.setUserRole(0);
        userDTO.setStoreId(0L);
        userDTO.setMobile("mobile");
        exchangeApplyListReq.setUserDTO(userDTO);

        final PagerInfo pager = new PagerInfo(0, 0);

        // Configure OrderExchangeMapper.getExchangeOrderListByPage(...).
        final ExchangeOrderDetailDTO example = new ExchangeOrderDetailDTO();
        example.setMemberId(0);
        example.setStoreId(0L);
        example.setOrderSn("orderSn");
        example.setOrderProductId(0L);
        example.setProductNum(0);
        example.setExchangeOrderSn("exchangeOrderSn");
        example.setExchangeOrderProductId(0L);
        example.setExchangeOrderState(0);
        example.setExchangeOrderStateList(Arrays.asList(0));
        example.setAfsSn("afsSn");
        when(mockOrderExchangeMapper.getExchangeOrderListByPage(example, 0, 0)).thenReturn(Collections.emptyList());

        // Configure OrderExchangeMapper.getExchangeOrderListCount(...).
        final ExchangeOrderDetailDTO example1 = new ExchangeOrderDetailDTO();
        example1.setMemberId(0);
        example1.setStoreId(0L);
        example1.setOrderSn("orderSn");
        example1.setOrderProductId(0L);
        example1.setProductNum(0);
        example1.setExchangeOrderSn("exchangeOrderSn");
        example1.setExchangeOrderProductId(0L);
        example1.setExchangeOrderState(0);
        example1.setExchangeOrderStateList(Arrays.asList(0));
        example1.setAfsSn("afsSn");
        when(mockOrderExchangeMapper.getExchangeOrderListCount(example1)).thenReturn(0);

        // Run the test
        final List<OrderExchangeReturnListVO> result = orderExchangeServiceImplUnderTest.getOrderExchangeReturnList(
                exchangeApplyListReq, pager);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetExchangeApplyList() {
        // Setup
        final ExchangeApplyListPcReq exchangeApplyListReq = new ExchangeApplyListPcReq();
        final UserDTO userDTO = new UserDTO();
        userDTO.setUserId(0L);
        userDTO.setUserName("operatorName");
        userDTO.setUserRole(0);
        userDTO.setStoreId(0L);
        userDTO.setMobile("mobile");
        exchangeApplyListReq.setUserDTO(userDTO);
        exchangeApplyListReq.setExchangeOrderState(0);
        exchangeApplyListReq.setCustomerId("userId");
        exchangeApplyListReq.setStoreId(0L);
        exchangeApplyListReq.setOrderState(0);

        final PagerInfo pager = new PagerInfo(0, 0);
        final ExchangeOrderDetailDTO exchangeOrderDetailDTO = new ExchangeOrderDetailDTO();
        exchangeOrderDetailDTO.setMemberId(0);
        exchangeOrderDetailDTO.setStoreId(0L);
        exchangeOrderDetailDTO.setOrderSn("orderSn");
        exchangeOrderDetailDTO.setOrderProductId(0L);
        exchangeOrderDetailDTO.setProductNum(0);
        exchangeOrderDetailDTO.setExchangeOrderSn("exchangeOrderSn");
        exchangeOrderDetailDTO.setExchangeOrderProductId(0L);
        exchangeOrderDetailDTO.setExchangeOrderState(0);
        exchangeOrderDetailDTO.setExchangeOrderStateList(Arrays.asList(0));
        exchangeOrderDetailDTO.setAfsSn("afsSn");
        final List<OrderExchangeReturnListVO> expectedResult = Arrays.asList(
                new OrderExchangeReturnListVO(exchangeOrderDetailDTO));

        // Configure OrderExchangeMapper.getExchangeApplyListPageNew(...).
        final ExchangeOrderDetailDTO exchangeOrderDetailDTO1 = new ExchangeOrderDetailDTO();
        exchangeOrderDetailDTO1.setMemberId(0);
        exchangeOrderDetailDTO1.setStoreId(0L);
        exchangeOrderDetailDTO1.setOrderSn("orderSn");
        exchangeOrderDetailDTO1.setOrderProductId(0L);
        exchangeOrderDetailDTO1.setProductNum(0);
        exchangeOrderDetailDTO1.setExchangeOrderSn("exchangeOrderSn");
        exchangeOrderDetailDTO1.setExchangeOrderProductId(0L);
        exchangeOrderDetailDTO1.setExchangeOrderState(0);
        exchangeOrderDetailDTO1.setExchangeOrderStateList(Arrays.asList(0));
        exchangeOrderDetailDTO1.setAfsSn("afsSn");
        final List<OrderExchangeReturnListVO> list = Arrays.asList(
                new OrderExchangeReturnListVO(exchangeOrderDetailDTO1));
        final ExchangeApplyListPcReq example = new ExchangeApplyListPcReq();
        final UserDTO userDTO1 = new UserDTO();
        userDTO1.setUserId(0L);
        userDTO1.setUserName("operatorName");
        userDTO1.setUserRole(0);
        userDTO1.setStoreId(0L);
        userDTO1.setMobile("mobile");
        example.setUserDTO(userDTO1);
        example.setExchangeOrderState(0);
        example.setCustomerId("userId");
        example.setStoreId(0L);
        example.setOrderState(0);
        when(mockOrderExchangeMapper.getExchangeApplyListPageNew(example, 0, 0)).thenReturn(list);

        // Configure OrderExchangeMapper.getExchangeApplyListCountNew(...).
        final ExchangeApplyListPcReq example1 = new ExchangeApplyListPcReq();
        final UserDTO userDTO2 = new UserDTO();
        userDTO2.setUserId(0L);
        userDTO2.setUserName("operatorName");
        userDTO2.setUserRole(0);
        userDTO2.setStoreId(0L);
        userDTO2.setMobile("mobile");
        example1.setUserDTO(userDTO2);
        example1.setExchangeOrderState(0);
        example1.setCustomerId("userId");
        example1.setStoreId(0L);
        example1.setOrderState(0);
        when(mockOrderExchangeMapper.getExchangeApplyListCountNew(example1)).thenReturn(0);

        // Run the test
        final List<OrderExchangeReturnListVO> result = orderExchangeServiceImplUnderTest.getExchangeApplyList(
                exchangeApplyListReq, pager);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetExchangeApplyList_OrderExchangeMapperGetExchangeApplyListPageNewReturnsNoItems() {
        // Setup
        final ExchangeApplyListPcReq exchangeApplyListReq = new ExchangeApplyListPcReq();
        final UserDTO userDTO = new UserDTO();
        userDTO.setUserId(0L);
        userDTO.setUserName("operatorName");
        userDTO.setUserRole(0);
        userDTO.setStoreId(0L);
        userDTO.setMobile("mobile");
        exchangeApplyListReq.setUserDTO(userDTO);
        exchangeApplyListReq.setExchangeOrderState(0);
        exchangeApplyListReq.setCustomerId("userId");
        exchangeApplyListReq.setStoreId(0L);
        exchangeApplyListReq.setOrderState(0);

        final PagerInfo pager = new PagerInfo(0, 0);

        // Configure OrderExchangeMapper.getExchangeApplyListPageNew(...).
        final ExchangeApplyListPcReq example = new ExchangeApplyListPcReq();
        final UserDTO userDTO1 = new UserDTO();
        userDTO1.setUserId(0L);
        userDTO1.setUserName("operatorName");
        userDTO1.setUserRole(0);
        userDTO1.setStoreId(0L);
        userDTO1.setMobile("mobile");
        example.setUserDTO(userDTO1);
        example.setExchangeOrderState(0);
        example.setCustomerId("userId");
        example.setStoreId(0L);
        example.setOrderState(0);
        when(mockOrderExchangeMapper.getExchangeApplyListPageNew(example, 0, 0)).thenReturn(Collections.emptyList());

        // Configure OrderExchangeMapper.getExchangeApplyListCountNew(...).
        final ExchangeApplyListPcReq example1 = new ExchangeApplyListPcReq();
        final UserDTO userDTO2 = new UserDTO();
        userDTO2.setUserId(0L);
        userDTO2.setUserName("operatorName");
        userDTO2.setUserRole(0);
        userDTO2.setStoreId(0L);
        userDTO2.setMobile("mobile");
        example1.setUserDTO(userDTO2);
        example1.setExchangeOrderState(0);
        example1.setCustomerId("userId");
        example1.setStoreId(0L);
        example1.setOrderState(0);
        when(mockOrderExchangeMapper.getExchangeApplyListCountNew(example1)).thenReturn(0);

        // Run the test
        final List<OrderExchangeReturnListVO> result = orderExchangeServiceImplUnderTest.getExchangeApplyList(
                exchangeApplyListReq, pager);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testExchangeOrderAutoCancelJob() throws Exception {
        // Setup
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure OrderExchangeMapper.exchangeOrderAutoCancelData(...).
        final OrderExchangePO orderExchangePO = new OrderExchangePO();
        orderExchangePO.setExchangeSn("exchangeSn");
        orderExchangePO.setMemberId(0);
        orderExchangePO.setStoreId(0L);
        orderExchangePO.setExchangeReason("exchangeReason");
        orderExchangePO.setBuyerConfirmFlag(0);
        orderExchangePO.setExchangeOrderState(0);
        orderExchangePO.setApplicantId(0L);
        orderExchangePO.setApplicantName("operatorName");
        orderExchangePO.setApplicantRole(0);
        orderExchangePO.setApproverId(0L);
        orderExchangePO.setApproverName("approverName");
        orderExchangePO.setApproverRole(0);
        orderExchangePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExchangePO.setCreateBy("operatorName");
        orderExchangePO.setUpdateBy("updateBy");
        final List<OrderExchangePO> orderExchangePOList = Arrays.asList(orderExchangePO);
        when(mockOrderExchangeMapper.exchangeOrderAutoCancelData("applicationTime")).thenReturn(orderExchangePOList);

        // Run the test
        orderExchangeServiceImplUnderTest.exchangeOrderAutoCancelJob();

        // Verify the results
        // Confirm IOrderExchangeService.exchangeOrderAudit(...).
        final OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO.setExchangeAuditState(0);
        orderExchangeAuditDTO.setChannel("value");
        verify(mockOrderExchangeService).exchangeOrderAudit(orderExchangeAuditDTO,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"));
    }

    @Test
    public void testExchangeOrderAutoCancelJob_OrderExchangeMapperReturnsNoItems() throws Exception {
        // Setup
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);
        when(mockOrderExchangeMapper.exchangeOrderAutoCancelData("applicationTime"))
                .thenReturn(Collections.emptyList());

        // Run the test
        orderExchangeServiceImplUnderTest.exchangeOrderAutoCancelJob();

        // Verify the results
    }

    @Test
    public void testExchangeOrderAutoCancelJob_IOrderExchangeServiceThrowsException() throws Exception {
        // Setup
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure OrderExchangeMapper.exchangeOrderAutoCancelData(...).
        final OrderExchangePO orderExchangePO = new OrderExchangePO();
        orderExchangePO.setExchangeSn("exchangeSn");
        orderExchangePO.setMemberId(0);
        orderExchangePO.setStoreId(0L);
        orderExchangePO.setExchangeReason("exchangeReason");
        orderExchangePO.setBuyerConfirmFlag(0);
        orderExchangePO.setExchangeOrderState(0);
        orderExchangePO.setApplicantId(0L);
        orderExchangePO.setApplicantName("operatorName");
        orderExchangePO.setApplicantRole(0);
        orderExchangePO.setApproverId(0L);
        orderExchangePO.setApproverName("approverName");
        orderExchangePO.setApproverRole(0);
        orderExchangePO.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderExchangePO.setCreateBy("operatorName");
        orderExchangePO.setUpdateBy("updateBy");
        final List<OrderExchangePO> orderExchangePOList = Arrays.asList(orderExchangePO);
        when(mockOrderExchangeMapper.exchangeOrderAutoCancelData("applicationTime")).thenReturn(orderExchangePOList);

        // Configure IOrderExchangeService.exchangeOrderAudit(...).
        final OrderExchangeAuditDTO orderExchangeAuditDTO = new OrderExchangeAuditDTO();
        orderExchangeAuditDTO.setExchangeSn("exchangeSn");
        orderExchangeAuditDTO.setExchangeAuditState(0);
        orderExchangeAuditDTO.setChannel("value");
        doThrow(Exception.class).when(mockOrderExchangeService).exchangeOrderAudit(orderExchangeAuditDTO,
                new UserDTO(0L, "operatorName", 0, 0L, "mobile"));

        // Run the test
        assertThatThrownBy(() -> orderExchangeServiceImplUnderTest.exchangeOrderAutoCancelJob())
                .isInstanceOf(Exception.class);
    }
}

