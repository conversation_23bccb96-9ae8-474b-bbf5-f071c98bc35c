package com.cfpamf.ms.mallorder.service;

import com.cfpamf.ms.mallLogistic.dto.ExpressDTO;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.integration.logistic.LogisticIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.model.OrderExtendModel;
import com.cfpamf.ms.mallorder.po.OrderExtendPO;
import com.cfpamf.ms.mallorder.service.impl.OrderServiceImpl;
import com.cfpamf.ms.mallorder.vo.OrderFrontDeliveryVO;
import com.cfpamf.ms.mallorder.vo.OrderProductDeliveryVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderDeliveryTest {
	@Mock
	private OrderProductMapper orderProductMapper;
	@Mock
	private OrderExtendModel orderExtendModel;
	@Mock
	private OrderLocalUtils orderLocalUtils;
	@Mock
	private StringRedisTemplate stringRedisTemplate;
	@Mock
	private LogisticIntegration logisticIntegration;

	@InjectMocks
	private OrderServiceImpl orderService;

	@Test(expected = RuntimeException.class)
	public void testFrontDeliveryListWithEmptyOrderProduct() {
		List<OrderProductDeliveryVO> emptyList = Collections.emptyList();
		when(orderProductMapper.orderProductDelivery(anyString(), anyLong())).thenReturn(emptyList);

		orderService.frontDeliveryList("order_sn", 123L);
	}

	@Test
	public void testFrontDeliveryList() {
		OrderProductDeliveryVO productDeliveryVO1 = new OrderProductDeliveryVO();
		productDeliveryVO1.setExpressNumber("express_number_1");
		productDeliveryVO1.setLogisticId(123L);
		productDeliveryVO1.setDeliverType(1);
		productDeliveryVO1.setOrderProductId(1L);
		productDeliveryVO1.setProductImage("product_image_1");
		productDeliveryVO1.setProductNum(3);
		productDeliveryVO1.setReturnNumber(0);

		OrderProductDeliveryVO productDeliveryVO2 = new OrderProductDeliveryVO();
		productDeliveryVO2.setExpressNumber("express_number_2");
		productDeliveryVO2.setLogisticId(null);
		productDeliveryVO2.setDeliverType(1);
		productDeliveryVO2.setOrderProductId(2L);
		productDeliveryVO2.setProductImage("product_image_2");
		productDeliveryVO2.setProductNum(3);
		productDeliveryVO2.setReturnNumber(0);

		List<OrderProductDeliveryVO> productDeliveryVOS = Arrays.asList(productDeliveryVO1, productDeliveryVO2);

		when(orderProductMapper.orderProductDelivery(anyString(), anyLong())).thenReturn(productDeliveryVOS);

		OrderExtendPO orderExtendPO = new OrderExtendPO();
		orderExtendPO.setReceiverMobile("13112341234");
		when(orderExtendModel.getOrderExtendByOrderSn(anyString())).thenReturn(orderExtendPO);

		ExpressDTO expressDTO = new ExpressDTO();
//		when(logisticIntegration.getTracks(Mockito.any())).thenReturn(expressDTO);

		List<OrderFrontDeliveryVO> frontDeliveryVOs = orderService.frontDeliveryList("order_sn", 123L);

		assertEquals(1, frontDeliveryVOs.size());

		OrderFrontDeliveryVO frontDeliveryVO = frontDeliveryVOs.get(0);
		assertEquals("13112341234", frontDeliveryVO.getOrderExtendVO().getReceiverMobile());
		assertEquals("express_number_1", frontDeliveryVO.getExpressNumber());
		assertEquals(Arrays.asList(1L), frontDeliveryVO.getOrderProductIds());
		assertEquals(1, frontDeliveryVO.getOrderProductBaseVOS().size());
	}
}
