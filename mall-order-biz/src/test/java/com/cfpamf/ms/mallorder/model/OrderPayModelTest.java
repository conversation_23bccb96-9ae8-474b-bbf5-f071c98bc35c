package com.cfpamf.ms.mallorder.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.cfpamf.common.ms.result.CommonError;
import com.cfpamf.common.ms.result.ErrorContext;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.customer.facade.request.bankcard.QueryBankcardInfoReq;
import com.cfpamf.ms.customer.facade.vo.bankcard.BankcardVo;
import com.cfpamf.ms.mall.account.api.StmAccountFacade;
import com.cfpamf.ms.mall.account.enums.AccountCardTypeEnum;
import com.cfpamf.ms.mall.account.request.AccountQuery;
import com.cfpamf.ms.mall.account.vo.AccountCard;
import com.cfpamf.ms.mallmember.api.MemberBalanceLogFeignClient;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.config.ChannelFeeRateConfig;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.controller.fegin.facade.AccountCardFacade;
import com.cfpamf.ms.mallorder.controller.fegin.facade.CustomerServiceFeign;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderEventEnum;
import com.cfpamf.ms.mallorder.integration.omsbase.OmsBaseIntegration;
import com.cfpamf.ms.mallorder.integration.settlement.BillOperatinIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.mapper.OrderPayMapper;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.mapper.OrderPromotionSendCouponMapper;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.PayWayChangeRequest;
import com.cfpamf.ms.mallorder.request.OrderPayExample;
import com.cfpamf.ms.mallorder.request.OrderPromotionSendCouponExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.service.impl.OrderCreateHelper;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.v2.service.PayService;
import com.cfpamf.ms.mallorder.v2.strategy.context.OrderPayProcessStrategyContext;
import com.cfpamf.ms.mallorder.vo.OrderPayInfoVO;
import com.cfpamf.ms.mallpromotion.api.CouponFeignClient;
import com.cfpamf.ms.mallpromotion.api.CouponMemberFeignClient;
import com.cfpamf.ms.mallpromotion.api.CouponUseLogFeignClient;
import com.cfpamf.ms.mallpromotion.api.PromotionCommonFeignClient;
import com.cfpamf.ms.mallpromotion.request.Coupon;
import com.cfpamf.ms.mallpromotion.request.CouponMember;
import com.cfpamf.ms.mallpromotion.request.CouponUseLog;
import com.cfpamf.ms.mallpromotion.vo.CouponVO;
import com.cfpamf.ms.mallshop.api.StoreBindCategoryFeignClient;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.request.StoreBindCategoryExample;
import com.cfpamf.ms.mallshop.resp.Store;
import com.cfpamf.ms.mallshop.resp.StoreBindCategory;
import com.cfpamf.ms.mallshop.resp.StoreContractReceiptInfoVO;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.starter.mq.entity.MessageSendProperty;
import com.slodon.bbc.starter.mq.entity.MessageSendVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderPayModelTest {

    @Mock
    private ChannelFeeRateConfig mockChannelFeeRateConfig;
    @Mock
    private OrderPayMapper mockOrderPayMapper;
    @Mock
    private OrderMapper mockOrderMapper;
    @Mock
    private OrderPromotionSendCouponMapper mockOrderPromotionSendCouponMapper;
    @Mock
    private OrderProductMapper mockOrderProductMapper;
    @Mock
    private OrderLogModel mockOrderLogModel;
    @Mock
    private OrderProductModel mockOrderProductModel;
    @Mock
    private MemberFeignClient mockMemberFeignClient;
    @Mock
    private MemberBalanceLogFeignClient mockMemberBalanceLogFeignClient;
    @Mock
    private PromotionCommonFeignClient mockPromotionCommonFeignClient;
    @Mock
    private CouponFeignClient mockCouponFeignClient;
    @Mock
    private CouponMemberFeignClient mockCouponMemberFeignClient;
    @Mock
    private CouponUseLogFeignClient mockCouponUseLogFeignClient;
    @Mock
    private RabbitTemplate mockRabbitTemplate;
    @Mock
    private OrderModel mockOrderModel;
    @Mock
    private IOrderService mockOrderService;
    @Mock
    private OrderCreateHelper mockOrderCreateHelper;
    @Mock
    private IOrderProductService mockOrderProductService;
    @Mock
    private IOrderPayService mockOrderPayService;
    @Mock
    private IOrderReturnService mockOrderReturnService;
    @Mock
    private IBzBankTransferService mockBzBankTransferService;
    @Mock
    private IOrderExtendFinanceService mockFinanceService;
    @Mock
    private ITaskQueueService mockTaskQueueService;
    @Mock
    private StoreFeignClient mockStoreFeignClient;
    @Mock
    private AccountCardFacade mockAccountCardFacade;
    @Mock
    private CustomerServiceFeign mockCustomerServiceFeign;
    @Mock
    private ILoanResultService mockLoanResultService;
    @Mock
    private BillOperatinIntegration mockBillOperatinIntegration;
    @Mock
    private StmAccountFacade mockStmAccountFacade;
    @Mock
    private OrderPayProcessStrategyContext mockOrderPayProcessStrategyContext;
    @Mock
    private PayService mockPayService;
    @Mock
    private BankTransferModel mockBankTransferModel;
    @Mock
    private OrderOfflineService mockOrderOfflineService;
    @Mock
    private IOrderAmountStateRecordService mockOrderAmountRecordService;
    @Mock
    private OrderPresellService mockOrderPresellService;
    @Mock
    private OmsBaseIntegration mockOmsBaseIntegration;
    @Mock
    private IOrderAmountStateRecordService mockIOrderAmountStateRecordService;
    @Mock
    private IBzOldUserPoolService mockIBzOldUserPoolService;
    @Mock
    private StoreBindCategoryFeignClient mockStoreBindCategoryFeignClient;

    @InjectMocks
    private OrderPayModel orderPayModelUnderTest;

    @Test
    void testSaveOrderPay() throws Exception {
        // Setup
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");

        // Configure OrderPayMapper.insert(...).
        final OrderPayPO entity = new OrderPayPO();
        entity.setPayId(0);
        entity.setPaySn("paySn");
        entity.setOrderSn("pOrderSn");
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setMemberId(0);
        entity.setApiPayState("0");
        entity.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setTradeSn("memberName");
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setLoanSuccess(0);
        entity.setEnjoyPayVipFlag(0);
        entity.setPayWayExtraInfo(new JSONObject(0, false));
        entity.setOutBizSource("channel");
        entity.setOutBizId("paySn");
        //when(mockOrderPayMapper.insert(entity)).thenReturn(1);

        // Run the test
        final Integer result = orderPayModelUnderTest.saveOrderPay(orderPayPO);

        // Verify the results
        assertThat(result).isEqualTo(1);
    }

    @Test
    void testDeleteOrderPay() throws Exception {
        // Setup
        when(mockOrderPayMapper.deleteByPrimaryKey("paySn")).thenReturn(1);

        // Run the test
        final Integer result = orderPayModelUnderTest.deleteOrderPay("paySn");

        // Verify the results
        assertThat(result).isEqualTo(1);
    }

    @Test
    void testUpdateOrderPay() throws Exception {
        // Setup
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");

        // Configure OrderPayMapper.updateByPrimaryKeySelective(...).
        final OrderPayPO record = new OrderPayPO();
        record.setPayId(0);
        record.setPaySn("paySn");
        record.setOrderSn("pOrderSn");
        record.setPayAmount(new BigDecimal("0.00"));
        record.setMemberId(0);
        record.setApiPayState("0");
        record.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setTradeSn("memberName");
        record.setPaymentName("paymentName");
        record.setPaymentCode("paymentCode");
        record.setLoanSuccess(0);
        record.setEnjoyPayVipFlag(0);
        record.setPayWayExtraInfo(new JSONObject(0, false));
        record.setOutBizSource("channel");
        record.setOutBizId("paySn");
        when(mockOrderPayMapper.updateByPrimaryKeySelective(record)).thenReturn(1);

        // Run the test
        final Integer result = orderPayModelUnderTest.updateOrderPay(orderPayPO);

        // Verify the results
        assertThat(result).isEqualTo(1);
    }

    @Test
    void testGetOrderPayByPaySn() throws Exception {
        // Setup
        final OrderPayPO expectedResult = new OrderPayPO();
        expectedResult.setPayId(0);
        expectedResult.setPaySn("paySn");
        expectedResult.setOrderSn("pOrderSn");
        expectedResult.setPayAmount(new BigDecimal("0.00"));
        expectedResult.setMemberId(0);
        expectedResult.setApiPayState("0");
        expectedResult.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setTradeSn("memberName");
        expectedResult.setPaymentName("paymentName");
        expectedResult.setPaymentCode("paymentCode");
        expectedResult.setLoanSuccess(0);
        expectedResult.setEnjoyPayVipFlag(0);
        expectedResult.setPayWayExtraInfo(new JSONObject(0, false));
        expectedResult.setOutBizSource("channel");
        expectedResult.setOutBizId("paySn");

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        // Run the test
        final OrderPayPO result = orderPayModelUnderTest.getOrderPayByPaySn("paySn");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetOrderPayList() throws Exception {
        // Setup
        final OrderPayExample example = new OrderPayExample();
        example.setPaySnNotEquals("paySnNotEquals");
        example.setPaySnIn("paySnIn");
        example.setPayId(0);
        example.setPaySn("paySn");
        example.setPaySnLike("paySnLike");

        final PagerInfo pager = new PagerInfo(10, 1);
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        final List<OrderPayPO> expectedResult = Arrays.asList(orderPayPO);

        // Configure OrderPayMapper.countByExample(...).
        final OrderPayExample example1 = new OrderPayExample();
        example1.setPaySnNotEquals("paySnNotEquals");
        example1.setPaySnIn("paySnIn");
        example1.setPayId(0);
        example1.setPaySn("paySn");
        example1.setPaySnLike("paySnLike");
        when(mockOrderPayMapper.countByExample(example1)).thenReturn(1);

        // Configure OrderPayMapper.listPageByExample(...).
        final OrderPayPO orderPayPO1 = new OrderPayPO();
        orderPayPO1.setPayId(0);
        orderPayPO1.setPaySn("paySn");
        orderPayPO1.setOrderSn("pOrderSn");
        orderPayPO1.setPayAmount(new BigDecimal("0.00"));
        orderPayPO1.setMemberId(0);
        orderPayPO1.setApiPayState("0");
        orderPayPO1.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO1.setTradeSn("memberName");
        orderPayPO1.setPaymentName("paymentName");
        orderPayPO1.setPaymentCode("paymentCode");
        orderPayPO1.setLoanSuccess(0);
        orderPayPO1.setEnjoyPayVipFlag(0);
        orderPayPO1.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO1.setOutBizSource("channel");
        orderPayPO1.setOutBizId("paySn");
        final List<OrderPayPO> orderPayPOS = Arrays.asList(orderPayPO1);
        final OrderPayExample example2 = new OrderPayExample();
        example2.setPaySnNotEquals("paySnNotEquals");
        example2.setPaySnIn("paySnIn");
        example2.setPayId(0);
        example2.setPaySn("paySn");
        example2.setPaySnLike("paySnLike");
        when(mockOrderPayMapper.listPageByExample(example2, 0, 10)).thenReturn(orderPayPOS);

        // Configure OrderPayMapper.listByExample(...).
        final OrderPayPO orderPayPO2 = new OrderPayPO();
        orderPayPO2.setPayId(0);
        orderPayPO2.setPaySn("paySn");
        orderPayPO2.setOrderSn("pOrderSn");
        orderPayPO2.setPayAmount(new BigDecimal("0.00"));
        orderPayPO2.setMemberId(0);
        orderPayPO2.setApiPayState("0");
        orderPayPO2.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO2.setTradeSn("memberName");
        orderPayPO2.setPaymentName("paymentName");
        orderPayPO2.setPaymentCode("paymentCode");
        orderPayPO2.setLoanSuccess(0);
        orderPayPO2.setEnjoyPayVipFlag(0);
        orderPayPO2.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO2.setOutBizSource("channel");
        orderPayPO2.setOutBizId("paySn");
        final List<OrderPayPO> orderPayPOS1 = Arrays.asList(orderPayPO2);
        final OrderPayExample example3 = new OrderPayExample();
        example3.setPaySnNotEquals("paySnNotEquals");
        example3.setPaySnIn("paySnIn");
        example3.setPayId(0);
        example3.setPaySn("paySn");
        example3.setPaySnLike("paySnLike");
        // when(mockOrderPayMapper.listByExample(example3)).thenReturn(orderPayPOS1);

        // Run the test
        final List<OrderPayPO> result = orderPayModelUnderTest.getOrderPayList(example, pager);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetOrderPayList_OrderPayMapperListPageByExampleReturnsNoItems() throws Exception {
        // Setup
        final OrderPayExample example = new OrderPayExample();
        example.setPaySnNotEquals("paySnNotEquals");
        example.setPaySnIn("paySnIn");
        example.setPayId(0);
        example.setPaySn("paySn");
        example.setPaySnLike("paySnLike");

        final PagerInfo pager = new PagerInfo(10, 1);

        // Configure OrderPayMapper.countByExample(...).
        final OrderPayExample example1 = new OrderPayExample();
        example1.setPaySnNotEquals("paySnNotEquals");
        example1.setPaySnIn("paySnIn");
        example1.setPayId(0);
        example1.setPaySn("paySn");
        example1.setPaySnLike("paySnLike");
        when(mockOrderPayMapper.countByExample(example1)).thenReturn(0);

        // Configure OrderPayMapper.listPageByExample(...).
        final OrderPayExample example2 = new OrderPayExample();
        example2.setPaySnNotEquals("paySnNotEquals");
        example2.setPaySnIn("paySnIn");
        example2.setPayId(0);
        example2.setPaySn("paySn");
        example2.setPaySnLike("paySnLike");
        when(mockOrderPayMapper.listPageByExample(example2, 0, 10)).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderPayPO> result = orderPayModelUnderTest.getOrderPayList(example, pager);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetOrderPayList_OrderPayMapperListByExampleReturnsNoItems() throws Exception {
        // Setup
        final OrderPayExample example = new OrderPayExample();
        example.setPaySnNotEquals("paySnNotEquals");
        example.setPaySnIn("paySnIn");
        example.setPayId(0);
        example.setPaySn("paySn");
        example.setPaySnLike("paySnLike");

        final PagerInfo pager = new PagerInfo(10, 1);

        // Configure OrderPayMapper.listByExample(...).
        final OrderPayExample example1 = new OrderPayExample();
        example1.setPaySnNotEquals("paySnNotEquals");
        example1.setPaySnIn("paySnIn");
        example1.setPayId(0);
        example1.setPaySn("paySn");
        example1.setPaySnLike("paySnLike");
        when(mockOrderPayMapper.listByExample(example1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderPayPO> result = orderPayModelUnderTest.getOrderPayList(example, null);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testBuildOrderPayPO() throws Exception {
        // Setup
        final OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        final OrderSubmitParamDTO paramDTO = new OrderSubmitParamDTO();
        paramDTO.setChannel("channel");
        consumerDTO.setParamDTO(paramDTO);
        final ChannelOrderSubmitDTO channelOrderSubmitDTO = new ChannelOrderSubmitDTO();
        channelOrderSubmitDTO.setOutBizSource("channel");
        channelOrderSubmitDTO.setOutBizId("paySn");
        consumerDTO.setChannelOrderSubmitDTO(channelOrderSubmitDTO);

        final Cart cart = new Cart();
        cart.setCartId(0);
        cart.setMemberId(0);
        cart.setStoreId(0L);
        cart.setStoreName("storeName");
        cart.setGoodsId(0L);
        cart.setIsVirtualGoods(8);
        final OrderSubmitParamDTO submitParamDTO = new OrderSubmitParamDTO();
        submitParamDTO.setChannel("channel");
        submitParamDTO.setUsrNo("usrNo");
        submitParamDTO.setSource(0);
        submitParamDTO.setOrderFrom(0);
        submitParamDTO.setAddressId(0);
        final OrderSubmitDTO orderSubmitDTO = new OrderSubmitDTO(Arrays.asList(cart), submitParamDTO);

        final OrderPayPO expectedResult = new OrderPayPO();
        expectedResult.setPayId(0);
        expectedResult.setPaySn("paySn");
        expectedResult.setOrderSn("pOrderSn");
        expectedResult.setPayAmount(new BigDecimal("0.00"));
        expectedResult.setMemberId(0);
        expectedResult.setApiPayState("0");
        expectedResult.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setTradeSn("memberName");
        expectedResult.setPaymentName("paymentName");
        expectedResult.setPaymentCode("paymentCode");
        expectedResult.setLoanSuccess(0);
        expectedResult.setEnjoyPayVipFlag(0);
        expectedResult.setPayWayExtraInfo(new JSONObject(0, false));
        expectedResult.setOutBizSource("channel");
        expectedResult.setOutBizId("paySn");

        // Run the test
        final OrderPayPO result = orderPayModelUnderTest.buildOrderPayPO(consumerDTO, orderSubmitDTO, "pOrderSn",
                "paySn", new BigDecimal("0.00"), 0);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    void testOrderPayCheck() throws Exception {
        // Setup
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName(PayMethodEnum.WXPAY.getDesc());
        orderPayPO.setPaymentCode(PayMethodEnum.WXPAY.getValue());
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");

        final Member member = new Member();
        member.setMemberId(0);
        member.setMemberName("adminName");
        member.setLastPaymentCode("BALANCE");
        member.setBalanceAvailable(new BigDecimal("0.00"));
        member.setBalanceFrozen(new BigDecimal("0.00"));
        member.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final BizUserInfoDTO bizUserInfoDTO = new BizUserInfoDTO();
        bizUserInfoDTO.setUserId(0);
        bizUserInfoDTO.setEmployeeId(0);
        bizUserInfoDTO.setHrUserId(0);
        bizUserInfoDTO.setAccount("account");
        bizUserInfoDTO.setUserName("userName");


        final Member stationMaster = new Member();
        stationMaster.setMemberId(10);
        stationMaster.setMemberName("zhanzhang");
        stationMaster.setLastPaymentCode("BALANCE");
        stationMaster.setBalanceAvailable(new BigDecimal("0.00"));
        stationMaster.setBalanceFrozen(new BigDecimal("0.00"));
        stationMaster.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName(PayMethodEnum.WXPAY.getDesc());
        orderPO.setPaymentCode(PayMethodEnum.WXPAY.getValue());
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);

        // Run the test
        orderPayModelUnderTest.orderPayCheck(orderPayPO, member, bizUserInfoDTO, stationMaster, orderPOList, PayMethodEnum.WXPAY);

        // Verify the results
    }

    @Test
    void testPayWayChange() throws Exception {
        // Setup
        final Member member = new Member();
        member.setMemberId(0);
        member.setMemberName("adminName");
        member.setLastPaymentCode("BALANCE");
        member.setBalanceAvailable(new BigDecimal("0.00"));
        member.setBalanceFrozen(new BigDecimal("0.00"));
        member.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final PayWayChangeRequest payRequest = new PayWayChangeRequest();
        payRequest.setPaySn("paySn");
        payRequest.setPayMethod(PayMethodEnum.WXPAY);
        final OrderPayInfoVO.EnjoyPayExtraInfo enjoyPayExtraInfo = new OrderPayInfoVO.EnjoyPayExtraInfo();
        enjoyPayExtraInfo.setRepaymentDay("repaymentDay");
        enjoyPayExtraInfo.setRepaymentMode("repaymentMode");
        enjoyPayExtraInfo.setRepaymentModeDesc("repaymentModeDesc");
        enjoyPayExtraInfo.setLoanPeriod(0);
        enjoyPayExtraInfo.setEnjoyPayVipFlag(0);
        payRequest.setEnjoyPayExtraInfo(enjoyPayExtraInfo);
        final OrderPayInfoVO.BankTransferInfo bankTransferInfo = new OrderPayInfoVO.BankTransferInfo();
        bankTransferInfo.setPaymentAccount("paymentAccount");
        bankTransferInfo.setPaymentName("paymentName");
        bankTransferInfo.setPaymentBankName("paymentBankName");
        bankTransferInfo.setPaymentBankCode("paymentBankCode");
        payRequest.setBankTransferInfo(bankTransferInfo);

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName(PayMethodEnum.WXPAY.getDesc());
        orderPayPO.setPaymentCode(PayMethodEnum.WXPAY.getValue());
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        // Configure IOrderService.listByPaySn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName(PayMethodEnum.WXPAY.getDesc());
        orderPO.setPaymentCode(PayMethodEnum.WXPAY.getValue());
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        when(mockOrderService.listByPaySn("paySn")).thenReturn(orderPOList);

        // Configure IOrderService.update(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName(PayMethodEnum.WXPAY.getDesc());
        entity.setPaymentCode(PayMethodEnum.WXPAY.getValue());
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.update(any(), any())).thenReturn(true);

        // Configure IOrderPayService.update(...).
        final OrderPayPO entity1 = new OrderPayPO();
        entity1.setPayId(0);
        entity1.setPaySn("paySn");
        entity1.setOrderSn("pOrderSn");
        entity1.setPayAmount(new BigDecimal("0.00"));
        entity1.setMemberId(0);
        entity1.setApiPayState("0");
        entity1.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity1.setTradeSn("memberName");
        entity1.setPaymentName(PayMethodEnum.WXPAY.getDesc());
        entity1.setPaymentCode(PayMethodEnum.WXPAY.getValue());
        entity1.setLoanSuccess(0);
        entity1.setEnjoyPayVipFlag(0);
        entity1.setPayWayExtraInfo(new JSONObject(0, false));
        entity1.setOutBizSource("channel");
        entity1.setOutBizId("paySn");
        when(mockOrderPayService.update(any(), any())).thenReturn(true);

        // Run the test
        final Boolean result = orderPayModelUnderTest.payWayChange(member, payRequest);

        // Verify the results
        assertThat(result).isTrue();
        // verify(mockOrderService).setCustomerUnConfirmStatus("memberName");
    }

   /* @Test
    void testPayWayChange_IOrderServiceListByPaySnReturnsNoItems() throws Exception {
        // Setup
        final Member member = new Member();
        member.setMemberId(0);
        member.setMemberName("adminName");
        member.setLastPaymentCode("BALANCE");
        member.setBalanceAvailable(new BigDecimal("0.00"));
        member.setBalanceFrozen(new BigDecimal("0.00"));
        member.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final PayWayChangeRequest payRequest = new PayWayChangeRequest();
        payRequest.setPaySn("paySn");
        payRequest.setPayMethod(PayMethodEnum.WXPAY);
        final OrderPayInfoVO.EnjoyPayExtraInfo enjoyPayExtraInfo = new OrderPayInfoVO.EnjoyPayExtraInfo();
        enjoyPayExtraInfo.setRepaymentDay("repaymentDay");
        enjoyPayExtraInfo.setRepaymentMode("repaymentMode");
        enjoyPayExtraInfo.setRepaymentModeDesc("repaymentModeDesc");
        enjoyPayExtraInfo.setLoanPeriod(0);
        enjoyPayExtraInfo.setEnjoyPayVipFlag(0);
        payRequest.setEnjoyPayExtraInfo(enjoyPayExtraInfo);
        final OrderPayInfoVO.BankTransferInfo bankTransferInfo = new OrderPayInfoVO.BankTransferInfo();
        bankTransferInfo.setPaymentAccount("paymentAccount");
        bankTransferInfo.setPaymentName("paymentName");
        bankTransferInfo.setPaymentBankName("paymentBankName");
        bankTransferInfo.setPaymentBankCode("paymentBankCode");
        payRequest.setBankTransferInfo(bankTransferInfo);

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        when(mockOrderService.listByPaySn("paySn")).thenReturn(Collections.emptyList());

        // Configure IOrderPayService.update(...).
        final OrderPayPO entity = new OrderPayPO();
        entity.setPayId(0);
        entity.setPaySn("paySn");
        entity.setOrderSn("pOrderSn");
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setMemberId(0);
        entity.setApiPayState("0");
        entity.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setTradeSn("memberName");
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setLoanSuccess(0);
        entity.setEnjoyPayVipFlag(0);
        entity.setPayWayExtraInfo(new JSONObject(0, false));
        entity.setOutBizSource("channel");
        entity.setOutBizId("paySn");
        when(mockOrderPayService.update(eq(entity), any(LambdaUpdateWrapper.class))).thenReturn(true);

        // Run the test
        final Boolean result = orderPayModelUnderTest.payWayChange(member, payRequest);

        // Verify the results
        assertThat(result).isFalse();
    }
*/
   /* @Test
    void testPayWayChange_IOrderServiceUpdateReturnsFalse() throws Exception {
        // Setup
        final Member member = new Member();
        member.setMemberId(0);
        member.setMemberName("adminName");
        member.setLastPaymentCode("BALANCE");
        member.setBalanceAvailable(new BigDecimal("0.00"));
        member.setBalanceFrozen(new BigDecimal("0.00"));
        member.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final PayWayChangeRequest payRequest = new PayWayChangeRequest();
        payRequest.setPaySn("paySn");
        payRequest.setPayMethod(PayMethodEnum.WXPAY);
        final OrderPayInfoVO.EnjoyPayExtraInfo enjoyPayExtraInfo = new OrderPayInfoVO.EnjoyPayExtraInfo();
        enjoyPayExtraInfo.setRepaymentDay("repaymentDay");
        enjoyPayExtraInfo.setRepaymentMode("repaymentMode");
        enjoyPayExtraInfo.setRepaymentModeDesc("repaymentModeDesc");
        enjoyPayExtraInfo.setLoanPeriod(0);
        enjoyPayExtraInfo.setEnjoyPayVipFlag(0);
        payRequest.setEnjoyPayExtraInfo(enjoyPayExtraInfo);
        final OrderPayInfoVO.BankTransferInfo bankTransferInfo = new OrderPayInfoVO.BankTransferInfo();
        bankTransferInfo.setPaymentAccount("paymentAccount");
        bankTransferInfo.setPaymentName("paymentName");
        bankTransferInfo.setPaymentBankName("paymentBankName");
        bankTransferInfo.setPaymentBankCode("paymentBankCode");
        payRequest.setBankTransferInfo(bankTransferInfo);

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        // Configure IOrderService.listByPaySn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        when(mockOrderService.listByPaySn("paySn")).thenReturn(orderPOList);

        // Configure IOrderService.update(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.update(eq(entity), any(LambdaUpdateWrapper.class))).thenReturn(false);

        // Run the test
//        assertThatThrownBy(() -> orderPayModelUnderTest.payWayChange(member, payRequest))
//                .isInstanceOf(BusinessException.class);
    }
*/
  /*  @Test
    void testPayWayChange_IOrderPayServiceReturnsFalse() throws Exception {
        // Setup
        final Member member = new Member();
        member.setMemberId(0);
        member.setMemberName("adminName");
        member.setLastPaymentCode("BALANCE");
        member.setBalanceAvailable(new BigDecimal("0.00"));
        member.setBalanceFrozen(new BigDecimal("0.00"));
        member.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final PayWayChangeRequest payRequest = new PayWayChangeRequest();
        payRequest.setPaySn("paySn");
        payRequest.setPayMethod(PayMethodEnum.WXPAY);
        final OrderPayInfoVO.EnjoyPayExtraInfo enjoyPayExtraInfo = new OrderPayInfoVO.EnjoyPayExtraInfo();
        enjoyPayExtraInfo.setRepaymentDay("repaymentDay");
        enjoyPayExtraInfo.setRepaymentMode("repaymentMode");
        enjoyPayExtraInfo.setRepaymentModeDesc("repaymentModeDesc");
        enjoyPayExtraInfo.setLoanPeriod(0);
        enjoyPayExtraInfo.setEnjoyPayVipFlag(0);
        payRequest.setEnjoyPayExtraInfo(enjoyPayExtraInfo);
        final OrderPayInfoVO.BankTransferInfo bankTransferInfo = new OrderPayInfoVO.BankTransferInfo();
        bankTransferInfo.setPaymentAccount("paymentAccount");
        bankTransferInfo.setPaymentName("paymentName");
        bankTransferInfo.setPaymentBankName("paymentBankName");
        bankTransferInfo.setPaymentBankCode("paymentBankCode");
        payRequest.setBankTransferInfo(bankTransferInfo);

        // Configure OrderPayMapper.getByPrimaryKey(...).
        final OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPayId(0);
        orderPayPO.setPaySn("paySn");
        orderPayPO.setOrderSn("pOrderSn");
        orderPayPO.setPayAmount(new BigDecimal("0.00"));
        orderPayPO.setMemberId(0);
        orderPayPO.setApiPayState("0");
        orderPayPO.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPayPO.setTradeSn("memberName");
        orderPayPO.setPaymentName("paymentName");
        orderPayPO.setPaymentCode("paymentCode");
        orderPayPO.setLoanSuccess(0);
        orderPayPO.setEnjoyPayVipFlag(0);
        orderPayPO.setPayWayExtraInfo(new JSONObject(0, false));
        orderPayPO.setOutBizSource("channel");
        orderPayPO.setOutBizId("paySn");
        when(mockOrderPayMapper.getByPrimaryKey("paySn")).thenReturn(orderPayPO);

        // Configure IOrderService.listByPaySn(...).
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        when(mockOrderService.listByPaySn("paySn")).thenReturn(orderPOList);

        // Configure IOrderService.update(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.update(eq(entity), any(LambdaUpdateWrapper.class))).thenReturn(true);

        // Configure IOrderPayService.update(...).
        final OrderPayPO entity1 = new OrderPayPO();
        entity1.setPayId(0);
        entity1.setPaySn("paySn");
        entity1.setOrderSn("pOrderSn");
        entity1.setPayAmount(new BigDecimal("0.00"));
        entity1.setMemberId(0);
        entity1.setApiPayState("0");
        entity1.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity1.setTradeSn("memberName");
        entity1.setPaymentName("paymentName");
        entity1.setPaymentCode("paymentCode");
        entity1.setLoanSuccess(0);
        entity1.setEnjoyPayVipFlag(0);
        entity1.setPayWayExtraInfo(new JSONObject(0, false));
        entity1.setOutBizSource("channel");
        entity1.setOutBizId("paySn");
        when(mockOrderPayService.update(eq(entity1), any(LambdaUpdateWrapper.class))).thenReturn(false);

        // Run the test
//        assertThatThrownBy(() -> orderPayModelUnderTest.payWayChange(member, payRequest))
//                .isInstanceOf(BusinessException.class);
        //       verify(mockOrderService).setCustomerUnConfirmStatus("memberName");
    }
*/
    @Test
    void testCheckAndGetPayWayExtraInfo() throws Exception {
        // Setup
        final PayWayChangeRequest payRequest = new PayWayChangeRequest();
        payRequest.setPaySn("paySn");
        payRequest.setPayMethod(PayMethodEnum.WXPAY);
        final OrderPayInfoVO.EnjoyPayExtraInfo enjoyPayExtraInfo = new OrderPayInfoVO.EnjoyPayExtraInfo();
        enjoyPayExtraInfo.setRepaymentDay("repaymentDay");
        enjoyPayExtraInfo.setRepaymentMode("repaymentMode");
        enjoyPayExtraInfo.setRepaymentModeDesc("repaymentModeDesc");
        enjoyPayExtraInfo.setLoanPeriod(0);
        enjoyPayExtraInfo.setEnjoyPayVipFlag(0);
        payRequest.setEnjoyPayExtraInfo(enjoyPayExtraInfo);
        final OrderPayInfoVO.BankTransferInfo bankTransferInfo = new OrderPayInfoVO.BankTransferInfo();
        bankTransferInfo.setPaymentAccount("paymentAccount");
        bankTransferInfo.setPaymentName("paymentName");
        bankTransferInfo.setPaymentBankName("paymentBankName");
        bankTransferInfo.setPaymentBankCode("paymentBankCode");
        payRequest.setBankTransferInfo(bankTransferInfo);

        final JSONObject expectedResult = new JSONObject(0, false);

        // Run the test
        final JSONObject result = orderPayModelUnderTest.checkAndGetPayWayExtraInfo(payRequest);

        // Verify the results
        //    assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testEnjoyPayInfo() throws Exception {
        // Setup
        final OrderPayInfoVO.EnjoyPayExtraInfo enjoyPayExtraInfo = new OrderPayInfoVO.EnjoyPayExtraInfo("repaymentDay",
                "repaymentMode", "repaymentModeDesc", 0, 0);
        final JSONObject expectedResult = new JSONObject(0, false);

        // Run the test
        final JSONObject result = orderPayModelUnderTest.enjoyPayInfo(enjoyPayExtraInfo);

        // Verify the results
        //  assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testBankTransferInfo() throws Exception {
        // Setup
        final OrderPayInfoVO.BankTransferInfo bankTransferInfo = new OrderPayInfoVO.BankTransferInfo("paymentAccount",
                "paymentName", "paymentBankName", "paymentBankCode");
        final JSONObject expectedResult = new JSONObject(0, false);

        // Run the test
        final JSONObject result = orderPayModelUnderTest.bankTransferInfo(bankTransferInfo);

        // Verify the results
       // assertThat(result).isEqualTo(expectedResult);
    }

   /* @Test
    void testBalancePay() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final Member memberDb = new Member();
        memberDb.setMemberId(0);
        memberDb.setMemberName("adminName");
        memberDb.setLastPaymentCode("BALANCE");
        memberDb.setBalanceAvailable(new BigDecimal("0.00"));
        memberDb.setBalanceFrozen(new BigDecimal("0.00"));
        memberDb.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure BillOperatinIntegration.detailByBankAccount(...).
        final AccountCard accountCard = new AccountCard();
        accountCard.setCardId(0L);
        accountCard.setStoreId("storeId");
        accountCard.setStoreName("storeName");
        accountCard.setOrgType("orgType");
        accountCard.setBankAccountNumber("bankAccountNumber");
        when(mockBillOperatinIntegration.detailByBankAccount("UIN_PLF_STORE_ID101",
                AccountCardTypeEnum.UNI_JS_PLF_SUP)).thenReturn(accountCard);

        // Configure IBzBankTransferService.list(...).
        final BzBankTransferPO bzBankTransferPO = new BzBankTransferPO();
        bzBankTransferPO.setEnabledFlag(0);
        bzBankTransferPO.setPaySn("paySn");
        bzBankTransferPO.setOutOrderNo("outOrderNo");
        bzBankTransferPO.setPayAmount(new BigDecimal("0.00"));
        bzBankTransferPO.setReceiptAccount("receiptAccount");
        final List<BzBankTransferPO> bzBankTransferPOS = Arrays.asList(bzBankTransferPO);
        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(bzBankTransferPOS);

        // Configure StoreFeignClient.getStoreContractReciptInfo(...).
        final StoreContractReceiptInfoVO storeContractReceiptInfoVO = new StoreContractReceiptInfoVO();
        storeContractReceiptInfoVO.setWxSellerId("wxSellerId");
        storeContractReceiptInfoVO.setAliSellerId("aliSellerId");
        final Store store = new Store();
        store.setAcctId("acctId");
        storeContractReceiptInfoVO.setStore(store);
        storeContractReceiptInfoVO.setRecommentWxSellerId("recommentWxSellerId");
        storeContractReceiptInfoVO.setRecommentAliSellerId("recommentAliSellerId");
        when(mockStoreFeignClient.getStoreContractReciptInfo(0L)).thenReturn(storeContractReceiptInfoVO);

        // Configure CustomerServiceFeign.info(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final BankcardVo bankcardVo = new BankcardVo();
        bankcardVo.setCardId("cardId");
        bankcardVo.setCardNo("cardNo");
        bankcardVo.setAcctName("acctName");
        bankcardVo.setBankCode("bankCode");
        bankcardVo.setBankName("bankName");
        final Result<BankcardVo> bankcardVoResult = new Result<>(false, errorContext, bankcardVo);
        final QueryBankcardInfoReq var1 = new QueryBankcardInfoReq();
        var1.setCardId("cardId");
        when(mockCustomerServiceFeign.info(var1)).thenReturn(bankcardVoResult);

        // Configure StmAccountFacade.detailV2(...).
        final AccountQuery accountQuery = new AccountQuery();
        accountQuery.setCurrent(0);
        accountQuery.setPageSize(0);
        accountQuery.setAccountId("accountId");
        accountQuery.setStoreId("UIN_PLF_STORE_ID101");
        accountQuery.setAccountType("accountType");
        when(mockStmAccountFacade.detailV2(accountQuery)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderOfflineService.queryOrderOfflineList(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setReceiptAccount("receiptAccount");
        orderOfflinePO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflinePO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderOfflinePO> orderOfflinePOS = Arrays.asList(orderOfflinePO);
        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(orderOfflinePOS);

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        // Configure OrderPayMapper.updateByPrimaryKeySelective(...).
        final OrderPayPO record = new OrderPayPO();
        record.setPayId(0);
        record.setPaySn("paySn");
        record.setOrderSn("pOrderSn");
        record.setPayAmount(new BigDecimal("0.00"));
        record.setMemberId(0);
        record.setApiPayState("0");
        record.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setTradeSn("memberName");
        record.setPaymentName("paymentName");
        record.setPaymentCode("paymentCode");
        record.setLoanSuccess(0);
        record.setEnjoyPayVipFlag(0);
        record.setPayWayExtraInfo(new JSONObject(0, false));
        record.setOutBizSource("channel");
        record.setOutBizId("paySn");
        when(mockOrderPayMapper.updateByPrimaryKeySelective(record)).thenReturn(0);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext1, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Configure OrderMapper.updateByExampleSelective(...).
        final OrderPO record1 = new OrderPO();
        record1.setOrderId(0);
        record1.setOrderSn("memberName");
        record1.setUserNo("userNo");
        record1.setPaySn("paySn");
        record1.setSellerId("sellerId");
        record1.setBankPayTrxNo("bankPayTrxNo");
        record1.setStoreId(0L);
        record1.setRecommendStoreId(0L);
        record1.setMemberName("memberName");
        record1.setMemberId(0);
        record1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record1.setOrderState(0);
        record1.setLoanPayState(0);
        record1.setPaymentName("paymentName");
        record1.setPaymentCode("paymentCode");
        record1.setOrderAmount(new BigDecimal("0.00"));
        record1.setGoodsAmount(new BigDecimal("0.00"));
        record1.setExpressFee(new BigDecimal("0.00"));
        record1.setStoreVoucherAmount(new BigDecimal("0.00"));
        record1.setStoreActivityAmount(new BigDecimal("0.00"));
        record1.setXzCardAmount(new BigDecimal("0.00"));
        record1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        record1.setComposePayName("composeWay");
        record1.setBalanceAmount(new BigDecimal("0.00"));
        record1.setPayAmount(new BigDecimal("0.00"));
        record1.setAreaCode("areaCode");
        record1.setOrderType(0);
        record1.setServiceFee(new BigDecimal("0.00"));
        record1.setServiceFeeRate(new BigDecimal("0.00"));
        record1.setSettleMode("settleMode");
        record1.setFinanceRuleCode("financeRuleCode");
        record1.setIsDelivery(0);
        record1.setChannel("channel");
        record1.setChannelServiceFee(new BigDecimal("0.00"));
        record1.setNewOrder(false);
        record1.setCustomerConfirmStatus(0);
        record1.setOrderPlaceUserRoleCode(0);
        record1.setExchangeFlag(0);
        final OrderExample example1 = new OrderExample();
        example1.setOrderIdNotEquals(0);
        example1.setUserNo("userNo");
        example1.setUserMobile("userMobile");
        example1.setOrderSn("memberName");
        example1.setPaySn("paySn");
        when(mockOrderMapper.updateByExampleSelective(record1, example1)).thenReturn(0);

        // Run the test
        final BigDecimal result = orderPayModelUnderTest.balancePay(orderPOList, memberDb);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));

        // Confirm MemberBalanceLogFeignClient.saveMemberBalanceLog(...).
        final MemberBalanceLogVO memberBalanceLog = new MemberBalanceLogVO();
        memberBalanceLog.setMemberId(0);
        memberBalanceLog.setMemberName("adminName");
        memberBalanceLog.setAfterChangeAmount(new BigDecimal("0.00"));
        memberBalanceLog.setChangeValue(new BigDecimal("0.00"));
        memberBalanceLog.setFreezeAmount(new BigDecimal("0.00"));
        memberBalanceLog.setFreezeValue(new BigDecimal("0.00"));
        memberBalanceLog.setType(0);
        memberBalanceLog.setDescription("description");
        memberBalanceLog.setAdminId(0);
        memberBalanceLog.setAdminName("adminName");
        memberBalanceLog.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockMemberBalanceLogFeignClient).saveMemberBalanceLog(memberBalanceLog);

        // Confirm MemberFeignClient.updateMember(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setMemberName("adminName");
        member.setLastPaymentCode("BALANCE");
        member.setBalanceAvailable(new BigDecimal("0.00"));
        member.setBalanceFrozen(new BigDecimal("0.00"));
        member.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockMemberFeignClient).updateMember(member);
    }
*/
  /*  @Test
    void testBalancePay_StoreBindCategoryFeignClientReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final Member memberDb = new Member();
        memberDb.setMemberId(0);
        memberDb.setMemberName("adminName");
        memberDb.setLastPaymentCode("BALANCE");
        memberDb.setBalanceAvailable(new BigDecimal("0.00"));
        memberDb.setBalanceFrozen(new BigDecimal("0.00"));
        memberDb.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(Collections.emptyList());

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure BillOperatinIntegration.detailByBankAccount(...).
        final AccountCard accountCard = new AccountCard();
        accountCard.setCardId(0L);
        accountCard.setStoreId("storeId");
        accountCard.setStoreName("storeName");
        accountCard.setOrgType("orgType");
        accountCard.setBankAccountNumber("bankAccountNumber");
        when(mockBillOperatinIntegration.detailByBankAccount("UIN_PLF_STORE_ID101",
                AccountCardTypeEnum.UNI_JS_PLF_SUP)).thenReturn(accountCard);

        // Configure IBzBankTransferService.list(...).
        final BzBankTransferPO bzBankTransferPO = new BzBankTransferPO();
        bzBankTransferPO.setEnabledFlag(0);
        bzBankTransferPO.setPaySn("paySn");
        bzBankTransferPO.setOutOrderNo("outOrderNo");
        bzBankTransferPO.setPayAmount(new BigDecimal("0.00"));
        bzBankTransferPO.setReceiptAccount("receiptAccount");
        final List<BzBankTransferPO> bzBankTransferPOS = Arrays.asList(bzBankTransferPO);
        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(bzBankTransferPOS);

        // Configure StoreFeignClient.getStoreContractReciptInfo(...).
        final StoreContractReceiptInfoVO storeContractReceiptInfoVO = new StoreContractReceiptInfoVO();
        storeContractReceiptInfoVO.setWxSellerId("wxSellerId");
        storeContractReceiptInfoVO.setAliSellerId("aliSellerId");
        final Store store = new Store();
        store.setAcctId("acctId");
        storeContractReceiptInfoVO.setStore(store);
        storeContractReceiptInfoVO.setRecommentWxSellerId("recommentWxSellerId");
        storeContractReceiptInfoVO.setRecommentAliSellerId("recommentAliSellerId");
        when(mockStoreFeignClient.getStoreContractReciptInfo(0L)).thenReturn(storeContractReceiptInfoVO);

        // Configure CustomerServiceFeign.info(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final BankcardVo bankcardVo = new BankcardVo();
        bankcardVo.setCardId("cardId");
        bankcardVo.setCardNo("cardNo");
        bankcardVo.setAcctName("acctName");
        bankcardVo.setBankCode("bankCode");
        bankcardVo.setBankName("bankName");
        final Result<BankcardVo> bankcardVoResult = new Result<>(false, errorContext, bankcardVo);
        final QueryBankcardInfoReq var1 = new QueryBankcardInfoReq();
        var1.setCardId("cardId");
        when(mockCustomerServiceFeign.info(var1)).thenReturn(bankcardVoResult);

        // Configure StmAccountFacade.detailV2(...).
        final AccountQuery accountQuery = new AccountQuery();
        accountQuery.setCurrent(0);
        accountQuery.setPageSize(0);
        accountQuery.setAccountId("accountId");
        accountQuery.setStoreId("UIN_PLF_STORE_ID101");
        accountQuery.setAccountType("accountType");
        when(mockStmAccountFacade.detailV2(accountQuery)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderOfflineService.queryOrderOfflineList(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setReceiptAccount("receiptAccount");
        orderOfflinePO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflinePO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderOfflinePO> orderOfflinePOS = Arrays.asList(orderOfflinePO);
        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(orderOfflinePOS);

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext1, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        final BigDecimal result = orderPayModelUnderTest.balancePay(orderPOList, memberDb);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));

        // Confirm MemberFeignClient.updateMember(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setMemberName("adminName");
        member.setLastPaymentCode("BALANCE");
        member.setBalanceAvailable(new BigDecimal("0.00"));
        member.setBalanceFrozen(new BigDecimal("0.00"));
        member.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockMemberFeignClient).updateMember(member);
    }
*/
  /*  @Test
    void testBalancePay_IBzBankTransferServiceReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final Member memberDb = new Member();
        memberDb.setMemberId(0);
        memberDb.setMemberName("adminName");
        memberDb.setLastPaymentCode("BALANCE");
        memberDb.setBalanceAvailable(new BigDecimal("0.00"));
        memberDb.setBalanceFrozen(new BigDecimal("0.00"));
        memberDb.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        final BigDecimal result = orderPayModelUnderTest.balancePay(orderPOList, memberDb);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));

        // Confirm MemberFeignClient.updateMember(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setMemberName("adminName");
        member.setLastPaymentCode("BALANCE");
        member.setBalanceAvailable(new BigDecimal("0.00"));
        member.setBalanceFrozen(new BigDecimal("0.00"));
        member.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockMemberFeignClient).updateMember(member);
    }
*/
   /* @Test
    void testBalancePay_OrderOfflineServiceReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final Member memberDb = new Member();
        memberDb.setMemberId(0);
        memberDb.setMemberName("adminName");
        memberDb.setLastPaymentCode("BALANCE");
        memberDb.setBalanceAvailable(new BigDecimal("0.00"));
        memberDb.setBalanceFrozen(new BigDecimal("0.00"));
        memberDb.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(Collections.emptyList());

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        final BigDecimal result = orderPayModelUnderTest.balancePay(orderPOList, memberDb);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));

        // Confirm MemberFeignClient.updateMember(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setMemberName("adminName");
        member.setLastPaymentCode("BALANCE");
        member.setBalanceAvailable(new BigDecimal("0.00"));
        member.setBalanceFrozen(new BigDecimal("0.00"));
        member.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockMemberFeignClient).updateMember(member);
    }*/

   /* @Test
    void testBalancePay_IOrderServiceListReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final Member memberDb = new Member();
        memberDb.setMemberId(0);
        memberDb.setMemberName("adminName");
        memberDb.setLastPaymentCode("BALANCE");
        memberDb.setBalanceAvailable(new BigDecimal("0.00"));
        memberDb.setBalanceFrozen(new BigDecimal("0.00"));
        memberDb.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure OrderPayMapper.updateByPrimaryKeySelective(...).
        final OrderPayPO record = new OrderPayPO();
        record.setPayId(0);
        record.setPaySn("paySn");
        record.setOrderSn("pOrderSn");
        record.setPayAmount(new BigDecimal("0.00"));
        record.setMemberId(0);
        record.setApiPayState("0");
        record.setCallbackTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        record.setTradeSn("memberName");
        record.setPaymentName("paymentName");
        record.setPaymentCode("paymentCode");
        record.setLoanSuccess(0);
        record.setEnjoyPayVipFlag(0);
        record.setPayWayExtraInfo(new JSONObject(0, false));
        record.setOutBizSource("channel");
        record.setOutBizId("paySn");
        when(mockOrderPayMapper.updateByPrimaryKeySelective(record)).thenReturn(0);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO1);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO2)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        final BigDecimal result = orderPayModelUnderTest.balancePay(orderPOList, memberDb);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO3, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));

        // Confirm MemberFeignClient.updateMember(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setMemberName("adminName");
        member.setLastPaymentCode("BALANCE");
        member.setBalanceAvailable(new BigDecimal("0.00"));
        member.setBalanceFrozen(new BigDecimal("0.00"));
        member.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockMemberFeignClient).updateMember(member);
    }
*/
   /* @Test
    void testBalancePay_OrderProductMapperSelectListReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final Member memberDb = new Member();
        memberDb.setMemberId(0);
        memberDb.setMemberName("adminName");
        memberDb.setLastPaymentCode("BALANCE");
        memberDb.setBalanceAvailable(new BigDecimal("0.00"));
        memberDb.setBalanceFrozen(new BigDecimal("0.00"));
        memberDb.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS);

        // Run the test
        final BigDecimal result = orderPayModelUnderTest.balancePay(orderPOList, memberDb);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));

        // Confirm MemberFeignClient.updateMember(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setMemberName("adminName");
        member.setLastPaymentCode("BALANCE");
        member.setBalanceAvailable(new BigDecimal("0.00"));
        member.setBalanceFrozen(new BigDecimal("0.00"));
        member.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockMemberFeignClient).updateMember(member);
    }*/

   /* @Test
    void testBalancePay_OrderPromotionSendCouponMapperReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final Member memberDb = new Member();
        memberDb.setMemberId(0);
        memberDb.setMemberName("adminName");
        memberDb.setLastPaymentCode("BALANCE");
        memberDb.setBalanceAvailable(new BigDecimal("0.00"));
        memberDb.setBalanceFrozen(new BigDecimal("0.00"));
        memberDb.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(Collections.emptyList());

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        final BigDecimal result = orderPayModelUnderTest.balancePay(orderPOList, memberDb);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));

        // Confirm MemberFeignClient.updateMember(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setMemberName("adminName");
        member.setLastPaymentCode("BALANCE");
        member.setBalanceAvailable(new BigDecimal("0.00"));
        member.setBalanceFrozen(new BigDecimal("0.00"));
        member.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockMemberFeignClient).updateMember(member);
    }
*/
  /*  @Test
    void testBalancePay_OrderProductModelGetOrderProductListByOrderSnReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final Member memberDb = new Member();
        memberDb.setMemberId(0);
        memberDb.setMemberName("adminName");
        memberDb.setLastPaymentCode("BALANCE");
        memberDb.setBalanceAvailable(new BigDecimal("0.00"));
        memberDb.setBalanceFrozen(new BigDecimal("0.00"));
        memberDb.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = orderPayModelUnderTest.balancePay(orderPOList, memberDb);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));

        // Confirm MemberFeignClient.updateMember(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setMemberName("adminName");
        member.setLastPaymentCode("BALANCE");
        member.setBalanceAvailable(new BigDecimal("0.00"));
        member.setBalanceFrozen(new BigDecimal("0.00"));
        member.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockMemberFeignClient).updateMember(member);
    }
*/
  /*  @Test
    void testBalancePay_RabbitTemplateThrowsAmqpException() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO);
        final Member memberDb = new Member();
        memberDb.setMemberId(0);
        memberDb.setMemberName("adminName");
        memberDb.setLastPaymentCode("BALANCE");
        memberDb.setBalanceAvailable(new BigDecimal("0.00"));
        memberDb.setBalanceFrozen(new BigDecimal("0.00"));
        memberDb.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList1 = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList1);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        doThrow(AmqpException.class).when(mockRabbitTemplate).convertAndSend("newmall_exchange",
                "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));

        // Run the test
        final BigDecimal result = orderPayModelUnderTest.balancePay(orderPOList, memberDb);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Confirm MemberFeignClient.updateMember(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setMemberName("adminName");
        member.setLastPaymentCode("BALANCE");
        member.setBalanceAvailable(new BigDecimal("0.00"));
        member.setBalanceFrozen(new BigDecimal("0.00"));
        member.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockMemberFeignClient).updateMember(member);
    }
*/
   /* @Test
    void testOrderPaySuccess() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure BillOperatinIntegration.detailByBankAccount(...).
        final AccountCard accountCard = new AccountCard();
        accountCard.setCardId(0L);
        accountCard.setStoreId("storeId");
        accountCard.setStoreName("storeName");
        accountCard.setOrgType("orgType");
        accountCard.setBankAccountNumber("bankAccountNumber");
        when(mockBillOperatinIntegration.detailByBankAccount("UIN_PLF_STORE_ID101",
                AccountCardTypeEnum.UNI_JS_PLF_SUP)).thenReturn(accountCard);

        // Configure IBzBankTransferService.list(...).
        final BzBankTransferPO bzBankTransferPO = new BzBankTransferPO();
        bzBankTransferPO.setEnabledFlag(0);
        bzBankTransferPO.setPaySn("paySn");
        bzBankTransferPO.setOutOrderNo("outOrderNo");
        bzBankTransferPO.setPayAmount(new BigDecimal("0.00"));
        bzBankTransferPO.setReceiptAccount("receiptAccount");
        final List<BzBankTransferPO> bzBankTransferPOS = Arrays.asList(bzBankTransferPO);
        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(bzBankTransferPOS);

        // Configure StoreFeignClient.getStoreContractReciptInfo(...).
        final StoreContractReceiptInfoVO storeContractReceiptInfoVO = new StoreContractReceiptInfoVO();
        storeContractReceiptInfoVO.setWxSellerId("wxSellerId");
        storeContractReceiptInfoVO.setAliSellerId("aliSellerId");
        final Store store = new Store();
        store.setAcctId("acctId");
        storeContractReceiptInfoVO.setStore(store);
        storeContractReceiptInfoVO.setRecommentWxSellerId("recommentWxSellerId");
        storeContractReceiptInfoVO.setRecommentAliSellerId("recommentAliSellerId");
        when(mockStoreFeignClient.getStoreContractReciptInfo(0L)).thenReturn(storeContractReceiptInfoVO);

        // Configure CustomerServiceFeign.info(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final BankcardVo bankcardVo = new BankcardVo();
        bankcardVo.setCardId("cardId");
        bankcardVo.setCardNo("cardNo");
        bankcardVo.setAcctName("acctName");
        bankcardVo.setBankCode("bankCode");
        bankcardVo.setBankName("bankName");
        final Result<BankcardVo> bankcardVoResult = new Result<>(false, errorContext, bankcardVo);
        final QueryBankcardInfoReq var1 = new QueryBankcardInfoReq();
        var1.setCardId("cardId");
        when(mockCustomerServiceFeign.info(var1)).thenReturn(bankcardVoResult);

        // Configure StmAccountFacade.detailV2(...).
        final AccountQuery accountQuery = new AccountQuery();
        accountQuery.setCurrent(0);
        accountQuery.setPageSize(0);
        accountQuery.setAccountId("accountId");
        accountQuery.setStoreId("UIN_PLF_STORE_ID101");
        accountQuery.setAccountType("accountType");
        when(mockStmAccountFacade.detailV2(accountQuery)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderOfflineService.queryOrderOfflineList(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setReceiptAccount("receiptAccount");
        orderOfflinePO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflinePO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderOfflinePO> orderOfflinePOS = Arrays.asList(orderOfflinePO);
        when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(orderOfflinePOS);

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList);

        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext1, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        orderPayModelUnderTest.orderPaySuccess(orderPO, "memberName", "paymentCode", "paymentName",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "payNo");

        // Verify the results
        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
                new BigDecimal("0.00"));
        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
                OrderCreateChannel.H5);
        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }*/

    @Test
    void testOrderPaySuccess_StoreBindCategoryFeignClientReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        //when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        //when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        //when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
         //       .thenReturn(Collections.emptyList());

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        //when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        //when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        //when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        //when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        //when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure BillOperatinIntegration.detailByBankAccount(...).
        final AccountCard accountCard = new AccountCard();
        accountCard.setCardId(0L);
        accountCard.setStoreId("storeId");
        accountCard.setStoreName("storeName");
        accountCard.setOrgType("orgType");
        accountCard.setBankAccountNumber("bankAccountNumber");
        //when(mockBillOperatinIntegration.detailByBankAccount("UIN_PLF_STORE_ID101",
         //       AccountCardTypeEnum.UNI_JS_PLF_SUP)).thenReturn(accountCard);

        // Configure IBzBankTransferService.list(...).
        final BzBankTransferPO bzBankTransferPO = new BzBankTransferPO();
        bzBankTransferPO.setEnabledFlag(0);
        bzBankTransferPO.setPaySn("paySn");
        bzBankTransferPO.setOutOrderNo("outOrderNo");
        bzBankTransferPO.setPayAmount(new BigDecimal("0.00"));
        bzBankTransferPO.setReceiptAccount("receiptAccount");
        final List<BzBankTransferPO> bzBankTransferPOS = Arrays.asList(bzBankTransferPO);
        //when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(bzBankTransferPOS);

        // Configure StoreFeignClient.getStoreContractReciptInfo(...).
        final StoreContractReceiptInfoVO storeContractReceiptInfoVO = new StoreContractReceiptInfoVO();
        storeContractReceiptInfoVO.setWxSellerId("wxSellerId");
        storeContractReceiptInfoVO.setAliSellerId("aliSellerId");
        final Store store = new Store();
        store.setAcctId("acctId");
        storeContractReceiptInfoVO.setStore(store);
        storeContractReceiptInfoVO.setRecommentWxSellerId("recommentWxSellerId");
        storeContractReceiptInfoVO.setRecommentAliSellerId("recommentAliSellerId");
        //when(mockStoreFeignClient.getStoreContractReciptInfo(0L)).thenReturn(storeContractReceiptInfoVO);

        // Configure CustomerServiceFeign.info(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final BankcardVo bankcardVo = new BankcardVo();
        bankcardVo.setCardId("cardId");
        bankcardVo.setCardNo("cardNo");
        bankcardVo.setAcctName("acctName");
        bankcardVo.setBankCode("bankCode");
        bankcardVo.setBankName("bankName");
        final Result<BankcardVo> bankcardVoResult = new Result<>(false, errorContext, bankcardVo);
        final QueryBankcardInfoReq var1 = new QueryBankcardInfoReq();
        var1.setCardId("cardId");
        //when(mockCustomerServiceFeign.info(var1)).thenReturn(bankcardVoResult);

        // Configure StmAccountFacade.detailV2(...).
        final AccountQuery accountQuery = new AccountQuery();
        accountQuery.setCurrent(0);
        accountQuery.setPageSize(0);
        accountQuery.setAccountId("accountId");
        accountQuery.setStoreId("UIN_PLF_STORE_ID101");
        accountQuery.setAccountType("accountType");
        //when(mockStmAccountFacade.detailV2(accountQuery)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderOfflineService.queryOrderOfflineList(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setReceiptAccount("receiptAccount");
        orderOfflinePO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflinePO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderOfflinePO> orderOfflinePOS = Arrays.asList(orderOfflinePO);
        //when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(orderOfflinePOS);

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        //when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList);

        //when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
         //       "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        //when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        //when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        //when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        //when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        //when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext1, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        //when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        //when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
//        orderPayModelUnderTest.orderPaySuccess(orderPO, "memberName", "paymentCode", "paymentName",
//                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "payNo");

        // Verify the results
        //verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
         //       new BigDecimal("0.00"));
        //verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
         //       OrderCreateChannel.H5);
        //verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        //verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        //verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        //verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        //verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
    //    verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
        //        new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
//                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
//                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
//                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }

    @Test
    void testOrderPaySuccess_IBzBankTransferServiceReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

//        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
//        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
//        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
//                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
//        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);
//
//        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        //when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
//        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);
//
//        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        //when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList);

//        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
//                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        //when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        //when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        //when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(Collections.emptyList());

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        //when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(null);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        //when(mockOrderService.getById(0)).thenReturn(null);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        //when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
//        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(Collections.emptyList());
//
//        // Run the test
//        orderPayModelUnderTest.orderPaySuccess(orderPO, "memberName", "paymentCode", "paymentName",
//                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "payNo");

        // Verify the results
//        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
//                new BigDecimal("0.00"));
//        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
//                OrderCreateChannel.H5);
//        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
//        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        //verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        //verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        //verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
//        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
//                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
//                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
//                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
//                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }

    @Test
    void testOrderPaySuccess_OrderOfflineServiceReturnsNoItems() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        //when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        //when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        //when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
        //        .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        //when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        //when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        //when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        //when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        //when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(Collections.emptyList());

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        //when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList);

        //when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
         //       "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        //when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        //when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        //when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        //when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        //when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        //when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        //when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        //orderPayModelUnderTest.orderPaySuccess(orderPO, "memberName", "paymentCode", "paymentName",
         //       new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "payNo");

        // Verify the results
        //verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
         //       new BigDecimal("0.00"));
        //verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
        //        OrderCreateChannel.H5);
        //verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        //verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        //verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        //verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        //verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        //verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
        //        new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        //verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
//                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
//                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
//                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }


    @Test
    void testOrderPaySuccess_StoreBindCategoryFeignClientReturnsNoItems2() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        //when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        //when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        //when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
        //       .thenReturn(Collections.emptyList());

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        //when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        //when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        //when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        //when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        //when(mockAccountCardFacade.defaultCard(0L)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure BillOperatinIntegration.detailByBankAccount(...).
        final AccountCard accountCard = new AccountCard();
        accountCard.setCardId(0L);
        accountCard.setStoreId("storeId");
        accountCard.setStoreName("storeName");
        accountCard.setOrgType("orgType");
        accountCard.setBankAccountNumber("bankAccountNumber");
        //when(mockBillOperatinIntegration.detailByBankAccount("UIN_PLF_STORE_ID101",
        //       AccountCardTypeEnum.UNI_JS_PLF_SUP)).thenReturn(accountCard);

        // Configure IBzBankTransferService.list(...).
        final BzBankTransferPO bzBankTransferPO = new BzBankTransferPO();
        bzBankTransferPO.setEnabledFlag(0);
        bzBankTransferPO.setPaySn("paySn");
        bzBankTransferPO.setOutOrderNo("outOrderNo");
        bzBankTransferPO.setPayAmount(new BigDecimal("0.00"));
        bzBankTransferPO.setReceiptAccount("receiptAccount");
        final List<BzBankTransferPO> bzBankTransferPOS = Arrays.asList(bzBankTransferPO);
        //when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(bzBankTransferPOS);

        // Configure StoreFeignClient.getStoreContractReciptInfo(...).
        final StoreContractReceiptInfoVO storeContractReceiptInfoVO = new StoreContractReceiptInfoVO();
        storeContractReceiptInfoVO.setWxSellerId("wxSellerId");
        storeContractReceiptInfoVO.setAliSellerId("aliSellerId");
        final Store store = new Store();
        store.setAcctId("acctId");
        storeContractReceiptInfoVO.setStore(store);
        storeContractReceiptInfoVO.setRecommentWxSellerId("recommentWxSellerId");
        storeContractReceiptInfoVO.setRecommentAliSellerId("recommentAliSellerId");
        //when(mockStoreFeignClient.getStoreContractReciptInfo(0L)).thenReturn(storeContractReceiptInfoVO);

        // Configure CustomerServiceFeign.info(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final BankcardVo bankcardVo = new BankcardVo();
        bankcardVo.setCardId("cardId");
        bankcardVo.setCardNo("cardNo");
        bankcardVo.setAcctName("acctName");
        bankcardVo.setBankCode("bankCode");
        bankcardVo.setBankName("bankName");
        final Result<BankcardVo> bankcardVoResult = new Result<>(false, errorContext, bankcardVo);
        final QueryBankcardInfoReq var1 = new QueryBankcardInfoReq();
        var1.setCardId("cardId");
        //when(mockCustomerServiceFeign.info(var1)).thenReturn(bankcardVoResult);

        // Configure StmAccountFacade.detailV2(...).
        final AccountQuery accountQuery = new AccountQuery();
        accountQuery.setCurrent(0);
        accountQuery.setPageSize(0);
        accountQuery.setAccountId("accountId");
        accountQuery.setStoreId("UIN_PLF_STORE_ID101");
        accountQuery.setAccountType("accountType");
        //when(mockStmAccountFacade.detailV2(accountQuery)).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderOfflineService.queryOrderOfflineList(...).
        final OrderOfflinePO orderOfflinePO = new OrderOfflinePO();
        orderOfflinePO.setEnabledFlag(0);
        orderOfflinePO.setPaySn("paySn");
        orderOfflinePO.setReceiptAccount("receiptAccount");
        orderOfflinePO.setReceiptAmount(new BigDecimal("0.00"));
        orderOfflinePO.setReceiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderOfflinePO> orderOfflinePOS = Arrays.asList(orderOfflinePO);
        //when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(orderOfflinePOS);

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        //when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList);

        //when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
        //       "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        //when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        //when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        //when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        //when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        //when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext1 = new ErrorContext();
        errorContext1.setErrorStack(
                Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext1.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext1, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        //when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        //when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
//        orderPayModelUnderTest.orderPaySuccess(orderPO, "memberName", "paymentCode", "paymentName",
//                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "payNo");

        // Verify the results
        //verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
        //       new BigDecimal("0.00"));
        //verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
        //       OrderCreateChannel.H5);
        //verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        //verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        //verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        //verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        //verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        //    verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
        //        new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
//                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
//                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
//                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }

    @Test
    void testOrderPaySuccess_IBzBankTransferServiceReturnsNoItems2() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

//        when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
//        when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
//        when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
//                .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
//        when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);
//
//        when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        //when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
//        when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);
//
//        when(mockBzBankTransferService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        //when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList);

//        when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
//                "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        //when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        //when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        //when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(Collections.emptyList());

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        //when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(null);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        //when(mockOrderService.getById(0)).thenReturn(null);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        //when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
//        when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(Collections.emptyList());
//
//        // Run the test
//        orderPayModelUnderTest.orderPaySuccess(orderPO, "memberName", "paymentCode", "paymentName",
//                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "payNo");

        // Verify the results
//        verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
//                new BigDecimal("0.00"));
//        verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
//                OrderCreateChannel.H5);
//        verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
//        verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        //verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        //verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        //verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
//        verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
//                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
//                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
//                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
//                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }

    @Test
    void testOrderPaySuccess_OrderOfflineServiceReturnsNoItems2() throws Exception {
        // Setup
        final OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(0);
        orderPO.setOrderSn("memberName");
        orderPO.setUserNo("userNo");
        orderPO.setPaySn("paySn");
        orderPO.setSellerId("sellerId");
        orderPO.setBankPayTrxNo("bankPayTrxNo");
        orderPO.setStoreId(0L);
        orderPO.setRecommendStoreId(0L);
        orderPO.setMemberName("memberName");
        orderPO.setMemberId(0);
        orderPO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO.setOrderState(0);
        orderPO.setLoanPayState(0);
        orderPO.setPaymentName("paymentName");
        orderPO.setPaymentCode("paymentCode");
        orderPO.setOrderAmount(new BigDecimal("0.00"));
        orderPO.setGoodsAmount(new BigDecimal("0.00"));
        orderPO.setExpressFee(new BigDecimal("0.00"));
        orderPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO.setXzCardAmount(new BigDecimal("0.00"));
        orderPO.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO.setComposePayName("composeWay");
        orderPO.setBalanceAmount(new BigDecimal("0.00"));
        orderPO.setPayAmount(new BigDecimal("0.00"));
        orderPO.setAreaCode("areaCode");
        orderPO.setOrderType(0);
        orderPO.setServiceFee(new BigDecimal("0.00"));
        orderPO.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO.setSettleMode("settleMode");
        orderPO.setFinanceRuleCode("financeRuleCode");
        orderPO.setIsDelivery(0);
        orderPO.setChannel("channel");
        orderPO.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO.setNewOrder(false);
        orderPO.setCustomerConfirmStatus(0);
        orderPO.setOrderPlaceUserRoleCode(0);
        orderPO.setExchangeFlag(0);

        //when(mockOrderPayProcessStrategyContext.getStrategy(0)).thenReturn(null);
        //when(mockChannelFeeRateConfig.getMappedRate()).thenReturn(new HashMap<>());

        // Configure StoreBindCategoryFeignClient.getStoreBindCategoryList(...).
        final StoreBindCategory storeBindCategory = new StoreBindCategory();
        storeBindCategory.setBindId(0);
        storeBindCategory.setStoreId(0L);
        storeBindCategory.setCreateVendorId(0L);
        storeBindCategory.setGoodsCategoryId1(0);
        storeBindCategory.setGoodsCategoryId2(0);
        final List<StoreBindCategory> storeBindCategories = Arrays.asList(storeBindCategory);
        final StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setBindIdNotEquals(0);
        storeBindCategoryExample.setBindIdIn("bindIdIn");
        storeBindCategoryExample.setBindId(0);
        storeBindCategoryExample.setStoreId(0L);
        storeBindCategoryExample.setStoreIdNotEquals(0);
        //when(mockStoreBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample))
        //        .thenReturn(storeBindCategories);

        // Configure OmsBaseIntegration.query(...).
        final RuleServiceFeeQueryDTO ruleServiceFeeQueryDTO = new RuleServiceFeeQueryDTO();
        ruleServiceFeeQueryDTO.setIntroduceMerchant("introduceMerchant");
        ruleServiceFeeQueryDTO.setPayWay("paymentCode");
        ruleServiceFeeQueryDTO.setFirstCategory(Arrays.asList(0));
        ruleServiceFeeQueryDTO.setSecondCategory(Arrays.asList(0));
        //when(mockOmsBaseIntegration.query(ruleServiceFeeQueryDTO, "memberName")).thenReturn(false);

        //when(mockOrderProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure IOrderService.updateById(...).
        final OrderPO entity = new OrderPO();
        entity.setOrderId(0);
        entity.setOrderSn("memberName");
        entity.setUserNo("userNo");
        entity.setPaySn("paySn");
        entity.setSellerId("sellerId");
        entity.setBankPayTrxNo("bankPayTrxNo");
        entity.setStoreId(0L);
        entity.setRecommendStoreId(0L);
        entity.setMemberName("memberName");
        entity.setMemberId(0);
        entity.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setOrderState(0);
        entity.setLoanPayState(0);
        entity.setPaymentName("paymentName");
        entity.setPaymentCode("paymentCode");
        entity.setOrderAmount(new BigDecimal("0.00"));
        entity.setGoodsAmount(new BigDecimal("0.00"));
        entity.setExpressFee(new BigDecimal("0.00"));
        entity.setStoreVoucherAmount(new BigDecimal("0.00"));
        entity.setStoreActivityAmount(new BigDecimal("0.00"));
        entity.setXzCardAmount(new BigDecimal("0.00"));
        entity.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        entity.setComposePayName("composeWay");
        entity.setBalanceAmount(new BigDecimal("0.00"));
        entity.setPayAmount(new BigDecimal("0.00"));
        entity.setAreaCode("areaCode");
        entity.setOrderType(0);
        entity.setServiceFee(new BigDecimal("0.00"));
        entity.setServiceFeeRate(new BigDecimal("0.00"));
        entity.setSettleMode("settleMode");
        entity.setFinanceRuleCode("financeRuleCode");
        entity.setIsDelivery(0);
        entity.setChannel("channel");
        entity.setChannelServiceFee(new BigDecimal("0.00"));
        entity.setNewOrder(false);
        entity.setCustomerConfirmStatus(0);
        entity.setOrderPlaceUserRoleCode(0);
        entity.setExchangeFlag(0);
        //when(mockOrderService.updateById(entity)).thenReturn(false);

        // Configure IOrderProductService.updateBatchById(...).
        final OrderProductPO orderProductPO = new OrderProductPO();
        orderProductPO.setOrderProductId(0L);
        orderProductPO.setOrderSn("orderSn");
        orderProductPO.setStoreName("memberName");
        orderProductPO.setGoodsName("memberName");
        orderProductPO.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO.setProductNum(0);
        orderProductPO.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO.setServiceFee(new BigDecimal("0.00"));
        orderProductPO.setSpellTeamId(0);
        orderProductPO.setEnabledFlag(0);
        final List<OrderProductPO> entityList = Arrays.asList(orderProductPO);
        //when(mockOrderProductService.updateBatchById(entityList)).thenReturn(false);

        //when(mockOrderOfflineService.queryOrderOfflineList("paySn")).thenReturn(Collections.emptyList());

        // Configure IOrderService.list(...).
        final OrderPO orderPO1 = new OrderPO();
        orderPO1.setOrderId(0);
        orderPO1.setOrderSn("memberName");
        orderPO1.setUserNo("userNo");
        orderPO1.setPaySn("paySn");
        orderPO1.setSellerId("sellerId");
        orderPO1.setBankPayTrxNo("bankPayTrxNo");
        orderPO1.setStoreId(0L);
        orderPO1.setRecommendStoreId(0L);
        orderPO1.setMemberName("memberName");
        orderPO1.setMemberId(0);
        orderPO1.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO1.setOrderState(0);
        orderPO1.setLoanPayState(0);
        orderPO1.setPaymentName("paymentName");
        orderPO1.setPaymentCode("paymentCode");
        orderPO1.setOrderAmount(new BigDecimal("0.00"));
        orderPO1.setGoodsAmount(new BigDecimal("0.00"));
        orderPO1.setExpressFee(new BigDecimal("0.00"));
        orderPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardAmount(new BigDecimal("0.00"));
        orderPO1.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO1.setComposePayName("composeWay");
        orderPO1.setBalanceAmount(new BigDecimal("0.00"));
        orderPO1.setPayAmount(new BigDecimal("0.00"));
        orderPO1.setAreaCode("areaCode");
        orderPO1.setOrderType(0);
        orderPO1.setServiceFee(new BigDecimal("0.00"));
        orderPO1.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO1.setSettleMode("settleMode");
        orderPO1.setFinanceRuleCode("financeRuleCode");
        orderPO1.setIsDelivery(0);
        orderPO1.setChannel("channel");
        orderPO1.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO1.setNewOrder(false);
        orderPO1.setCustomerConfirmStatus(0);
        orderPO1.setOrderPlaceUserRoleCode(0);
        orderPO1.setExchangeFlag(0);
        final List<OrderPO> orderPOList = Arrays.asList(orderPO1);
        //when(mockOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(orderPOList);

        //when(mockPromotionCommonFeignClient.orderPaySuccess("memberName", 0, "paySn", "memberName", "paymentName",
        //       "paymentCode")).thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderProductMapper.selectOne(...).
        final OrderProductPO orderProductPO1 = new OrderProductPO();
        orderProductPO1.setOrderProductId(0L);
        orderProductPO1.setOrderSn("orderSn");
        orderProductPO1.setStoreName("memberName");
        orderProductPO1.setGoodsName("memberName");
        orderProductPO1.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO1.setProductNum(0);
        orderProductPO1.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO1.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO1.setServiceFee(new BigDecimal("0.00"));
        orderProductPO1.setSpellTeamId(0);
        orderProductPO1.setEnabledFlag(0);
        //when(mockOrderProductMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderProductPO1);

        // Configure OrderProductMapper.selectList(...).
        final OrderProductPO orderProductPO2 = new OrderProductPO();
        orderProductPO2.setOrderProductId(0L);
        orderProductPO2.setOrderSn("orderSn");
        orderProductPO2.setStoreName("memberName");
        orderProductPO2.setGoodsName("memberName");
        orderProductPO2.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO2.setProductNum(0);
        orderProductPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO2.setServiceFee(new BigDecimal("0.00"));
        orderProductPO2.setSpellTeamId(0);
        orderProductPO2.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS = Arrays.asList(orderProductPO2);
        //when(mockOrderProductMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderProductPOS);

        // Configure OrderPromotionSendCouponMapper.listByExample(...).
        final OrderPromotionSendCouponPO orderPromotionSendCouponPO = new OrderPromotionSendCouponPO();
        orderPromotionSendCouponPO.setSendCouponId(0);
        orderPromotionSendCouponPO.setOrderSn("orderSn");
        orderPromotionSendCouponPO.setPromotionGrade(0);
        orderPromotionSendCouponPO.setCouponId(0);
        orderPromotionSendCouponPO.setNumber(0);
        final List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS = Arrays.asList(orderPromotionSendCouponPO);
        final OrderPromotionSendCouponExample example = new OrderPromotionSendCouponExample();
        example.setSendCouponIdNotEquals(0);
        example.setSendCouponIdIn("sendCouponIdIn");
        example.setSendCouponId(0);
        example.setOrderSn("memberName");
        example.setOrderSnLike("orderSnLike");
        //when(mockOrderPromotionSendCouponMapper.listByExample(example)).thenReturn(orderPromotionSendCouponPOS);

        // Configure CouponFeignClient.getCouponByCouponId(...).
        final CouponVO couponVO = new CouponVO();
        couponVO.setCouponId(0);
        couponVO.setCouponType(0);
        couponVO.setReceivedNum(0);
        couponVO.setRandomMax(new BigDecimal("0.00"));
        couponVO.setRandomMin(new BigDecimal("0.00"));
        couponVO.setPublishNum(0);
        couponVO.setEffectiveTimeType(0);
        couponVO.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponVO.setCycle(0);
        couponVO.setState(0);
        couponVO.setStoreId(0L);
        couponVO.setUseType(0);
        //when(mockCouponFeignClient.getCouponByCouponId(0)).thenReturn(couponVO);

        // Configure IOrderService.getById(...).
        final OrderPO orderPO2 = new OrderPO();
        orderPO2.setOrderId(0);
        orderPO2.setOrderSn("memberName");
        orderPO2.setUserNo("userNo");
        orderPO2.setPaySn("paySn");
        orderPO2.setSellerId("sellerId");
        orderPO2.setBankPayTrxNo("bankPayTrxNo");
        orderPO2.setStoreId(0L);
        orderPO2.setRecommendStoreId(0L);
        orderPO2.setMemberName("memberName");
        orderPO2.setMemberId(0);
        orderPO2.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO2.setOrderState(0);
        orderPO2.setLoanPayState(0);
        orderPO2.setPaymentName("paymentName");
        orderPO2.setPaymentCode("paymentCode");
        orderPO2.setOrderAmount(new BigDecimal("0.00"));
        orderPO2.setGoodsAmount(new BigDecimal("0.00"));
        orderPO2.setExpressFee(new BigDecimal("0.00"));
        orderPO2.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO2.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardAmount(new BigDecimal("0.00"));
        orderPO2.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO2.setComposePayName("composeWay");
        orderPO2.setBalanceAmount(new BigDecimal("0.00"));
        orderPO2.setPayAmount(new BigDecimal("0.00"));
        orderPO2.setAreaCode("areaCode");
        orderPO2.setOrderType(0);
        orderPO2.setServiceFee(new BigDecimal("0.00"));
        orderPO2.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO2.setSettleMode("settleMode");
        orderPO2.setFinanceRuleCode("financeRuleCode");
        orderPO2.setIsDelivery(0);
        orderPO2.setChannel("channel");
        orderPO2.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO2.setNewOrder(false);
        orderPO2.setCustomerConfirmStatus(0);
        orderPO2.setOrderPlaceUserRoleCode(0);
        orderPO2.setExchangeFlag(0);
        //when(mockOrderService.getById(0)).thenReturn(orderPO2);

        // Configure IOrderAmountStateRecordService.initOrderAmountState(...).
        final ErrorContext errorContext = new ErrorContext();
        errorContext.setErrorStack(Arrays.asList(new CommonError("code", "msg", "location", new Exception("message"))));
        errorContext.setThirdPartyError("thirdPartyError");
        final Result<Void> voidResult = new Result<>(false, errorContext, null);
        final OrderPO orderPO3 = new OrderPO();
        orderPO3.setOrderId(0);
        orderPO3.setOrderSn("memberName");
        orderPO3.setUserNo("userNo");
        orderPO3.setPaySn("paySn");
        orderPO3.setSellerId("sellerId");
        orderPO3.setBankPayTrxNo("bankPayTrxNo");
        orderPO3.setStoreId(0L);
        orderPO3.setRecommendStoreId(0L);
        orderPO3.setMemberName("memberName");
        orderPO3.setMemberId(0);
        orderPO3.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO3.setOrderState(0);
        orderPO3.setLoanPayState(0);
        orderPO3.setPaymentName("paymentName");
        orderPO3.setPaymentCode("paymentCode");
        orderPO3.setOrderAmount(new BigDecimal("0.00"));
        orderPO3.setGoodsAmount(new BigDecimal("0.00"));
        orderPO3.setExpressFee(new BigDecimal("0.00"));
        orderPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardAmount(new BigDecimal("0.00"));
        orderPO3.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO3.setComposePayName("composeWay");
        orderPO3.setBalanceAmount(new BigDecimal("0.00"));
        orderPO3.setPayAmount(new BigDecimal("0.00"));
        orderPO3.setAreaCode("areaCode");
        orderPO3.setOrderType(0);
        orderPO3.setServiceFee(new BigDecimal("0.00"));
        orderPO3.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO3.setSettleMode("settleMode");
        orderPO3.setFinanceRuleCode("financeRuleCode");
        orderPO3.setIsDelivery(0);
        orderPO3.setChannel("channel");
        orderPO3.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO3.setNewOrder(false);
        orderPO3.setCustomerConfirmStatus(0);
        orderPO3.setOrderPlaceUserRoleCode(0);
        orderPO3.setExchangeFlag(0);
        //when(mockOrderAmountRecordService.initOrderAmountState(orderPO3)).thenReturn(voidResult);

        // Configure OrderProductModel.getOrderProductListByOrderSn(...).
        final OrderProductPO orderProductPO3 = new OrderProductPO();
        orderProductPO3.setOrderProductId(0L);
        orderProductPO3.setOrderSn("orderSn");
        orderProductPO3.setStoreName("memberName");
        orderProductPO3.setGoodsName("memberName");
        orderProductPO3.setProductShowPrice(new BigDecimal("0.00"));
        orderProductPO3.setProductNum(0);
        orderProductPO3.setStoreActivityAmount(new BigDecimal("0.00"));
        orderProductPO3.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderProductPO3.setServiceFee(new BigDecimal("0.00"));
        orderProductPO3.setSpellTeamId(0);
        orderProductPO3.setEnabledFlag(0);
        final List<OrderProductPO> orderProductPOS1 = Arrays.asList(orderProductPO3);
        //when(mockOrderProductModel.getOrderProductListByOrderSn("memberName")).thenReturn(orderProductPOS1);

        // Run the test
        //orderPayModelUnderTest.orderPaySuccess(orderPO, "memberName", "paymentCode", "paymentName",
        //       new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "payNo");

        // Verify the results
        //verify(mockIOrderAmountStateRecordService).saveServiceFeeAmount("memberName", "memberName",
        //       new BigDecimal("0.00"));
        //verify(mockOrderLogModel).insertOrderLog(3, 0L, "memberName", "memberName", 0, 20, 0, "订单支付完成",
        //        OrderCreateChannel.H5);
        //verify(mockOrderProductModel).orderPaySuccessAddSales("memberName");
        //verify(mockOrderService).setOrdersDeliverable(Arrays.asList("value"));

        // Confirm CouponMemberFeignClient.saveCouponMember(...).
        final CouponMember couponMember = new CouponMember();
        couponMember.setCouponId(0);
        couponMember.setCouponCode("couponCode");
        couponMember.setStoreId(0L);
        couponMember.setMemberId(0);
        couponMember.setMemberName("memberName");
        couponMember.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseState(0);
        couponMember.setEffectiveStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setEffectiveEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponMember.setUseType(0);
        couponMember.setRandomAmount(new BigDecimal("0.00"));
        //verify(mockCouponMemberFeignClient).saveCouponMember(couponMember);

        // Confirm CouponUseLogFeignClient.saveCouponUseLog(...).
        final CouponUseLog couponUseLog = new CouponUseLog();
        couponUseLog.setCouponCode("couponCode");
        couponUseLog.setMemberId(0);
        couponUseLog.setMemberName("memberName");
        couponUseLog.setStoreId(0L);
        couponUseLog.setLogType(0);
        couponUseLog.setLogTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        couponUseLog.setLogContent("logContent");
        //verify(mockCouponUseLogFeignClient).saveCouponUseLog(couponUseLog);

        // Confirm CouponFeignClient.updateOrderCoupon(...).
        final Coupon coupon = new Coupon();
        coupon.setCouponId(0);
        coupon.setCouponName("couponName");
        coupon.setCouponContent("couponContent");
        coupon.setDescription("description");
        coupon.setReceivedNum(0);
        //verify(mockCouponFeignClient).updateOrderCoupon(coupon);

        // Confirm OrderCreateHelper.addOrderChangeEvent(...).
        final OrderPO orderPO4 = new OrderPO();
        orderPO4.setOrderId(0);
        orderPO4.setOrderSn("memberName");
        orderPO4.setUserNo("userNo");
        orderPO4.setPaySn("paySn");
        orderPO4.setSellerId("sellerId");
        orderPO4.setBankPayTrxNo("bankPayTrxNo");
        orderPO4.setStoreId(0L);
        orderPO4.setRecommendStoreId(0L);
        orderPO4.setMemberName("memberName");
        orderPO4.setMemberId(0);
        orderPO4.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setPayUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setLendingSuccessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderPO4.setOrderState(0);
        orderPO4.setLoanPayState(0);
        orderPO4.setPaymentName("paymentName");
        orderPO4.setPaymentCode("paymentCode");
        orderPO4.setOrderAmount(new BigDecimal("0.00"));
        orderPO4.setGoodsAmount(new BigDecimal("0.00"));
        orderPO4.setExpressFee(new BigDecimal("0.00"));
        orderPO4.setStoreVoucherAmount(new BigDecimal("0.00"));
        orderPO4.setStoreActivityAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardAmount(new BigDecimal("0.00"));
        orderPO4.setXzCardExpressFeeAmount(new BigDecimal("0.00"));
        orderPO4.setComposePayName("composeWay");
        orderPO4.setBalanceAmount(new BigDecimal("0.00"));
        orderPO4.setPayAmount(new BigDecimal("0.00"));
        orderPO4.setAreaCode("areaCode");
        orderPO4.setOrderType(0);
        orderPO4.setServiceFee(new BigDecimal("0.00"));
        orderPO4.setServiceFeeRate(new BigDecimal("0.00"));
        orderPO4.setSettleMode("settleMode");
        orderPO4.setFinanceRuleCode("financeRuleCode");
        orderPO4.setIsDelivery(0);
        orderPO4.setChannel("channel");
        orderPO4.setChannelServiceFee(new BigDecimal("0.00"));
        orderPO4.setNewOrder(false);
        orderPO4.setCustomerConfirmStatus(0);
        orderPO4.setOrderPlaceUserRoleCode(0);
        orderPO4.setExchangeFlag(0);
        //verify(mockOrderCreateHelper).addOrderChangeEvent(orderPO4, OrderEventEnum.CREATE,
        //        new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        //verify(mockRabbitTemplate).convertAndSend("newmall_exchange", "newmall_queue_member_msg",
//                new MessageSendVO(Arrays.asList(new MessageSendProperty("availableBalance", "memberName")),
//                        Arrays.asList(new MessageSendProperty("availableBalance", "memberName")), "changeTime", 0,
//                        "balance_change_reminder", "{\"type\":\"balance_change\"}"));
    }
}
