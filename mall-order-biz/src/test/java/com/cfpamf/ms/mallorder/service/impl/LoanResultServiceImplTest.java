package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mall.account.constant.AccountConstans;
import com.cfpamf.ms.mall.account.enums.AccountCardTypeEnum;
import com.cfpamf.ms.mall.account.vo.AccountCard;
import com.cfpamf.ms.mallorder.integration.settlement.BillOperatinIntegration;
import com.cfpamf.ms.mallorder.po.OrderPO;
import com.cfpamf.ms.mallorder.service.IOrderService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;


/**
 * @ClassName: LoanResultServiceImplTest
 * @Description: TODO
 * @Author: maoliang
 * @Date: 2023/6/25
 * @version: 1.0
 */
@RunWith(MockitoJUnitRunner.class)
public class LoanResultServiceImplTest {

    // 创建被测试对象的实例
    @InjectMocks
    private LoanResultServiceImpl loanResultService;

    @Mock
    private IOrderService orderService;

    @Mock
    private BillOperatinIntegration billOperatinIntegration;

    @Test
    public void queryLoanResultPage() {

    }

    @Test
    public void loanRepay() {
    }

    @Test
    public void reLendingByOrderSn() {
    }

    @Test
    public void test_reLendingByOrderSn_withNewOrder() {
        String payNo = "pay_12345";
        String operator = "operator_123";

//        // mock orderService to return a new order
//        OrderPO orderPo = new OrderPO();
//        orderPo.setNewOrder(true);
//        Mockito.when(orderService.getByOrderSn(payNo)).thenReturn(orderPo);
//
//        // mock billOperatinIntegration to return an account card
//        AccountCard accountCard = new AccountCard();
//        accountCard.setLoanCardId("loan_card_123");
//        Mockito.when(billOperatinIntegration.detailByBankAccount(AccountConstans.UNI_PLF_STORE_ID, AccountCardTypeEnum.UNI_JS_PLF_SUP))
//                .thenReturn(accountCard);
//
//        Result result = loanResultService.reLendingByOrderSn(payNo, operator);

        // assert that the result is not null and contains the expected response
//        Assert.assertNotNull(result);
//        Assert.assertEquals("200", result.getCode());
    }


}