<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="receiveDocumentParallel" name="收文并行审批流程" isExecutable="true">
    <startEvent id="startEvent" name="开始"></startEvent>
    
    <!-- 办公室主任登记 -->
    <userTask id="officeDirectorRegister" name="办公室主任登记" flowable:assignee="${officeDirector}">
      <documentation>办公室主任对收文进行登记</documentation>
    </userTask>
    
    <!-- 书记审批 -->
    <userTask id="secretaryApproval" name="书记审批" flowable:assignee="${secretary}">
      <documentation>书记对收文进行审批</documentation>
    </userTask>
    
    <!-- 并行网关开始 -->
    <parallelGateway id="parallelGatewayStart" name="并行审批开始"></parallelGateway>
    
    <!-- 分管领导1审阅 -->
    <userTask id="leader1Review" name="分管领导1审阅" flowable:assignee="${leader1}">
      <documentation>分管领导1审阅</documentation>
    </userTask>
    
    <!-- 分管领导2审阅 -->
    <userTask id="leader2Review" name="分管领导2审阅" flowable:assignee="${leader2}">
      <documentation>分管领导2审阅</documentation>
    </userTask>
    
    <!-- 分管领导3审阅 -->
    <userTask id="leader3Review" name="分管领导3审阅" flowable:assignee="${leader3}">
      <documentation>分管领导3审阅</documentation>
    </userTask>
    
    <!-- 分管领导4审阅 -->
    <userTask id="leader4Review" name="分管领导4审阅" flowable:assignee="${leader4}">
      <documentation>分管领导4审阅</documentation>
    </userTask>
    
    <!-- 分管领导5审阅 -->
    <userTask id="leader5Review" name="分管领导5审阅" flowable:assignee="${leader5}">
      <documentation>分管领导5审阅</documentation>
    </userTask>
    
    <!-- 科室负责人1确认 -->
    <userTask id="dept1Confirm" name="科室负责人1确认" flowable:assignee="${deptHead1}">
      <documentation>科室负责人1确认</documentation>
    </userTask>
    
    <!-- 科室负责人2确认 -->
    <userTask id="dept2Confirm" name="科室负责人2确认" flowable:assignee="${deptHead2}">
      <documentation>科室负责人2确认</documentation>
    </userTask>
    
    <!-- 科室负责人3确认 -->
    <userTask id="dept3Confirm" name="科室负责人3确认" flowable:assignee="${deptHead3}">
      <documentation>科室负责人3确认</documentation>
    </userTask>
    
    <!-- 科室负责人4确认 -->
    <userTask id="dept4Confirm" name="科室负责人4确认" flowable:assignee="${deptHead4}">
      <documentation>科室负责人4确认</documentation>
    </userTask>
    
    <!-- 科室负责人5确认 -->
    <userTask id="dept5Confirm" name="科室负责人5确认" flowable:assignee="${deptHead5}">
      <documentation>科室负责人5确认</documentation>
    </userTask>
    
    <!-- 经办人1确认 -->
    <userTask id="handler1Confirm" name="经办人1确认" flowable:assignee="${handler1}">
      <documentation>经办人1确认</documentation>
    </userTask>
    
    <!-- 经办人2确认 -->
    <userTask id="handler2Confirm" name="经办人2确认" flowable:assignee="${handler2}">
      <documentation>经办人2确认</documentation>
    </userTask>
    
    <!-- 经办人3确认 -->
    <userTask id="handler3Confirm" name="经办人3确认" flowable:assignee="${handler3}">
      <documentation>经办人3确认</documentation>
    </userTask>
    
    <!-- 经办人4确认 -->
    <userTask id="handler4Confirm" name="经办人4确认" flowable:assignee="${handler4}">
      <documentation>经办人4确认</documentation>
    </userTask>
    
    <!-- 经办人5确认 -->
    <userTask id="handler5Confirm" name="经办人5确认" flowable:assignee="${handler5}">
      <documentation>经办人5确认</documentation>
    </userTask>
    
    <!-- 并行网关结束 -->
    <parallelGateway id="parallelGatewayEnd" name="并行审批结束"></parallelGateway>
    
    <!-- 办公室汇总督办 -->
    <userTask id="officeSummary" name="办公室汇总督办" flowable:assignee="${officeDirector}">
      <documentation>办公室汇总所有审批意见并督办</documentation>
    </userTask>
    
    <!-- 流程结束 -->
    <endEvent id="endEvent" name="结束"></endEvent>
    
    <!-- 流程连线 -->
    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="officeDirectorRegister"></sequenceFlow>
    <sequenceFlow id="flow2" sourceRef="officeDirectorRegister" targetRef="secretaryApproval"></sequenceFlow>
    <sequenceFlow id="flow3" sourceRef="secretaryApproval" targetRef="parallelGatewayStart"></sequenceFlow>
    
    <!-- 并行分支 -->
    <sequenceFlow id="flow4" sourceRef="parallelGatewayStart" targetRef="leader1Review"></sequenceFlow>
    <sequenceFlow id="flow5" sourceRef="parallelGatewayStart" targetRef="leader2Review"></sequenceFlow>
    <sequenceFlow id="flow6" sourceRef="parallelGatewayStart" targetRef="leader3Review"></sequenceFlow>
    <sequenceFlow id="flow7" sourceRef="parallelGatewayStart" targetRef="leader4Review"></sequenceFlow>
    <sequenceFlow id="flow8" sourceRef="parallelGatewayStart" targetRef="leader5Review"></sequenceFlow>
    
    <!-- 分管领导到科室负责人 -->
    <sequenceFlow id="flow9" sourceRef="leader1Review" targetRef="dept1Confirm"></sequenceFlow>
    <sequenceFlow id="flow10" sourceRef="leader2Review" targetRef="dept2Confirm"></sequenceFlow>
    <sequenceFlow id="flow11" sourceRef="leader3Review" targetRef="dept3Confirm"></sequenceFlow>
    <sequenceFlow id="flow12" sourceRef="leader4Review" targetRef="dept4Confirm"></sequenceFlow>
    <sequenceFlow id="flow13" sourceRef="leader5Review" targetRef="dept5Confirm"></sequenceFlow>
    
    <!-- 科室负责人到经办人 -->
    <sequenceFlow id="flow14" sourceRef="dept1Confirm" targetRef="handler1Confirm"></sequenceFlow>
    <sequenceFlow id="flow15" sourceRef="dept2Confirm" targetRef="handler2Confirm"></sequenceFlow>
    <sequenceFlow id="flow16" sourceRef="dept3Confirm" targetRef="handler3Confirm"></sequenceFlow>
    <sequenceFlow id="flow17" sourceRef="dept4Confirm" targetRef="handler4Confirm"></sequenceFlow>
    <sequenceFlow id="flow18" sourceRef="dept5Confirm" targetRef="handler5Confirm"></sequenceFlow>
    
    <!-- 经办人到并行网关结束 -->
    <sequenceFlow id="flow19" sourceRef="handler1Confirm" targetRef="parallelGatewayEnd"></sequenceFlow>
    <sequenceFlow id="flow20" sourceRef="handler2Confirm" targetRef="parallelGatewayEnd"></sequenceFlow>
    <sequenceFlow id="flow21" sourceRef="handler3Confirm" targetRef="parallelGatewayEnd"></sequenceFlow>
    <sequenceFlow id="flow22" sourceRef="handler4Confirm" targetRef="parallelGatewayEnd"></sequenceFlow>
    <sequenceFlow id="flow23" sourceRef="handler5Confirm" targetRef="parallelGatewayEnd"></sequenceFlow>
    
    <!-- 汇总督办 -->
    <sequenceFlow id="flow24" sourceRef="parallelGatewayEnd" targetRef="officeSummary"></sequenceFlow>
    <sequenceFlow id="flow25" sourceRef="officeSummary" targetRef="endEvent"></sequenceFlow>
  </process>
  
  <bpmndi:BPMNDiagram id="BPMNDiagram_receiveDocumentParallel">
    <bpmndi:BPMNPlane bpmnElement="receiveDocumentParallel" id="BPMNPlane_receiveDocumentParallel">
      <bpmndi:BPMNShape bpmnElement="startEvent" id="BPMNShape_startEvent">
        <omgdc:Bounds height="30.0" width="30.0" x="100.0" y="163.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="officeDirectorRegister" id="BPMNShape_officeDirectorRegister">
        <omgdc:Bounds height="80.0" width="100.0" x="175.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="secretaryApproval" id="BPMNShape_secretaryApproval">
        <omgdc:Bounds height="80.0" width="100.0" x="320.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="parallelGatewayStart" id="BPMNShape_parallelGatewayStart">
        <omgdc:Bounds height="40.0" width="40.0" x="465.0" y="158.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="parallelGatewayEnd" id="BPMNShape_parallelGatewayEnd">
        <omgdc:Bounds height="40.0" width="40.0" x="1200.0" y="158.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="officeSummary" id="BPMNShape_officeSummary">
        <omgdc:Bounds height="80.0" width="100.0" x="1285.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="endEvent" id="BPMNShape_endEvent">
        <omgdc:Bounds height="28.0" width="28.0" x="1430.0" y="164.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      
      <!-- 并行分支的形状定义 -->
      <bpmndi:BPMNShape bpmnElement="leader1Review" id="BPMNShape_leader1Review">
        <omgdc:Bounds height="80.0" width="100.0" x="550.0" y="50.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="leader2Review" id="BPMNShape_leader2Review">
        <omgdc:Bounds height="80.0" width="100.0" x="550.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="leader3Review" id="BPMNShape_leader3Review">
        <omgdc:Bounds height="80.0" width="100.0" x="550.0" y="226.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="leader4Review" id="BPMNShape_leader4Review">
        <omgdc:Bounds height="80.0" width="100.0" x="550.0" y="314.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="leader5Review" id="BPMNShape_leader5Review">
        <omgdc:Bounds height="80.0" width="100.0" x="550.0" y="402.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      
      <!-- 连线定义 -->
      <bpmndi:BPMNEdge bpmnElement="flow1" id="BPMNEdge_flow1">
        <omgdi:waypoint x="130.0" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="175.0" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow2" id="BPMNEdge_flow2">
        <omgdi:waypoint x="275.0" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="320.0" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow3" id="BPMNEdge_flow3">
        <omgdi:waypoint x="420.0" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="465.0" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
