package com.ruoyi.oa.seal.domain;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.oa.document.domain.OaFileAttachment;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 印章使用申请对象 oa_seal_application
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public class OaSealApplication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 申请ID */
    private Long applicationId;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applicationNo;

    /** 申请人ID */
    @Excel(name = "申请人ID")
    private Long applicantId;

    /** 申请人姓名 */
    @Excel(name = "申请人姓名")
    private String applicantName;

    /** 申请部门ID */
    @Excel(name = "申请部门ID")
    private Long deptId;

    /** 申请部门名称 */
    @Excel(name = "申请部门名称")
    private String deptName;

    /** 印章ID */
    @Excel(name = "印章ID")
    private Long sealId;

    /** 印章名称 */
    @Excel(name = "印章名称")
    private String sealName;

    /** 申请事由 */
    @Excel(name = "申请事由")
    private String applicationReason;

    /** 使用目的 */
    @Excel(name = "使用目的")
    private String usagePurpose;

    /** 文件名称 */
    @Excel(name = "文件名称")
    private String documentName;

    /** 文件数量 */
    @Excel(name = "文件数量")
    private Integer documentCount;

    /** 预计使用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "预计使用时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expectedUseTime;

    /** 实际使用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际使用时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date actualUseTime;

    /** 归还时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "归还时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date returnTime;

    /** 使用地点 */
    @Excel(name = "使用地点")
    private String useLocation;

    /** 紧急程度(1普通2紧急3特急) */
    @Excel(name = "紧急程度", readConverterExp = "1=普通,2=紧急,3=特急")
    private String urgencyLevel;

    /** 状态(1待审批2已审批3已拒绝4使用中5已归还6已取消) */
    @Excel(name = "状态", readConverterExp = "1=待审批,2=已审批,3=已拒绝,4=使用中,5=已归还,6=已取消")
    private String status;

    /** 审批人ID */
    @Excel(name = "审批人ID")
    private Long approverId;

    /** 审批人姓名 */
    @Excel(name = "审批人姓名")
    private String approverName;

    /** 审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审批时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;

    /** 审批意见 */
    @Excel(name = "审批意见")
    private String approvalComment;

    /** 保管人确认 */
    @Excel(name = "保管人确认", readConverterExp = "0=未确认,1=已确认")
    private String keeperConfirm;

    /** 保管人确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "保管人确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date keeperConfirmTime;

    /** 使用说明 */
    @Excel(name = "使用说明")
    private String usageInstructions;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 流程实例ID */
    private Long instanceId;

    /** 附件列表 */
    private List<OaFileAttachment> attachments;

    /** 印章信息 */
    private OaSealCertificate sealCertificate;

    public void setApplicationId(Long applicationId) 
    {
        this.applicationId = applicationId;
    }

    public Long getApplicationId() 
    {
        return applicationId;
    }
    public void setApplicationNo(String applicationNo) 
    {
        this.applicationNo = applicationNo;
    }

    public String getApplicationNo() 
    {
        return applicationNo;
    }
    public void setApplicantId(Long applicantId) 
    {
        this.applicantId = applicantId;
    }

    public Long getApplicantId() 
    {
        return applicantId;
    }
    public void setApplicantName(String applicantName) 
    {
        this.applicantName = applicantName;
    }

    public String getApplicantName() 
    {
        return applicantName;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptName() 
    {
        return deptName;
    }
    public void setSealId(Long sealId) 
    {
        this.sealId = sealId;
    }

    public Long getSealId() 
    {
        return sealId;
    }
    public void setSealName(String sealName) 
    {
        this.sealName = sealName;
    }

    public String getSealName() 
    {
        return sealName;
    }
    public void setApplicationReason(String applicationReason) 
    {
        this.applicationReason = applicationReason;
    }

    public String getApplicationReason() 
    {
        return applicationReason;
    }
    public void setUsagePurpose(String usagePurpose) 
    {
        this.usagePurpose = usagePurpose;
    }

    public String getUsagePurpose() 
    {
        return usagePurpose;
    }
    public void setDocumentName(String documentName) 
    {
        this.documentName = documentName;
    }

    public String getDocumentName() 
    {
        return documentName;
    }
    public void setDocumentCount(Integer documentCount) 
    {
        this.documentCount = documentCount;
    }

    public Integer getDocumentCount() 
    {
        return documentCount;
    }
    public void setExpectedUseTime(Date expectedUseTime) 
    {
        this.expectedUseTime = expectedUseTime;
    }

    public Date getExpectedUseTime() 
    {
        return expectedUseTime;
    }
    public void setActualUseTime(Date actualUseTime) 
    {
        this.actualUseTime = actualUseTime;
    }

    public Date getActualUseTime() 
    {
        return actualUseTime;
    }
    public void setReturnTime(Date returnTime) 
    {
        this.returnTime = returnTime;
    }

    public Date getReturnTime() 
    {
        return returnTime;
    }
    public void setUseLocation(String useLocation) 
    {
        this.useLocation = useLocation;
    }

    public String getUseLocation() 
    {
        return useLocation;
    }
    public void setUrgencyLevel(String urgencyLevel) 
    {
        this.urgencyLevel = urgencyLevel;
    }

    public String getUrgencyLevel() 
    {
        return urgencyLevel;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setApproverId(Long approverId) 
    {
        this.approverId = approverId;
    }

    public Long getApproverId() 
    {
        return approverId;
    }
    public void setApproverName(String approverName) 
    {
        this.approverName = approverName;
    }

    public String getApproverName() 
    {
        return approverName;
    }
    public void setApprovalTime(Date approvalTime) 
    {
        this.approvalTime = approvalTime;
    }

    public Date getApprovalTime() 
    {
        return approvalTime;
    }
    public void setApprovalComment(String approvalComment) 
    {
        this.approvalComment = approvalComment;
    }

    public String getApprovalComment() 
    {
        return approvalComment;
    }
    public void setKeeperConfirm(String keeperConfirm) 
    {
        this.keeperConfirm = keeperConfirm;
    }

    public String getKeeperConfirm() 
    {
        return keeperConfirm;
    }
    public void setKeeperConfirmTime(Date keeperConfirmTime) 
    {
        this.keeperConfirmTime = keeperConfirmTime;
    }

    public Date getKeeperConfirmTime() 
    {
        return keeperConfirmTime;
    }
    public void setUsageInstructions(String usageInstructions) 
    {
        this.usageInstructions = usageInstructions;
    }

    public String getUsageInstructions() 
    {
        return usageInstructions;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }

    public Long getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(Long instanceId) {
        this.instanceId = instanceId;
    }

    public List<OaFileAttachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<OaFileAttachment> attachments) {
        this.attachments = attachments;
    }

    public OaSealCertificate getSealCertificate() {
        return sealCertificate;
    }

    public void setSealCertificate(OaSealCertificate sealCertificate) {
        this.sealCertificate = sealCertificate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("applicationId", getApplicationId())
            .append("applicationNo", getApplicationNo())
            .append("applicantId", getApplicantId())
            .append("applicantName", getApplicantName())
            .append("deptId", getDeptId())
            .append("deptName", getDeptName())
            .append("sealId", getSealId())
            .append("sealName", getSealName())
            .append("applicationReason", getApplicationReason())
            .append("usagePurpose", getUsagePurpose())
            .append("documentName", getDocumentName())
            .append("documentCount", getDocumentCount())
            .append("expectedUseTime", getExpectedUseTime())
            .append("actualUseTime", getActualUseTime())
            .append("returnTime", getReturnTime())
            .append("useLocation", getUseLocation())
            .append("urgencyLevel", getUrgencyLevel())
            .append("status", getStatus())
            .append("approverId", getApproverId())
            .append("approverName", getApproverName())
            .append("approvalTime", getApprovalTime())
            .append("approvalComment", getApprovalComment())
            .append("keeperConfirm", getKeeperConfirm())
            .append("keeperConfirmTime", getKeeperConfirmTime())
            .append("usageInstructions", getUsageInstructions())
            .append("contactPhone", getContactPhone())
            .append("createTime", getCreateTime())
            .toString();
    }
}
