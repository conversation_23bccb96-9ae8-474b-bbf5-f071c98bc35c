package com.ruoyi.oa.seal.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 印章证照对象 oa_seal_certificate
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public class OaSealCertificate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 印章ID */
    private Long sealId;

    /** 印章名称 */
    @Excel(name = "印章名称")
    private String sealName;

    /** 印章类型(1公章2财务章3法人章4合同章5部门章6业务章) */
    @Excel(name = "印章类型", readConverterExp = "1=公章,2=财务章,3=法人章,4=合同章,5=部门章,6=业务章")
    private String sealType;

    /** 印章编号 */
    @Excel(name = "印章编号")
    private String sealCode;

    /** 印章图片 */
    private String sealImage;

    /** 印章描述 */
    @Excel(name = "印章描述")
    private String sealDescription;

    /** 保管人ID */
    @Excel(name = "保管人ID")
    private Long keeperId;

    /** 保管人姓名 */
    @Excel(name = "保管人姓名")
    private String keeperName;

    /** 保管部门ID */
    @Excel(name = "保管部门ID")
    private Long keeperDeptId;

    /** 保管部门名称 */
    @Excel(name = "保管部门名称")
    private String keeperDeptName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 状态(0停用1启用2封存) */
    @Excel(name = "状态", readConverterExp = "0=停用,1=启用,2=封存")
    private String status;

    /** 启用日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "启用日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date enableDate;

    /** 停用日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "停用日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date disableDate;

    /** 制作单位 */
    @Excel(name = "制作单位")
    private String manufacturer;

    /** 制作日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "制作日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date manufactureDate;

    /** 材质 */
    @Excel(name = "材质")
    private String material;

    /** 规格尺寸 */
    @Excel(name = "规格尺寸")
    private String specifications;

    /** 使用范围 */
    @Excel(name = "使用范围")
    private String usageScope;

    /** 审批权限 */
    @Excel(name = "审批权限")
    private String approvalAuthority;

    /** 备案编号 */
    @Excel(name = "备案编号")
    private String recordNumber;

    /** 备案日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "备案日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date recordDate;

    /** 排序 */
    @Excel(name = "排序")
    private Integer orderNum;

    public void setSealId(Long sealId) 
    {
        this.sealId = sealId;
    }

    public Long getSealId() 
    {
        return sealId;
    }
    public void setSealName(String sealName) 
    {
        this.sealName = sealName;
    }

    public String getSealName() 
    {
        return sealName;
    }
    public void setSealType(String sealType) 
    {
        this.sealType = sealType;
    }

    public String getSealType() 
    {
        return sealType;
    }
    public void setSealCode(String sealCode) 
    {
        this.sealCode = sealCode;
    }

    public String getSealCode() 
    {
        return sealCode;
    }
    public void setSealImage(String sealImage) 
    {
        this.sealImage = sealImage;
    }

    public String getSealImage() 
    {
        return sealImage;
    }
    public void setSealDescription(String sealDescription) 
    {
        this.sealDescription = sealDescription;
    }

    public String getSealDescription() 
    {
        return sealDescription;
    }
    public void setKeeperId(Long keeperId) 
    {
        this.keeperId = keeperId;
    }

    public Long getKeeperId() 
    {
        return keeperId;
    }
    public void setKeeperName(String keeperName) 
    {
        this.keeperName = keeperName;
    }

    public String getKeeperName() 
    {
        return keeperName;
    }
    public void setKeeperDeptId(Long keeperDeptId) 
    {
        this.keeperDeptId = keeperDeptId;
    }

    public Long getKeeperDeptId() 
    {
        return keeperDeptId;
    }
    public void setKeeperDeptName(String keeperDeptName) 
    {
        this.keeperDeptName = keeperDeptName;
    }

    public String getKeeperDeptName() 
    {
        return keeperDeptName;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setEnableDate(Date enableDate) 
    {
        this.enableDate = enableDate;
    }

    public Date getEnableDate() 
    {
        return enableDate;
    }
    public void setDisableDate(Date disableDate) 
    {
        this.disableDate = disableDate;
    }

    public Date getDisableDate() 
    {
        return disableDate;
    }
    public void setManufacturer(String manufacturer) 
    {
        this.manufacturer = manufacturer;
    }

    public String getManufacturer() 
    {
        return manufacturer;
    }
    public void setManufactureDate(Date manufactureDate) 
    {
        this.manufactureDate = manufactureDate;
    }

    public Date getManufactureDate() 
    {
        return manufactureDate;
    }
    public void setMaterial(String material) 
    {
        this.material = material;
    }

    public String getMaterial() 
    {
        return material;
    }
    public void setSpecifications(String specifications) 
    {
        this.specifications = specifications;
    }

    public String getSpecifications() 
    {
        return specifications;
    }
    public void setUsageScope(String usageScope) 
    {
        this.usageScope = usageScope;
    }

    public String getUsageScope() 
    {
        return usageScope;
    }
    public void setApprovalAuthority(String approvalAuthority) 
    {
        this.approvalAuthority = approvalAuthority;
    }

    public String getApprovalAuthority() 
    {
        return approvalAuthority;
    }
    public void setRecordNumber(String recordNumber) 
    {
        this.recordNumber = recordNumber;
    }

    public String getRecordNumber() 
    {
        return recordNumber;
    }
    public void setRecordDate(Date recordDate) 
    {
        this.recordDate = recordDate;
    }

    public Date getRecordDate() 
    {
        return recordDate;
    }
    public void setOrderNum(Integer orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() 
    {
        return orderNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("sealId", getSealId())
            .append("sealName", getSealName())
            .append("sealType", getSealType())
            .append("sealCode", getSealCode())
            .append("sealImage", getSealImage())
            .append("sealDescription", getSealDescription())
            .append("keeperId", getKeeperId())
            .append("keeperName", getKeeperName())
            .append("keeperDeptId", getKeeperDeptId())
            .append("keeperDeptName", getKeeperDeptName())
            .append("contactPhone", getContactPhone())
            .append("status", getStatus())
            .append("enableDate", getEnableDate())
            .append("disableDate", getDisableDate())
            .append("manufacturer", getManufacturer())
            .append("manufactureDate", getManufactureDate())
            .append("material", getMaterial())
            .append("specifications", getSpecifications())
            .append("usageScope", getUsageScope())
            .append("approvalAuthority", getApprovalAuthority())
            .append("recordNumber", getRecordNumber())
            .append("recordDate", getRecordDate())
            .append("orderNum", getOrderNum())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
