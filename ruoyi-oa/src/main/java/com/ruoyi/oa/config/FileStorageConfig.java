package com.ruoyi.oa.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.io.File;

/**
 * 文件存储配置
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Configuration
public class FileStorageConfig {

    /** 文件上传路径 */
    @Value("${ruoyi.profile:./uploadPath}")
    private String profile;

    /** 获取头像上传路径 */
    public static String getAvatarPath() {
        return getProfile() + "/avatar";
    }

    /** 获取下载路径 */
    public static String getDownloadPath() {
        return getProfile() + "/download/";
    }

    /** 获取上传路径 */
    public static String getUploadPath() {
        return getProfile() + "/upload";
    }

    /** 获取OA文件上传路径 */
    public static String getOaUploadPath() {
        return getProfile() + "/oa";
    }

    /** 获取公文附件上传路径 */
    public static String getDocumentPath() {
        return getOaUploadPath() + "/document";
    }

    /** 获取签名图片上传路径 */
    public static String getSignaturePath() {
        return getOaUploadPath() + "/signature";
    }

    /** 获取印章图片上传路径 */
    public static String getSealPath() {
        return getOaUploadPath() + "/seal";
    }

    /** 获取会议附件上传路径 */
    public static String getMeetingPath() {
        return getOaUploadPath() + "/meeting";
    }

    /** 获取工作报告附件上传路径 */
    public static String getReportPath() {
        return getOaUploadPath() + "/report";
    }

    /** 获取临时文件路径 */
    public static String getTempPath() {
        return getProfile() + "/temp";
    }

    public static String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        FileStorageConfig.profile = profile;
    }

    /**
     * 初始化创建目录
     */
    @PostConstruct
    public void initPath() {
        createDir(getProfile());
        createDir(getAvatarPath());
        createDir(getDownloadPath());
        createDir(getUploadPath());
        createDir(getOaUploadPath());
        createDir(getDocumentPath());
        createDir(getSignaturePath());
        createDir(getSealPath());
        createDir(getMeetingPath());
        createDir(getReportPath());
        createDir(getTempPath());
    }

    /**
     * 创建目录
     */
    private void createDir(String path) {
        File dir = new File(path);
        if (!dir.exists()) {
            dir.mkdirs();
        }
    }
}
