package com.ruoyi.oa.config;

import org.flowable.engine.ProcessEngine;
import org.flowable.engine.ProcessEngineConfiguration;
import org.flowable.engine.impl.cfg.StandaloneProcessEngineConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * Flowable工作流引擎配置
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@Configuration
public class FlowableConfig {

    @Autowired
    private DataSource dataSource;

    @Value("${flowable.database-schema-update:true}")
    private boolean databaseSchemaUpdate;

    @Value("${flowable.async-executor-activate:false}")
    private boolean asyncExecutorActivate;

    /**
     * 配置流程引擎
     */
    @Bean
    public ProcessEngine processEngine() {
        ProcessEngineConfiguration cfg = new StandaloneProcessEngineConfiguration()
            .setDataSource(dataSource)
            .setDatabaseSchemaUpdate(databaseSchemaUpdate ?
                ProcessEngineConfiguration.DB_SCHEMA_UPDATE_TRUE :
                ProcessEngineConfiguration.DB_SCHEMA_UPDATE_FALSE)
            .setAsyncExecutorActivate(asyncExecutorActivate)
            // 设置字体，支持中文
            .setActivityFontName("宋体")
            .setLabelFontName("宋体")
            .setAnnotationFontName("宋体")
            // 禁用定时任务JOB
            .setJobExecutorActivate(false);

        return cfg.buildProcessEngine();
    }
}
