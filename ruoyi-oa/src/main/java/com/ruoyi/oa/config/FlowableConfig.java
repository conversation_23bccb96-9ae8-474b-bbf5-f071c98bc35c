package com.ruoyi.oa.config;

import org.flowable.engine.ProcessEngine;
import org.flowable.engine.ProcessEngineConfiguration;
import org.flowable.engine.impl.cfg.StandaloneProcessEngineConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * Flowable工作流引擎配置
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Configuration
public class FlowableConfig {

    @Autowired
    private DataSource dataSource;

    /**
     * 配置流程引擎
     */
    @Bean
    public ProcessEngine processEngine() {
        ProcessEngineConfiguration cfg = new StandaloneProcessEngineConfiguration()
            .setDataSource(dataSource)
            .setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_TRUE)
            .setAsyncExecutorActivate(false)
            .setMailServerHost("localhost")
            .setMailServerPort(1025);
        
        return cfg.buildProcessEngine();
    }
}
