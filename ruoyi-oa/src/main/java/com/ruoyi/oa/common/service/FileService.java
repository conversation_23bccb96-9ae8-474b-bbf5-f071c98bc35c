package com.ruoyi.oa.common.service;

import java.io.File;
import java.io.IOException;
import java.util.UUID;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.MimeTypeUtils;
import com.ruoyi.oa.config.FileStorageConfig;

/**
 * OA文件服务
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class FileService {

    /**
     * 上传文件
     * 
     * @param file 文件
     * @param subPath 子路径
     * @return 文件路径
     */
    public String uploadFile(MultipartFile file, String subPath) throws IOException {
        if (file.isEmpty()) {
            throw new ServiceException("上传文件不能为空");
        }

        // 获取文件名
        String fileName = file.getOriginalFilename();
        String extension = getExtension(fileName);
        
        // 验证文件类型
        assertAllowed(file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        
        // 生成新文件名
        String newFileName = generateFileName(extension);
        
        // 构建文件路径
        String datePath = DateUtils.datePath();
        String uploadPath = FileStorageConfig.getOaUploadPath() + "/" + subPath + "/" + datePath;
        
        // 创建目录
        File uploadDir = new File(uploadPath);
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }
        
        // 保存文件
        String filePath = uploadPath + "/" + newFileName;
        File dest = new File(filePath);
        file.transferTo(dest);
        
        // 返回相对路径
        return "/" + subPath + "/" + datePath + "/" + newFileName;
    }

    /**
     * 上传签名图片
     */
    public String uploadSignature(MultipartFile file) throws IOException {
        return uploadFile(file, "signature");
    }

    /**
     * 上传公文附件
     */
    public String uploadDocument(MultipartFile file) throws IOException {
        return uploadFile(file, "document");
    }

    /**
     * 上传印章图片
     */
    public String uploadSeal(MultipartFile file) throws IOException {
        return uploadFile(file, "seal");
    }

    /**
     * 上传会议附件
     */
    public String uploadMeeting(MultipartFile file) throws IOException {
        return uploadFile(file, "meeting");
    }

    /**
     * 上传工作报告附件
     */
    public String uploadReport(MultipartFile file) throws IOException {
        return uploadFile(file, "report");
    }

    /**
     * 删除文件
     */
    public boolean deleteFile(String filePath) {
        if (StringUtils.isEmpty(filePath)) {
            return false;
        }
        
        String fullPath = FileStorageConfig.getOaUploadPath() + filePath;
        File file = new File(fullPath);
        if (file.exists() && file.isFile()) {
            return file.delete();
        }
        return false;
    }

    /**
     * 获取文件完整路径
     */
    public String getFullPath(String relativePath) {
        if (StringUtils.isEmpty(relativePath)) {
            return null;
        }
        return FileStorageConfig.getOaUploadPath() + relativePath;
    }

    /**
     * 获取文件扩展名
     */
    private String getExtension(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex == -1) {
            return "";
        }
        return fileName.substring(lastDotIndex);
    }

    /**
     * 生成新文件名
     */
    private String generateFileName(String extension) {
        return UUID.randomUUID().toString().replace("-", "") + extension;
    }

    /**
     * 文件大小校验
     */
    public static final void assertAllowed(MultipartFile file, String[] allowedExtension) throws ServiceException {
        long size = file.getSize();
        if (size > Constants.DEFAULT_MAX_SIZE) {
            throw new ServiceException("上传的文件大小超出限制的文件大小！");
        }

        String fileName = file.getOriginalFilename();
        String extension = FileUploadUtils.getExtension(file);
        if (allowedExtension != null && !isAllowedExtension(extension, allowedExtension)) {
            throw new ServiceException("文件[" + fileName + "]后缀[" + extension + "]不正确，请上传" + String.join(",", allowedExtension) + "格式");
        }
    }

    /**
     * 判断MIME类型是否是允许的MIME类型
     */
    public static final boolean isAllowedExtension(String extension, String[] allowedExtension) {
        for (String str : allowedExtension) {
            if (str.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }
}
