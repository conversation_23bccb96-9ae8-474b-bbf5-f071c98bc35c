package com.ruoyi.oa.personal.domain;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.oa.document.domain.OaFileAttachment;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 工作报告对象 oa_work_report
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public class OaWorkReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 报告ID */
    private Long reportId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户姓名 */
    @Excel(name = "用户姓名")
    private String userName;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 报告类型(1日报2周报3月报4年报) */
    @Excel(name = "报告类型", readConverterExp = "1=日报,2=周报,3=月报,4=年报")
    private String reportType;

    /** 报告日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "报告日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date reportDate;

    /** 报告标题 */
    @Excel(name = "报告标题")
    private String title;

    /** 工作内容 */
    @Excel(name = "工作内容")
    private String workContent;

    /** 完成情况 */
    @Excel(name = "完成情况")
    private String completionStatus;

    /** 存在问题 */
    @Excel(name = "存在问题")
    private String problems;

    /** 下步计划 */
    @Excel(name = "下步计划")
    private String nextPlan;

    /** 需要协调事项 */
    @Excel(name = "需要协调事项")
    private String coordinationMatters;

    /** 状态(1草稿2已提交3已审阅) */
    @Excel(name = "状态", readConverterExp = "1=草稿,2=已提交,3=已审阅")
    private String status;

    /** 提交时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /** 审阅人ID */
    @Excel(name = "审阅人ID")
    private Long reviewerId;

    /** 审阅人姓名 */
    @Excel(name = "审阅人姓名")
    private String reviewerName;

    /** 审阅时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审阅时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    /** 审阅意见 */
    @Excel(name = "审阅意见")
    private String reviewComment;

    /** 工作量统计 */
    @Excel(name = "工作量统计")
    private String workloadStats;

    /** 附件列表 */
    private List<OaFileAttachment> attachments;

    public void setReportId(Long reportId) 
    {
        this.reportId = reportId;
    }

    public Long getReportId() 
    {
        return reportId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptName() 
    {
        return deptName;
    }
    public void setReportType(String reportType) 
    {
        this.reportType = reportType;
    }

    public String getReportType() 
    {
        return reportType;
    }
    public void setReportDate(Date reportDate) 
    {
        this.reportDate = reportDate;
    }

    public Date getReportDate() 
    {
        return reportDate;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setWorkContent(String workContent) 
    {
        this.workContent = workContent;
    }

    public String getWorkContent() 
    {
        return workContent;
    }
    public void setCompletionStatus(String completionStatus) 
    {
        this.completionStatus = completionStatus;
    }

    public String getCompletionStatus() 
    {
        return completionStatus;
    }
    public void setProblems(String problems) 
    {
        this.problems = problems;
    }

    public String getProblems() 
    {
        return problems;
    }
    public void setNextPlan(String nextPlan) 
    {
        this.nextPlan = nextPlan;
    }

    public String getNextPlan() 
    {
        return nextPlan;
    }
    public void setCoordinationMatters(String coordinationMatters) 
    {
        this.coordinationMatters = coordinationMatters;
    }

    public String getCoordinationMatters() 
    {
        return coordinationMatters;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setSubmitTime(Date submitTime) 
    {
        this.submitTime = submitTime;
    }

    public Date getSubmitTime() 
    {
        return submitTime;
    }
    public void setReviewerId(Long reviewerId) 
    {
        this.reviewerId = reviewerId;
    }

    public Long getReviewerId() 
    {
        return reviewerId;
    }
    public void setReviewerName(String reviewerName) 
    {
        this.reviewerName = reviewerName;
    }

    public String getReviewerName() 
    {
        return reviewerName;
    }
    public void setReviewTime(Date reviewTime) 
    {
        this.reviewTime = reviewTime;
    }

    public Date getReviewTime() 
    {
        return reviewTime;
    }
    public void setReviewComment(String reviewComment) 
    {
        this.reviewComment = reviewComment;
    }

    public String getReviewComment() 
    {
        return reviewComment;
    }
    public void setWorkloadStats(String workloadStats) 
    {
        this.workloadStats = workloadStats;
    }

    public String getWorkloadStats() 
    {
        return workloadStats;
    }

    public List<OaFileAttachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<OaFileAttachment> attachments) {
        this.attachments = attachments;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("reportId", getReportId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("deptId", getDeptId())
            .append("deptName", getDeptName())
            .append("reportType", getReportType())
            .append("reportDate", getReportDate())
            .append("title", getTitle())
            .append("workContent", getWorkContent())
            .append("completionStatus", getCompletionStatus())
            .append("problems", getProblems())
            .append("nextPlan", getNextPlan())
            .append("coordinationMatters", getCoordinationMatters())
            .append("status", getStatus())
            .append("submitTime", getSubmitTime())
            .append("reviewerId", getReviewerId())
            .append("reviewerName", getReviewerName())
            .append("reviewTime", getReviewTime())
            .append("reviewComment", getReviewComment())
            .append("workloadStats", getWorkloadStats())
            .append("createTime", getCreateTime())
            .toString();
    }
}
