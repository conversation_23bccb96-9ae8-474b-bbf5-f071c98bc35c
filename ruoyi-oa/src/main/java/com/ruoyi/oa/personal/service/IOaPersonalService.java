package com.ruoyi.oa.personal.service;

import java.util.List;
import com.ruoyi.oa.personal.domain.OaPersonalSchedule;
import com.ruoyi.oa.personal.domain.OaPersonalContact;
import com.ruoyi.oa.personal.domain.OaWorkReport;

/**
 * 个人办公服务接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface IOaPersonalService 
{
    // ==================== 个人信息管理 ====================

    /**
     * 获取用户个人信息
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    public Object getUserProfile(Long userId);

    /**
     * 更新用户个人信息
     * 
     * @param userId 用户ID
     * @param userProfile 用户信息
     * @return 结果
     */
    public int updateUserProfile(Long userId, Object userProfile);

    // ==================== 日程管理 ====================

    /**
     * 查询个人日程列表
     * 
     * @param oaPersonalSchedule 个人日程
     * @return 个人日程集合
     */
    public List<OaPersonalSchedule> selectOaPersonalScheduleList(OaPersonalSchedule oaPersonalSchedule);

    /**
     * 查询个人日程
     * 
     * @param scheduleId 个人日程主键
     * @return 个人日程
     */
    public OaPersonalSchedule selectOaPersonalScheduleByScheduleId(Long scheduleId);

    /**
     * 新增个人日程
     * 
     * @param oaPersonalSchedule 个人日程
     * @return 结果
     */
    public int insertOaPersonalSchedule(OaPersonalSchedule oaPersonalSchedule);

    /**
     * 修改个人日程
     * 
     * @param oaPersonalSchedule 个人日程
     * @return 结果
     */
    public int updateOaPersonalSchedule(OaPersonalSchedule oaPersonalSchedule);

    /**
     * 批量删除个人日程
     * 
     * @param scheduleIds 需要删除的个人日程主键集合
     * @return 结果
     */
    public int deleteOaPersonalScheduleByScheduleIds(Long[] scheduleIds);

    /**
     * 删除个人日程信息
     * 
     * @param scheduleId 个人日程主键
     * @return 结果
     */
    public int deleteOaPersonalScheduleByScheduleId(Long scheduleId);

    /**
     * 查询今日日程
     * 
     * @param userId 用户ID
     * @return 今日日程列表
     */
    public List<OaPersonalSchedule> selectTodaySchedules(Long userId);

    /**
     * 查询本周日程
     * 
     * @param userId 用户ID
     * @return 本周日程列表
     */
    public List<OaPersonalSchedule> selectWeekSchedules(Long userId);

    /**
     * 查询本月日程
     * 
     * @param userId 用户ID
     * @return 本月日程列表
     */
    public List<OaPersonalSchedule> selectMonthSchedules(Long userId);

    // ==================== 个人通讯录 ====================

    /**
     * 查询个人通讯录列表
     * 
     * @param oaPersonalContact 个人通讯录
     * @return 个人通讯录集合
     */
    public List<OaPersonalContact> selectOaPersonalContactList(OaPersonalContact oaPersonalContact);

    /**
     * 查询个人通讯录
     * 
     * @param contactId 个人通讯录主键
     * @return 个人通讯录
     */
    public OaPersonalContact selectOaPersonalContactByContactId(Long contactId);

    /**
     * 新增个人通讯录
     * 
     * @param oaPersonalContact 个人通讯录
     * @return 结果
     */
    public int insertOaPersonalContact(OaPersonalContact oaPersonalContact);

    /**
     * 修改个人通讯录
     * 
     * @param oaPersonalContact 个人通讯录
     * @return 结果
     */
    public int updateOaPersonalContact(OaPersonalContact oaPersonalContact);

    /**
     * 批量删除个人通讯录
     * 
     * @param contactIds 需要删除的个人通讯录主键集合
     * @return 结果
     */
    public int deleteOaPersonalContactByContactIds(Long[] contactIds);

    /**
     * 删除个人通讯录信息
     * 
     * @param contactId 个人通讯录主键
     * @return 结果
     */
    public int deleteOaPersonalContactByContactId(Long contactId);

    /**
     * 查询内部通讯录
     * 
     * @return 内部通讯录列表
     */
    public List<Object> selectInternalContactList();

    // ==================== 工作报告 ====================

    /**
     * 查询工作报告列表
     * 
     * @param oaWorkReport 工作报告
     * @return 工作报告集合
     */
    public List<OaWorkReport> selectOaWorkReportList(OaWorkReport oaWorkReport);

    /**
     * 查询工作报告
     * 
     * @param reportId 工作报告主键
     * @return 工作报告
     */
    public OaWorkReport selectOaWorkReportByReportId(Long reportId);

    /**
     * 新增工作报告
     * 
     * @param oaWorkReport 工作报告
     * @return 结果
     */
    public int insertOaWorkReport(OaWorkReport oaWorkReport);

    /**
     * 修改工作报告
     * 
     * @param oaWorkReport 工作报告
     * @return 结果
     */
    public int updateOaWorkReport(OaWorkReport oaWorkReport);

    /**
     * 批量删除工作报告
     * 
     * @param reportIds 需要删除的工作报告主键集合
     * @return 结果
     */
    public int deleteOaWorkReportByReportIds(Long[] reportIds);

    /**
     * 删除工作报告信息
     * 
     * @param reportId 工作报告主键
     * @return 结果
     */
    public int deleteOaWorkReportByReportId(Long reportId);

    /**
     * 提交工作报告
     * 
     * @param reportId 工作报告ID
     * @return 结果
     */
    public int submitWorkReport(Long reportId);

    /**
     * 查询下属工作报告
     * 
     * @param userId 用户ID
     * @param oaWorkReport 查询条件
     * @return 下属工作报告列表
     */
    public List<OaWorkReport> selectSubordinateReports(Long userId, OaWorkReport oaWorkReport);

    /**
     * 审阅工作报告
     * 
     * @param reportId 工作报告ID
     * @param reviewComment 审阅意见
     * @param reviewerId 审阅人ID
     * @return 结果
     */
    public int reviewWorkReport(Long reportId, String reviewComment, Long reviewerId);

    /**
     * 统计用户工作报告
     * 
     * @param userId 用户ID
     * @param reportType 报告类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    public Object getWorkReportStatistics(Long userId, String reportType, String startDate, String endDate);
}
