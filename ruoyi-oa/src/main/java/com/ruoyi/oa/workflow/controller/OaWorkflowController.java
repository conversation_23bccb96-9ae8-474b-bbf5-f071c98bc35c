package com.ruoyi.oa.workflow.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.oa.workflow.domain.OaWorkflowDefinition;
import com.ruoyi.oa.workflow.domain.OaWorkflowInstance;
import com.ruoyi.oa.workflow.domain.OaWorkflowTask;
import com.ruoyi.oa.workflow.service.IOaWorkflowService;

/**
 * 工作流管理Controller
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@RestController
@RequestMapping("/oa/workflow")
public class OaWorkflowController extends BaseController
{
    @Autowired
    private IOaWorkflowService oaWorkflowService;

    /**
     * 查询工作流定义列表
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:definition:list')")
    @GetMapping("/definition/list")
    public TableDataInfo list(OaWorkflowDefinition oaWorkflowDefinition)
    {
        startPage();
        List<OaWorkflowDefinition> list = oaWorkflowService.selectOaWorkflowDefinitionList(oaWorkflowDefinition);
        return getDataTable(list);
    }

    /**
     * 导出工作流定义列表
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:definition:export')")
    @Log(title = "工作流定义", businessType = BusinessType.EXPORT)
    @PostMapping("/definition/export")
    public void export(HttpServletResponse response, OaWorkflowDefinition oaWorkflowDefinition)
    {
        List<OaWorkflowDefinition> list = oaWorkflowService.selectOaWorkflowDefinitionList(oaWorkflowDefinition);
        ExcelUtil<OaWorkflowDefinition> util = new ExcelUtil<OaWorkflowDefinition>(OaWorkflowDefinition.class);
        util.exportExcel(response, list, "工作流定义数据");
    }

    /**
     * 获取工作流定义详细信息
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:definition:query')")
    @GetMapping(value = "/definition/{workflowId}")
    public AjaxResult getInfo(@PathVariable("workflowId") Long workflowId)
    {
        return success(oaWorkflowService.selectOaWorkflowDefinitionByWorkflowId(workflowId));
    }

    /**
     * 新增工作流定义
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:definition:add')")
    @Log(title = "工作流定义", businessType = BusinessType.INSERT)
    @PostMapping("/definition")
    public AjaxResult add(@RequestBody OaWorkflowDefinition oaWorkflowDefinition)
    {
        return toAjax(oaWorkflowService.insertOaWorkflowDefinition(oaWorkflowDefinition));
    }

    /**
     * 修改工作流定义
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:definition:edit')")
    @Log(title = "工作流定义", businessType = BusinessType.UPDATE)
    @PutMapping("/definition")
    public AjaxResult edit(@RequestBody OaWorkflowDefinition oaWorkflowDefinition)
    {
        return toAjax(oaWorkflowService.updateOaWorkflowDefinition(oaWorkflowDefinition));
    }

    /**
     * 删除工作流定义
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:definition:remove')")
    @Log(title = "工作流定义", businessType = BusinessType.DELETE)
    @DeleteMapping("/definition/{workflowIds}")
    public AjaxResult remove(@PathVariable Long[] workflowIds)
    {
        return toAjax(oaWorkflowService.deleteOaWorkflowDefinitionByWorkflowIds(workflowIds));
    }

    /**
     * 启动工作流程
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:process:start')")
    @Log(title = "启动流程", businessType = BusinessType.INSERT)
    @PostMapping("/process/start")
    public AjaxResult startProcess(@RequestBody Map<String, Object> params)
    {
        String workflowKey = (String) params.get("workflowKey");
        String businessKey = (String) params.get("businessKey");
        Map<String, Object> variables = (Map<String, Object>) params.get("variables");
        
        String processInstanceId = oaWorkflowService.startProcess(workflowKey, businessKey, variables);
        if (processInstanceId != null) {
            return success("流程启动成功", processInstanceId);
        } else {
            return error("流程启动失败");
        }
    }

    /**
     * 完成任务
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:task:complete')")
    @Log(title = "完成任务", businessType = BusinessType.UPDATE)
    @PostMapping("/task/complete/{taskId}")
    public AjaxResult completeTask(@PathVariable String taskId, @RequestBody Map<String, Object> params)
    {
        Map<String, Object> variables = (Map<String, Object>) params.get("variables");
        String comment = (String) params.get("comment");
        
        boolean result = oaWorkflowService.completeTask(taskId, variables, comment);
        return result ? success("任务完成成功") : error("任务完成失败");
    }

    /**
     * 查询待办任务列表
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:task:todo')")
    @GetMapping("/task/todo")
    public TableDataInfo todoTasks()
    {
        Long userId = getUserId();
        List<OaWorkflowTask> list = oaWorkflowService.selectTodoTasks(userId);
        return getDataTable(list);
    }

    /**
     * 查询已办任务列表
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:task:done')")
    @GetMapping("/task/done")
    public TableDataInfo doneTasks()
    {
        Long userId = getUserId();
        List<OaWorkflowTask> list = oaWorkflowService.selectDoneTasks(userId);
        return getDataTable(list);
    }

    /**
     * 查询流程实例列表
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:instance:list')")
    @GetMapping("/instance/list")
    public TableDataInfo instanceList(OaWorkflowInstance oaWorkflowInstance)
    {
        startPage();
        List<OaWorkflowInstance> list = oaWorkflowService.selectOaWorkflowInstanceList(oaWorkflowInstance);
        return getDataTable(list);
    }

    /**
     * 获取流程实例详细信息
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:instance:query')")
    @GetMapping("/instance/{instanceId}")
    public AjaxResult getInstanceInfo(@PathVariable("instanceId") Long instanceId)
    {
        return success(oaWorkflowService.selectOaWorkflowInstanceByInstanceId(instanceId));
    }

    /**
     * 终止流程实例
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:instance:terminate')")
    @Log(title = "终止流程", businessType = BusinessType.UPDATE)
    @PostMapping("/instance/terminate/{instanceId}")
    public AjaxResult terminateProcess(@PathVariable Long instanceId, @RequestBody Map<String, Object> params)
    {
        String reason = (String) params.get("reason");
        boolean result = oaWorkflowService.terminateProcess(instanceId, reason);
        return result ? success("流程终止成功") : error("流程终止失败");
    }

    /**
     * 转办任务
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:task:delegate')")
    @Log(title = "转办任务", businessType = BusinessType.UPDATE)
    @PostMapping("/task/delegate/{taskId}")
    public AjaxResult delegateTask(@PathVariable String taskId, @RequestBody Map<String, Object> params)
    {
        Long targetUserId = Long.valueOf(params.get("targetUserId").toString());
        String comment = (String) params.get("comment");
        
        boolean result = oaWorkflowService.delegateTask(taskId, targetUserId, comment);
        return result ? success("任务转办成功") : error("任务转办失败");
    }

    /**
     * 获取流程图
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:diagram:view')")
    @GetMapping("/diagram/{processInstanceId}")
    public AjaxResult getProcessDiagram(@PathVariable String processInstanceId)
    {
        byte[] diagram = oaWorkflowService.getProcessDiagram(processInstanceId);
        if (diagram != null) {
            return success("获取流程图成功", diagram);
        } else {
            return error("获取流程图失败");
        }
    }

    /**
     * 查询流程历史
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:history:list')")
    @GetMapping("/history/{processInstanceId}")
    public AjaxResult getProcessHistory(@PathVariable String processInstanceId)
    {
        List<OaWorkflowTask> history = oaWorkflowService.selectProcessHistory(processInstanceId);
        return success(history);
    }

    /**
     * 部署流程定义
     */
    @PreAuthorize("@ss.hasPermi('oa:workflow:deploy')")
    @Log(title = "部署流程", businessType = BusinessType.INSERT)
    @PostMapping("/deploy")
    public AjaxResult deployProcess(@RequestBody Map<String, Object> params)
    {
        String workflowName = (String) params.get("workflowName");
        String workflowKey = (String) params.get("workflowKey");
        String bpmnXml = (String) params.get("bpmnXml");
        
        String deploymentId = oaWorkflowService.deployProcess(workflowName, workflowKey, bpmnXml);
        if (deploymentId != null) {
            return success("流程部署成功", deploymentId);
        } else {
            return error("流程部署失败");
        }
    }
}
