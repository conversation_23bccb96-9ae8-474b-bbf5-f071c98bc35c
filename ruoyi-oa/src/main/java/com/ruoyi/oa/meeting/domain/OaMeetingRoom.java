package com.ruoyi.oa.meeting.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 会议室对象 oa_meeting_room
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public class OaMeetingRoom extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 会议室ID */
    private Long roomId;

    /** 会议室名称 */
    @Excel(name = "会议室名称")
    private String roomName;

    /** 会议室编号 */
    @Excel(name = "会议室编号")
    private String roomCode;

    /** 会议室位置 */
    @Excel(name = "会议室位置")
    private String location;

    /** 容纳人数 */
    @Excel(name = "容纳人数")
    private Integer capacity;

    /** 设备配置 */
    @Excel(name = "设备配置")
    private String equipment;

    /** 会议室图片 */
    private String roomImage;

    /** 状态(0停用1启用) */
    @Excel(name = "状态", readConverterExp = "0=停用,1=启用")
    private String status;

    /** 管理员ID */
    @Excel(name = "管理员ID")
    private Long adminId;

    /** 管理员姓名 */
    @Excel(name = "管理员姓名")
    private String adminName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 使用说明 */
    @Excel(name = "使用说明")
    private String instructions;

    /** 预约规则 */
    @Excel(name = "预约规则")
    private String bookingRules;

    /** 收费标准 */
    @Excel(name = "收费标准")
    private String chargingStandard;

    /** 排序 */
    @Excel(name = "排序")
    private Integer orderNum;

    public void setRoomId(Long roomId) 
    {
        this.roomId = roomId;
    }

    public Long getRoomId() 
    {
        return roomId;
    }
    public void setRoomName(String roomName) 
    {
        this.roomName = roomName;
    }

    public String getRoomName() 
    {
        return roomName;
    }
    public void setRoomCode(String roomCode) 
    {
        this.roomCode = roomCode;
    }

    public String getRoomCode() 
    {
        return roomCode;
    }
    public void setLocation(String location) 
    {
        this.location = location;
    }

    public String getLocation() 
    {
        return location;
    }
    public void setCapacity(Integer capacity) 
    {
        this.capacity = capacity;
    }

    public Integer getCapacity() 
    {
        return capacity;
    }
    public void setEquipment(String equipment) 
    {
        this.equipment = equipment;
    }

    public String getEquipment() 
    {
        return equipment;
    }
    public void setRoomImage(String roomImage) 
    {
        this.roomImage = roomImage;
    }

    public String getRoomImage() 
    {
        return roomImage;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setAdminId(Long adminId) 
    {
        this.adminId = adminId;
    }

    public Long getAdminId() 
    {
        return adminId;
    }
    public void setAdminName(String adminName) 
    {
        this.adminName = adminName;
    }

    public String getAdminName() 
    {
        return adminName;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setInstructions(String instructions) 
    {
        this.instructions = instructions;
    }

    public String getInstructions() 
    {
        return instructions;
    }
    public void setBookingRules(String bookingRules) 
    {
        this.bookingRules = bookingRules;
    }

    public String getBookingRules() 
    {
        return bookingRules;
    }
    public void setChargingStandard(String chargingStandard) 
    {
        this.chargingStandard = chargingStandard;
    }

    public String getChargingStandard() 
    {
        return chargingStandard;
    }
    public void setOrderNum(Integer orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() 
    {
        return orderNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("roomId", getRoomId())
            .append("roomName", getRoomName())
            .append("roomCode", getRoomCode())
            .append("location", getLocation())
            .append("capacity", getCapacity())
            .append("equipment", getEquipment())
            .append("roomImage", getRoomImage())
            .append("status", getStatus())
            .append("adminId", getAdminId())
            .append("adminName", getAdminName())
            .append("contactPhone", getContactPhone())
            .append("instructions", getInstructions())
            .append("bookingRules", getBookingRules())
            .append("chargingStandard", getChargingStandard())
            .append("orderNum", getOrderNum())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
