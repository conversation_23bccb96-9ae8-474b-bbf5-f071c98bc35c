package com.ruoyi.oa.meeting.domain;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.oa.document.domain.OaFileAttachment;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 会议对象 oa_meeting
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public class OaMeeting extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 会议ID */
    private Long meetingId;

    /** 会议主题 */
    @Excel(name = "会议主题")
    private String meetingTitle;

    /** 会议类型(1内部会议2外部会议3视频会议4电话会议) */
    @Excel(name = "会议类型", readConverterExp = "1=内部会议,2=外部会议,3=视频会议,4=电话会议")
    private String meetingType;

    /** 会议室ID */
    @Excel(name = "会议室ID")
    private Long roomId;

    /** 会议室名称 */
    @Excel(name = "会议室名称")
    private String roomName;

    /** 会议地点 */
    @Excel(name = "会议地点")
    private String location;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 组织者ID */
    @Excel(name = "组织者ID")
    private Long organizerId;

    /** 组织者姓名 */
    @Excel(name = "组织者姓名")
    private String organizerName;

    /** 参会人员 */
    @Excel(name = "参会人员")
    private String participants;

    /** 参会人员ID列表 */
    private String participantIds;

    /** 会议内容 */
    @Excel(name = "会议内容")
    private String content;

    /** 会议议程 */
    @Excel(name = "会议议程")
    private String agenda;

    /** 会议纪要 */
    @Excel(name = "会议纪要")
    private String minutes;

    /** 状态(1待开始2进行中3已结束4已取消) */
    @Excel(name = "状态", readConverterExp = "1=待开始,2=进行中,3=已结束,4=已取消")
    private String status;

    /** 重要程度(1普通2重要3紧急) */
    @Excel(name = "重要程度", readConverterExp = "1=普通,2=重要,3=紧急")
    private String priority;

    /** 是否需要签到(0否1是) */
    @Excel(name = "是否需要签到", readConverterExp = "0=否,1=是")
    private String needSignIn;

    /** 提醒时间(分钟) */
    @Excel(name = "提醒时间")
    private Integer remindMinutes;

    /** 会议链接 */
    @Excel(name = "会议链接")
    private String meetingLink;

    /** 会议密码 */
    @Excel(name = "会议密码")
    private String meetingPassword;

    /** 预计参会人数 */
    @Excel(name = "预计参会人数")
    private Integer expectedAttendees;

    /** 实际参会人数 */
    @Excel(name = "实际参会人数")
    private Integer actualAttendees;

    /** 会议费用 */
    @Excel(name = "会议费用")
    private Double meetingCost;

    /** 附件列表 */
    private List<OaFileAttachment> attachments;

    /** 会议室信息 */
    private OaMeetingRoom meetingRoom;

    public void setMeetingId(Long meetingId) 
    {
        this.meetingId = meetingId;
    }

    public Long getMeetingId() 
    {
        return meetingId;
    }
    public void setMeetingTitle(String meetingTitle) 
    {
        this.meetingTitle = meetingTitle;
    }

    public String getMeetingTitle() 
    {
        return meetingTitle;
    }
    public void setMeetingType(String meetingType) 
    {
        this.meetingType = meetingType;
    }

    public String getMeetingType() 
    {
        return meetingType;
    }
    public void setRoomId(Long roomId) 
    {
        this.roomId = roomId;
    }

    public Long getRoomId() 
    {
        return roomId;
    }
    public void setRoomName(String roomName) 
    {
        this.roomName = roomName;
    }

    public String getRoomName() 
    {
        return roomName;
    }
    public void setLocation(String location) 
    {
        this.location = location;
    }

    public String getLocation() 
    {
        return location;
    }
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    public void setOrganizerId(Long organizerId) 
    {
        this.organizerId = organizerId;
    }

    public Long getOrganizerId() 
    {
        return organizerId;
    }
    public void setOrganizerName(String organizerName) 
    {
        this.organizerName = organizerName;
    }

    public String getOrganizerName() 
    {
        return organizerName;
    }
    public void setParticipants(String participants) 
    {
        this.participants = participants;
    }

    public String getParticipants() 
    {
        return participants;
    }
    public void setParticipantIds(String participantIds) 
    {
        this.participantIds = participantIds;
    }

    public String getParticipantIds() 
    {
        return participantIds;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setAgenda(String agenda) 
    {
        this.agenda = agenda;
    }

    public String getAgenda() 
    {
        return agenda;
    }
    public void setMinutes(String minutes) 
    {
        this.minutes = minutes;
    }

    public String getMinutes() 
    {
        return minutes;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setPriority(String priority) 
    {
        this.priority = priority;
    }

    public String getPriority() 
    {
        return priority;
    }
    public void setNeedSignIn(String needSignIn) 
    {
        this.needSignIn = needSignIn;
    }

    public String getNeedSignIn() 
    {
        return needSignIn;
    }
    public void setRemindMinutes(Integer remindMinutes) 
    {
        this.remindMinutes = remindMinutes;
    }

    public Integer getRemindMinutes() 
    {
        return remindMinutes;
    }
    public void setMeetingLink(String meetingLink) 
    {
        this.meetingLink = meetingLink;
    }

    public String getMeetingLink() 
    {
        return meetingLink;
    }
    public void setMeetingPassword(String meetingPassword) 
    {
        this.meetingPassword = meetingPassword;
    }

    public String getMeetingPassword() 
    {
        return meetingPassword;
    }
    public void setExpectedAttendees(Integer expectedAttendees) 
    {
        this.expectedAttendees = expectedAttendees;
    }

    public Integer getExpectedAttendees() 
    {
        return expectedAttendees;
    }
    public void setActualAttendees(Integer actualAttendees) 
    {
        this.actualAttendees = actualAttendees;
    }

    public Integer getActualAttendees() 
    {
        return actualAttendees;
    }
    public void setMeetingCost(Double meetingCost) 
    {
        this.meetingCost = meetingCost;
    }

    public Double getMeetingCost() 
    {
        return meetingCost;
    }

    public List<OaFileAttachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<OaFileAttachment> attachments) {
        this.attachments = attachments;
    }

    public OaMeetingRoom getMeetingRoom() {
        return meetingRoom;
    }

    public void setMeetingRoom(OaMeetingRoom meetingRoom) {
        this.meetingRoom = meetingRoom;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("meetingId", getMeetingId())
            .append("meetingTitle", getMeetingTitle())
            .append("meetingType", getMeetingType())
            .append("roomId", getRoomId())
            .append("roomName", getRoomName())
            .append("location", getLocation())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("organizerId", getOrganizerId())
            .append("organizerName", getOrganizerName())
            .append("participants", getParticipants())
            .append("content", getContent())
            .append("agenda", getAgenda())
            .append("minutes", getMinutes())
            .append("status", getStatus())
            .append("priority", getPriority())
            .append("needSignIn", getNeedSignIn())
            .append("remindMinutes", getRemindMinutes())
            .append("meetingLink", getMeetingLink())
            .append("meetingPassword", getMeetingPassword())
            .append("expectedAttendees", getExpectedAttendees())
            .append("actualAttendees", getActualAttendees())
            .append("meetingCost", getMeetingCost())
            .append("createTime", getCreateTime())
            .toString();
    }
}
