package com.cfpamf.ms.mallorder.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 订单变更事件类型订单
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Getter
@AllArgsConstructor
@ToString
public enum OrderEventEnum {

    /**
     * 正向 -> 创建
     */
    CREATE("CREATE", "创建", 10, "待支付"),

    /**
     * 正向 -> 创建
     */
    UPDATE("UPDATE", "更新", -1, "未知"),
    /**
     * 正向 -> 等待付款
     */
    WAITING("WAITING","等待付款", -1, "未知"),
    
    /**
     * 正向 -> 定金付款
     */
    PAY_DEPOSIT("PAY_DEPOSIT", "定金付款成功", -1, "未知"),

    /**
     * 正向 -> 支付中
     */
    PAYING("PAYING", "支付中", 15, "支付中"),

    /**
     * 正向 -> 付款
     */
    PAY("PAY", "付款成功", 20, "待发货"),

    /**
     * 正向 -> 拼团成功
     */
    SPELL_SUCCESS("SPELL_SUCCESS", "拼团成功", -1, "未知"),

    /**
     * 正向 -> 延长收货时间
     */
    EXTEND_RECEIVE_TIME("EXTEND_RECEIVE_TIME", "延长收货时间", -1, "未知"),

    /**
     * 正向 -> 部分发货发货
     */
    PART_DELIVERY("PART_DELIVERY", "部分发货", 25, "部分发货"),

    /**
     * 正向 -> 发货
     */
    DELIVERY("DELIVERY", "发货", 30, "待签收"),

    /**
     * 正向 -> 交易成功
     */
    FINISH("FINISH", "交易成功", 40, "交易成功"),

    /**
     * 正向 -> 评价
     */
    EVALUATE("EVALUATE", "评价", -1, "未知"),

    /**
     * 逆向 -> 失败(未创建成功)
     */
    FAIL ("FAIL", "失败", -1, "未知"),

    /**
     * 正向 -> 取消
     */
    CANCEL("CANCEL", "取消", 0, "已取消"),

    /**
     * 逆向 -> 售后申请
     */
    REFUND_APPLY("REFUND_APPLY", "售后申请", -1, "未知"),

    /**
     * 逆向 -> 商家拒绝
     */
    STORE_REFUSED("STORE_REFUSED", "商家拒绝", -1, "未知"),
    /**
     * 逆向 -> 商家同意
     */
    STORE_AGREE("STORE_AGREE", "商家同意", -1, "未知"),

    /**
     * 逆向 -> 平台拒绝
     */
    PLATFORM_REFUSED("PLATFORM_REFUSED", "平台拒绝", -1, "未知"),
    /**
     * 逆向 -> 平台同意
     */
    PLATFORM_AGREE("PLATFORM_AGREE", "平台同意", -1, "未知"),

    /**
     * 逆向 -> 退款撤销
     */
    REVOKE_REFUND("REVOKE_REFUND", "退款撤销", -1, "未知"),

    /**
     * 逆向 -> 售后用户发货
     */
    REFUND_DELIVER_GOODS("REFUND_DELIVER_GOODS", "售后用户发货", -1, "未知"),
    /**
     * 逆向 -> 售后商家收货
     */
    REFUND_STORE_RECEIVE_GOODS("REFUND_STORE_RECEIVE_GOODS", "售后商家收货", -1, "未知"),
    /**
     * 逆向 -> 售后商家拒绝收货
     */
    REFUND_STORE_REJECT_RECEIVE_GOODS("REFUND_STORE_REJECT_RECEIVE_GOODS", "售后商家拒绝收货", -1, "未知"),

    /**
     * 逆向 -> 退款成功
     */
    REFUND("REFUND", "退款成功", -1, "未知"),

    /**
     * 正向 -> 交易关闭
     */
    CLOSE("CLOSE", "交易关闭", 50, "交易关闭"),

    /**
     * 逆向 -> 逻辑删除
     */
    DELETE("DELETE", "逻辑删除", -1, "未知"),

    /**
     * 正向 -> 用呗放款成功
     */
    LENDING_SUCCESS("LENDING_SUCCESS", "放款成功", -1, "未知"),

    /**
     * 逆向 -> 分支维护
     */
    MANAGE_BRANCH("MANAGE_BRANCH", "分支维护", -1, "未知");

    private String code;
    private String desc;
    private Integer orderStateCode;
    private String orderStateDesc;

    /**
     *  是否是正向事件
     */
    public static boolean isForwardEvent(String event) {
        if (Objects.isNull(event)) {
            return false;
        }
        return event.equals(CREATE.code) || event.equals(PAY.code) || event.equals(DELIVERY.code)
                || event.equals(FINISH.code) || event.equals(CANCEL.code) || event.equals(CLOSE.code);
    }

    /**
     *  是否为逆向事件
     */
    public static boolean isReverseEvent(String event) {
        if (Objects.isNull(event)) {
            return false;
        }
        return event.equals(REFUND.code);
    }

    public static OrderEventEnum getByCode(String code) {
        for (OrderEventEnum orderEventEnum : OrderEventEnum.values()) {
            if (orderEventEnum.code.equals(code)) {
                return orderEventEnum;
            }
        }
        return null;
    }

    public static OrderEventEnum getByOrderStatusCode(Integer orderStatusCode) {
        for (OrderEventEnum orderEventEnum : OrderEventEnum.values()) {
            if (orderEventEnum.orderStateCode.equals(orderStatusCode)) {
                return orderEventEnum;
            }
        }
        return null;
    }

    /**
     * 获取正向同步交易中心需要处理的事件
     * @param eventType
     * @return
     */
    public static Boolean isOrderSync2TradeEvent(String eventType) {
        if (StringUtils.isBlank(eventType)) {
            return false;
        }
        return CREATE.code.equals(eventType) || PAY.code.equals(eventType) || PAYING.code.equals(eventType)
                || PART_DELIVERY.code.equals(eventType) || DELIVERY.code.equals(eventType)
                || FINISH.code.equals(eventType) || CANCEL.code.equals(eventType) || CLOSE.code.equals(eventType)
                ;
    }

    /**
     * 获取逆向同步交易中心需要处理的事件
     * @param eventType
     * @return
     */
    public static Boolean isOrderRefundSync2TradeEvent(String eventType) {
        if (StringUtils.isBlank(eventType)) {
            return false;
        }
        return REFUND_APPLY.code.equals(eventType) || REFUND.code.equals(eventType) || STORE_REFUSED.code.equals(eventType) || STORE_AGREE.code.equals(eventType)
                || PLATFORM_REFUSED.code.equals(eventType) || PLATFORM_AGREE.code.equals(eventType) || REVOKE_REFUND.code.equals(eventType)
                ;
    }
}
