package com.cfpamf.ms.mallorder.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2021/9/14.
 */
@Data
public class OrderReturnInfoVO {

    @ApiModelProperty("销售订单单号")
    private String orderSn;

    @ApiModelProperty("订单状态")
    private Integer orderStatus;

    @ApiModelProperty("订单上一个状态")
    private Integer orderPreStatus;

    @ApiModelProperty("用呗放款是否成功：true/false")
    private Boolean lendSuccess;

    @ApiModelProperty("店铺ID")
    private Long storeId;

    @ApiModelProperty(value = "1-自营；2-商家")
    private Integer isSelf;

    @ApiModelProperty("退款商品信息")
    private OrderReturnProductInfoVO orderReturnProductInfo;

     @ApiModelProperty("退款赠品商品信息集合")
    private List<GiftOrderReturnProductInfoVO> giftOrderReturnProductInfoVOList;

}
