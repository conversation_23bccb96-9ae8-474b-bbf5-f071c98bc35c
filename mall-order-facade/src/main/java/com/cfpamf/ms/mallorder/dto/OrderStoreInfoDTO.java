package com.cfpamf.ms.mallorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 订单相关店铺信息 参数
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Data
public class OrderStoreInfoDTO {

    @ApiModelProperty(value = "店铺id", required = true)
    private Long storeId;

    @ApiModelProperty(value = "会员发票id")
    private Integer invoiceId;

    @ApiModelProperty(value = "使用店铺优惠券编码")
    private String storeCouponCode;

    @ApiModelProperty(value = "给商家留言")
    private String remark;
}
