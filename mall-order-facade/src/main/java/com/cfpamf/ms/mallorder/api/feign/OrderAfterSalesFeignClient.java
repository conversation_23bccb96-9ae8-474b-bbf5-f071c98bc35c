package com.cfpamf.ms.mallorder.api.feign;

import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.ExtAfterSalesApplyDTO;
import com.cfpamf.ms.mallorder.dto.OrderForceRefundDTO;
import com.slodon.bbc.core.response.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = OrderConst.MALL_ORDER_APPLICATION_NAME, contextId = "orderAfterSale")
public interface OrderAfterSalesFeignClient {

    /**
     * 强制退款
     *
     * @param orderForceRefundDTO
     * @return JsonResult<Boolean>
     */
    @PostMapping("/v1/feign/business/performance/forceRefund")
    JsonResult<Void> extOrderOperate(@RequestBody OrderForceRefundDTO orderForceRefundDTO);

    /**
     * 售后申请回调
     *
     * @param extAfterSalesApplyDTO
     * @return JsonResult<Boolean>
     */
    @PostMapping("/v1/feign/business/performance/afterSales/extSellerAudit")
    JsonResult<Void> extSellerAudit(@RequestBody ExtAfterSalesApplyDTO extAfterSalesApplyDTO);
}
