package com.cfpamf.ms.mallorder.api.feign;

import com.cfpamf.common.ms.vo.PageVO;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.PayMethodStoreDTO;
import com.cfpamf.ms.mallorder.request.req.PayMethodMerchantReq;
import com.cfpamf.ms.mallorder.vo.BzOrderPayWhitelistVO;
import com.cfpamf.ms.mallorder.vo.PayMethodVOV2;
import com.slodon.bbc.core.response.JsonResult;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Create 2021-08-05 09:34
 * @Description :支付方式配置外部接口
 */
@FeignClient(name = OrderConst.MALL_ORDER_APPLICATION_NAME, contextId = "PayMethod", url = "")
public interface PayMethodFeignClient {

    /**
     * 根据支付方式编码批量查询支付方式信息
     *
     * @param payMethodCodes    支付方式编码
     * @return                  支付方式信息
     */
    @PostMapping("/v1/feign/business/payMethod/listPayMethodByCode")
    JsonResult<List<PayMethodVOV2>> listPayMethodByCode(@RequestBody List<String> payMethodCodes);

    /**
     * 商家入驻新增默认支付
     */
    @PostMapping("admin/payMethod/insertPayMethodMerchant")
    JsonResult insertPayMethodMerchant(@RequestParam(value = "vendorId") Long vendorId);

    /**
     * 分页查询适用商家列表V2
     */
    @PostMapping("admin/payMethod/merchantV2/pageList")
    JsonResult<PageVO<BzOrderPayWhitelistVO>> pageMerchantV2List(@RequestBody PayMethodMerchantReq queryDTO);

    /**
     * 为指定商家新增指定支付方式
     *
     * @param payMethodStoreDTO     店铺支付方式信息入参
     * @return                      新增结果
     */
    @PostMapping("admin/payMethod/insertPayMethodStore")
    JsonResult<String> insertPayMethodStore(@RequestBody PayMethodStoreDTO payMethodStoreDTO);

    /**
     * 删除指定商家的指定支付方式
     *
     * @param payMethodStoreDTO     店铺支付方式信息入参
     * @return                      删除
     */
    @PostMapping("admin/payMethod/deletePayMethodStore")
    JsonResult<String> deletePayMethodStore(@RequestBody PayMethodStoreDTO payMethodStoreDTO);

    /**
     * 入驻增量增加渠道白名单
     */
    @GetMapping("admin/payMethod/addChannelWhiteList")
    JsonResult<Boolean> addChannelWhiteList(@RequestParam(value = "storeId") @ApiParam("店铺id") String storeId,
                                            @RequestParam(value = "channel") @ApiParam("1-云直通，2-收付通") Integer channel);

}
