package com.cfpamf.ms.mallorder.po;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单退货表
 */
@Data
public class OrderReturn implements Serializable {
    private static final long serialVersionUID = -9095259077494686915L;
    @ApiModelProperty("退货id")
    private Integer returnId;

    @ApiModelProperty("售后服务单号")
    private String afsSn;

    @ApiModelProperty("订单号")
    private String orderSn;

    @ApiModelProperty("店铺id")
    private Long storeId;

    @ApiModelProperty(value = "1-自营；2-商家")
    private Integer storeIsSelf;

    @ApiModelProperty("店铺名称")
    private String storeName;

    @ApiModelProperty("用户ID")
    private Integer memberId;

    @ApiModelProperty("用户名称")
    private String memberName;

    @ApiModelProperty("退款方式：0-原路退回，1-退账户余额")
    private Integer returnMoneyType;

    @ApiModelProperty("退款类型：1==仅退款 2=退货退款 3=换货退款")
    private Integer returnType;

    @ApiModelProperty("退货数量")
    private Integer returnNum;

    @ApiModelProperty("退款申请金额")
    private BigDecimal returnMoneyAmount;

    @ApiModelProperty("退还积分")
    private Integer returnIntegralAmount;

    @ApiModelProperty("扣除积分数量（购物赠送的积分）")
    private Integer deductIntegralAmount;

    @ApiModelProperty("退还运费的金额（用于待发货订单仅退款时处理）")
    private BigDecimal returnExpressAmount;

    @ApiModelProperty("退还优惠券编码（最后一单退还优惠券）")
    private String returnVoucherCode;

    @ApiModelProperty("平台对应类别的佣金比例，0-1数字，")
    private BigDecimal commissionRate;

    @ApiModelProperty("佣金金额")
    private BigDecimal commissionAmount;

    @ApiModelProperty("平台优惠券金额")
    private BigDecimal platformVoucherAmount;

    @ApiModelProperty("平台活动金额")
    private BigDecimal platformActivityAmount;

    @ApiModelProperty("平台服务费")
    private BigDecimal serviceFee;

    @ApiModelProperty("代运营服务费")
    private BigDecimal thirdpartnarFee;

    // 退还的乡助卡优惠金额
    private BigDecimal xzCardAmount;

    // 退还的乡助卡运费优惠金额
    private BigDecimal xzCardExpressFeeAmount;

    @ApiModelProperty("订单佣金")
    private BigDecimal orderCommission;

    @ApiModelProperty("订单佣金服务费")
    private BigDecimal commissionServiceFee;

    @ApiModelProperty("退订单佣金激励费")
    private BigDecimal orderCommissionIncentiveFee;

    @ApiModelProperty("退订单佣金激励服务费")
    private BigDecimal commissionIncentiveServiceFee;

    @ApiModelProperty("业务佣金")
    private BigDecimal businessCommission;

    @ApiModelProperty("退货退款状态：100-买家申请仅退款；101-买家申请退货退款；102-买家退货给商家；200-商家同意退款申请；201-商家同意退货退款申请；202-商家拒绝退款申请(退款关闭/拒收关闭)；203-商家确认收货；300-平台确认退款(已完成)")
    private Integer state;

    @ApiModelProperty("退款失败次数")
    private Integer refundFailTimes;

    @ApiModelProperty("申请时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    @ApiModelProperty("退货完成时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    @ApiModelProperty("拒绝原因")
    private String refuseReason;

    @ApiModelProperty("支付方式")
    private String paymentMethod;

    @ApiModelProperty("支付渠道手续费")
    private BigDecimal channelServiceFee;

    //  #######新增########
    @ApiModelProperty("实际退款金额")
    private BigDecimal actualReturnMoneyAmount;

    @ApiModelProperty("客户承担金额")
    private BigDecimal customerAssumeAmount;

    @ApiModelProperty("退款类型")
    private Integer refundType;

    @ApiModelProperty("退款开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundStartTime;

    @ApiModelProperty("退款结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundEndTime;

    @ApiModelProperty("其他赔偿")
    private BigDecimal otherCompensationAmount;

    @ApiModelProperty("预贴息金额（从核算试算）")
    private BigDecimal planDiscountAmount;

    @ApiModelProperty("利息承担方")
    private String interestPayer;

    @ApiModelProperty("平台自动审核失败次数")
    private Integer platformAutoAuditFailTimes;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("冲正时间")
    private Date reversalDate;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty("退款发起者：0-系统 1-客户 2-商家 3-平台")
    private Integer returnBy;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "业务说明")
    private String businessDescription;

    @ApiModelProperty(value = "是否可用:1-是 0-否")
    private Integer enabledFlag;
}