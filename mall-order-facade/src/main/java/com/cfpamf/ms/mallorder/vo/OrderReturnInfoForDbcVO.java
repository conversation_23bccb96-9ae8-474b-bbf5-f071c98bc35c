package com.cfpamf.ms.mallorder.vo;

import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 2021/9/16.
 *
 * 退款信息：传分销
 */
@Data
public class OrderReturnInfoForDbcVO {

    @ApiModelProperty("退订单号")
    private String afsSn;

    @ApiModelProperty("订单编号")
    private String orderSn;

    @ApiModelProperty("实退金额")
    private BigDecimal actualReturnMoneyAmount;

    @ApiModelProperty("退款时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING,pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date completeTime;

    @ApiModelProperty("是否整单退:1-是 0-否")
    private Integer allRefundFlag;

    @ApiModelProperty("订单状态")
    private Integer orderState;

    private List<OrderAfterSaleInfoForDbcVO> orderAfterSaleInfo;

    public Integer getOrderState() {
        if (this.orderState == OrderConst.ORDER_STATE_10) {
            return 1;
        } else if (this.orderState == OrderConst.ORDER_STATE_15) {
            return 2;
        } else if (this.orderState == OrderConst.ORDER_STATE_20) {
            return 4;
        } else if (this.orderState == OrderConst.ORDER_STATE_30) {
            return 3;
        } else if (this.orderState == OrderConst.ORDER_STATE_40) {
            return 5;
        } else if (this.orderState == OrderConst.ORDER_STATE_50) {
            return 6;
        }
        return orderState;
    }

    @Data
    public static class OrderAfterSaleInfoForDbcVO {

        @ApiModelProperty("订单明细 ID")
        private Integer returnId;

        @ApiModelProperty("退订单号")
        private String afsSn;

        @ApiModelProperty("订单编号")
        private String orderSn;

        @ApiModelProperty("skuId")
        private Long productId;

        @ApiModelProperty("spuId")
        private Long goodsId;

        @ApiModelProperty("活动ID")
        private String promotionId;

        @ApiModelProperty("申请售后的货品数量")
        private Integer afsNum;

        @ApiModelProperty("该笔退单是否是该商品行的最后一笔退款，即该退款单完成后，该商品行商品全退完成：1-是，0-否")
        private Integer productLastRefund;

        @ApiModelProperty("退款类型：1-仅退款 2-退货退款 3-换货退款")
        private Integer returnType;

        @ApiModelProperty("实退金额")
        private BigDecimal actualReturnMoneyAmount;

        @ApiModelProperty("退款时间")
        @JsonFormat(shape=JsonFormat.Shape.STRING,pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
        private Date completeTime;

        @ApiModelProperty("退货退款状态：100-买家申请仅退款；101-买家申请退货退款；102-买家退货给商家；200-商家同意退款申请；201-商家同意退货退款申请；202-商家拒绝退款申请(退款关闭/拒收关闭)；203-商家确认收货；300-平台确认退款(已完成)")
        private Integer state;

    }
}
