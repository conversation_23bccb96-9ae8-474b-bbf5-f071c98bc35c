package com.cfpamf.ms.mallorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 订单购买商品信息 参数
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Data
public class ChannelOrderSkuInfoDTO {
    @NotNull(message = "商品信息productId不能为空")
    @ApiModelProperty(value = "productId")
    private Long productId;

    @NotNull(message = "购买数量不能为空")
    @Min(value = 1, message = "购买数量，不能小于1")
    @ApiModelProperty(value = "购买数量，非购物车下单必传")
    private Integer number;
}
