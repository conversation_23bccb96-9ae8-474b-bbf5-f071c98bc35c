package com.cfpamf.ms.mallorder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 订单状态定义
 *
 * <AUTHOR>
 * @date 2021/6/19 17:16
 * @return
 */
@AllArgsConstructor
@Getter
public enum FacadeOrderReturnStatus {

    REFUND_APPLY(100, "退款申请", "待商家审核（仅退款）"),
    RETURN_APPLY(101, "退货退款申请", "待商家审核（退货退款）"),
    WAIT_RETURN(102, "等待客户退货", "待商家收货（退货退款）"),
    STORE_AGREE_REFUND(200, "商家同意退款", "待平台审核（仅退款）"),
    STORE_AGREE_RETURN(201, "商家同意退货退款", "待买家退货（退货退款）"),
    STORE_REFUSED(202, "商家拒绝(退款关闭/拒收关闭)", "售后关闭（商家拒绝）"),
    STORE_RECEIVED(203, "商家确认收货", "待平台处理（退货退款）"),
    PLATFORM_AGREE(300, "平台确认退款", "退款中"),
    PLATFORM_REFUSED(301, "平台拒绝退款(退款关闭)", "售后关闭（平台拒绝）"),
    REVOKE_REFUND(302, "退款撤销", "售后关闭（退款撤销）"),
    REFUND_SUCCESS(400, "400-退款成功(已完成)", "退款成功"),
    REFUND_FAILED(401, "401-退款失败(已完成)", "退款失败");

    public static List<Integer> refuseStateList() {
        List<Integer> result = new ArrayList<>();
        result.add(STORE_REFUSED.getValue());
        result.add(REVOKE_REFUND.getValue());
        result.add(PLATFORM_REFUSED.getValue());
        return result;
    }

    public static List<Integer> endStatus() {
        List<Integer> result = new ArrayList<>();
        result.add(STORE_REFUSED.getValue());
        result.add(PLATFORM_REFUSED.getValue());
        return result;
    }

    public static List<Integer> duringRefundStatus() {
        List<Integer> result = new ArrayList<>();
        result.add(REFUND_APPLY.getValue());
        result.add(RETURN_APPLY.getValue());
        result.add(WAIT_RETURN.getValue());
        result.add(STORE_AGREE_REFUND.getValue());
        result.add(STORE_AGREE_RETURN.getValue());
        result.add(STORE_RECEIVED.getValue());
        return result;
    }

    public static boolean isFinish(int value) {
        return PLATFORM_AGREE.value == value
                || REFUND_SUCCESS.value == value;
    }

    public static boolean isClosed(int value) {
        return STORE_REFUSED.value == value
                || PLATFORM_REFUSED.value == value
                || REFUND_FAILED.value == value;
    }

    public static boolean duringRefund(int value) {
        return !(STORE_REFUSED.value == value
                || PLATFORM_REFUSED.value == value
                || REFUND_SUCCESS.value == value
                || REFUND_FAILED.value == value);
    }

    Integer value;
    String desc;
    String showDesc;

    public static FacadeOrderReturnStatus valueOf(int value) {
        for (FacadeOrderReturnStatus ps : FacadeOrderReturnStatus.values()) {
            if (value == ps.value) {
                return ps;
            }
        }
        return null;
    }

    public static String getDesc(Integer value) {
        for (FacadeOrderReturnStatus v : FacadeOrderReturnStatus.values()) {
            if (value.equals(v.value)) {
                return v.desc;
            }
        }
        return null;
    }

    public static String getShowDesc(Integer value) {
        FacadeOrderReturnStatus orderReturnStatus = FacadeOrderReturnStatus.valueOf(value);
        return Objects.isNull(orderReturnStatus) ? null : orderReturnStatus.getShowDesc();
    }
}
