package com.cfpamf.ms.mallorder.api.feign;

import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.BankTransferDTO;
import com.cfpamf.ms.mallorder.vo.BankTransferVO;
import com.cfpamf.ms.mallorder.vo.OrderPresellVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = OrderConst.MALL_ORDER_APPLICATION_NAME, contextId = "orderPresell")
public interface OrderPresellFeignClient {

    @ApiOperation("根据orderSn查询")
    @PostMapping("/v1/feign/business/orderPresell/listByOrderSn")
    List<OrderPresellVO> listByOrderSn(@RequestParam("orderSn") String orderSn,
                                       @RequestParam(value = "type",required = false) Integer type);

    @ApiOperation("根据payNo查询")
    @PostMapping("/v1/feign/business/orderPresell/findByPayNo")
    OrderPresellVO findByPayNo(@RequestParam("payNo") String payNo);
}
