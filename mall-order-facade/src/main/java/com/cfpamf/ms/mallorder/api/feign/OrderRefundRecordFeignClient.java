package com.cfpamf.ms.mallorder.api.feign;

import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.vo.OrderRefundRecordVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = OrderConst.MALL_ORDER_APPLICATION_NAME, contextId = "orderRefundRecord")
public interface OrderRefundRecordFeignClient {

    @ApiOperation("根据afsSn查询")
    @PostMapping("/v1/feign/business/orderRefundRecord/listByAfsSn")
    List<OrderRefundRecordVO> listByAfsSn(@RequestParam("afsSn") String afsSn,
                                          @RequestParam(value = "refundStatus",required = false) Integer refundStatus);

    @ApiOperation("根据payNo查询")
    @PostMapping("/v1/feign/business/orderRefundRecord/listByPayNo")
    List<OrderRefundRecordVO> listByPayNo(@RequestParam("payNo") String payNo,
                                          @RequestParam(value = "refundStatusList",required = false) List<Integer> refundStatusList);

    @ApiOperation("根据orderSn查询")
    @PostMapping("/v1/feign/business/orderRefundRecord/listByOrderSn")
    List<OrderRefundRecordVO> listByOrderSn(@RequestParam("orderSn") String orderSn,
                                          @RequestParam(value = "refundStatusList",required = false) List<Integer> refundStatusList);

    @ApiOperation("根据refundNo查询")
    @PostMapping("/v1/feign/business/orderRefundRecord/findByRefundNo")
    OrderRefundRecordVO findByRefundNo(@RequestParam("refundNo") String refundNo);
}
