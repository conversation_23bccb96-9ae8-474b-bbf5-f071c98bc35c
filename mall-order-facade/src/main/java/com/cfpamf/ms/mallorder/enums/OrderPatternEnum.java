package com.cfpamf.ms.mallorder.enums;

import com.alibaba.fastjson.annotation.JSONType;
import com.slodon.bbc.core.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 *
 * 订单来源：1 - B端采购中心，2 - C端店铺街，3 - 农服订单
 */
@AllArgsConstructor
@Getter
@JSONType(serializeEnumAsJavaBean = true)
public enum OrderPatternEnum {

    SHOP_STREET(1, "C端订单"),
    PURCHASE_CENTRE(2, "采购订单"),
    FARMERS_SERVICE(3, "农服订单"),
    COUPON_CENTRE(4, "卡券订单"),
    EXCHANGE_ORDER(5, "换货订单"),
    SELF_LIFT(6, "自提订单");

    Integer value;
    String desc;

    public static OrderPatternEnum valueOf(int value) {
        for (OrderPatternEnum ps : OrderPatternEnum.values()) {
            if (value == ps.value) {
                return ps;
            }
        }
        return null;
    }
}
