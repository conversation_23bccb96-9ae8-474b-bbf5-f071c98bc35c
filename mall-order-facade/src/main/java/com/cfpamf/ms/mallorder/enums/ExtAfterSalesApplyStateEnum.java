package com.cfpamf.ms.mallorder.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@AllArgsConstructor
@Getter
public enum ExtAfterSalesApplyStateEnum {


    RETURN_APPLY_STATE_20 (20, "仅退款申请失败");

    Integer value;
    String desc;


    public static ExtAfterSalesApplyStateEnum getValue (Integer value) {
        for (ExtAfterSalesApplyStateEnum ps : ExtAfterSalesApplyStateEnum.values ()) {
            if (Objects.equals (value, ps.value)) {
                return ps;
            }
        }
        return null;
    }
}
