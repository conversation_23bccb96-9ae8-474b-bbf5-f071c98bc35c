package com.cfpamf.ms.mallorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 订单收货地址 参数
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Data
public class OrderAddressDTO {


    @ApiModelProperty(value = "收件人姓名")
    private String receiverName;

    @ApiModelProperty(value = "收件人手机号码")
    private String receiverMobile;

    @ApiModelProperty(value = "省", example = "湖南省")
    private String province;

    @ApiModelProperty(value = "市", example = "长沙市")
    private String city;

    @ApiModelProperty(value = "市编码")
    private String cityCode;

    @ApiModelProperty(value = "区/县", example = "岳麓区")
    private String district;

    @ApiModelProperty(value = "街道/镇", example = "麓谷街道")
    private String town;

    @ApiModelProperty(value = "详细地址", example = "芯城科技园110栋 120楼")
    private String detailAddress;

    @ApiModelProperty(hidden = true, name = "自提点仓库编码")
    private String warehouseCode;

    @ApiModelProperty(hidden = true, name = "自提点id")
    private Long pointId;

    @ApiModelProperty(hidden = true, name = "自提点名称")
    private String pointName;

    @ApiModelProperty("自提点分支编码")
    private String selfLiftingBranchCode;
}
