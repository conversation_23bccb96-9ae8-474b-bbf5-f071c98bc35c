package com.cfpamf.ms.mallorder.api.feign;

import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.po.OrderLog;
import com.cfpamf.ms.mallorder.request.OrderLogExample;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = OrderConst.MALL_ORDER_APPLICATION_NAME, contextId = "orderLog")
public interface OrderLogFeignClient {
    /**
     * 自定义 mapping
     */
    String PREFIX = "/v1/feign/business/orderLog";
    String GET = PREFIX + "/get";//前缀mapping + 方法mapping;
    String GET_LIST = PREFIX + "/getList";
    String ADD_ORDER_LOG = PREFIX + "/addOrderLog";
    String UPDATE_ORDER_LOG = PREFIX + "/updateOrderLog";
    String DELETE_ORDER_LOG = PREFIX + "/deleteOrderLog";

    /**
     * 根据logId获取订单操作日志表详情
     *
     * @param logId logId
     * @return
     */
    @GetMapping(GET)
    OrderLog getOrderLogByLogId(@RequestParam("logId") Integer logId);

    /**
     * 获取条件获取订单操作日志表列表
     *
     * @param example 查询条件信息
     * @return
     */
    @PostMapping(GET_LIST)
    List<OrderLog> getOrderLogList(@RequestBody OrderLogExample example);

    /**
     * 新增订单操作日志表
     *
     * @param orderLog
     * @return
     */
    @PostMapping(ADD_ORDER_LOG)
    Integer saveOrderLog(@RequestBody OrderLog orderLog);

    /**
     * 根据logId更新订单操作日志表
     *
     * @param orderLog
     * @return
     */
    @PostMapping(UPDATE_ORDER_LOG)
    Integer updateOrderLog(@RequestBody OrderLog orderLog);

    /**
     * 根据logId删除订单操作日志表
     *
     * @param logId logId
     * @return
     */
    @PostMapping(DELETE_ORDER_LOG)
    Integer deleteOrderLog(@RequestParam("logId") Integer logId);
}