package com.cfpamf.ms.mallorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class OrderExchangeListExample {
    @ApiModelProperty("订单号")
    private String orderSn;

    @ApiModelProperty("换货号")
    private String exchangeSn;

    @ApiModelProperty("订单货品id")
    private Long orderProductId;

    @ApiModelProperty("换货申请订单状态：0-【待审批】、1-【撤销换货】、2-【拒绝换货】、3-【同意换货】、4-【换货完成】、5-【换货取消】 6-【换货关闭】")
    private Integer exchangeOrderState;

    @ApiModelProperty("换货后的订单状态")
    private Integer orderState;

    @ApiModelProperty("买家会员名")
    private String memberName;

    @ApiModelProperty("店铺Id")
    private Long storeId;

    @ApiModelProperty("换货单申请开始时间")
    private Date createTimeBefore;

    @ApiModelProperty("换货单申请结束时间")
    private Date createTimeAfter;
}
