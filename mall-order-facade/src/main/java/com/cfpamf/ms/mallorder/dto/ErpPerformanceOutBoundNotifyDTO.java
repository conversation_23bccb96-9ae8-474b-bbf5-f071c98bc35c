package com.cfpamf.ms.mallorder.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * ERP 出库通知实体
 * <AUTHOR>
 * @Date 2023-08-10
 */
@Data
public class ErpPerformanceOutBoundNotifyDTO {

    @ApiModelProperty("1-标准电商 2-自研电商 3-乡信助农")
    private Integer channel;

    @ApiModelProperty("履约出入库唯一值 必填")
    private String bizNo;

    @ApiModelProperty("业务单号 必填")
    private String businessNo;

    @ApiModelProperty("业务类型 1-订单号 2-退货单号")
    private Integer businessType;

    @ApiModelProperty("类型 1-入库 2-出库")
    private Integer type;

    @ApiModelProperty("店铺ID 必填")
    private Long storeId;

    @ApiModelProperty("履约模式  1：自提 2物流 必填")
    private Integer performanceMode;

    @ApiModelProperty("仓库编码")
    private String depotCode;

    @ApiModelProperty("分支编码")
    private String branch;

    @ApiModelProperty("响应编码 100幂等 200成功 201部分成功 401失败，见PerformanceCodeEnum.java")
    private Integer code;

    @ApiModelProperty("处理结果")
    private String result;

    @ApiModelProperty("商品信息")
    private List<PerformanceSkuOutBoundResultDTO> performanceSkus;

    @ApiModelProperty("订单额外信息")
    private PerformanceStockDeliverInfo deliverInfo;

}
