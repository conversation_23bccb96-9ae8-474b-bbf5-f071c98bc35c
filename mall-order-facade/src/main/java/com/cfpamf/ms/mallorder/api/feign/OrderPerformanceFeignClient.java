package com.cfpamf.ms.mallorder.api.feign;

import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.ErpDeliveryDTO;
import com.cfpamf.ms.mallorder.dto.OrderPerformanceDeliveryDTO;
import com.cfpamf.ms.mallorder.dto.OrderPerformanceOutboundDTO;
import com.cfpamf.ms.mallorder.dto.OrderPerformanceReceiptDTO;
import com.slodon.bbc.core.response.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 订单履约外部接口
 *
 * @Author: zml
 * @CreateTime: 2022/7/5 15:36
 */
@FeignClient(name = OrderConst.MALL_ORDER_APPLICATION_NAME, contextId = "performance", url = "${mall-order.url:}")
public interface OrderPerformanceFeignClient {


    /**
     * 外部发货接口
     *
     * @param orderPerformanceDeliveryDTO
     * @return JsonResult<Boolean>
     */
    @PostMapping("/v1/feign/business/order/performance/delivery")
    JsonResult<Boolean> extDeliver(@RequestBody OrderPerformanceDeliveryDTO orderPerformanceDeliveryDTO);


    /**
     * 外部签收接口
     *
     * @param orderPerformanceReceiptDTO
     * @return JsonResult<Boolean>
     */
    @PostMapping("/v1/feign/business/order/performance/receipt")
    JsonResult<Boolean> extReceipt(@RequestBody OrderPerformanceReceiptDTO orderPerformanceReceiptDTO);


    @PostMapping("/v1/feign/business/order/performance/updateDeliveryInfo")
    JsonResult<Boolean> updateDeliveryInfo(@RequestBody ErpDeliveryDTO erpDeliveryDTO);

    @PostMapping("/v1/feign/business/order/performance/outbound")
    JsonResult<Boolean> extOutbound(@RequestBody @Valid @NotNull OrderPerformanceOutboundDTO orderPerformanceDeliveryDTO);

    @PostMapping("/v1/feign/business/order/performance/outboundToWaitDelivery")
    JsonResult<Boolean> outboundToWaitDelivery(@RequestBody @Valid @NotNull OrderPerformanceOutboundDTO orderPerformanceDeliveryDTO);

}
