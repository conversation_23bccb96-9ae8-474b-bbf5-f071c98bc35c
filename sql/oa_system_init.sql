-- OA系统数据库初始化脚本
-- 创建时间：2024-07-01
-- 说明：基于若依框架的OA办公自动化系统数据库表结构

-- ----------------------------
-- 1. 工作流相关表
-- ----------------------------

-- 工作流定义表
DROP TABLE IF EXISTS oa_workflow_definition;
CREATE TABLE oa_workflow_definition (
    workflow_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '流程ID',
    workflow_name VARCHAR(100) NOT NULL COMMENT '流程名称',
    workflow_key VARCHAR(50) NOT NULL COMMENT '流程标识',
    workflow_category VARCHAR(50) DEFAULT NULL COMMENT '流程分类',
    workflow_xml TEXT COMMENT '流程定义XML',
    version INT DEFAULT 1 COMMENT '版本号',
    status CHAR(1) DEFAULT '1' COMMENT '状态(0停用1启用)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT NULL COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (workflow_id),
    UNIQUE KEY uk_workflow_key_version (workflow_key, version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流定义表';

-- 工作流实例表
DROP TABLE IF EXISTS oa_workflow_instance;
CREATE TABLE oa_workflow_instance (
    instance_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '实例ID',
    workflow_id BIGINT NOT NULL COMMENT '流程定义ID',
    business_key VARCHAR(100) DEFAULT NULL COMMENT '业务主键',
    start_user_id BIGINT NOT NULL COMMENT '发起人ID',
    start_user_name VARCHAR(50) DEFAULT NULL COMMENT '发起人姓名',
    current_task VARCHAR(100) DEFAULT NULL COMMENT '当前任务',
    status CHAR(1) DEFAULT '1' COMMENT '状态(1进行中2已完成3已终止)',
    start_time DATETIME DEFAULT NULL COMMENT '开始时间',
    end_time DATETIME DEFAULT NULL COMMENT '结束时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (instance_id),
    KEY idx_workflow_id (workflow_id),
    KEY idx_start_user_id (start_user_id),
    KEY idx_business_key (business_key),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流实例表';

-- 工作流任务表
DROP TABLE IF EXISTS oa_workflow_task;
CREATE TABLE oa_workflow_task (
    task_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '任务ID',
    instance_id BIGINT NOT NULL COMMENT '流程实例ID',
    task_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    task_key VARCHAR(50) DEFAULT NULL COMMENT '任务标识',
    assignee_id BIGINT DEFAULT NULL COMMENT '处理人ID',
    assignee_name VARCHAR(50) DEFAULT NULL COMMENT '处理人姓名',
    status CHAR(1) DEFAULT '1' COMMENT '状态(1待处理2已处理3已转办)',
    priority INT DEFAULT 50 COMMENT '优先级',
    due_date DATETIME DEFAULT NULL COMMENT '到期时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    complete_time DATETIME DEFAULT NULL COMMENT '完成时间',
    comment TEXT COMMENT '处理意见',
    PRIMARY KEY (task_id),
    KEY idx_instance_id (instance_id),
    KEY idx_assignee_id (assignee_id),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流任务表';

-- ----------------------------
-- 2. 公文管理相关表
-- ----------------------------

-- 公文基础表
DROP TABLE IF EXISTS oa_document;
CREATE TABLE oa_document (
    doc_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '文档ID',
    doc_title VARCHAR(200) NOT NULL COMMENT '文档标题',
    doc_number VARCHAR(50) DEFAULT NULL COMMENT '文档编号',
    doc_type CHAR(1) NOT NULL COMMENT '文档类型(1收文2发文)',
    doc_category VARCHAR(50) DEFAULT NULL COMMENT '文档分类',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    creator_name VARCHAR(50) DEFAULT NULL COMMENT '创建人姓名',
    dept_id BIGINT DEFAULT NULL COMMENT '所属部门ID',
    dept_name VARCHAR(50) DEFAULT NULL COMMENT '部门名称',
    urgency_level CHAR(1) DEFAULT '2' COMMENT '紧急程度(1紧急2普通3缓办)',
    security_level CHAR(1) DEFAULT '1' COMMENT '密级(1公开2内部3秘密4机密)',
    status CHAR(1) DEFAULT '1' COMMENT '状态(1草稿2审批中3已发布4已归档)',
    content TEXT COMMENT '文档内容',
    source_unit VARCHAR(200) DEFAULT NULL COMMENT '来文单位',
    receive_date DATE DEFAULT NULL COMMENT '收文日期',
    main_send_unit VARCHAR(500) DEFAULT NULL COMMENT '主送单位',
    copy_send_unit VARCHAR(500) DEFAULT NULL COMMENT '抄送单位',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    publish_time DATETIME DEFAULT NULL COMMENT '发布时间',
    PRIMARY KEY (doc_id),
    KEY idx_doc_type (doc_type),
    KEY idx_creator_id (creator_id),
    KEY idx_dept_id (dept_id),
    KEY idx_status (status),
    KEY idx_doc_number (doc_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公文基础表';

-- 公文流程关联表
DROP TABLE IF EXISTS oa_document_flow;
CREATE TABLE oa_document_flow (
    flow_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '流程ID',
    doc_id BIGINT NOT NULL COMMENT '文档ID',
    instance_id BIGINT NOT NULL COMMENT '流程实例ID',
    flow_type CHAR(1) NOT NULL COMMENT '流程类型(1收文2发文3特办)',
    status CHAR(1) DEFAULT '1' COMMENT '状态',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (flow_id),
    KEY idx_doc_id (doc_id),
    KEY idx_instance_id (instance_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公文流程关联表';

-- 文件附件表
DROP TABLE IF EXISTS oa_file_attachment;
CREATE TABLE oa_file_attachment (
    file_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '文件ID',
    business_id BIGINT NOT NULL COMMENT '业务ID',
    business_type VARCHAR(20) NOT NULL COMMENT '业务类型',
    file_name VARCHAR(200) NOT NULL COMMENT '文件名称',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_type VARCHAR(20) DEFAULT NULL COMMENT '文件类型',
    file_size BIGINT DEFAULT NULL COMMENT '文件大小',
    file_ext VARCHAR(10) DEFAULT NULL COMMENT '文件扩展名',
    is_main CHAR(1) DEFAULT '0' COMMENT '是否为主要文件(0否1是)',
    upload_user_id BIGINT DEFAULT NULL COMMENT '上传人ID',
    upload_user_name VARCHAR(50) DEFAULT NULL COMMENT '上传人姓名',
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    PRIMARY KEY (file_id),
    KEY idx_business_id_type (business_id, business_type),
    KEY idx_upload_user_id (upload_user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件附件表';

-- ----------------------------
-- 3. 个人办公相关表
-- ----------------------------

-- 个人日程表
DROP TABLE IF EXISTS oa_personal_schedule;
CREATE TABLE oa_personal_schedule (
    schedule_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '日程ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    title VARCHAR(200) NOT NULL COMMENT '日程标题',
    content TEXT COMMENT '日程内容',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    location VARCHAR(200) DEFAULT NULL COMMENT '地点',
    remind_time DATETIME DEFAULT NULL COMMENT '提醒时间',
    status CHAR(1) DEFAULT '1' COMMENT '状态(1正常2已完成3已取消)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (schedule_id),
    KEY idx_user_id (user_id),
    KEY idx_start_time (start_time),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人日程表';

-- 工作报告表
DROP TABLE IF EXISTS oa_work_report;
CREATE TABLE oa_work_report (
    report_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '报告ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    report_type CHAR(1) NOT NULL COMMENT '报告类型(1日报2周报3月报)',
    report_date DATE NOT NULL COMMENT '报告日期',
    title VARCHAR(200) NOT NULL COMMENT '报告标题',
    content TEXT NOT NULL COMMENT '报告内容',
    status CHAR(1) DEFAULT '1' COMMENT '状态(1草稿2已提交)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    submit_time DATETIME DEFAULT NULL COMMENT '提交时间',
    PRIMARY KEY (report_id),
    KEY idx_user_id (user_id),
    KEY idx_report_type (report_type),
    KEY idx_report_date (report_date),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作报告表';

-- 个人通讯录表
DROP TABLE IF EXISTS oa_personal_contact;
CREATE TABLE oa_personal_contact (
    contact_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '联系人ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    name VARCHAR(50) NOT NULL COMMENT '联系人姓名',
    phone VARCHAR(20) DEFAULT NULL COMMENT '电话号码',
    email VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    company VARCHAR(100) DEFAULT NULL COMMENT '公司',
    position VARCHAR(50) DEFAULT NULL COMMENT '职位',
    address VARCHAR(200) DEFAULT NULL COMMENT '地址',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (contact_id),
    KEY idx_user_id (user_id),
    KEY idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人通讯录表';

-- ----------------------------
-- 4. 系统配置相关表
-- ----------------------------

-- 公文模板表
DROP TABLE IF EXISTS oa_document_template;
CREATE TABLE oa_document_template (
    template_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '模板ID',
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_type CHAR(1) NOT NULL COMMENT '模板类型(1收文2发文)',
    template_content TEXT COMMENT '模板内容',
    template_file VARCHAR(500) DEFAULT NULL COMMENT '模板文件路径',
    status CHAR(1) DEFAULT '1' COMMENT '状态(0停用1启用)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (template_id),
    KEY idx_template_type (template_type),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公文模板表';

-- 印章证照表
DROP TABLE IF EXISTS oa_seal_certificate;
CREATE TABLE oa_seal_certificate (
    seal_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '印章ID',
    seal_name VARCHAR(100) NOT NULL COMMENT '印章名称',
    seal_type CHAR(1) NOT NULL COMMENT '印章类型(1公章2财务章3法人章4合同章)',
    seal_image VARCHAR(500) DEFAULT NULL COMMENT '印章图片路径',
    keeper_id BIGINT DEFAULT NULL COMMENT '保管人ID',
    keeper_name VARCHAR(50) DEFAULT NULL COMMENT '保管人姓名',
    status CHAR(1) DEFAULT '1' COMMENT '状态(0停用1启用)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (seal_id),
    KEY idx_seal_type (seal_type),
    KEY idx_keeper_id (keeper_id),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='印章证照表';

-- ----------------------------
-- 5. 初始化数据
-- ----------------------------

-- 插入菜单数据
INSERT INTO sys_menu VALUES(2000, 'OA办公', 0, 1, 'oa', NULL, '', 1, 0, 'M', '0', '0', '', 'office', 'admin', sysdate(), '', null, 'OA办公自动化系统');

INSERT INTO sys_menu VALUES(2001, '工作台', 2000, 1, 'dashboard', 'oa/dashboard/index', '', 1, 0, 'C', '0', '0', 'oa:dashboard:view', 'dashboard', 'admin', sysdate(), '', null, 'OA工作台');

INSERT INTO sys_menu VALUES(2010, '流程管理', 2000, 2, 'workflow', NULL, '', 1, 0, 'M', '0', '0', '', 'workflow', 'admin', sysdate(), '', null, '工作流程管理');
INSERT INTO sys_menu VALUES(2011, '流程定义', 2010, 1, 'definition', 'oa/workflow/definition/index', '', 1, 0, 'C', '0', '0', 'oa:workflow:definition:list', 'form', 'admin', sysdate(), '', null, '流程定义管理');
INSERT INTO sys_menu VALUES(2012, '流程监控', 2010, 2, 'monitor', 'oa/workflow/monitor/index', '', 1, 0, 'C', '0', '0', 'oa:workflow:monitor:list', 'monitor', 'admin', sysdate(), '', null, '流程监控');

INSERT INTO sys_menu VALUES(2020, '公文管理', 2000, 3, 'document', NULL, '', 1, 0, 'M', '0', '0', '', 'documentation', 'admin', sysdate(), '', null, '公文管理');
INSERT INTO sys_menu VALUES(2021, '收文管理', 2020, 1, 'receive', 'oa/document/receive/index', '', 1, 0, 'C', '0', '0', 'oa:document:receive:list', 'download', 'admin', sysdate(), '', null, '收文管理');
INSERT INTO sys_menu VALUES(2022, '发文管理', 2020, 2, 'send', 'oa/document/send/index', '', 1, 0, 'C', '0', '0', 'oa:document:send:list', 'upload', 'admin', sysdate(), '', null, '发文管理');

INSERT INTO sys_menu VALUES(2030, '个人办公', 2000, 4, 'personal', NULL, '', 1, 0, 'M', '0', '0', '', 'user', 'admin', sysdate(), '', null, '个人办公');
INSERT INTO sys_menu VALUES(2031, '日程管理', 2030, 1, 'schedule', 'oa/personal/schedule/index', '', 1, 0, 'C', '0', '0', 'oa:personal:schedule:list', 'date', 'admin', sysdate(), '', null, '日程管理');
INSERT INTO sys_menu VALUES(2032, '工作报告', 2030, 2, 'report', 'oa/personal/report/index', '', 1, 0, 'C', '0', '0', 'oa:personal:report:list', 'edit', 'admin', sysdate(), '', null, '工作报告');

-- 插入字典类型
INSERT INTO sys_dict_type VALUES(100, 'oa_doc_type', '公文类型', '0', 'admin', sysdate(), '', null, 'OA公文类型列表');
INSERT INTO sys_dict_type VALUES(101, 'oa_urgency_level', '紧急程度', '0', 'admin', sysdate(), '', null, 'OA紧急程度列表');
INSERT INTO sys_dict_type VALUES(102, 'oa_security_level', '密级', '0', 'admin', sysdate(), '', null, 'OA密级列表');
INSERT INTO sys_dict_type VALUES(103, 'oa_doc_status', '公文状态', '0', 'admin', sysdate(), '', null, 'OA公文状态列表');

-- 插入字典数据
INSERT INTO sys_dict_data VALUES(100, 1, '收文', '1', 'oa_doc_type', '', '', 'Y', '0', 'admin', sysdate(), '', null, '收文');
INSERT INTO sys_dict_data VALUES(101, 2, '发文', '2', 'oa_doc_type', '', '', 'Y', '0', 'admin', sysdate(), '', null, '发文');

INSERT INTO sys_dict_data VALUES(102, 1, '紧急', '1', 'oa_urgency_level', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '紧急');
INSERT INTO sys_dict_data VALUES(103, 2, '普通', '2', 'oa_urgency_level', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '普通');
INSERT INTO sys_dict_data VALUES(104, 3, '缓办', '3', 'oa_urgency_level', '', 'info', 'Y', '0', 'admin', sysdate(), '', null, '缓办');

INSERT INTO sys_dict_data VALUES(105, 1, '公开', '1', 'oa_security_level', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '公开');
INSERT INTO sys_dict_data VALUES(106, 2, '内部', '2', 'oa_security_level', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '内部');
INSERT INTO sys_dict_data VALUES(107, 3, '秘密', '3', 'oa_security_level', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '秘密');
INSERT INTO sys_dict_data VALUES(108, 4, '机密', '4', 'oa_security_level', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '机密');

INSERT INTO sys_dict_data VALUES(109, 1, '草稿', '1', 'oa_doc_status', '', 'info', 'Y', '0', 'admin', sysdate(), '', null, '草稿');
INSERT INTO sys_dict_data VALUES(110, 2, '审批中', '2', 'oa_doc_status', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '审批中');
INSERT INTO sys_dict_data VALUES(111, 3, '已发布', '3', 'oa_doc_status', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '已发布');
INSERT INTO sys_dict_data VALUES(112, 4, '已归档', '4', 'oa_doc_status', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '已归档');
