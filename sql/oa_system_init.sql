-- OA系统数据库初始化脚本
-- 创建时间：2024-07-01
-- 说明：基于若依框架的OA办公自动化系统数据库表结构

-- ----------------------------
-- 1. 工作流相关表
-- ----------------------------

-- 工作流定义表
DROP TABLE IF EXISTS oa_workflow_definition;
CREATE TABLE oa_workflow_definition (
    workflow_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '流程ID',
    workflow_name VARCHAR(100) NOT NULL COMMENT '流程名称',
    workflow_key VARCHAR(50) NOT NULL COMMENT '流程标识',
    workflow_category VARCHAR(50) DEFAULT NULL COMMENT '流程分类',
    workflow_xml TEXT COMMENT '流程定义XML',
    version INT DEFAULT 1 COMMENT '版本号',
    status CHAR(1) DEFAULT '1' COMMENT '状态(0停用1启用)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT NULL COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (workflow_id),
    UNIQUE KEY uk_workflow_key_version (workflow_key, version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流定义表';

-- 工作流实例表
DROP TABLE IF EXISTS oa_workflow_instance;
CREATE TABLE oa_workflow_instance (
    instance_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '实例ID',
    workflow_id BIGINT NOT NULL COMMENT '流程定义ID',
    business_key VARCHAR(100) DEFAULT NULL COMMENT '业务主键',
    start_user_id BIGINT NOT NULL COMMENT '发起人ID',
    start_user_name VARCHAR(50) DEFAULT NULL COMMENT '发起人姓名',
    current_task VARCHAR(100) DEFAULT NULL COMMENT '当前任务',
    status CHAR(1) DEFAULT '1' COMMENT '状态(1进行中2已完成3已终止)',
    start_time DATETIME DEFAULT NULL COMMENT '开始时间',
    end_time DATETIME DEFAULT NULL COMMENT '结束时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (instance_id),
    KEY idx_workflow_id (workflow_id),
    KEY idx_start_user_id (start_user_id),
    KEY idx_business_key (business_key),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流实例表';

-- 工作流任务表
DROP TABLE IF EXISTS oa_workflow_task;
CREATE TABLE oa_workflow_task (
    task_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '任务ID',
    instance_id BIGINT NOT NULL COMMENT '流程实例ID',
    task_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    task_key VARCHAR(50) DEFAULT NULL COMMENT '任务标识',
    assignee_id BIGINT DEFAULT NULL COMMENT '处理人ID',
    assignee_name VARCHAR(50) DEFAULT NULL COMMENT '处理人姓名',
    status CHAR(1) DEFAULT '1' COMMENT '状态(1待处理2已处理3已转办)',
    priority INT DEFAULT 50 COMMENT '优先级',
    due_date DATETIME DEFAULT NULL COMMENT '到期时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    complete_time DATETIME DEFAULT NULL COMMENT '完成时间',
    comment TEXT COMMENT '处理意见',
    PRIMARY KEY (task_id),
    KEY idx_instance_id (instance_id),
    KEY idx_assignee_id (assignee_id),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流任务表';

-- ----------------------------
-- 2. 公文管理相关表
-- ----------------------------

-- 公文基础表
DROP TABLE IF EXISTS oa_document;
CREATE TABLE oa_document (
    doc_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '文档ID',
    doc_title VARCHAR(200) NOT NULL COMMENT '文档标题',
    doc_number VARCHAR(50) DEFAULT NULL COMMENT '文档编号',
    doc_type CHAR(1) NOT NULL COMMENT '文档类型(1收文2发文)',
    doc_category VARCHAR(50) DEFAULT NULL COMMENT '文档分类',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    creator_name VARCHAR(50) DEFAULT NULL COMMENT '创建人姓名',
    dept_id BIGINT DEFAULT NULL COMMENT '所属部门ID',
    dept_name VARCHAR(50) DEFAULT NULL COMMENT '部门名称',
    urgency_level CHAR(1) DEFAULT '2' COMMENT '紧急程度(1紧急2普通3缓办)',
    security_level CHAR(1) DEFAULT '1' COMMENT '密级(1公开2内部3秘密4机密)',
    status CHAR(1) DEFAULT '1' COMMENT '状态(1草稿2审批中3已发布4已归档)',
    content TEXT COMMENT '文档内容',
    source_unit VARCHAR(200) DEFAULT NULL COMMENT '来文单位',
    receive_date DATE DEFAULT NULL COMMENT '收文日期',
    main_send_unit VARCHAR(500) DEFAULT NULL COMMENT '主送单位',
    copy_send_unit VARCHAR(500) DEFAULT NULL COMMENT '抄送单位',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    publish_time DATETIME DEFAULT NULL COMMENT '发布时间',
    PRIMARY KEY (doc_id),
    KEY idx_doc_type (doc_type),
    KEY idx_creator_id (creator_id),
    KEY idx_dept_id (dept_id),
    KEY idx_status (status),
    KEY idx_doc_number (doc_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公文基础表';

-- 公文流程关联表
DROP TABLE IF EXISTS oa_document_flow;
CREATE TABLE oa_document_flow (
    flow_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '流程ID',
    doc_id BIGINT NOT NULL COMMENT '文档ID',
    instance_id BIGINT NOT NULL COMMENT '流程实例ID',
    flow_type CHAR(1) NOT NULL COMMENT '流程类型(1收文2发文3特办)',
    status CHAR(1) DEFAULT '1' COMMENT '状态',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (flow_id),
    KEY idx_doc_id (doc_id),
    KEY idx_instance_id (instance_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公文流程关联表';

-- 文件附件表
DROP TABLE IF EXISTS oa_file_attachment;
CREATE TABLE oa_file_attachment (
    file_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '文件ID',
    business_id BIGINT NOT NULL COMMENT '业务ID',
    business_type VARCHAR(20) NOT NULL COMMENT '业务类型',
    file_name VARCHAR(200) NOT NULL COMMENT '文件名称',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_type VARCHAR(20) DEFAULT NULL COMMENT '文件类型',
    file_size BIGINT DEFAULT NULL COMMENT '文件大小',
    file_ext VARCHAR(10) DEFAULT NULL COMMENT '文件扩展名',
    is_main CHAR(1) DEFAULT '0' COMMENT '是否为主要文件(0否1是)',
    upload_user_id BIGINT DEFAULT NULL COMMENT '上传人ID',
    upload_user_name VARCHAR(50) DEFAULT NULL COMMENT '上传人姓名',
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    PRIMARY KEY (file_id),
    KEY idx_business_id_type (business_id, business_type),
    KEY idx_upload_user_id (upload_user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件附件表';

-- ----------------------------
-- 3. 个人办公相关表
-- ----------------------------

-- 个人日程表
DROP TABLE IF EXISTS oa_personal_schedule;
CREATE TABLE oa_personal_schedule (
    schedule_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '日程ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    user_name VARCHAR(50) DEFAULT NULL COMMENT '用户姓名',
    title VARCHAR(200) NOT NULL COMMENT '日程标题',
    content TEXT COMMENT '日程内容',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    location VARCHAR(200) DEFAULT NULL COMMENT '地点',
    remind_time DATETIME DEFAULT NULL COMMENT '提醒时间',
    status CHAR(1) DEFAULT '1' COMMENT '状态(1正常2已完成3已取消)',
    repeat_type CHAR(1) DEFAULT '0' COMMENT '重复类型(0不重复1每天2每周3每月4每年)',
    priority CHAR(1) DEFAULT '1' COMMENT '重要程度(1普通2重要3紧急)',
    participants VARCHAR(500) DEFAULT NULL COMMENT '参与人员',
    is_all_day CHAR(1) DEFAULT '0' COMMENT '是否全天(0否1是)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (schedule_id),
    KEY idx_user_id (user_id),
    KEY idx_start_time (start_time),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人日程表';

-- 工作报告表
DROP TABLE IF EXISTS oa_work_report;
CREATE TABLE oa_work_report (
    report_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '报告ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    user_name VARCHAR(50) DEFAULT NULL COMMENT '用户姓名',
    dept_id BIGINT DEFAULT NULL COMMENT '部门ID',
    dept_name VARCHAR(50) DEFAULT NULL COMMENT '部门名称',
    report_type CHAR(1) NOT NULL COMMENT '报告类型(1日报2周报3月报4年报)',
    report_date DATE NOT NULL COMMENT '报告日期',
    title VARCHAR(200) NOT NULL COMMENT '报告标题',
    work_content TEXT COMMENT '工作内容',
    completion_status TEXT COMMENT '完成情况',
    problems TEXT COMMENT '存在问题',
    next_plan TEXT COMMENT '下步计划',
    coordination_matters TEXT COMMENT '需要协调事项',
    status CHAR(1) DEFAULT '1' COMMENT '状态(1草稿2已提交3已审阅)',
    submit_time DATETIME DEFAULT NULL COMMENT '提交时间',
    reviewer_id BIGINT DEFAULT NULL COMMENT '审阅人ID',
    reviewer_name VARCHAR(50) DEFAULT NULL COMMENT '审阅人姓名',
    review_time DATETIME DEFAULT NULL COMMENT '审阅时间',
    review_comment TEXT COMMENT '审阅意见',
    workload_stats VARCHAR(500) DEFAULT NULL COMMENT '工作量统计',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (report_id),
    KEY idx_user_id (user_id),
    KEY idx_report_type (report_type),
    KEY idx_report_date (report_date),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作报告表';

-- 个人通讯录表
DROP TABLE IF EXISTS oa_personal_contact;
CREATE TABLE oa_personal_contact (
    contact_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '联系人ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    name VARCHAR(50) NOT NULL COMMENT '联系人姓名',
    phone VARCHAR(20) DEFAULT NULL COMMENT '电话号码',
    mobile VARCHAR(20) DEFAULT NULL COMMENT '手机号码',
    email VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    company VARCHAR(100) DEFAULT NULL COMMENT '公司',
    position VARCHAR(50) DEFAULT NULL COMMENT '职位',
    department VARCHAR(50) DEFAULT NULL COMMENT '部门',
    address VARCHAR(200) DEFAULT NULL COMMENT '地址',
    postcode VARCHAR(10) DEFAULT NULL COMMENT '邮编',
    qq VARCHAR(20) DEFAULT NULL COMMENT 'QQ号码',
    wechat VARCHAR(50) DEFAULT NULL COMMENT '微信号',
    group_name VARCHAR(50) DEFAULT NULL COMMENT '分组',
    avatar VARCHAR(200) DEFAULT NULL COMMENT '头像',
    birthday VARCHAR(20) DEFAULT NULL COMMENT '生日',
    gender CHAR(1) DEFAULT '2' COMMENT '性别(0男1女2未知)',
    tags VARCHAR(200) DEFAULT NULL COMMENT '标签',
    is_favorite CHAR(1) DEFAULT '0' COMMENT '是否收藏(0否1是)',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (contact_id),
    KEY idx_user_id (user_id),
    KEY idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人通讯录表';

-- ----------------------------
-- 4. 系统配置相关表
-- ----------------------------

-- 公文模板表
DROP TABLE IF EXISTS oa_document_template;
CREATE TABLE oa_document_template (
    template_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '模板ID',
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_type CHAR(1) NOT NULL COMMENT '模板类型(1收文2发文)',
    template_content TEXT COMMENT '模板内容',
    template_file VARCHAR(500) DEFAULT NULL COMMENT '模板文件路径',
    status CHAR(1) DEFAULT '1' COMMENT '状态(0停用1启用)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (template_id),
    KEY idx_template_type (template_type),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公文模板表';

-- 印章证照表
DROP TABLE IF EXISTS oa_seal_certificate;
CREATE TABLE oa_seal_certificate (
    seal_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '印章ID',
    seal_name VARCHAR(100) NOT NULL COMMENT '印章名称',
    seal_type CHAR(1) NOT NULL COMMENT '印章类型(1公章2财务章3法人章4合同章5部门章6业务章)',
    seal_code VARCHAR(50) DEFAULT NULL COMMENT '印章编号',
    seal_image VARCHAR(500) DEFAULT NULL COMMENT '印章图片路径',
    seal_description VARCHAR(500) DEFAULT NULL COMMENT '印章描述',
    keeper_id BIGINT DEFAULT NULL COMMENT '保管人ID',
    keeper_name VARCHAR(50) DEFAULT NULL COMMENT '保管人姓名',
    keeper_dept_id BIGINT DEFAULT NULL COMMENT '保管部门ID',
    keeper_dept_name VARCHAR(50) DEFAULT NULL COMMENT '保管部门名称',
    contact_phone VARCHAR(20) DEFAULT NULL COMMENT '联系电话',
    status CHAR(1) DEFAULT '1' COMMENT '状态(0停用1启用2封存)',
    enable_date DATE DEFAULT NULL COMMENT '启用日期',
    disable_date DATE DEFAULT NULL COMMENT '停用日期',
    manufacturer VARCHAR(100) DEFAULT NULL COMMENT '制作单位',
    manufacture_date DATE DEFAULT NULL COMMENT '制作日期',
    material VARCHAR(50) DEFAULT NULL COMMENT '材质',
    specifications VARCHAR(100) DEFAULT NULL COMMENT '规格尺寸',
    usage_scope VARCHAR(500) DEFAULT NULL COMMENT '使用范围',
    approval_authority VARCHAR(200) DEFAULT NULL COMMENT '审批权限',
    record_number VARCHAR(50) DEFAULT NULL COMMENT '备案编号',
    record_date DATE DEFAULT NULL COMMENT '备案日期',
    order_num INT DEFAULT 0 COMMENT '排序',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (seal_id),
    KEY idx_seal_type (seal_type),
    KEY idx_keeper_id (keeper_id),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='印章证照表';

-- 印章使用申请表
DROP TABLE IF EXISTS oa_seal_application;
CREATE TABLE oa_seal_application (
    application_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '申请ID',
    application_no VARCHAR(50) NOT NULL COMMENT '申请编号',
    applicant_id BIGINT NOT NULL COMMENT '申请人ID',
    applicant_name VARCHAR(50) DEFAULT NULL COMMENT '申请人姓名',
    dept_id BIGINT DEFAULT NULL COMMENT '申请部门ID',
    dept_name VARCHAR(50) DEFAULT NULL COMMENT '申请部门名称',
    seal_id BIGINT NOT NULL COMMENT '印章ID',
    seal_name VARCHAR(100) DEFAULT NULL COMMENT '印章名称',
    application_reason VARCHAR(500) NOT NULL COMMENT '申请事由',
    usage_purpose VARCHAR(500) DEFAULT NULL COMMENT '使用目的',
    document_name VARCHAR(200) DEFAULT NULL COMMENT '文件名称',
    document_count INT DEFAULT 1 COMMENT '文件数量',
    expected_use_time DATETIME DEFAULT NULL COMMENT '预计使用时间',
    actual_use_time DATETIME DEFAULT NULL COMMENT '实际使用时间',
    return_time DATETIME DEFAULT NULL COMMENT '归还时间',
    use_location VARCHAR(200) DEFAULT NULL COMMENT '使用地点',
    urgency_level CHAR(1) DEFAULT '1' COMMENT '紧急程度(1普通2紧急3特急)',
    status CHAR(1) DEFAULT '1' COMMENT '状态(1待审批2已审批3已拒绝4使用中5已归还6已取消)',
    approver_id BIGINT DEFAULT NULL COMMENT '审批人ID',
    approver_name VARCHAR(50) DEFAULT NULL COMMENT '审批人姓名',
    approval_time DATETIME DEFAULT NULL COMMENT '审批时间',
    approval_comment VARCHAR(500) DEFAULT NULL COMMENT '审批意见',
    keeper_confirm CHAR(1) DEFAULT '0' COMMENT '保管人确认(0未确认1已确认)',
    keeper_confirm_time DATETIME DEFAULT NULL COMMENT '保管人确认时间',
    usage_instructions VARCHAR(500) DEFAULT NULL COMMENT '使用说明',
    contact_phone VARCHAR(20) DEFAULT NULL COMMENT '联系电话',
    instance_id BIGINT DEFAULT NULL COMMENT '流程实例ID',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (application_id),
    UNIQUE KEY uk_application_no (application_no),
    KEY idx_applicant_id (applicant_id),
    KEY idx_seal_id (seal_id),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='印章使用申请表';

-- ----------------------------
-- 5. 会议管理相关表
-- ----------------------------

-- 会议室表
DROP TABLE IF EXISTS oa_meeting_room;
CREATE TABLE oa_meeting_room (
    room_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '会议室ID',
    room_name VARCHAR(100) NOT NULL COMMENT '会议室名称',
    room_code VARCHAR(50) DEFAULT NULL COMMENT '会议室编号',
    location VARCHAR(200) DEFAULT NULL COMMENT '会议室位置',
    capacity INT DEFAULT 0 COMMENT '容纳人数',
    equipment VARCHAR(500) DEFAULT NULL COMMENT '设备配置',
    room_image VARCHAR(500) DEFAULT NULL COMMENT '会议室图片',
    status CHAR(1) DEFAULT '1' COMMENT '状态(0停用1启用)',
    admin_id BIGINT DEFAULT NULL COMMENT '管理员ID',
    admin_name VARCHAR(50) DEFAULT NULL COMMENT '管理员姓名',
    contact_phone VARCHAR(20) DEFAULT NULL COMMENT '联系电话',
    instructions TEXT COMMENT '使用说明',
    booking_rules TEXT COMMENT '预约规则',
    charging_standard VARCHAR(200) DEFAULT NULL COMMENT '收费标准',
    order_num INT DEFAULT 0 COMMENT '排序',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (room_id),
    KEY idx_room_code (room_code),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议室表';

-- 会议表
DROP TABLE IF EXISTS oa_meeting;
CREATE TABLE oa_meeting (
    meeting_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '会议ID',
    meeting_title VARCHAR(200) NOT NULL COMMENT '会议主题',
    meeting_type CHAR(1) DEFAULT '1' COMMENT '会议类型(1内部会议2外部会议3视频会议4电话会议)',
    room_id BIGINT DEFAULT NULL COMMENT '会议室ID',
    room_name VARCHAR(100) DEFAULT NULL COMMENT '会议室名称',
    location VARCHAR(200) DEFAULT NULL COMMENT '会议地点',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    organizer_id BIGINT NOT NULL COMMENT '组织者ID',
    organizer_name VARCHAR(50) DEFAULT NULL COMMENT '组织者姓名',
    participants TEXT COMMENT '参会人员',
    participant_ids VARCHAR(1000) DEFAULT NULL COMMENT '参会人员ID列表',
    content TEXT COMMENT '会议内容',
    agenda TEXT COMMENT '会议议程',
    minutes TEXT COMMENT '会议纪要',
    status CHAR(1) DEFAULT '1' COMMENT '状态(1待开始2进行中3已结束4已取消)',
    priority CHAR(1) DEFAULT '1' COMMENT '重要程度(1普通2重要3紧急)',
    need_sign_in CHAR(1) DEFAULT '0' COMMENT '是否需要签到(0否1是)',
    remind_minutes INT DEFAULT 30 COMMENT '提醒时间(分钟)',
    meeting_link VARCHAR(500) DEFAULT NULL COMMENT '会议链接',
    meeting_password VARCHAR(50) DEFAULT NULL COMMENT '会议密码',
    expected_attendees INT DEFAULT 0 COMMENT '预计参会人数',
    actual_attendees INT DEFAULT 0 COMMENT '实际参会人数',
    meeting_cost DECIMAL(10,2) DEFAULT 0.00 COMMENT '会议费用',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (meeting_id),
    KEY idx_organizer_id (organizer_id),
    KEY idx_room_id (room_id),
    KEY idx_start_time (start_time),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议表';

-- ----------------------------
-- 6. 初始化数据
-- ----------------------------

-- 插入菜单数据
INSERT INTO sys_menu VALUES(2000, 'OA办公', 0, 1, 'oa', NULL, '', 1, 0, 'M', '0', '0', '', 'office', 'admin', sysdate(), '', null, 'OA办公自动化系统');

INSERT INTO sys_menu VALUES(2001, '工作台', 2000, 1, 'dashboard', 'oa/dashboard/index', '', 1, 0, 'C', '0', '0', 'oa:dashboard:view', 'dashboard', 'admin', sysdate(), '', null, 'OA工作台');

INSERT INTO sys_menu VALUES(2010, '流程管理', 2000, 2, 'workflow', NULL, '', 1, 0, 'M', '0', '0', '', 'workflow', 'admin', sysdate(), '', null, '工作流程管理');
INSERT INTO sys_menu VALUES(2011, '流程定义', 2010, 1, 'definition', 'oa/workflow/definition/index', '', 1, 0, 'C', '0', '0', 'oa:workflow:definition:list', 'form', 'admin', sysdate(), '', null, '流程定义管理');
INSERT INTO sys_menu VALUES(2012, '流程监控', 2010, 2, 'monitor', 'oa/workflow/monitor/index', '', 1, 0, 'C', '0', '0', 'oa:workflow:monitor:list', 'monitor', 'admin', sysdate(), '', null, '流程监控');

INSERT INTO sys_menu VALUES(2020, '公文管理', 2000, 3, 'document', NULL, '', 1, 0, 'M', '0', '0', '', 'documentation', 'admin', sysdate(), '', null, '公文管理');
INSERT INTO sys_menu VALUES(2021, '收文管理', 2020, 1, 'receive', 'oa/document/receive/index', '', 1, 0, 'C', '0', '0', 'oa:document:receive:list', 'download', 'admin', sysdate(), '', null, '收文管理');
INSERT INTO sys_menu VALUES(2022, '发文管理', 2020, 2, 'send', 'oa/document/send/index', '', 1, 0, 'C', '0', '0', 'oa:document:send:list', 'upload', 'admin', sysdate(), '', null, '发文管理');

INSERT INTO sys_menu VALUES(2030, '个人办公', 2000, 4, 'personal', NULL, '', 1, 0, 'M', '0', '0', '', 'user', 'admin', sysdate(), '', null, '个人办公');
INSERT INTO sys_menu VALUES(2031, '个人信息', 2030, 1, 'profile', 'oa/personal/profile/index', '', 1, 0, 'C', '0', '0', 'oa:personal:profile:view', 'user', 'admin', sysdate(), '', null, '个人信息');
INSERT INTO sys_menu VALUES(2032, '日程管理', 2030, 2, 'schedule', 'oa/personal/schedule/index', '', 1, 0, 'C', '0', '0', 'oa:personal:schedule:list', 'date', 'admin', sysdate(), '', null, '日程管理');
INSERT INTO sys_menu VALUES(2033, '工作报告', 2030, 3, 'report', 'oa/personal/report/index', '', 1, 0, 'C', '0', '0', 'oa:personal:report:list', 'edit', 'admin', sysdate(), '', null, '工作报告');
INSERT INTO sys_menu VALUES(2034, '个人通讯录', 2030, 4, 'contact', 'oa/personal/contact/index', '', 1, 0, 'C', '0', '0', 'oa:personal:contact:list', 'phone', 'admin', sysdate(), '', null, '个人通讯录');
INSERT INTO sys_menu VALUES(2035, '内部通讯录', 2030, 5, 'internal-contact', 'oa/personal/internal-contact/index', '', 1, 0, 'C', '0', '0', 'oa:personal:internal:list', 'peoples', 'admin', sysdate(), '', null, '内部通讯录');

INSERT INTO sys_menu VALUES(2040, '会议管理', 2000, 5, 'meeting', NULL, '', 1, 0, 'M', '0', '0', '', 'skill', 'admin', sysdate(), '', null, '会议管理');
INSERT INTO sys_menu VALUES(2041, '会议室管理', 2040, 1, 'room', 'oa/meeting/room/index', '', 1, 0, 'C', '0', '0', 'oa:meeting:room:list', 'build', 'admin', sysdate(), '', null, '会议室管理');
INSERT INTO sys_menu VALUES(2042, '会议预约', 2040, 2, 'booking', 'oa/meeting/booking/index', '', 1, 0, 'C', '0', '0', 'oa:meeting:booking:list', 'time-range', 'admin', sysdate(), '', null, '会议预约');
INSERT INTO sys_menu VALUES(2043, '我的会议', 2040, 3, 'my-meeting', 'oa/meeting/my-meeting/index', '', 1, 0, 'C', '0', '0', 'oa:meeting:my:list', 'list', 'admin', sysdate(), '', null, '我的会议');

INSERT INTO sys_menu VALUES(2050, '行政管理', 2000, 6, 'admin', NULL, '', 1, 0, 'M', '0', '0', '', 'component', 'admin', sysdate(), '', null, '行政管理');
INSERT INTO sys_menu VALUES(2051, '印章管理', 2050, 1, 'seal', 'oa/seal/certificate/index', '', 1, 0, 'C', '0', '0', 'oa:seal:certificate:list', 'validCode', 'admin', sysdate(), '', null, '印章管理');
INSERT INTO sys_menu VALUES(2052, '印章申请', 2050, 2, 'seal-application', 'oa/seal/application/index', '', 1, 0, 'C', '0', '0', 'oa:seal:application:list', 'form', 'admin', sysdate(), '', null, '印章申请');

-- 插入字典类型
INSERT INTO sys_dict_type VALUES(100, 'oa_doc_type', '公文类型', '0', 'admin', sysdate(), '', null, 'OA公文类型列表');
INSERT INTO sys_dict_type VALUES(101, 'oa_urgency_level', '紧急程度', '0', 'admin', sysdate(), '', null, 'OA紧急程度列表');
INSERT INTO sys_dict_type VALUES(102, 'oa_security_level', '密级', '0', 'admin', sysdate(), '', null, 'OA密级列表');
INSERT INTO sys_dict_type VALUES(103, 'oa_doc_status', '公文状态', '0', 'admin', sysdate(), '', null, 'OA公文状态列表');
INSERT INTO sys_dict_type VALUES(104, 'oa_meeting_type', '会议类型', '0', 'admin', sysdate(), '', null, 'OA会议类型列表');
INSERT INTO sys_dict_type VALUES(105, 'oa_meeting_status', '会议状态', '0', 'admin', sysdate(), '', null, 'OA会议状态列表');
INSERT INTO sys_dict_type VALUES(106, 'oa_seal_type', '印章类型', '0', 'admin', sysdate(), '', null, 'OA印章类型列表');
INSERT INTO sys_dict_type VALUES(107, 'oa_seal_status', '印章状态', '0', 'admin', sysdate(), '', null, 'OA印章状态列表');
INSERT INTO sys_dict_type VALUES(108, 'oa_report_type', '报告类型', '0', 'admin', sysdate(), '', null, 'OA报告类型列表');
INSERT INTO sys_dict_type VALUES(109, 'oa_schedule_status', '日程状态', '0', 'admin', sysdate(), '', null, 'OA日程状态列表');

-- 插入字典数据
INSERT INTO sys_dict_data VALUES(100, 1, '收文', '1', 'oa_doc_type', '', '', 'Y', '0', 'admin', sysdate(), '', null, '收文');
INSERT INTO sys_dict_data VALUES(101, 2, '发文', '2', 'oa_doc_type', '', '', 'Y', '0', 'admin', sysdate(), '', null, '发文');

INSERT INTO sys_dict_data VALUES(102, 1, '紧急', '1', 'oa_urgency_level', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '紧急');
INSERT INTO sys_dict_data VALUES(103, 2, '普通', '2', 'oa_urgency_level', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '普通');
INSERT INTO sys_dict_data VALUES(104, 3, '缓办', '3', 'oa_urgency_level', '', 'info', 'Y', '0', 'admin', sysdate(), '', null, '缓办');

INSERT INTO sys_dict_data VALUES(105, 1, '公开', '1', 'oa_security_level', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '公开');
INSERT INTO sys_dict_data VALUES(106, 2, '内部', '2', 'oa_security_level', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '内部');
INSERT INTO sys_dict_data VALUES(107, 3, '秘密', '3', 'oa_security_level', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '秘密');
INSERT INTO sys_dict_data VALUES(108, 4, '机密', '4', 'oa_security_level', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '机密');

INSERT INTO sys_dict_data VALUES(109, 1, '草稿', '1', 'oa_doc_status', '', 'info', 'Y', '0', 'admin', sysdate(), '', null, '草稿');
INSERT INTO sys_dict_data VALUES(110, 2, '审批中', '2', 'oa_doc_status', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '审批中');
INSERT INTO sys_dict_data VALUES(111, 3, '已发布', '3', 'oa_doc_status', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '已发布');
INSERT INTO sys_dict_data VALUES(112, 4, '已归档', '4', 'oa_doc_status', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '已归档');

-- 会议类型字典
INSERT INTO sys_dict_data VALUES(113, 1, '内部会议', '1', 'oa_meeting_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '内部会议');
INSERT INTO sys_dict_data VALUES(114, 2, '外部会议', '2', 'oa_meeting_type', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '外部会议');
INSERT INTO sys_dict_data VALUES(115, 3, '视频会议', '3', 'oa_meeting_type', '', 'info', 'Y', '0', 'admin', sysdate(), '', null, '视频会议');
INSERT INTO sys_dict_data VALUES(116, 4, '电话会议', '4', 'oa_meeting_type', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '电话会议');

-- 会议状态字典
INSERT INTO sys_dict_data VALUES(117, 1, '待开始', '1', 'oa_meeting_status', '', 'info', 'Y', '0', 'admin', sysdate(), '', null, '待开始');
INSERT INTO sys_dict_data VALUES(118, 2, '进行中', '2', 'oa_meeting_status', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '进行中');
INSERT INTO sys_dict_data VALUES(119, 3, '已结束', '3', 'oa_meeting_status', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '已结束');
INSERT INTO sys_dict_data VALUES(120, 4, '已取消', '4', 'oa_meeting_status', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '已取消');

-- 印章类型字典
INSERT INTO sys_dict_data VALUES(121, 1, '公章', '1', 'oa_seal_type', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '公章');
INSERT INTO sys_dict_data VALUES(122, 2, '财务章', '2', 'oa_seal_type', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '财务章');
INSERT INTO sys_dict_data VALUES(123, 3, '法人章', '3', 'oa_seal_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '法人章');
INSERT INTO sys_dict_data VALUES(124, 4, '合同章', '4', 'oa_seal_type', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '合同章');
INSERT INTO sys_dict_data VALUES(125, 5, '部门章', '5', 'oa_seal_type', '', 'info', 'Y', '0', 'admin', sysdate(), '', null, '部门章');
INSERT INTO sys_dict_data VALUES(126, 6, '业务章', '6', 'oa_seal_type', '', 'default', 'Y', '0', 'admin', sysdate(), '', null, '业务章');

-- 印章状态字典
INSERT INTO sys_dict_data VALUES(127, 0, '停用', '0', 'oa_seal_status', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '停用');
INSERT INTO sys_dict_data VALUES(128, 1, '启用', '1', 'oa_seal_status', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '启用');
INSERT INTO sys_dict_data VALUES(129, 2, '封存', '2', 'oa_seal_status', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '封存');

-- 报告类型字典
INSERT INTO sys_dict_data VALUES(130, 1, '日报', '1', 'oa_report_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '日报');
INSERT INTO sys_dict_data VALUES(131, 2, '周报', '2', 'oa_report_type', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '周报');
INSERT INTO sys_dict_data VALUES(132, 3, '月报', '3', 'oa_report_type', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '月报');
INSERT INTO sys_dict_data VALUES(133, 4, '年报', '4', 'oa_report_type', '', 'info', 'Y', '0', 'admin', sysdate(), '', null, '年报');

-- 日程状态字典
INSERT INTO sys_dict_data VALUES(134, 1, '正常', '1', 'oa_schedule_status', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '正常');
INSERT INTO sys_dict_data VALUES(135, 2, '已完成', '2', 'oa_schedule_status', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '已完成');
INSERT INTO sys_dict_data VALUES(136, 3, '已取消', '3', 'oa_schedule_status', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '已取消');
