package com.base.study.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.enums.BusinessType;
import com.base.study.domain.StudyRegistration;
import com.base.study.service.IStudyRegistrationService;
import com.base.common.utils.poi.ExcelUtil;
import com.base.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 学员报名表Controller
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@RestController
@RequestMapping("/study/registration")
public class StudyRegistrationController extends BaseController
{
    @Autowired
    private IStudyRegistrationService studyRegistrationService;

    /**
     * 查询学员报名表列表
     */
    @PreAuthorize("@ss.hasPermi('study:registration:list')")
    @GetMapping("/list")
    public TableDataInfo list(StudyRegistration studyRegistration)
    {
        startPage();
        List<StudyRegistration> list = studyRegistrationService.selectStudyRegistrationList(studyRegistration);
        return getDataTable(list);
    }

    /**
     * 导出学员报名表列表
     */
    @PreAuthorize("@ss.hasPermi('study:registration:export')")
    @Log(title = "学员报名表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StudyRegistration studyRegistration)
    {
        List<StudyRegistration> list = studyRegistrationService.selectStudyRegistrationList(studyRegistration);
        ExcelUtil<StudyRegistration> util = new ExcelUtil<StudyRegistration>(StudyRegistration.class);
        util.exportExcel(response, list, "学员报名表数据");
    }

    /**
     * 获取报名信息模版
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<StudyRegistration> util = new ExcelUtil<StudyRegistration>(StudyRegistration.class);
        util.importTemplateExcel(response, "报名数据");
    }

    @Log(title = "报名管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('study:registration:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<StudyRegistration> util = new ExcelUtil<StudyRegistration>(StudyRegistration.class);
        List<StudyRegistration> studyRegistrationList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = studyRegistrationService.importStudyRegistration(studyRegistrationList, updateSupport, operName);
        return success(message);
    }

    /**
     * 获取学员报名表详细信息
     */
    @PreAuthorize("@ss.hasPermi('study:registration:query')")
    @GetMapping(value = "/{registrationId}")
    public AjaxResult getInfo(@PathVariable("registrationId") Long registrationId)
    {
        return success(studyRegistrationService.selectStudyRegistrationByRegistrationId(registrationId));
    }

    /**
     * 新增学员报名表
     */
    @PreAuthorize("@ss.hasPermi('study:registration:add')")
    @Log(title = "学员报名表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StudyRegistration studyRegistration)
    {
        //因为登录账号是手机号，所以这里保存手机号
        studyRegistration.setCreateBy(getLoginUser().getUser().getPhonenumber());
        return studyRegistrationService.insertStudyRegistration(studyRegistration);
    }

    /**
     * 修改学员报名表
     */
    @PreAuthorize("@ss.hasPermi('study:registration:edit')")
    @Log(title = "学员报名表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StudyRegistration studyRegistration)
    {
        studyRegistration.setUpdateBy(getUsername());
        return toAjax(studyRegistrationService.updateStudyRegistration(studyRegistration));
    }

    /**
     * 删除学员报名表
     */
    @PreAuthorize("@ss.hasPermi('study:registration:remove')")
    @Log(title = "学员报名表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{registrationIds}")
    public AjaxResult remove(@PathVariable Long[] registrationIds)
    {
        return toAjax(studyRegistrationService.deleteStudyRegistrationByRegistrationIds(registrationIds));
    }

    /**
     * 需要根据用户ID和班次ID，找到对应的报名ID
     * @return
     */
    @GetMapping("/getRegistrationByUserAndClass")
    public AjaxResult getRegistrationByUserAndClass(StudyRegistration studyRegistration)
    {
        List<StudyRegistration> list = studyRegistrationService.selectStudyRegistrationList(studyRegistration);
        if(!CollectionUtils.isEmpty(list)){
            return AjaxResult.success(list);
        }else{
            return AjaxResult.warn("未查询到学员的报名信息！");
        }
    }

    /**
     * 批准报名申请
     */
    @PreAuthorize("@ss.hasPermi('study:registration:approve')")
    @Log(title = "报名审批", businessType = BusinessType.UPDATE)
    @PutMapping("/approve/{registrationId}")
    public AjaxResult approveRegistration(@PathVariable("registrationId") Long registrationId)
    {
        StudyRegistration registration = studyRegistrationService.selectStudyRegistrationByRegistrationId(registrationId);
        if (registration == null) {
            return AjaxResult.error("报名记录不存在");
        }
        
        if (!"0".equals(registration.getStatus())) {
            return AjaxResult.error("只能审批待审核状态的报名申请");
        }
        
        registration.setStatus("1"); // 已通过
        registration.setUpdateBy(getUsername());
        registration.setUpdateTime(new java.util.Date());
        registration.setRemark("管理员审批通过");
        
        return toAjax(studyRegistrationService.updateStudyRegistration(registration));
    }

    /**
     * 拒绝报名申请
     */
    @PreAuthorize("@ss.hasPermi('study:registration:reject')")
    @Log(title = "报名审批", businessType = BusinessType.UPDATE)
    @PutMapping("/reject/{registrationId}")
    public AjaxResult rejectRegistration(@PathVariable("registrationId") Long registrationId, @RequestBody(required = false) String reason)
    {
        StudyRegistration registration = studyRegistrationService.selectStudyRegistrationByRegistrationId(registrationId);
        if (registration == null) {
            return AjaxResult.error("报名记录不存在");
        }
        
        if (!"0".equals(registration.getStatus())) {
            return AjaxResult.error("只能审批待审核状态的报名申请");
        }
        
        registration.setStatus("2"); // 已拒绝
        registration.setUpdateBy(getUsername());
        registration.setUpdateTime(new java.util.Date());
        registration.setRemark(reason != null && !reason.trim().isEmpty() ? reason : "管理员审批拒绝");
        
        return toAjax(studyRegistrationService.updateStudyRegistration(registration));
    }

    /**
     * 批量审批报名申请
     */
    @PreAuthorize("@ss.hasPermi('study:registration:approve')")
    @Log(title = "批量报名审批", businessType = BusinessType.UPDATE)
    @PutMapping("/batchApprove")
    public AjaxResult batchApproveRegistration(@RequestBody Long[] registrationIds)
    {
        if (registrationIds == null || registrationIds.length == 0) {
            return AjaxResult.error("请选择要审批的报名记录");
        }
        
        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMsg = new StringBuilder();
        
        for (Long registrationId : registrationIds) {
            try {
                StudyRegistration registration = studyRegistrationService.selectStudyRegistrationByRegistrationId(registrationId);
                if (registration != null && "0".equals(registration.getStatus())) {
                    registration.setStatus("1"); // 已通过
                    registration.setUpdateBy(getUsername());
                    registration.setUpdateTime(new java.util.Date());
                    registration.setRemark("管理员批量审批通过");
                    
                    if (studyRegistrationService.updateStudyRegistration(registration) > 0) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } else {
                    failCount++;
                    errorMsg.append("报名ID").append(registrationId).append("状态不符合审批条件；");
                }
            } catch (Exception e) {
                failCount++;
                errorMsg.append("报名ID").append(registrationId).append("审批失败：").append(e.getMessage()).append("；");
            }
        }
        
        String resultMsg = String.format("批量审批完成！成功：%d条，失败：%d条", successCount, failCount);
        if (errorMsg.length() > 0) {
            resultMsg += "。失败原因：" + errorMsg.toString();
        }
        
        return AjaxResult.success(resultMsg);
    }

    /**
     * 开始学习 - 将已通过的报名状态改为学习中
     */
    @PreAuthorize("@ss.hasPermi('study:registration:edit')")
    @Log(title = "开始学习", businessType = BusinessType.UPDATE)
    @PutMapping("/startStudy/{registrationId}")
    public AjaxResult startStudy(@PathVariable("registrationId") Long registrationId)
    {
        StudyRegistration registration = studyRegistrationService.selectStudyRegistrationByRegistrationId(registrationId);
        if (registration == null) {
            return AjaxResult.error("报名记录不存在");
        }
        
        if (!"1".equals(registration.getStatus())) {
            return AjaxResult.error("只有已通过审核的报名才能开始学习");
        }
        
        registration.setStatus("3"); // 学习中
        registration.setUpdateBy(getUsername());
        registration.setUpdateTime(new java.util.Date());
        
        return toAjax(studyRegistrationService.updateStudyRegistration(registration));
    }

    /**
     * 结业 - 将学习中的状态改为已结业
     */
    @PreAuthorize("@ss.hasPermi('study:registration:edit')")
    @Log(title = "学员结业", businessType = BusinessType.UPDATE)
    @PutMapping("/graduate/{registrationId}")
    public AjaxResult graduate(@PathVariable("registrationId") Long registrationId)
    {
        StudyRegistration registration = studyRegistrationService.selectStudyRegistrationByRegistrationId(registrationId);
        if (registration == null) {
            return AjaxResult.error("报名记录不存在");
        }
        
        if (!"3".equals(registration.getStatus())) {
            return AjaxResult.error("只有学习中的学员才能结业");
        }
        
        registration.setStatus("4"); // 已结业
        registration.setUpdateBy(getUsername());
        registration.setUpdateTime(new java.util.Date());
        
        return toAjax(studyRegistrationService.updateStudyRegistration(registration));
    }
} 