<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.study.mapper.StudyAttendanceMapper">
    
    <resultMap type="com.base.study.domain.StudyAttendance" id="StudyAttendanceResult">
        <result property="attendanceId"    column="attendance_id"    />
        <result property="registrationId"    column="registration_id"    />
        <result property="userId"    column="user_id"    />
        <result property="classId"    column="class_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="checkInTime"    column="check_in_time"    />
        <result property="checkOutTime"    column="check_out_time"    />
        <result property="checkInSource"    column="check_in_source"    />
        <result property="calculatedHours"    column="calculated_hours"    />
        <result property="calculatedCredits"    column="calculated_credits"    />
        <result property="attendanceStatus"    column="attendance_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />

        <!--接收不到数据，修改为显示-->
        <association property="sysUser"     javaType="com.base.common.core.domain.entity.SysUser"  >
            <id     property="userId"       column="user_id"      />
            <result property="userName"     column="user_name"    />
            <result property="nickName"     column="nick_name"    />
            <result property="phonenumber"  column="phonenumber"  />
        </association>
        <association property="studyClass"  javaType="com.base.study.domain.StudyClass"  >
            <id     property="classId"      column="class_id"      />
            <result property="className"    column="class_name"    />
            <result property="classCode"     column="class_code"     />
        </association>
        <association property="studyCourse"  javaType="com.base.study.domain.StudyCourse"  >
            <id     property="courseId"      column="course_id"      />
            <!--<id     property="classId"      column="class_id"      />-->
            <result property="courseName"    column="course_name"    />
        </association>
    </resultMap>

    <resultMap id="userResult" type="com.base.common.core.domain.entity.SysUser">
        <id     property="userId"       column="user_id"      />
        <result property="userName"     column="user_name"    />
        <result property="nickName"     column="nick_name"    />
        <result property="phonenumber"  column="phonenumber"  />
    </resultMap>

    <resultMap id="classResult" type="com.base.study.domain.StudyClass">
        <id     property="classId"      column="class_id"      />
        <result property="className"    column="class_name"    />
        <result property="classCode"     column="class_code"     />
    </resultMap>

    <resultMap id="courseResult" type="com.base.study.domain.StudyCourse">
        <id     property="courseId"      column="course_id"      />
        <id     property="classId"      column="class_id"      />
        <result property="courseName"    column="course_name"    />
    </resultMap>

    <sql id="selectStudyAttendanceVo">
        select a.attendance_id, a.registration_id, a.user_id, a.class_id, a.course_id, a.check_in_time, a.check_out_time, a.check_in_source, 
        a.calculated_hours, a.calculated_credits, a.attendance_status,
        a.create_by, a.create_time, a.update_by, a.update_time, a.remark,
        u.user_name , c.class_name  , co.course_name ,u.nick_name
        from study_attendance a
        left join sys_user u on a.user_id = u.user_id
        left join study_class c on a.class_id = c.class_id
        left join study_course co on a.course_id = co.course_id
    </sql>

    <select id="selectStudyAttendanceList" parameterType="com.base.study.domain.StudyAttendance" resultMap="StudyAttendanceResult">
        <include refid="selectStudyAttendanceVo"/>
        <where>
            1=1
            <if test="classId != null "> and a.class_id = #{classId}</if>
            <if test="userId != null "> and a.user_id = #{userId}</if>
            <if test="attendanceStatus != null  and attendanceStatus != ''"> and a.attendance_status = #{attendanceStatus}</if>
            <if test="params != null and params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(a.check_in_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params != null and params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(a.check_in_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="params != null and params.phonenumber != null and params.phonenumber != ''"><!-- 结束时间检索 -->
                and u.phonenumber = #{params.phonenumber}
            </if>
            <if test="sysUser != null and sysUser.phonenumber != null  and sysUser.phonenumber != ''">
                and u.phonenumber like concat('%', #{sysUser.phonenumber}, '%')
            </if>
            <if test="params != null and params.nickName != null  and params.nickName != ''">
                and u.nick_name like concat('%', #{params.nickName}, '%')
            </if>
            <if test="params != null and params.className != null  and params.className != ''">
                and c.class_name like concat('%', #{params.className}, '%')
            </if>
            <if test="courseId != null "> and a.course_id = #{courseId}</if>
        </where>
        ORDER BY c.create_time DESC
    </select>
    
    <select id="selectStudyAttendanceByAttendanceId" parameterType="Long" resultMap="StudyAttendanceResult">
        <include refid="selectStudyAttendanceVo"/>
        where a.attendance_id = #{attendanceId}
    </select>

    <select id="selectStudyAttendanceById" parameterType="Long" resultMap="StudyAttendanceResult">
        <include refid="selectStudyAttendanceVo"/>
        where a.attendance_id = #{attendanceId}
    </select>
        
    <insert id="insertStudyAttendance" parameterType="com.base.study.domain.StudyAttendance" useGeneratedKeys="true" keyProperty="attendanceId">
        insert into study_attendance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="registrationId != null">registration_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="classId != null">class_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="checkInTime != null">check_in_time,</if>
            <if test="checkOutTime != null">check_out_time,</if>
            <if test="checkInSource != null">check_in_source,</if>
            <if test="calculatedHours != null">calculated_hours,</if>
            <if test="calculatedCredits != null">calculated_credits,</if>
            <if test="attendanceStatus != null">attendance_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>

         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="registrationId != null">#{registrationId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="classId != null">#{classId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="checkInTime != null">#{checkInTime},</if>
            <if test="checkOutTime != null">#{checkOutTime},</if>
            <if test="checkInSource != null">#{checkInSource},</if>
            <if test="calculatedHours != null">#{calculatedHours},</if>
            <if test="calculatedCredits != null">#{calculatedCredits},</if>
            <if test="attendanceStatus != null">#{attendanceStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>

         </trim>
    </insert>

    <update id="updateStudyAttendance" parameterType="com.base.study.domain.StudyAttendance">
        update study_attendance
        <trim prefix="SET" suffixOverrides=",">
            <if test="registrationId != null">registration_id = #{registrationId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="classId != null">class_id = #{classId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="checkInTime != null">check_in_time = #{checkInTime},</if>
            <if test="checkOutTime != null">check_out_time = #{checkOutTime},</if>
            <if test="checkInSource != null">check_in_source = #{checkInSource},</if>
            <if test="calculatedHours != null">calculated_hours = #{calculatedHours},</if>
            <if test="calculatedCredits != null">calculated_credits = #{calculatedCredits},</if>
            <if test="attendanceStatus != null">attendance_status = #{attendanceStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>

        </trim>
        where attendance_id = #{attendanceId}
    </update>

    <delete id="deleteStudyAttendanceByAttendanceId" parameterType="Long">
        delete from study_attendance where attendance_id = #{attendanceId}
    </delete>

    <delete id="deleteStudyAttendanceByAttendanceIds" parameterType="String">
        delete from study_attendance where attendance_id in
        <foreach item="attendanceId" collection="array" open="(" separator="," close=")">
            #{attendanceId}
        </foreach>
    </delete>
    
    <select id="selectAttendanceByUserAndCourse" resultMap="StudyAttendanceResult">
        <include refid="selectStudyAttendanceVo"/>
        where a.user_id = #{userId} and a.course_id = #{courseId}
        limit 1
    </select>

    <select id="sumHoursByRegistrationId" resultType="java.math.BigDecimal">
        select sum(calculated_hours) from study_attendance where registration_id = #{registrationId}
    </select>

    <select id="sumCreditsByRegistrationId" resultType="java.math.BigDecimal">
        select sum(calculated_credits) from study_attendance where registration_id = #{registrationId}
    </select>

    <select id="countNumByClassId" parameterType="String" resultType="Int">
        select count(1) from study_attendance where class_id = #{classid}
    </select>

    <select id="countNumByClassIds" parameterType="Long" resultType="Int">
        select count(1) from study_attendance where class_id in
        <foreach item="classid" collection="classIds" open="(" separator="," close=")">
            #{classid}
        </foreach>
    </select>

</mapper> 