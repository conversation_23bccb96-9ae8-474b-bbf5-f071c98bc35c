package com.chongho.erp.codegenerator.oms;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

/**
 * 类SggCodeGenerator.java的实现描述：
 *
 * <AUTHOR> 23:46
 */
public class SggCodeGenerator {
    private static final String parentPackage = "com.chongho.erp.oms";
    private static final String jdbcUrl =
        "***************************************************************************************************************************************************************************************************************************************";
    private static final String username = "cd_mall";
    private static final String password = "Cd_Mall1";
    private static final String author = "Andy（陈洪桥）";

    protected static List<String> getTables(String tables) {
        return "all".equals(tables) ? Collections.emptyList() : Arrays.asList(tables.split(","));
    }

    public static void main(String[] args) {

        DataSourceConfig.Builder DATA_SOURCE_CONFIG = new DataSourceConfig.Builder(jdbcUrl, username, password);

        FastAutoGenerator.create(DATA_SOURCE_CONFIG)
            // 全局配置
            .globalConfig(builder -> builder.outputDir(System.getProperty("user.dir") +"/src/main/java")
                .author(author)./*disableOpenDir().*/enableSwagger().dateType(DateType.TIME_PACK)
                .commentDate("yyyy-MM-dd HH:mm:ss").fileOverride())
            // 包配置
            .packageConfig(builder -> builder.parent(parentPackage).entity("domain.po").service("repository")
                .serviceImpl("repository.impl").xml("")
                .pathInfo(Collections.singletonMap(OutputFile.mapperXml,
                    System.getProperty("user.dir") + "/src/main/resources/mapper/oms")))
            // 策略配置
            .strategyConfig((scanner, builder) -> builder.addInclude(getTables(scanner.apply("请输入表名，多个英文逗号分隔")))
                .addTablePrefix("oms_")// 可支持多个，按逗号分隔
                .entityBuilder().formatFileName("%sPO").enableLombok().mapperBuilder().formatMapperFileName("%sMapper")
                .formatXmlFileName("%sMapper").controllerBuilder().formatFileName("%sController").enableRestStyle()
                .serviceBuilder().formatServiceFileName("%sRepository").formatServiceImplFileName("%sRepositoryImpl"))
            .templateEngine(new FreemarkerTemplateEngine()).execute();
        System.out.println("代码生成完成！！！");
    }
}
