<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chongho.erp.mapper.ErpProductBoxMapper">

    <select id="queryPageList" resultType="com.chongho.erp.vo.productLibrary.ErpProductBoxVO">
        select
            *
        from
            erp_product_box
        where
            if_delete = 0
        <if test="query.productBoxCode != null and query.productBoxCode != ''">
            and product_box_code = #{query.productBoxCode}
        </if>
        <if test="query.productBoxName != null and query.productBoxName != ''">
            and product_box_name like CONCAT('%',#{query.productBoxName},'%')
        </if>
        <if test="query.createTimeStart != null">
            and create_time >= #{query.createTimeStart}
        </if>
        <if test="query.createTimeEnd != null">
            <![CDATA[ and create_time <= #{query.createTimeEnd} ]]>
        </if>
        <if test="query.createUserName != null and query.createUserName != ''">
            and create_user_name like CONCAT('%',#{query.createUserName},'%')
        </if>
        order by create_time desc
    </select>
    <select id="queryByInquiryCode"
            resultType="com.chongho.erp.vo.productLibrary.ErpInquiryProductSettingsInquiryVO">
        select
            ei.inquiry_code,
            bh.product_box_code,
            bh.product_box_name,
            bh.product_box_spec_name as productBoxSkuName,
            bh.crop_name,
            bh.suit_material_code ,
            eps.sku_id
        from
            erp_product_box bh
        left join erp_inquiry_scheme_item ei on
            bh.product_box_code = ei.product_box_code
            and ei.if_delete = 0
        left join ecp_product_sku eps on
            eps.sku_material_code = bh.suit_material_code
            and eps.if_delete = 0
        where
            bh.if_delete = 0
            and ei.inquiry_code = #{inquiryCode}
    </select>
    <select id="queryByProductBoxCodeList" resultType="com.chongho.erp.po.ErpProductBoxPO">
        select
            *
        from
            erp_product_box
        where
            if_delete = 0
            and product_box_code in
        <foreach collection="productBoxCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
