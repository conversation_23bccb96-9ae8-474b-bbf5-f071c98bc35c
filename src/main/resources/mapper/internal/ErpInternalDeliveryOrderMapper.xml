<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chongho.erp.mapper.internal.ErpInternalDeliveryOrderMapper">

    <select id="getInternalDeliveryOrderInfoPage" resultType="com.chongho.erp.vo.internalDeliverOrder.ErpInternalDeliverOrderInfoVO">
        select do.delivery_order_code,do.trade_company_code,do.order_type,do.order_time,do.link_no,do.dno,do.stock_record_sn,do.warehouse_type,
        do.direction,do.approval_status,do.generate_method,do.create_time,do.create_user_name,do.remark,dod.returnable_qty_snapshot,
        dod.stock_qty_snapshot,dod.sales_qty_snapshot,dod.delivery_qty_snapshot,dod.notes,
        do.related_delivery_order_code,dod.sku_id,dod.quantity,dod.depot_id,do.tenant_id
        from erp_internal_delivery_order do
        join erp_internal_delivery_order_detail dod on do.delivery_order_code=dod.delivery_order_code and dod.if_delete=0
        where do.if_delete=0
        <if test="query.skuIdList != null and query.skuIdList.size() > 0">
            and dod.sku_id in
            <foreach collection="query.skuIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.tradeCompanyCode != null and query.tradeCompanyCode!='' ">
            and do.trade_company_code=#{query.tradeCompanyCode}
        </if>
        <if test="query.deliveryOrderCode != null and query.deliveryOrderCode!='' ">
            and do.delivery_order_code=#{query.deliveryOrderCode}
        </if>
        <if test="query.linkNo != null and query.linkNo!='' ">
            and do.link_no=#{query.linkNo}
        </if>
        <if test="query.dno != null and query.dno!='' ">
            and do.dno=#{query.dno}
        </if>
        <if test="query.relatedDeliveryOrderCode != null and query.relatedDeliveryOrderCode!='' ">
            and do.related_delivery_order_code=#{query.relatedDeliveryOrderCode}
        </if>
        <if test="query.relatedDeliveryOrderCodeList != null and query.relatedDeliveryOrderCodeList.size() > 0">
            and do.related_delivery_order_code in
            <foreach collection="query.relatedDeliveryOrderCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.directionList != null and query.directionList.size() > 0">
            and do.direction in
            <foreach collection="query.directionList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.warehouseTypeList != null and query.warehouseTypeList.size() > 0">
            and do.warehouse_type in
            <foreach collection="query.warehouseTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.approvalStatusList != null and query.approvalStatusList.size() > 0">
            and do.approval_status in
            <foreach collection="query.approvalStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.orderType != null ">
            and do.order_type=#{query.orderType}
        </if>
        <if test="query.depotIdList != null and query.depotIdList.size() > 0">
            and dod.depot_id in
            <foreach collection="query.depotIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.generateMethod != null ">
            and do.generate_method=#{query.generateMethod}
        </if>
        <if test="query.createTimeStart != null">
            and do.create_time &gt;= DATE_FORMAT(#{query.createTimeStart},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="query.createTimeEnd != null">
            and do.create_time &lt;= DATE_FORMAT(#{query.createTimeEnd},'%Y-%m-%d %H:%i:%S')
        </if>
    </select>

    <select id="pageInternalDeliveryOrderInfo" resultType="com.chongho.erp.vo.internalDeliverOrder.ErpInternalDeliverOrderInfoVO">
        select do.delivery_order_code,do.trade_company_code,do.order_type,do.order_time,do.link_no,do.dno,do.stock_record_sn,do.warehouse_type,
        do.direction,do.approval_status,do.generate_method,do.create_time,do.create_user_name,do.remark,dod.returnable_qty_snapshot,
        dod.stock_qty_snapshot,dod.sales_qty_snapshot,dod.delivery_qty_snapshot,dod.notes,
        do.related_delivery_order_code,dod.sku_id,dod.depot_id,do.tenant_id,
        sum(if(do.direction=2,dod.quantity,-dod.quantity)) as returnableQty
        from erp_internal_delivery_order do
        join erp_internal_delivery_order_detail dod on do.delivery_order_code=dod.delivery_order_code and dod.if_delete=0
        where do.if_delete=0
        <if test="query.skuIdList != null and query.skuIdList.size() > 0">
            and dod.sku_id in
            <foreach collection="query.skuIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.tradeCompanyCode != null and query.tradeCompanyCode!='' ">
            and do.trade_company_code=#{query.tradeCompanyCode}
        </if>
        <if test="query.deliveryOrderCode != null and query.deliveryOrderCode!='' ">
            and do.delivery_order_code=#{query.deliveryOrderCode}
        </if>
        <if test="query.linkNo != null and query.linkNo!='' ">
            and do.link_no=#{query.linkNo}
        </if>
        <if test="query.dno != null and query.dno!='' ">
            and do.dno=#{query.dno}
        </if>
        <if test="query.relatedDeliveryOrderCode != null and query.relatedDeliveryOrderCode!='' ">
            and do.related_delivery_order_code=#{query.relatedDeliveryOrderCode}
        </if>
        <if test="query.relatedDeliveryOrderCodeList != null and query.relatedDeliveryOrderCodeList.size() > 0">
            and do.related_delivery_order_code in
            <foreach collection="query.relatedDeliveryOrderCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.directionList != null and query.directionList.size() > 0">
            and do.direction in
            <foreach collection="query.directionList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.warehouseTypeList != null and query.warehouseTypeList.size() > 0">
            and do.warehouse_type in
            <foreach collection="query.warehouseTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.approvalStatusList != null and query.approvalStatusList.size() > 0">
            and do.approval_status in
            <foreach collection="query.approvalStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.orderType != null ">
            and do.order_type=#{query.orderType}
        </if>
        <if test="query.depotIdList != null and query.depotIdList.size() > 0">
            and dod.depot_id in
            <foreach collection="query.depotIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.generateMethod != null ">
            and do.generate_method=#{query.generateMethod}
        </if>
        <if test="query.createTimeStart != null">
            and do.create_time &gt;= DATE_FORMAT(#{query.createTimeStart},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="query.createTimeEnd != null">
            and do.create_time &lt;= DATE_FORMAT(#{query.createTimeEnd},'%Y-%m-%d %H:%i:%S')
        </if>
        group by do.delivery_order_code,do.trade_company_code,dod.sku_id,dod.depot_id,do.tenant_id
        having returnableQty>0
    </select>
</mapper>
