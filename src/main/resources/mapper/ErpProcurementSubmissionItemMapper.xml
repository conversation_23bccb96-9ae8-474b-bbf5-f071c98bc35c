<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chongho.erp.mapper.ErpProcurementSubmissionItemMapper">

    <select id="getProSubItemList" resultType="com.chongho.erp.po.ErpProcurementSubmissionItemPO">
        select epsi.id,epsi.procurement_submission_code,epsi.goods_code,epsi.material_code,epsi.sku_code,epsi.procurement_nums,
               epsi.procurement_unit_tax_price,epsi.procurement_total_price,epsi.tax_amount,epsi.total_tax_price,
               epsi.business_price,epsi.customer_price,epsi.inventory_code,epsi.tenant_id,epsi.tag_code
        from erp_procurement_submission_item epsi
        join  erp_procurement_submission_head epsh on epsh.procurement_submission_code=epsi.procurement_submission_code
        and epsh.if_delete=0
        where epsi.if_delete=0 and epsh.procurement_submission_statu in(2,3,4,5,6,7)
        <if test="skuIdList!=null and skuIdList.size()>0">
            and epsi.sku_code in
            <foreach collection="skuIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="batchNoList!=null and batchNoList.size()>0">
            and epsi.procurement_submission_code in
            <foreach collection="batchNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getProSubHeandAndItemInfoList" resultType="com.chongho.erp.dto.procurement.ErpProSubHeadAndItemDTO">
        select epsi.id,epsi.procurement_submission_code,epsi.sku_code as skuId,epsi.business_price,epsi.customer_price,epsi.package_price,
        epsi.express_fee,
        epsi.procurement_unit_tax_price,epsi.tenant_id,epsh.supplier_code,s.supplier_name,s.supplier_short_name,epsh.batch_name,epsh.create_time
        from erp_procurement_submission_item epsi
        join  erp_procurement_submission_head epsh on epsh.procurement_submission_code=epsi.procurement_submission_code
        and epsh.if_delete=0
        left join supplier s on s.supplier_code=epsh.supplier_code and s.if_delete=0
        where epsi.if_delete=0
        <if test="skuIdList!=null and skuIdList.size()>0">
            and epsi.sku_code in
            <foreach collection="skuIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="batchNoList!=null and batchNoList.size()>0">
            and epsi.procurement_submission_code in
            <foreach collection="batchNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <update id="delProSubItem">
        update erp_procurement_submission_item set
        if_delete=null
        where procurement_submission_code in
        <foreach collection="batchNoList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getProSubItemInfoList" resultType="com.chongho.erp.vo.ErpProSubItemInfoVo">
        select ersi.procurement_submission_code, ersi.sku_code,ersi.tenant_id,
        sum(ifnull(ersi.procurement_nums, 0)) as procurementNums
        from erp_procurement_submission_item ersi
        join erp_procurement_submission_head epsh on epsh.procurement_submission_code=ersi.procurement_submission_code
        and epsh.if_delete=0
        where ersi.if_delete=0 and epsh.inventory_status=2
        <if test="skuIdList!=null and skuIdList.size()>0">
            and ersi.sku_code in
            <foreach collection="skuIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="batchNoList!=null and batchNoList.size()>0">
            and ersi.procurement_submission_code in
            <foreach collection="batchNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by ersi.procurement_submission_code, ersi.sku_code
    </select>
    <select id="queryByProcurementSubmissionItem"
            resultType="com.chongho.erp.vo.ProcurementSubmissionItemStockVO">
        select
	        epsh.procurement_submission_code,
	        epsi.sku_code,
	        epsh.supplier_code,
	        epsh.procurement_submission_statu,
	        epsi.procurement_nums,
	        epsi.procurement_total_price,
	        epsh.comments,
	        epsh.procurement_time,
	        epsh.create_user_name,
            epsi.procurement_unit_tax_price,
            epsi.total_tax_price
        from
	        erp_procurement_submission_item epsi
        left join erp_procurement_submission_head epsh
        on  epsi.procurement_submission_code = epsh.procurement_submission_code
	    and epsh.if_delete = 0
        where
	        epsi.if_delete = 0
	        and epsh.supplier_code = #{query.supplierCode}
	        and epsi.sku_code = #{query.skuNo}
            and epsh.procurement_submission_statu!=7
            and epsh.inventory_status in (1,2)
        <if test="query.procurementSubmissionCode!=null and query.procurementSubmissionCode!=''">
            and epsi.procurement_submission_code=#{query.procurementSubmissionCode}
        </if>
        <if test="query.createTime != null">
            and epsh.procurement_time &gt;= DATE_FORMAT(concat(#{query.createTime},' ','00:00:00'),'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="query.endTime != null">
            and epsh.procurement_time &lt;= DATE_FORMAT(concat(#{query.endTime},' ','23:59:59'),'%Y-%m-%d %H:%i:%S')
        </if>
    </select>
    <select id="querySkuIdsAndInventoryStatus" resultType="com.chongho.erp.vo.ErpProcurementSubmissionItemVo">
        select
            epsh.procurement_submission_code as batchNo,
            epsi.sku_code as skuId,
            epsi.customer_price,
            epsi.business_price
        from
            erp_procurement_submission_item epsi
            left join erp_procurement_submission_head epsh on epsi.procurement_submission_code = epsh.procurement_submission_code
            and epsh.if_delete=0
        where epsi.if_delete = 0 and
            (epsi.sku_code, epsh.id) in (
                select
                    epsi.sku_code,
                    max(epsh.id)
                from
                    erp_procurement_submission_item epsi
                left join erp_procurement_submission_head epsh on epsi.procurement_submission_code = epsh.procurement_submission_code
                    and epsh.if_delete = 0
                where
                     epsi.if_delete = 0
                    and epsh.procurement_submission_statu != 7
                    and epsh.inventory_status = #{inventoryStatus}
                    <if test="skuIds!=null and skuIds.size()>0">
                        and epsi.sku_code in
                        <foreach collection="skuIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                         </foreach>
                    </if>
        <if test="tenantId!=null">
            and epsi.tenant_id = #{tenantId}
        </if>
                group by
                    epsi.sku_code)
    </select>
    <select id="procurementSubmissionItemProductQuery"
            resultType="com.chongho.erp.vo.ProcurementSubmissionItemProductVO">
        select
            epsh.procurement_submission_code,
            epsh.procurement_submission_statu,
            epsi.sku_code as skuId,
            epsh.supplier_code,
            epsi.procurement_nums,
            epsi.procurement_total_price,
            epsh.comments,
            epsh.procurement_time,
            epsh.create_user_name,
            epsi.procurement_unit_tax_price,
            epsb.batch_ice,
            epsb.procurement_return_nums as currentReturnNums
        from
            erp_procurement_submission_item epsi
        join erp_procurement_submission_head epsh  on
            epsi.procurement_submission_code = epsh.procurement_submission_code
            and epsh.if_delete = 0
        join erp_procurement_submission_balance epsb on
            epsi.procurement_submission_code = epsb.batch_number
            and epsi.sku_code = epsb.sku_id
            and epsb.if_delete = 0
        where
            epsi.if_delete = 0
            and epsb.batch_ice >0
        <if test="query.skuIds !=null and query.skuIds.size()>0">
            and epsi.sku_code in
            <foreach collection="query.skuIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.procurementSubmissionCode!=null and query.procurementSubmissionCode!=''">
            and epsi.procurement_submission_code=#{query.procurementSubmissionCode}
        </if>
        <if test="query.createTime != null">
            and epsh.procurement_time &gt;= DATE_FORMAT(concat(#{query.createTime},' ','00:00:00'),'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="query.endTime != null">
            and epsh.procurement_time &lt;= DATE_FORMAT(concat(#{query.endTime},' ','23:59:59'),'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="query.supplierCode != null and query.supplierCode != ''">
            and epsh.supplier_code = #{query.supplierCode}
        </if>
        <if test="query.validStartTime != null">
            and epsh.create_time &gt;= #{query.validStartTime}
        </if>

        order by epsh.procurement_time desc
    </select>
    <select id="pageProSubItem" resultType="com.chongho.erp.po.ErpProcurementSubmissionItemPO">
        select epsi.id,epsi.procurement_submission_code,epsi.goods_code,epsi.material_code,epsi.sku_code,epsi.procurement_nums,
        epsi.procurement_unit_tax_price,epsi.procurement_total_price,epsi.tax_amount,epsi.total_tax_price,epsh.inventory_status,
        epsi.business_price,epsi.customer_price,epsi.inventory_code,epsi.tenant_id,epsi.package_price,epsi.express_fee
        from erp_procurement_submission_item epsi
        join  erp_procurement_submission_head epsh on epsh.procurement_submission_code=epsi.procurement_submission_code
        and epsh.if_delete=0
        where epsi.if_delete=0
        <if test="query.tenantId!=null ">
            and epsh.tenant_id =#{query.tenantId}
        </if>
        <if test="query.inventoryStatus!=null ">
            and epsh.inventory_status =#{query.inventoryStatus}
        </if>
        <if test="query.procurementSubmissionStatusList!=null and query.procurementSubmissionStatusList.size()>0">
            and epsh.procurement_submission_statu in
            <foreach collection="query.procurementSubmissionStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.skuIdList!=null and query.skuIdList.size()>0">
            and epsi.sku_code in
            <foreach collection="query.skuIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.batchNoList!=null and query.batchNoList.size()>0">
            and epsi.procurement_submission_code in
            <foreach collection="query.batchNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.skuIds!=null and query.skuIds.size()>0">
            and epsi.sku_code in
            <foreach collection="query.skuIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.batchNos!=null and query.batchNos.size()>0">
            and epsi.procurement_submission_code in
            <foreach collection="query.batchNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getHistoryTag"
            resultType="com.chongho.erp.po.ErpProcurementSubmissionItemPO">
        select
        tag_code,
        procurement_submission_code,
        tenant_id,
        sku_code
        from
        erp_procurement_submission_item
        where
        tag_code is not null
    </select>

    <sql id="headQueryCondition">
        <!--########以下是采购提报主单查询条件########-->
        hed.if_delete = 0
        <if test="query.tenantId != null">
            and hed.tenant_id = #{query.tenantId}
        </if>
        <if test="query.procurementSubmissionCodeList != null and query.procurementSubmissionCodeList.size() > 0 ">
            and hed.procurement_submission_code in
            <foreach collection="query.procurementSubmissionCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.batchName !=null and query.batchName!=''">
            <bind name="bindName" value="'%'+query.batchName+'%'"/>
            and hed.batch_name like #{bindName}
        </if>
        <if test="query.supplierCode != null and query.supplierCode != ''">
            and hed.supplier_code = #{query.supplierCode}
        </if>
        <if test="query.procurementSubmissionStatu != null">
            and hed.procurement_submission_statu = #{query.procurementSubmissionStatu}
        </if>
        <if test="query.inventoryStatus != null">
            and hed.inventory_status = #{query.inventoryStatus}
        </if>
        <if test="query.productionScheduling != null">
            and hed.production_scheduling = #{query.productionScheduling}
        </if>
    </sql>

    <sql id="itemQueryCondition">
        <!--########以下是采购提报子单明细查询条件########-->
        itm.if_delete = 0
        <if test="query.procurementSubmissionCodeList != null and query.procurementSubmissionCodeList.size() > 0 ">
            and itm.procurement_submission_code in
            <foreach collection="query.procurementSubmissionCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.goodsCode != null and query.goodsCode != ''">
            and itm.goods_code = #{query.goodsCode}
        </if>
        <if test="query.materialCode != null and query.materialCode != ''">
            and itm.material_code = #{query.materialCode}
        </if>
        <if test="query.skuCodeList != null and query.skuCodeList.size() > 0 ">
            and itm.sku_code in
            <foreach collection="query.skuCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.tagCode != null and query.tagCode != ''">
            and itm.tag_code = #{query.tagCode}
        </if>
    </sql>

    <select id="procurementSubmissionSkuGroupList" resultType="com.chongho.erp.po.ErpProcurementSubmissionItemPO">
        select itm.*
        from erp_procurement_submission_head hed
        join erp_procurement_submission_item itm on hed.procurement_submission_code = itm.procurement_submission_code
        <where>
            <include refid="headQueryCondition" />
            and
            <include refid="itemQueryCondition" />
        </where>
        order by itm.create_time desc
    </select>
    <select id="getProSubItemListNoTenantId" resultType="com.chongho.erp.po.ErpProcurementSubmissionItemPO">
        select *
        from
        erp_procurement_submission_item where if_delete = 0 and procurement_submission_code = #{procurementSubmissionCode}
    </select>

</mapper>
