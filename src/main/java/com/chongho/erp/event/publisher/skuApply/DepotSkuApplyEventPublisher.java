package com.chongho.erp.event.publisher.skuApply;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import com.cfpamf.athena.utils.Tools;
import com.chongho.erp.common.enums.depotSku.DepotSkuApplyEventEnum;
import com.chongho.erp.dto.depotSku.DepotSkuApplyEventDTO;
import com.chongho.erp.dto.depotSku.builder.DepotSkuApplyEventBuilder;
import com.chongho.erp.event.event.DepotSkuApplyEvent;
import com.chongho.erp.event.event.EventListenerCounter;
import com.chongho.erp.po.depotSku.ErpDepotSkuApplyPo;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class DepotSkuApplyEventPublisher {

	@Autowired
	private ApplicationEventPublisher eventPublisher;

	@Autowired
	private EventListenerCounter listenerCounter;

	public void transactionalEventPublisher(DepotSkuApplyEventEnum depotSkuApplyEvent, DepotSkuApplyEventDTO bodyMsg,
			ErpDepotSkuApplyPo apply) {
		DepotSkuApplyEventDTO msg = DepotSkuApplyEventBuilder.buildDepotSkuApplyEventDTO(depotSkuApplyEvent, bodyMsg,
				apply);
		if (Tools.isEmpty(msg)) {
			log.warn("[DepotSkuApplyEventPublisher] applyCode:{} {} {} body is null 拒绝发送发送货品申领事件消息", msg.getApplyCode(),
					msg.getOperationEventDesc());
			return;
		}
		// 获取 DepotSkuApplyEvent 事件的监听器数量
		int count = listenerCounter.getListenerCount(DepotSkuApplyEvent.class);
		// 创建可等待事件
		DepotSkuApplyEvent event = new DepotSkuApplyEvent(this, count, msg);
		eventPublisher.publishEvent(event);
		try {
			log.info("[DepotSkuApplyEventPublisher] await applyCode:{} [货品申领事件等待监听器执行完]", msg.getApplyCode(),
					msg.getOperationEventDesc());
			event.await();
		} catch (InterruptedException e) {
			Thread.currentThread().interrupt();
			throw new RuntimeException("等待货品申领事件处理被中断", e);
		}
		log.info("[DepotSkuApplyEventPublisher] done applyCode:{} {}  货品申领事件消息执行完成", msg.getApplyCode(),
				msg.getOperationEventDesc());

	}

}
