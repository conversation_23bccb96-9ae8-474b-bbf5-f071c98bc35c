package com.chongho.erp.event.event;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.EventListener;
import org.springframework.context.support.AbstractApplicationContext;
import org.springframework.core.GenericTypeResolver;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class EventListenerCounter implements InitializingBean {

    private final ApplicationContext applicationContext;
    private final Map<Class<?>, Integer> eventListenerCountMap = new ConcurrentHashMap<>();

    public EventListenerCounter(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public void afterPropertiesSet() {
        initializeListenerCountMap();
    }

    private void initializeListenerCountMap() {
        if (applicationContext instanceof AbstractApplicationContext) {
            AbstractApplicationContext context = (AbstractApplicationContext) applicationContext;
            
            // 1. 统计实现 ApplicationListener 接口的监听器
            String[] listenerBeanNames = context.getBeanNamesForType(ApplicationListener.class);
            for (String beanName : listenerBeanNames) {
                Class<?> listenerClass = context.getType(beanName);
                if (listenerClass != null) {
                    Class<?> eventType = resolveEventTypeFromInterface(listenerClass);
                    if (eventType != null) {
                        eventListenerCountMap.compute(eventType, (k, v) -> v == null ? 1 : v + 1);
                    }
                }
            }
            
            // 2. 统计使用 @EventListener 或 @TransactionalEventListener 注解的方法
            String[] beanNames = context.getBeanDefinitionNames();
            
            for (String beanName : beanNames) {
                Class<?> beanType = context.getType(beanName);
                if (beanType != null) {
                    for (Method method : beanType.getMethods()) {
                        // 检查是否有 @EventListener 或 @TransactionalEventListener 注解
                        if (AnnotatedElementUtils.hasAnnotation(method, EventListener.class) ||
                            AnnotatedElementUtils.hasAnnotation(method, TransactionalEventListener.class)) {
                            
                            // 获取方法参数中的事件类型
                            Class<?> eventType = resolveEventTypeFromMethod(method);
                            if (eventType != null) {
                                eventListenerCountMap.compute(eventType, (k, v) -> v == null ? 1 : v + 1);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 使用 GenericTypeResolver 从 ApplicationListener 接口实现中解析事件类型
     */
    private Class<?> resolveEventTypeFromInterface(Class<?> listenerClass) {
        // 处理直接实现 ApplicationListener 接口的类
        Class<?> eventType = resolveEventTypeFromDirectInterface(listenerClass);
        if (eventType != null) {
            return eventType;
        }
        
        // 处理继承自实现了 ApplicationListener 接口的抽象类的情况
        return resolveEventTypeFromSuperclass(listenerClass);
    }

    private Class<?> resolveEventTypeFromDirectInterface(Class<?> listenerClass) {
        // 获取直接实现的接口
        Class<?>[] interfaces = listenerClass.getInterfaces();
        for (Class<?> ifc : interfaces) {
            if (isApplicationListenerInterface(ifc)) {
                // 解析泛型参数类型
                return resolveEventTypeFromGenericInterface(listenerClass, ifc);
            }
        }
        return null;
    }

    private Class<?> resolveEventTypeFromSuperclass(Class<?> listenerClass) {
        Class<?> superclass = listenerClass.getSuperclass();
        while (superclass != null && !superclass.equals(Object.class)) {
            // 检查父类是否实现了 ApplicationListener 接口
            Class<?>[] interfaces = superclass.getInterfaces();
            for (Class<?> ifc : interfaces) {
                if (isApplicationListenerInterface(ifc)) {
                    return resolveEventTypeFromGenericInterface(superclass, ifc);
                }
            }
            
            // 继续向上检查父类
            superclass = superclass.getSuperclass();
        }
        return null;
    }

    private boolean isApplicationListenerInterface(Class<?> ifc) {
        return ApplicationListener.class.isAssignableFrom(ifc);
    }

    private Class<?> resolveEventTypeFromGenericInterface(Class<?> listenerClass, Class<?> interfaceClass) {
        // 使用 GenericTypeResolver 解析泛型参数
        Class<?>[] genericTypes = GenericTypeResolver.resolveTypeArguments(listenerClass, interfaceClass);
        if (genericTypes != null && genericTypes.length > 0) {
            // 获取第一个泛型参数，即事件类型
            Class<?> eventType = genericTypes[0];
            if (ApplicationEvent.class.isAssignableFrom(eventType)) {
                return eventType;
            }
        }
        return null;
    }

    /**
     * 从 @EventListener 方法中解析事件类型
     */
    private Class<?> resolveEventTypeFromMethod(Method method) {
        Class<?>[] parameterTypes = method.getParameterTypes();
        if (parameterTypes.length > 0) {
            // 获取第一个参数类型
            Class<?> firstParamType = parameterTypes[0];
            if (ApplicationEvent.class.isAssignableFrom(firstParamType)) {
                return firstParamType;
            }
        }
        return null;
    }

    /**
     * 获取特定事件类型的监听器数量
     */
    public int getListenerCount(Class<?> eventType) {
        return eventListenerCountMap.getOrDefault(eventType, 0);
    }
}