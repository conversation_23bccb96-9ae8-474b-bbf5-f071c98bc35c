package com.chongho.erp.vo;

import com.chongho.erp.vo.material.ProductSkuInfoVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08 09:33:32
 */
@Data
public class ErpSubPurchasePricesSkuInfoVo extends ProductSkuInfoVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("原采购明细与价格单关联ID")
    private Long priceBillLinkId;

    @ApiModelProperty("采购提报单编码")
    private String procurementSubmissionCode;

    @ApiModelProperty("关联价格单子单号")
    private String purchaseSubCode;

    @ApiModelProperty("关联主价格单编码")
    private String purchaseCode;

    @ApiModelProperty("关联价格单名称")
    private String purchaseName;

    @ApiModelProperty("含税单价")
    private BigDecimal procurementUnitTaxPrice;

    @ApiModelProperty("toB供货价")
    private BigDecimal businessPrice;

    @ApiModelProperty("toC供货价")
    private BigDecimal customerPrice;

    @ApiModelProperty("包装费用")
    private BigDecimal packagePrice;

    @ApiModelProperty("生效时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectuateTime;

}
