package com.chongho.erp.vo.purchase;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chongho.erp.common.enums.PurchaseStatusEnum;
import com.chongho.erp.common.util.DateUtil;
import com.chongho.erp.po.purchase.ErpPurchasePricesItemPO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
@ApiModel(value = "ErpPurchasePricesItem展示对象", description = "采购价格单子表展示对象")
public class ErpPurchasePricesItemVO implements Serializable {

    private static final long serialVersionUID = -6711416628957888530L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("价格子单编码")
    @ExcelProperty(value = "价格子单号")
    private String purchaseSubCode;

    @ApiModelProperty("价格单编号")
    @ExcelProperty(value = "价格单编号")
    private String purchaseCode;

    @ApiModelProperty("价格单名称")
    @ExcelProperty(value = "价格单名称")
    private String purchaseName;

    @ApiModelProperty("物料编码")
    @ExcelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty("货品名称")
    @ExcelProperty(value = "货品名称")
    private String productName;

    @ApiModelProperty("规格编码")
    @ExcelProperty(value = "规格编码")
    private String skuId;

    @ApiModelProperty("规格名称")
    @ExcelProperty(value = "规格名称")
    private String skuName;

    @ApiModelProperty("供应商编码")
    @ExcelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty("可用库存")
    @ExcelIgnore
    private BigDecimal stockNum;

    @ApiModelProperty("供应商名称")
    @ExcelProperty(value = "供应商")
    private String supplierName;

    @ApiModelProperty("含税单价（财务单位）")
    @ExcelProperty(value = "含税单价")
    private BigDecimal taxPrice;

    @ApiModelProperty("包装费")
    @ExcelProperty(value = "包装费")
    private BigDecimal packageFee;

    @ApiModelProperty("运费")
    @ExcelProperty(value = "运费")
    private BigDecimal expressFee;

    @ApiModelProperty("toC供货价(财务单位)")
    @ExcelProperty(value = "toC价格")
    private BigDecimal customerPrice;

    @ApiModelProperty("toB供货价(财务单位)")
    @ExcelProperty(value = "toB价格")
    private BigDecimal businessPrice;

    @ApiModelProperty("状态编码(参考PurchaseStatusEnum枚举)")
    @ExcelIgnore
    private Integer state;

    @ApiModelProperty("状态描述(参考PurchaseStatusEnum枚举)")
    @ExcelProperty(value = "单据状态")
    private String stateDesc;

    @ApiModelProperty("标签")
    private String label;

    @ApiModelProperty("标签编码列表")
    private List<String> labelList;

    @ApiModelProperty("标签名称")
    @ExcelProperty(value = "标签")
    private String labelName;

    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注")
    private String comments;

    @ApiModelProperty("报价日期")
    @ExcelProperty(value = "报价日期")
    private String quoteDate;

    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_TIME, timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_TIME, timezone = "GMT+8")
    @ExcelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("失效时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_TIME, timezone = "GMT+8")
    @ExcelProperty(value = "失效时间")
    private LocalDateTime expirationTime;

    @ApiModelProperty("创建人id")
    @ExcelIgnore
    private Integer createUserId;

    @ApiModelProperty("创建人名称")
    @ExcelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty("财务单位编码")
    @ExcelIgnore
    private String financeUnitCode;

    @ApiModelProperty("财务单位名称")
    @ExcelIgnore
    private String financeUnitName;


    @ApiModelProperty("租户id")
    @ExcelIgnore
    private Long tenantId;



    @ApiModelProperty("修改人id")
    @ExcelIgnore
    private Integer updateUserId;

    @ApiModelProperty("修改人名称")
    @ExcelIgnore
    private String updateUserName;

    @ApiModelProperty("使用状态 0-禁用  1-可用")
    @ExcelIgnore
    private Integer useStatus;

    @ApiModelProperty("使用状态 0-禁用  1-可用")
    @ExcelProperty(value = "使用状态")
    private String useStatusDesc;

    @ApiModelProperty("内采供货价（元）")
    @ExcelProperty(value = "内采供货价（元）")
    private BigDecimal internalSupplyPrice;


    public ErpPurchasePricesItemVO() {
    }

    public ErpPurchasePricesItemVO(ErpPurchasePricesItemPO purchasePricesItemPO) {
        this.purchaseSubCode = purchasePricesItemPO.getPurchaseSubCode();
        this.purchaseCode = purchasePricesItemPO.getPurchaseCode();
        this.supplierCode = purchasePricesItemPO.getSupplierCode();
        this.supplierName = purchasePricesItemPO.getSupplierName();
        this.state = purchasePricesItemPO.getState();
        this.stateDesc = PurchaseStatusEnum.getDesc(purchasePricesItemPO.getState());
        this.comments = purchasePricesItemPO.getComments();
        if (!Objects.isNull(purchasePricesItemPO.getExpirationTime())) {
            this.expirationTime = purchasePricesItemPO.getExpirationTime();
        }
        this.skuId = purchasePricesItemPO.getSkuId();
        this.skuName = purchasePricesItemPO.getSkuName();
        this.materialCode = purchasePricesItemPO.getMaterialCode();
        this.financeUnitCode = purchasePricesItemPO.getFinanceUnitCode();
        this.financeUnitName = purchasePricesItemPO.getFinanceUnitName();
        this.productName = purchasePricesItemPO.getProductName();
        this.taxPrice = purchasePricesItemPO.getTaxPrice();
        this.businessPrice = purchasePricesItemPO.getBusinessPrice();
        this.customerPrice = purchasePricesItemPO.getCustomerPrice();
        this.packageFee = purchasePricesItemPO.getPackageFee();
        this.expressFee = purchasePricesItemPO.getExpressFee();
        this.tenantId = purchasePricesItemPO.getTenantId();
        this.createTime = purchasePricesItemPO.getCreateTime();
        this.updateTime = purchasePricesItemPO.getUpdateTime();
        this.createUserId = purchasePricesItemPO.getCreateUserId();
        this.createUserName = purchasePricesItemPO.getCreateUserName();
        this.updateUserId = purchasePricesItemPO.getUpdateUserId();
        this.updateUserName = purchasePricesItemPO.getUpdateUserName();
        this.internalSupplyPrice = purchasePricesItemPO.getInternalSupplyPrice();
    }
}
