package com.chongho.erp.vo.stock.actual;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PersonalOrBranchStockVo {

    @ApiModelProperty("仓库编码")
    private String depotCode;

    @ApiModelProperty("仓库名称")
    private String depotName;

    @ApiModelProperty("仓库类型 10-个人仓 20-分支仓")
    private Integer checkStockDepotType;

    @ApiModelProperty("仓库类型 10-个人仓 20-分支仓")
    private String checkStockDepotTypeName;

    @ApiModelProperty("结余库存数量")
    private BigDecimal stock;

    @ApiModelProperty("机构总仓盘点sku列表")
    private List<ErpCheckStockTaskItemVo> skuList;
}
