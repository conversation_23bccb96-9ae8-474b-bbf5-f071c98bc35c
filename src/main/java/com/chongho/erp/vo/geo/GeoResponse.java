package com.chongho.erp.vo.geo;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class GeoResponse {
    @ApiModelProperty("返回值为 0 或 1，0 表示请求失败；1 表示请求成功")
    private String status;
    @ApiModelProperty("当 status 为 0 时，info 会返回具体错误原因，否则返回OK")
    private String info;
    @ApiModelProperty("当 status 为 0 时，info 会返回具体错误原因，否则返回OK")
    private String infocode;
    private String count;
    private List<Geocode> geocodes;

    // Getter and Setter methods
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getInfocode() {
        return infocode;
    }

    public void setInfocode(String infocode) {
        this.infocode = infocode;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public List<Geocode> getGeocodes() {
        return geocodes;
    }

    public void setGeocodes(List<Geocode> geocodes) {
        this.geocodes = geocodes;
    }

    public static class Geocode {

        private String formatted_address;
        private String country;
        private String province;
        private String citycode;
        private String city;
        private String district;
        private List<String> township;
        private Neighborhood neighborhood;
        private Building building;
        private String adcode;
        private List<String> street;
        private List<String> number;

        @ApiModelProperty("经度，纬度")
        private String location;
        private String level;

        // Getter and Setter methods
        public String getFormatted_address() {
            return formatted_address;
        }

        public void setFormatted_address(String formatted_address) {
            this.formatted_address = formatted_address;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public String getProvince() {
            return province;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public String getCitycode() {
            return citycode;
        }

        public void setCitycode(String citycode) {
            this.citycode = citycode;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getDistrict() {
            return district;
        }

        public void setDistrict(String district) {
            this.district = district;
        }

        public List<String> getTownship() {
            return township;
        }

        public void setTownship(List<String> township) {
            this.township = township;
        }

        public Neighborhood getNeighborhood() {
            return neighborhood;
        }

        public void setNeighborhood(Neighborhood neighborhood) {
            this.neighborhood = neighborhood;
        }

        public Building getBuilding() {
            return building;
        }

        public void setBuilding(Building building) {
            this.building = building;
        }

        public String getAdcode() {
            return adcode;
        }

        public void setAdcode(String adcode) {
            this.adcode = adcode;
        }

        public List<String> getStreet() {
            return street;
        }

        public void setStreet(List<String> street) {
            this.street = street;
        }

        public List<String> getNumber() {
            return number;
        }

        public void setNumber(List<String> number) {
            this.number = number;
        }

        public String getLocation() {
            return location;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public String getLevel() {
            return level;
        }

        public void setLevel(String level) {
            this.level = level;
        }
    }

    public static class Neighborhood {
        private List<String> name;
        private List<String> type;

        // Getter and Setter methods
        public List<String> getName() {
            return name;
        }

        public void setName(List<String> name) {
            this.name = name;
        }

        public List<String> getType() {
            return type;
        }

        public void setType(List<String> type) {
            this.type = type;
        }
    }

    public static class Building {
        private List<String> name;
        private List<String> type;

        // Getter and Setter methods
        public List<String> getName() {
            return name;
        }

        public void setName(List<String> name) {
            this.name = name;
        }

        public List<String> getType() {
            return type;
        }

        public void setType(List<String> type) {
            this.type = type;
        }
    }
}
