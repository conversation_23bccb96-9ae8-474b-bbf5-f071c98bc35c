package com.chongho.erp.vo.material;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ErpSiteDepotVo{

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "编号")
    private String skuId;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "类型：1-发货站 2-中转仓")
    private Integer type;

    @ApiModelProperty(value = "供应商")
    private String supplier;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "所属乡镇名称")
    private String townDesc;

    @ApiModelProperty(value = "所属省")
    private String provinceDesc;

    @ApiModelProperty(value = "所属市")
    private String cityDesc;

    @ApiModelProperty(value = "所属区")
    private String countyDesc;

    @ApiModelProperty(value = "详细地址")
    private String detailAddress;

    @ApiModelProperty(value = "分支编号")
    private String branchCode;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty("负责人姓名")
    private String principalName;

    @ApiModelProperty("负责人手机号")
    private String mobile;

    /*@ApiModelProperty("发货站、前置仓对应组织编码")
    private List<String> companyCodeList;*/

    @ApiModelProperty("发货站、前置仓对应组织编码")
    private List<ErpSiteSkuCompanyCodeDTO> skuCompanyCodeDTOList;
}
