package com.chongho.erp.vo.material;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Program: wms-service
 * @Description: 货品规格站点信息
 * @author: LS
 * @create: 2022-06-21 16:38
 **/
@Data
public class ErpDepotProductSkuVo {
    //product
    @ApiModelProperty("货品主键ID")
    private Long productId;

    @ApiModelProperty("货品名称")
    private String productName;

    @ApiModelProperty("spuId")
    private String spuId;

    @ApiModelProperty(value = "一级货品分类")
    private String category1Code;

    @ApiModelProperty("一级分类名称")
    private String category1Name;

    @ApiModelProperty("三级分类编码")
    private String category3Code;

    @ApiModelProperty("三级分类名称")
    private String category3Name;

    @ApiModelProperty("三级分类路径")
    private String categoryPath;

    @ApiModelProperty(value = "品牌Id",hidden = true)
    private Long brandId;

    @ApiModelProperty("品牌名称")
    private String brandName;
    //product_sku
    @ApiModelProperty("skuId")
    private String skuId;

    @ApiModelProperty("仓库ID")
    private Long depotId;

    @ApiModelProperty("仓库编码")
    private String depotCode;

    @ApiModelProperty("规格物料名称")
    private String skuName;

    @ApiModelProperty("规格物料编码")
    private String materialCode;

    @ApiModelProperty("单位编码")
    private String unitCode;

    @ApiModelProperty("财务单位")
    private String unitName;

    @ApiModelProperty("库存")
    private BigDecimal stockNum;

    @ApiModelProperty("实仓库存")
    private BigDecimal realStockNum;

    @ApiModelProperty("库存文本")
    private String stockNumText;

    @ApiModelProperty("实仓库存文本")
    private String realStockNumText;

    @ApiModelProperty("采购数量")
    private BigDecimal procurementNums;

    @ApiModelProperty("含税单价")
    private BigDecimal sellPrice;
    @ApiModelProperty("进项税率")
    private BigDecimal inTax;

    @ApiModelProperty("京东外部编码")
    private String externalJdCode;

    @ApiModelProperty("重量")
    private BigDecimal weight;

    @ApiModelProperty("重量单位")
    private String unit;

    @ApiModelProperty("货品类型")
    private Integer productType;

    @ApiModelProperty("账期")
    private Integer accountPeriod;

    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("转换系数")
    private BigDecimal unitRatio;

    @ApiModelProperty("目标规格单位名称")
    private String targetUnitName;

    @ApiModelProperty("库存单位规格名称")
    private String stockUnitName;

    @ApiModelProperty(value = "最小单位")
    private String stockUnit;

    @ApiModelProperty("toC供货价")
    private BigDecimal toCPrice;

    @ApiModelProperty("所属分支")
    private String branchCode;
}
