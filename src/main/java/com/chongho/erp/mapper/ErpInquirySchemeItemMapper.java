package com.chongho.erp.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.chongho.erp.po.ErpInquirySchemeItemPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 方案询价单明细表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-02-18 17:00:12
 */
public interface ErpInquirySchemeItemMapper extends BaseMapper<ErpInquirySchemeItemPO> {

    List<ErpInquirySchemeItemPO> listQuoteInquiryItemByProductBoxCode(@Param("productBoxCodeList") List<String> productBoxCodeList);

    @InterceptorIgnore(tenantLine = "true")
    List<ErpInquirySchemeItemPO> getListByInquiryCode(@Param("channelInquiryCode") String channelInquiryCode);

    @InterceptorIgnore(tenantLine = "true")
    List<ErpInquirySchemeItemPO> getListByInquiryCodeAndBox(@Param("inquiryCode") String inquiryCode,
                                                            @Param("productBoxCodeList") List<String> productBoxCodeList);
}
