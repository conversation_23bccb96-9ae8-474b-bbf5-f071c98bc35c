package com.chongho.erp.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chongho.erp.dto.query.SiteQuery;
import com.chongho.erp.po.SitePo;
import com.chongho.erp.vo.SiteVo;
import com.chongho.erp.vo.material.ErpSiteAndDepotVo;
import com.chongho.erp.vo.material.ErpSiteVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
public interface SiteMapper extends BaseMapper<SitePo> {

    Page<SiteVo> pageList(Page page, @Param("query") SiteQuery query);

    List<SiteVo> listBySupplier(@Param("supplierCode") String supplierCode);

    SiteVo detail(@Param("siteId") Long siteId);

    List<SiteVo> listProductsSomeSite(@Param("skuIds") List<String> skuIds,
                                      @Param("count") Integer count);

    List<SiteVo> listBySku(@Param("skuId") String skuId);

    /**
     * description: 通过供应商编号/站点名称获取站点ID
     *
     * @return List<Long>
     * @Param: siteName,supplierCode
     */
    List<Long> getSiteIds(@Param("siteName") String siteName,@Param("supplierCode") String supplierCode);

    /**
     * description: 批量获取站点信息
     *
     * @return List<SiteVo>
     * @Param: siteIds
    */
    List<ErpSiteVo> batchSiteByIds(@Param("siteIds") List<Long> siteIds);

    /**
     * description: 数据清理
     *
     * @return a
     * @Param: null
     * */
    Integer clearTable();

    @InterceptorIgnore(tenantLine = "true")
    List<SiteVo> listBySkuAndSupplier(@Param("skuId") String skuId,@Param("supplierCode") String supplierCode);

    /**
     * 根据名称模糊查询发货站和中转仓
     *
     * @param name 模糊名称
     * @return List<ErpSiteAndDepotVo>
     */
    List<ErpSiteAndDepotVo> querySIteAndDepotListByName(@Param("name") String name);

    void setNullDepotId(@Param("depotId")Long depotId);
}
