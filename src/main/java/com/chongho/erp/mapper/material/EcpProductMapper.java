package com.chongho.erp.mapper.material;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.framework.autoconfigure.mybatis.MyBaseMapper;
import com.chongho.erp.dto.DepotSupplierDTO;
import com.chongho.erp.dto.material.EcpProductQueryDTO;
import com.chongho.erp.dto.material.ManyTenantProductDTO;
import com.chongho.erp.dto.query.ErpProductDetailQuery;
import com.chongho.erp.dto.query.StockInProductQuery;
import com.chongho.erp.po.material.EcpProductPO;
import com.chongho.erp.req.GoodsApplySku;
import com.chongho.erp.req.ProductReq;
import com.chongho.erp.vo.StockInProductVO;
import com.chongho.erp.vo.material.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EcpProductMapper extends MyBaseMapper<EcpProductPO> {

    List<EcpProductPO> listProduct(@Param("query") ProductReq query);

    List<ErpProductDetailVo> getProductDetailList(@Param("query") ErpProductDetailQuery productDetailQuery);

    List<ErpProductSupplierVo> getSupperList(@Param("spuIds") List<String> spuIds);

    /**
     * description: 获取货品列表
     *
     * @return List<ProductVO>
     * @Param: query
     * */
    List<ProductVO> getProductVoList(@Param("query") EcpProductQueryDTO query);

    /**
     * description: 获取前置仓绑定的skuId
     *
     * @return List<ErpProductDepotVo>
     * @Param: skuIdList
     * */
    List<ErpProductDepotVo> getSkuIdByDepotId(@Param("skuIdList") List<String> skuIdList,
                                              @Param("depotId") Long depotId);


    /**
     * 货品路径同步
     * @param updateList
     */
    void syncProductCategory(@Param("updateList")List<EcpProductPO> updateList);

    Page<ErpProductDetailVo> getProductDetailPageList(@Param("page")Page<EcpProductPO> page,@Param("query") ErpProductDetailQuery productDetailQuery);

    Page<ProductVO> getProductVoPageList(@Param("page")Page<ProductVO> page,@Param("query") EcpProductQueryDTO productQueryDTO);

    Page<ProductVO> getManyTenantProduct(@Param("page") Page<ProductVO> page, @Param("query") ManyTenantProductDTO productQueryDTO);

    Page<ErpProductDetailVo> getProductByShiftDepot(@Param("page")Page<EcpProductPO> page,@Param("query") ErpProductDetailQuery productDetailQuery);

    List<ProductCategoryBySkuIdVO> getProductCategoryBySkuId(@Param("skuIds") List<String> skuIds);

    List<DepotSupplierDTO> getDepotIdBySupplier(@Param("supplierCodeList")List<String> supplierCodeList,@Param("skuIds") List<String> skuIds);

    Page<StockInProductVO> getStockInProductList(@Param("page")Page page, @Param("query") StockInProductQuery query);


    /**
     * 获取货品、规格、单位、供应商信息分页列表
     * @param productDetailQuery 查询条件
     * @return Page<ErpProductDetailVo>
     */
    Page<ErpProductDetailVo> queryProductDetailPageList(@Param("page")Page<EcpProductPO> page,@Param("query") ErpProductDetailQuery productDetailQuery);
    /**
     * 刷数处理
     * */
    void changeProductCategoryCode();
}