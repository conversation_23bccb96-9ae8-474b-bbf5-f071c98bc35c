package com.chongho.erp.domain.builder;
import com.chongho.erp.common.enums.*;
import com.chongho.erp.common.enums.common.ValidEnum;
import com.chongho.erp.common.util.DateUtil;
import com.chongho.erp.dto.material.EcpProductAndSkuDTO;
import com.chongho.erp.dto.procurement.ErpProSubHeadAndItemDTO;
import com.chongho.erp.po.purchase.ErpProcurementSubmissionItemPriceBillLinkPO;
import com.chongho.erp.po.purchase.ErpPurchasePricesHeadPO;
import com.chongho.erp.po.purchase.ErpPurchasePricesItemPO;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

/**
 * 类SplitGoodsBuilder.java的实现描述：
 */
public class ErpPurchasePricesLinkBuilder {

    public static ErpPurchasePricesHeadPO buildPurchasePricesHeadPO(ErpProSubHeadAndItemDTO itemDTO,String purchaseCode,String prefixName){
        // 保存价格单主表
        ErpPurchasePricesHeadPO erpPurchasePricesHead = new ErpPurchasePricesHeadPO();
        erpPurchasePricesHead.setPurchaseCode(purchaseCode);
        erpPurchasePricesHead.setState(PurchaseStatusEnum.EFFECTIVE.getValue());
        String supplierName=StringUtils.isNotBlank(itemDTO.getSupplierShortName())?itemDTO.getSupplierShortName():itemDTO.getSupplierName();
        erpPurchasePricesHead.setPurchaseName(prefixName+DateUtil.localDateTimeToStr(itemDTO.getCreateTime(),DateUtil.FORMAT_DATE_BATCHNO)+itemDTO.getId()+supplierName+"报价");
        erpPurchasePricesHead.setQuoteDate(itemDTO.getCreateTime());
        erpPurchasePricesHead.setTenantId(itemDTO.getTenantId());
        erpPurchasePricesHead.setSupplierCode(StringUtils.isNotBlank(itemDTO.getSupplierCode())?itemDTO.getSupplierCode():"未知");
        erpPurchasePricesHead.setSupplierName(supplierName);
        return erpPurchasePricesHead;
    }

    public static ErpPurchasePricesItemPO buildPurchasePricesItemPO(ErpProSubHeadAndItemDTO itemDTO, EcpProductAndSkuDTO skuDTO,
                                                                    String purchaseSubCode, String purchaseCode){
        // 保存价格单明细表
        ErpPurchasePricesItemPO erpPurchasePricesItem = new ErpPurchasePricesItemPO();
        erpPurchasePricesItem.setPurchaseCode(purchaseCode);
        erpPurchasePricesItem.setPurchaseSubCode(purchaseSubCode);
        erpPurchasePricesItem.setState(PurchaseStatusEnum.EFFECTIVE.getValue());
        String supplierName=StringUtils.isNotBlank(itemDTO.getSupplierShortName())?itemDTO.getSupplierShortName():itemDTO.getSupplierName();
        erpPurchasePricesItem.setTenantId(itemDTO.getTenantId());
        erpPurchasePricesItem.setSupplierCode(StringUtils.isNotBlank(itemDTO.getSupplierCode())?itemDTO.getSupplierCode():"未知");
        erpPurchasePricesItem.setSupplierName(supplierName);
        erpPurchasePricesItem.setCustomerPrice(itemDTO.getCustomerPrice());
        erpPurchasePricesItem.setBusinessPrice(itemDTO.getBusinessPrice());
        erpPurchasePricesItem.setTaxPrice(itemDTO.getProcurementUnitTaxPrice());
        erpPurchasePricesItem.setPackageFee(itemDTO.getPackagePrice());
        erpPurchasePricesItem.setExpressFee(itemDTO.getExpressFee());
        erpPurchasePricesItem.setProductName(skuDTO.getProductName());
        erpPurchasePricesItem.setSkuId(skuDTO.getSkuId());
        erpPurchasePricesItem.setSkuName(skuDTO.getSkuName());
        erpPurchasePricesItem.setMaterialCode(skuDTO.getSkuMaterialCode());
        erpPurchasePricesItem.setFinanceUnitCode(skuDTO.getFinanceUnitCode());
        erpPurchasePricesItem.setFinanceUnitName(skuDTO.getFinanceUnit());
        return erpPurchasePricesItem;
    }

    public static ErpProcurementSubmissionItemPriceBillLinkPO buildProcurementSubmissionItemPriceBillLinkPO(ErpPurchasePricesItemPO item,
                                                                                                            String procurementSubmissionCode,
                                                                                                            String purchaseName){
        // 保存价格单明细表
        ErpProcurementSubmissionItemPriceBillLinkPO linkPO = new ErpProcurementSubmissionItemPriceBillLinkPO();
        linkPO.setProcurementSubmissionCode(procurementSubmissionCode);
        linkPO.setBillStatus(ValidEnum.SUCCESS_EFFICACY.getCode());
        linkPO.setBillStatusDesc(ValidEnum.SUCCESS_EFFICACY.getName());
        linkPO.setSkuId(item.getSkuId());
        linkPO.setProductName(item.getProductName());
        linkPO.setBusinessPrice(item.getBusinessPrice());
        linkPO.setCustomerPrice(item.getCustomerPrice());
        linkPO.setPackagePrice(item.getPackageFee());
        linkPO.setExpressFee(item.getExpressFee());
        linkPO.setProcurementUnitTaxPrice(item.getTaxPrice());
        linkPO.setMaterialCode(item.getMaterialCode());
        linkPO.setEffectuateTime(LocalDateTime.now());
        linkPO.setSkuName(item.getSkuName());
        linkPO.setPurchaseCode(item.getPurchaseCode());
        linkPO.setPurchaseSubCode(item.getPurchaseSubCode());
        linkPO.setPurchaseName(purchaseName);
        linkPO.setTenantId(item.getTenantId());
        return linkPO;
    }
}
