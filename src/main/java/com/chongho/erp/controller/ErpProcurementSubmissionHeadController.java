package com.chongho.erp.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.SyncReadListener;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.framework.autoconfigure.common.exception.BusinessException;
import com.cfpamf.framework.autoconfigure.common.result.Result;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.chongho.erp.common.constant.CommonConstants;
import com.chongho.erp.common.constant.WmsConstants;
import com.chongho.erp.common.enums.DepotHeadTypeEnum;
import com.chongho.erp.common.enums.ErpBizErrorEnum;
import com.chongho.erp.common.enums.ErrorCodeEnum;
import com.chongho.erp.common.enums.OperateLogTypeEnum;
import com.chongho.erp.common.enums.flow.StockApplyStatusEnum;
import com.chongho.erp.common.util.*;
import com.chongho.erp.config.BasicsConfig;
import com.chongho.erp.dto.ErpActualAuditDTO;
import com.chongho.erp.dto.procurement.*;
import com.chongho.erp.dto.stock.actual.ErpActualInFlowAddDTO;
import com.chongho.erp.po.ErpExtWarehouseInOutDetailPO;
import com.chongho.erp.po.ErpExtWarehouseInOutPO;
import com.chongho.erp.po.ErpUserPO;
import com.chongho.erp.repository.ErpExtWarehouseInOutDetailRepo;
import com.chongho.erp.repository.ErpExtWarehouseInOutRepo;
import com.chongho.erp.repository.ErpOperateLogRepo;
import com.chongho.erp.repository.ErpProcurementSubmissionBalanceRepo;
import com.chongho.erp.req.ErpExtWarehouseAuditReq;
import com.chongho.erp.req.ErpExtWarehouseCreateReq;
import com.chongho.erp.req.ErpExtWarehouseDetailCreateReq;
import com.chongho.erp.req.ErpExtWarehouseEditReq;
import com.chongho.erp.service.ErpProcurementSubmissionHeadRepo;
import com.chongho.erp.service.ErpProcurementSubmissionHeadService;
import com.chongho.erp.service.ErpProcurementSubmissionItemRepo;
import com.chongho.erp.service.IBmsIntegration;
import com.chongho.erp.service.export.ErpSubPurchasePricesItemExportService;
import com.chongho.erp.service.purchase.ErpProcurementSubmissionItemPriceBillLinkService;
import com.chongho.erp.service.stock.actual.IErpActualInFlowService;
import com.chongho.erp.thirdpart.api.vo.ErpRequireGoodsItemQuery;
import com.chongho.erp.thirdpart.api.vo.ErpRequireGoodsItemVO;
import com.chongho.erp.vo.ErpOperateLogVO;
import com.chongho.erp.vo.ErpSubPurchasePricesItemVo;
import com.chongho.erp.vo.ErpSubPurchasePricesSkuInfoVo;
import com.chongho.erp.vo.ExportErpProcurementSubmissionHeadItemVO;
import com.chongho.erp.vo.purchase.PurchasePriceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08 09:33:32
 */
@Api(tags = "采购提报单")
@RestController
@RequestMapping("/procurementSubmissionHead")
public class ErpProcurementSubmissionHeadController {
    @Autowired
    private ErpProcurementSubmissionHeadRepo procurementSubmissionHeadRepo;
    @Resource
    private ErpProcurementSubmissionItemRepo erpProcurementSubmissionItemRepo;
    @Autowired
    private TenantUtil tenantUtil;
    @Autowired
    private BasicsConfig basicsConfig;

    @Autowired
    IBmsIntegration bmsIntegration;
    @Autowired
    private DistributeLockUtils distributeLockUtils;
    @Autowired
    private ErpOperateLogRepo operateLogRepo;
    @Autowired
    private ErpProcurementSubmissionHeadService procurementSubmissionHeadService;
    @Autowired
    private ErpProcurementSubmissionItemPriceBillLinkService priceBillLinkService;

    @Autowired
    private ErpSubPurchasePricesItemExportService erpSubPurchasePricesItemExportService;
    @Resource
    private IErpActualInFlowService erpActualInFlowService;
    @Resource
    private ErpProcurementSubmissionBalanceRepo erpProcurementSubmissionBalanceRepo;
    @Resource
    private ErpExtWarehouseInOutRepo erpExtWarehouseInOutRepo;
    @Resource
    private ErpExtWarehouseInOutDetailRepo erpExtWarehouseInOutDetailRepo;

    @PostMapping(value = "/addProcurement")
    @ApiOperation(value = "增加采购提报单")
    public Boolean addProcurement(@RequestBody @Valid ProcurementSubmissionRequest procurementSubmissionRequest) {
        return procurementSubmissionHeadRepo.addProcurement(procurementSubmissionRequest);
    }


    @PostMapping(value = "/updateProcurement")
    @ApiOperation(value = "修改采购提报单")
    public Boolean updateProcurement(@RequestBody @Valid ProcurementSubmissionRequest procurementSubmissionRequest) {
        Boolean result = procurementSubmissionHeadRepo.updateProcurement(procurementSubmissionRequest);
        return result;
    }


    @DeleteMapping(value = "/deleteProcurement")
    @ApiOperation(value = "删除采购提报单")
    public Boolean deleteProcurement(@RequestParam("procurementSubmissionCode") String procurementSubmissionCode) {
        Boolean result = procurementSubmissionHeadRepo.deleteProcurement(procurementSubmissionCode);
        return result;
    }

    @PostMapping(value = "/getProcurements")
    @ApiOperation(value = "查找采购提报单")
    public Page<ProcurementSubmissionHeadResponse> getProcurements(@RequestBody QueryProcurementSubmissionHeadRequest queryProcurementSubmissionHeadRequest) {
        return procurementSubmissionHeadRepo.getProcurements(queryProcurementSubmissionHeadRequest);
    }

    /*@PostMapping(value = "/exportProcurements")
    @ApiOperation(value = " 采购管理-采购订单导出")
    public void exportProcurements(HttpServletResponse response, @RequestBody QueryProcurementSubmissionHeadRequest queryProcurementSubmissionHeadRequest) {
        List<ExportErpProcurementSubmissionHeadItemVO> exportErpProcurementSubmissionHeadItemVoList =  procurementSubmissionHeadRepo.exportProcurements(response,queryProcurementSubmissionHeadRequest);
        EasyPoiUtil easyPoiUtil = new EasyPoiUtil();
        try {
            String fileName = String.format("采购管理-采购订单_%s", DateUtil.format(new Date(), "yyyyMMdd"));
            easyPoiUtil.downloadField(response, fileName, ExportErpProcurementSubmissionHeadItemVO.class, exportErpProcurementSubmissionHeadItemVoList, null);
        } catch (Exception e) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_EXCEPTION.getCode(), "采购管理-采购订单异常"+e);
        }
    }*/

    @PostMapping(value = "/exportProcurements/v2")
    @ApiOperation(value = " 采购管理-采购订单导出")
    public FileDTO exportProcurementsV2(@RequestBody QueryProcurementSubmissionHeadRequest request) {
        return procurementSubmissionHeadService.exportProcurement(request);
    }


    @GetMapping(value = "/getProcurementByCode")
    @ApiOperation(value = "查找采购提报单详情")
    public ProcurementSubmissionResponse getProcurementByCode(@RequestParam("procurementSubmissionCode") String procurementSubmissionCode) {
        ProcurementSubmissionResponse procurementByCode = procurementSubmissionHeadRepo.getProcurementDetailByCode(procurementSubmissionCode);
        return procurementByCode;
    }

    @GetMapping(value = "/copyProcurementByCode")
    @ApiOperation(value = "复制采购提报单")
    public List<ProcurementSubmissionItemResponse> copyProcurementByCode(@RequestParam("procurementSubmissionCode") String procurementSubmissionCode) {
        List<ProcurementSubmissionItemResponse> procurementSubmissionItemResponses = procurementSubmissionHeadRepo.copyProcurementByCode(procurementSubmissionCode);
        return procurementSubmissionItemResponses;
    }

    @PostMapping(value = "/auditProcurement")
    @ApiOperation(value = "审核采购提报单")
    public Boolean auditProcurement(@RequestBody AuditProcurementRequest auditProcurementRequest) {
        Boolean result = procurementSubmissionHeadRepo.auditProcurement(auditProcurementRequest);
        return result;
    }

    @GetMapping(value = "/rejectProcurement")
    @ApiOperation(value = "驳回采购提报单")
    public Boolean rejectProcurement(@RequestParam("procurementSubmissionCode") String procurementSubmissionCode) {
        Boolean result = procurementSubmissionHeadRepo.rejectProcurement(procurementSubmissionCode);
        return result;
    }

    @PostMapping(value = "/signProcurement")
    @ApiOperation(value = "签约采购提报单")
    public Boolean signProcurement(@RequestBody ProcurementSignRequest procurementSignRequest) {
        String key=String.format("%s:%s", CommonConstants.PROCUREMENT_SUBMISSION_REDIS_LOCK,procurementSignRequest.getProcurementSubmissionCode());
        return distributeLockUtils.lockAndProcess(Collections.singletonList(key), 2L, -1, TimeUnit.SECONDS
                , () -> procurementSubmissionHeadRepo.signProcurementing(procurementSignRequest));
    }

    @PostMapping(value = "/payProcurement")
    @ApiOperation(value = "采购提报单绑定支付单")
    public Boolean payProcurement(@RequestBody ProcurementPayRequest procurementPayRequest) {
        Boolean result = procurementSubmissionHeadRepo.payProcurement(procurementPayRequest);
        return result;
    }

    @PostMapping(value = "/caculateProcurement")
    @ApiOperation(value = "实时计算采购提报单")
    public ProcurementCaculateResponse caculateProcurement(@RequestBody @Valid ProcurementCaculateRequest procurementCaculateRequest) {
        ProcurementCaculateResponse procurementCaculateResponse = procurementSubmissionHeadRepo.caculateProcurement(procurementCaculateRequest);
        return procurementCaculateResponse;
    }

    @GetMapping("/compensate/sub")
    @ApiOperation("采购提报单入库补偿")
    public String compensate(){
        return procurementSubmissionHeadRepo.compensate();
    }

    @GetMapping("/compensate/sign")
    @ApiOperation("采购提报单签约补偿")
    public String compensateSine(){
        return procurementSubmissionHeadRepo.compensateSine();
    }

    @PostMapping(value = "/getSkuProcurements")
    @ApiOperation(value = "实物出库-获取批次号库存过滤")
    public Page<DepotSkuProcurementResponse> getSkuProcurements(@RequestBody SkuProcurementSubmissionHeadRequest skuProcurementSubmissionHeadRequest) {
        return procurementSubmissionHeadRepo.getSkuProcurements(skuProcurementSubmissionHeadRequest);
    }


    @PostMapping("/requireGoodsItem/pageList")
    @ApiOperation("要货单明细分页查询")
    public Page<ErpRequireGoodsItemVO> queryGoodsItemPage(@RequestBody ErpRequireGoodsItemQuery query) {
        return procurementSubmissionHeadRepo.queryGoodsItemPage(query);
    }

    @ApiOperation(value = "农服-选择业务审核部门")
    @GetMapping("/optionBusAuditDept")
    public List<DictionaryItemVO> optionBusAuditDept () {
        return bmsIntegration.getDictionaryItemsByTypeCode(WmsConstants.BUS_AUDIT_DEPT);
    }

    @PostMapping("/requireGoodsItem/checkSku")
    @ApiOperation("要货单明细规格校验")
    public Boolean queryGoodsItemPage(@RequestBody List<ErpRequireGoodsItemVO> goodsItemVoList) {

        return procurementSubmissionHeadRepo.checkGoodsSku(goodsItemVoList);
    }

    @PostMapping(value = "/getHasStockProcurements")
    @ApiOperation(value = "获取有库存的采购提报单")
    public Page<ProcurementSubmissionHeadExtResponse> getHasStockProcurements(@RequestBody QueryProcurementSubmissionHeadRequest queryProcurementSubmissionHeadRequest) {
        Page<ProcurementSubmissionHeadExtResponse> procurements = procurementSubmissionHeadRepo.getHasStockProcurements(queryProcurementSubmissionHeadRequest);
        return procurements;
    }

    @PostMapping(value = "/checkSupplyPriceAllowEdit")
    @ApiOperation(value = "查看供货价是否可以编辑")
    public Boolean checkSupplyPriceAllowEdit() {
        return procurementSubmissionHeadRepo.checkSupplyPriceAllowEdit();
    }

    @PostMapping(value = "/tagBatchSet")
    @ApiOperation(value = "批量设置采购用途标签")
    public void tagBatchSet(@RequestBody @Valid UseTagSettingVo useTagSettingVo) {
        ErpUserPO tenantUser = tenantUtil.getUser();
        procurementSubmissionHeadRepo.tagBatchSet(useTagSettingVo, tenantUser);
    }

    @ApiOperation(value = "采购订单-轨迹")
    @GetMapping(value = "/track")
    public List<ErpOperateLogVO> track(@RequestParam("headCode") String headCode) {
        return operateLogRepo.queryOperateLog(headCode, OperateLogTypeEnum.CGTBD);
    }

    @ApiOperation(value = "采购价格单-价格单列表")
    @PostMapping(value = "/purchasePricesItemPage")
    public Page<ErpSubPurchasePricesItemVo> purchasePricesItemPage(@RequestBody @Valid ProcurementSubmissionItemPriceBillLinkQuery query) {
        return priceBillLinkService.purchasePricesItemPage(query);
    }
    @ApiOperation(value = "采购价格单-选择采购物料及价格单")
    @PostMapping(value = "/purchasePricesSkuInfoPage")
    public Page<ErpSubPurchasePricesSkuInfoVo> purchasePricesSkuInfoPage(@RequestBody @Valid PurchasePricesProductSkuInfoQuery query) {
        return priceBillLinkService.purchasePricesSkuInfoPage(query);
    }

    @ApiOperation(value = "采购价格单-绑定价格单")
    @PostMapping(value = "/purchasePricesBind")
    public void purchasePricesBind(@RequestBody @Valid ErpProcurementSubmissionItemBindPriceBillDTO dto) {
        priceBillLinkService.purchasePricesBind(dto);
    }
    @ApiOperation(value = "采购价格单-作废")
    @GetMapping(value = "/cancelPurchasePricesBillLink")
    public void cancelPurchasePricesBillLink(@RequestParam("priceBillLinkId") Long priceBillLinkId) {
        priceBillLinkService.cancelPurchasePricesBillLink(priceBillLinkId);
    }

    @ApiOperation(value = "采购价格单-调整")
    @PostMapping(value = "/modifyPurchasePricesBillLink")
    public void modifyPurchasePricesBillLink(@RequestBody @Valid ErpModifyPurchasePriceBillLinkDTO dto) {
        priceBillLinkService.modifyPurchasePricesBillLink(dto);
    }
    @ApiOperation(value = "采购价格单-批量价格单调整")
    @PostMapping(value = "/batchModifyPurchasePricesBillLink")
    public Result<PurchasePriceVO> batchModifyPurchasePricesBillLink(@RequestParam("file") MultipartFile file) throws IOException {
        //获取excel数据
        SyncReadListener excelListener = new SyncReadListener();
        EasyExcel.read(file.getInputStream(), ErpProSubItemBindPriceBillImportDTO.class, excelListener).doReadAll();
        List<Object> list = excelListener.getList();
        List<ErpProSubItemBindPriceBillImportDTO> dbExcelList =
                list.stream().map(item -> (ErpProSubItemBindPriceBillImportDTO)item).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dbExcelList)) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "您导入的是空文件，请核查！");
        }
        return ResultUtils.resultSuccess(priceBillLinkService.batchModifyPurchasePricesBillLink(dbExcelList));
    }
    @ApiOperation(value = "采购价格单-价格单导出")
    @PostMapping(value = "/purchasePricesItem/export")
    public FileDTO purchasePricesItemExport(@RequestBody @Valid ProcurementSubmissionItemPriceBillLinkQuery query) {
        return erpSubPurchasePricesItemExportService.subPurchasePricesItemExport(query);
    }


    @ApiOperation(value = "处理历史采购用途标签")
    @GetMapping(value = "/historyTag")
    public void historyTag() {
        procurementSubmissionHeadRepo.historyTag();
    }
}
