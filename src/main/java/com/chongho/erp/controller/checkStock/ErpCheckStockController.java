package com.chongho.erp.controller.checkStock;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.framework.autoconfigure.common.result.Result;
import com.cfpamf.framework.autoconfigure.web.bms.JwtUserInfo;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.chongho.erp.common.enums.CheckStockStateEnum;
import com.chongho.erp.common.util.AliYunOssUtil;
import com.chongho.erp.common.util.ErpTenantUtil;
import com.chongho.erp.common.util.ResultUtils;
import com.chongho.erp.domain.builder.checkStock.CheckStockBuilder;
import com.chongho.erp.repository.stock.actual.ErpCheckStockTaskHeadRepo;
import com.chongho.erp.req.CheckDiffStockExportReq;
import com.chongho.erp.req.CheckDiffStockQuery;
import com.chongho.erp.req.CheckStockAddSkuReq;
import com.chongho.erp.req.CheckStockApplyReq;
import com.chongho.erp.req.CheckStockDepotQueryReq;
import com.chongho.erp.req.CheckStockQueryReq;
import com.chongho.erp.req.CheckStockTemporaryStorageReq;
import com.chongho.erp.service.BaseBmsTokenService;
import com.chongho.erp.vo.CheckStockDepotVo;
import com.chongho.erp.vo.material.ErpDepotProductSkuVo;
import com.chongho.erp.vo.stock.actual.ErpCheckDiffStockVo;
import com.chongho.erp.vo.stock.actual.ErpCheckStockDetailQueryVo;
import com.chongho.erp.vo.stock.actual.ErpCheckStockQueryVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/erpWebApi/checkStock")
@Api(tags = { "盘点明细详情" })
public class ErpCheckStockController {

	@Resource
	private ErpCheckStockTaskHeadRepo erpCheckStockTaskHeadRepo;

	@Resource
	private BaseBmsTokenService baseBmsTokenService;

	@Autowired
	private ErpTenantUtil erpTenantUtil;

	@Autowired
	private AliYunOssUtil aliYunOssUtil;

	@PostMapping("/list")
	@ApiOperation("盘点任务分页查询")
	public Result<Page<ErpCheckStockQueryVo>> listCheckStock(@RequestBody @Valid CheckStockQueryReq checkStockQueryReq,
			HttpServletRequest request) {
		JwtUserInfo userInfo = baseBmsTokenService.getUserByValid();
		// 查询bms配置的数据权限
		checkStockQueryReq.setPermissionOrgCodeList(baseBmsTokenService.getOrgCodeList(request));
		checkStockQueryReq.setLoginEmployeeNo(userInfo.getJobNumber());
		return ResultUtils.resultSuccess(erpCheckStockTaskHeadRepo.listCheckStock(checkStockQueryReq));
	}

	@GetMapping("/detail")
	@ApiOperation("盘点任务详情查询")
	public Result<ErpCheckStockDetailQueryVo> getDetail(@RequestParam String bizNo) {
		return ResultUtils.resultSuccess(erpCheckStockTaskHeadRepo.getDetail(bizNo));
	}

	@PostMapping("/sku/list")
	@ApiOperation("可添加货品列表")
	public Result<Page<ErpDepotProductSkuVo>> listSku(@RequestHeader("companyCode") String companyCode,
			@RequestBody CheckStockAddSkuReq addSkuReq) {
		return ResultUtils.resultSuccess(erpCheckStockTaskHeadRepo.listSku(addSkuReq));
	}

	@PostMapping("/apply")
	@ApiOperation("提交盘点")
	public Result<String> saveApply(@RequestHeader("companyCode") String companyCode,
			@Valid @RequestBody CheckStockApplyReq applyReq) {
		JwtUserInfo userInfo = baseBmsTokenService.getUserByValid();
		applyReq.setCompanyCode(companyCode);
		applyReq.setUserId(userInfo.getUserId());
		applyReq.setLoginEmployeeNo(userInfo.getJobNumber());
		applyReq.setLoginEmployeeName(userInfo.getUserName());
		erpCheckStockTaskHeadRepo.saveApply(applyReq);
		return ResultUtils.resultSuccess("成功");
	}

	@PostMapping("/pageCheckDiffStock")
	@ApiOperation("盘点差异-分页查询")
	public Result<Page<ErpCheckDiffStockVo>> pageCheckDiffStock(@RequestBody CheckDiffStockQuery checkDiffStockQuery,
			HttpServletRequest request) {
		// 查询bms配置的数据权限
		JwtUserInfo userInfo = baseBmsTokenService.getUserByValid();
		checkDiffStockQuery.setPermissionOrgCodeList(baseBmsTokenService.getOrgCodeList(request));
		checkDiffStockQuery.setLoginEmployeeNo(userInfo.getJobNumber());
		Page<ErpCheckDiffStockVo> page = erpCheckStockTaskHeadRepo.pageCheckDiffStock(checkDiffStockQuery);
		page.getRecords().forEach(erpCheckDiffStockVo -> {
			if (CheckStockStateEnum.WAIT.getValue().equals(erpCheckDiffStockVo.getStatus())) {
				erpCheckDiffStockVo.setSettleStock(null);
				erpCheckDiffStockVo.setCheckCurrentStock(null);
				erpCheckDiffStockVo.setCheckStock(null);
				erpCheckDiffStockVo.setCheckDiffStock(null);
				erpCheckDiffStockVo.setCheckDiffSettleStock(null);
				erpCheckDiffStockVo.setSettleChangeStock(null);
			}
		});
		return ResultUtils.resultSuccess(page);
	}

	@PostMapping("/exportCheckDiffStock")
	@ApiOperation("盘点差异导出")
	public Result<FileDTO> exportCheckDiffStock(@RequestBody CheckDiffStockExportReq stockExportReq,
			HttpServletRequest request) {
		// 查询bms配置的数据权限
		stockExportReq.setPermissionOrgCodeList(baseBmsTokenService.getOrgCodeList(request));
		JwtUserInfo userInfo = baseBmsTokenService.getUserByValid();
		stockExportReq.setUserId(Long.valueOf(userInfo.getUserId()));
		stockExportReq.setUserName(userInfo.getUserName());
		stockExportReq.setTenantId(erpTenantUtil.getTenantIdFromRequest());
		stockExportReq.setLoginEmployeeNo(userInfo.getJobNumber());
		log.info("exportCheckDiffStock {}", JSONObject.toJSONString(stockExportReq));
		stockExportReq.setExport(true);
		return ResultUtils.resultSuccess(erpCheckStockTaskHeadRepo.exportCheckDiffStock(stockExportReq));
	}

	@GetMapping("/getDepotListByOrgCode")
	@ApiOperation("盘点仓库查询")
	public Result<List<CheckStockDepotVo>> getDepotListByOrgCode(@Valid CheckStockDepotQueryReq req) {
		return ResultUtils.resultSuccess(erpCheckStockTaskHeadRepo.getDepotListByOrgCode(req));
	}

	@PostMapping("/temporaryStorage")
	@ApiOperation("暂存")
	public Result<Boolean> temporaryStorage(@Valid @RequestBody CheckStockTemporaryStorageReq checkStockTemporaryStorageReq) {
		String key = CheckStockBuilder.temporaryStorageKeyBuild(checkStockTemporaryStorageReq.getBizNo());
		return ResultUtils.resultSuccess(aliYunOssUtil.writeTxtToOss(key, checkStockTemporaryStorageReq.getJsonData()));
	}

	@GetMapping("/getTemporaryStorageInfo")
	@ApiOperation(value = "查询暂存信息", notes = "bizNo=盘点任务编号")
	public Result<String> getTemporaryStorageInfo(String bizNo) throws Exception {
		Integer total = erpCheckStockTaskHeadRepo.countTaskHeadByTodoStatus(bizNo);
		if (total <= 0) {
			log.info("暂存信息- 盘点任务编号:{} 盘点任务不为待盘点，不查询暂存", bizNo);
			return ResultUtils.resultSuccess();
		}
		bizNo = CheckStockBuilder.temporaryStorageKeyBuild(bizNo);
		return ResultUtils.resultSuccess(aliYunOssUtil.readTxtFromOss(bizNo));
	}

}
