package com.chongho.erp.controller.stock.actual;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.framework.autoconfigure.common.exception.BusinessException;
import com.chongho.erp.common.enums.ErrorCodeEnum;
import com.chongho.erp.common.util.DateUtil;
import com.chongho.erp.common.util.EasyPoiUtil;
import com.chongho.erp.common.util.TenantUtil;
import com.chongho.erp.config.BasicsConfig;
import com.chongho.erp.dto.query.stock.ErpActualInFlowQuery;
import com.chongho.erp.dto.stock.actual.*;
import com.chongho.erp.po.ErpTenantPO;
import com.chongho.erp.po.ErpUserPO;
import com.chongho.erp.service.stock.actual.IErpActualInFlowService;
import com.chongho.erp.service.stock.actual.IErpActualOutFlowService;
import com.chongho.erp.vo.stock.actual.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 实仓 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08 17:56:35
 */
@RestController
@RequestMapping(value = "/actual")
@Api(tags = {"实仓业务流水管理"})
@Validated
public class ErpActualController {

    @Autowired
    private IErpActualOutFlowService erpActualOutFlowService;
    @Autowired
    private IErpActualInFlowService erpActualInFlowService;
    @Autowired
    private TenantUtil tenantUtil;
    @Autowired
    private BasicsConfig basicsConfig;
    @ApiOperation(value = "实仓其他出库-新增")
    @PostMapping(value = "/out/other/add")
    public void otherAdd(@RequestBody ErpActualOutFlowAddDTO addDTO) {
        erpActualOutFlowService.otherAdd(addDTO);
    }

    @ApiOperation(value = "实仓其他出库-新增V2")
    @PostMapping(value = "/out/other/add/V2")
    public void otherAddV2(@RequestBody ErpActualOutFlowAddV2DTO addDTO) {
        if(Objects.nonNull(basicsConfig) && basicsConfig.getTransferOperateFlag()){
            erpActualOutFlowService.otherAddV2(addDTO);
        }else{
            throw new BusinessException(ErrorCodeEnum.SYSTEM_EXCEPTION.getCode(), "实仓出库新增禁用");
        }
    }


    @ApiOperation(value = "实仓其他出库-删除")
    @PostMapping(value = "/out/other/del")
    public void otherDel(@RequestBody List<Long> ids) {
    }

    @ApiOperation(value = "实仓其他出库-审批")
    @PostMapping(value = "/out/other/apply")
    public void otherApply(@RequestBody ErpApplyDTO applyDTO) {
        tenantUtil.subToParentCompany();
        erpActualOutFlowService.otherApply(applyDTO);
    }

    @ApiOperation(value = "实仓入库-新增")
    @PostMapping(value = "/in/other/add")
    public void inOtherAdd(@RequestBody ErpActualInFlowAddDTO addDTO) {
        erpActualInFlowService.add(addDTO);
    }

    @ApiOperation(value = "实仓入库-新增V2")
    @PostMapping(value = "/in/other/add/v2")
    public void inOtherAdd(@RequestBody ErpActualInFlowAddV2DTO addDTO) {
        if(Objects.nonNull(basicsConfig) && basicsConfig.getTransferOperateFlag()){
            erpActualInFlowService.addV2(addDTO);
        }else {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_EXCEPTION.getCode(), "实仓入库新增禁用");
        }
    }

    @ApiOperation(value = "实仓入库-修改")
    @PostMapping(value = "/in/other/edit")
    public void inOtherEdit(@RequestBody ErpActualInFlowAddV2DTO editDTO) {
        erpActualInFlowService.editNew(editDTO);
    }

    @ApiOperation(value = "实仓入库-删除")
    @PostMapping(value = "/in/other/del")
    public void inOtherDel(@RequestBody List<Long> ids) {
        erpActualInFlowService.del(ids);
    }

    @ApiOperation(value = "实仓入库-审批")
    @PostMapping(value = "/in/other/apply")
    public void inOtherApply(@RequestBody ErpApplyDTO applyDTO) {
        erpActualInFlowService.apply(applyDTO);
    }

    @ApiOperation(value = "实仓入库管理-分页列表")
    @PostMapping(value = "/in/page")
    public Page<ErpActualInFlowInfoVO> pageInFlow(@RequestBody ErpActualInFlowQuery query) {
        ErpUserPO user = tenantUtil.getUser();
        query.setTenantId(user.getTenantId());
        return erpActualInFlowService.pageInFlow(query);
    }
    @ApiOperation(value = "实仓入库管理-详情")
    @GetMapping(value = "/in/detail")
    public ErpActualInFlowAndDetailVO inDetail(@RequestParam("id") Long id) {
        tenantUtil.subToParentCompany();
        return erpActualInFlowService.inDetail(id);
    }

    @ApiOperation(value = "实仓出库管理-其他出库-分页列表")
    @GetMapping(value = "/in/other/page")
    public Page<ErpActualOtherFlowInfoV2VO> pageOtherFlow(ErpActualInFlowQuery query) {
        tenantUtil.subToParentCompany();
        return erpActualOutFlowService.pageOtherFlowV2(query);
    }
    @ApiOperation(value = "实仓出库管理-其他出库-分页列表导出")
    @PostMapping(value = "/in/other/exportOtherFlowList")
    public void exportOtherFlowList(HttpServletResponse response, @RequestBody ErpActualInFlowQuery query) {
        tenantUtil.subToParentCompany();
        List<ExportErpActualOtherFlowInfoVO> erpActualOtherFlowInfoVOList = erpActualOutFlowService.exportOtherFlowList(response, query);
        EasyPoiUtil easyPoiUtil = new EasyPoiUtil();
        try {
            String fileName = String.format("实物库存管理-其他出库_%s", DateUtil.format(new Date(), "yyyyMMdd"));
            easyPoiUtil.downloadField(response, fileName, ExportErpActualOtherFlowInfoVO.class, erpActualOtherFlowInfoVOList, query.getExportParams());
        } catch (Exception e) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_EXCEPTION.getCode(), "实物库存管理-其他出库导出异常"+e);
        }
    }

    @ApiOperation(value = "实仓入库管理-其他出库-详情")
    @GetMapping(value = "/in/other/detail")
    public ErpActualOtherFlowAndDetailVO otherDetail(@RequestParam("id") Long id) {
        tenantUtil.subToParentCompany();
        return erpActualOutFlowService.otherDetail(id);
    }

    @ApiOperation(value = "实仓入库-审批")
    @PostMapping(value = "/in/other/batchApply")
    public void inOtherBatchApply(@RequestBody @Valid ErpApplyDTO applyDTO) {
        tenantUtil.subToParentCompany();
        erpActualInFlowService.batchApply(applyDTO);
    }
}
