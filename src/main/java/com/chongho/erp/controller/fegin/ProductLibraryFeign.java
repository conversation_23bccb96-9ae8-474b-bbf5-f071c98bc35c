package com.chongho.erp.controller.fegin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.framework.autoconfigure.common.result.Result;
import com.chongho.erp.common.util.ResultUtils;
import com.chongho.erp.dto.productLibrary.ProductLibraryQuery;
import com.chongho.erp.po.ErpTenantPO;
import com.chongho.erp.repository.productLibrary.ErpProductLibraryRepo;
import com.chongho.erp.vo.productLibrary.ProductLibraryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/feign/productLibrary")
@Api(tags = {"产品库Feign"})
public class ProductLibraryFeign {

    @Autowired
    private ErpProductLibraryRepo erpProductLibraryRepo;

    @PostMapping(value = "/list")
    @ApiOperation(value = "列表查询")
    public Result<List<ProductLibraryVO>> listByProductNos(@RequestBody ProductLibraryQuery query) {
        Page<Object> objectPage = new Page<>();
        objectPage.setSearchCount(false);
        objectPage.setSize(-1);
        return ResultUtils.resultSuccess(erpProductLibraryRepo.listPages(objectPage,query).getRecords());
    }

    @PostMapping(value = "/listPages")
    @ApiOperation(value = "分页查询")
    public Result<Page<ProductLibraryVO>> listPages(@RequestBody ProductLibraryQuery query) {
        Page<Object> objectPage = new Page<>(query.getNumber(), query.getPageSize());
        return ResultUtils.resultSuccess(erpProductLibraryRepo.listPages(objectPage, query));
    }
}
