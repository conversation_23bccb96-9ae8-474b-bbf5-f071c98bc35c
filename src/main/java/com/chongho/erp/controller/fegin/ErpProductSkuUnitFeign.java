package com.chongho.erp.controller.fegin;

import com.cfpamf.framework.autoconfigure.common.result.Result;
import com.chongho.erp.common.util.ResultUtils;
import com.chongho.erp.po.ErpMaterialCurrentStockPO;
import com.chongho.erp.po.material.EcpProductSkuUnitPO;
import com.chongho.erp.repository.material.EcpProductSkuUnitRepo;
import com.chongho.erp.service.ErpMaterialCurrentStockRepo;
import com.chongho.erp.vo.material.ErpDepotProductSkuVo;
import com.chongho.erp.vo.material.ProductSkuUnitVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping(value = "/feign/skuUnit")
@Api(tags = {"sku单位"})
public class ErpProductSkuUnitFeign {

    @Resource
    private EcpProductSkuUnitRepo ecpProductSkuUnitRepo;

    @Autowired
    private ErpMaterialCurrentStockRepo erpMaterialCurrentStockRepo;

    @ApiOperation("通过skuId及规格分类获取货品规格单位信息列表")
    @PostMapping("/batchFindUnitListBySkuIdAndType")
    public List<ProductSkuUnitVO> batchFindUnitListBySkuIdAndType(@RequestBody List<String> skuIds, @RequestParam("type") String unitTypeCode) {
        return ecpProductSkuUnitRepo.batchFindUnitListBySkuIdAndType(skuIds, unitTypeCode);
    }

    @ApiOperation("通过skuUnitCode获取sku单位信息列表")
    @PostMapping("/getListBySkuUnitCode")
    public List<ProductSkuUnitVO> getListBySkuUnitCode(@RequestBody List<String> skuUnitCode){
        List<EcpProductSkuUnitPO> ecpProductSkuUnitPOS = ecpProductSkuUnitRepo.getListBySkuUnitCode(skuUnitCode);
        return ecpProductSkuUnitPOS.stream().map(ecpProductSkuUnitPO -> {
            ProductSkuUnitVO productSkuUnitVO = new ProductSkuUnitVO();
            BeanUtils.copyProperties(ecpProductSkuUnitPO, productSkuUnitVO);
            return productSkuUnitVO;
        }).collect(Collectors.toList());
    }


    @ApiOperation(value = "根据分支编码和Sku获取实仓库存信息")
    @GetMapping("/getCurrentStockListByBranchCodeAndSkuIds")
    public List<ErpDepotProductSkuVo> getCurrentStockListByBranchCodeAndSkuIds(@RequestParam String branchCode, @RequestParam List<String> skuIds) {
        return erpMaterialCurrentStockRepo.getCurrentStockListByBranchCodeAndSkuIds(branchCode, skuIds);
    }
}
