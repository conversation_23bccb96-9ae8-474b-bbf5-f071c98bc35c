package com.chongho.erp.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-13 14:26:43
 */
@Getter
@Setter
@TableName("erp_user")
@ApiModel(value = "ErpUserPO对象", description = "用户表")
public class ErpUserPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("用户姓名--例如张三")
    private String username;

    @ApiModelProperty("登录用户名")
    private String loginName;

    @ApiModelProperty("登陆密码")
    private String password;

    @ApiModelProperty("员工编码")
    private String employNo;

    @ApiModelProperty("员工名称")
    private String employName;

    @ApiModelProperty("职位")
    private String position;

    @ApiModelProperty("所属部门")
    private String department;

    @ApiModelProperty("电子邮箱")
    private String email;

    @ApiModelProperty("手机号码")
    private String phonenum;

    @ApiModelProperty("是否为管理者 0==管理者 1==员工")
    private Integer ismanager;

    @ApiModelProperty("是否系统自带数据 ")
    private Integer isystem;

    @ApiModelProperty("状态，0：正常，1：删除，2封禁")
    private Integer status;

    @ApiModelProperty("用户描述信息")
    private String description;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("员工登录bms信息")
    @TableField(exist = false)
    private String bmsToken;

    @ApiModelProperty("组织编码")
    @TableField(exist = false)
    private String companyCode;
}
