
package com.chongho.erp.po.stock.actual;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.cfpamf.framework.autoconfigure.mybatis.BaseEtyEnhancePo;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;


/**
 * 
 * Created by <PERSON><PERSON><PERSON>  on 2022-09-13 19:59:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_actual_check_detail")
@ApiModel(value="ErpActualCheckDetailPO对象", description="")
public class ErpActualCheckDetailPO extends BaseEtyEnhancePo{

	private static final long serialVersionUID = 1L;


	@ApiModelProperty("流水编号")
	private String bizNo;

	@ApiModelProperty("批次号")
	private String batchNo;

	@ApiModelProperty("货品sku编码")
	private String skuNo;

	@ApiModelProperty("物料编码")
	private String materialCode;

	@ApiModelProperty("商品计量单位")
	private String materialUnit;

	@ApiModelProperty("流水方向(出库-2/入库-1)")
	private Integer flowDirect;

	@ApiModelProperty("变更数量")
	private java.math.BigDecimal changeNumber;

	@ApiModelProperty("盘点数量")
	private java.math.BigDecimal actualNumber;

	@ApiModelProperty("账面快照数量")
	private java.math.BigDecimal snapNumber;

	@ApiModelProperty("仓库id")
	private Long depotId;

	@ApiModelProperty("备注")
	private String remark;

	@ApiModelProperty("租户id")
	private Long tenantId;

}

