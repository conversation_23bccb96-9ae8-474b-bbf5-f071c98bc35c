package com.chongho.erp.po.depotSku;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cfpamf.framework.autoconfigure.mybatis.BaseEtyPo;
import com.chongho.erp.common.enums.depotSku.OperationTypeEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仓库货品申领单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-13 14:45:11
 */
@Getter
@Setter
@TableName("erp_depot_sku_apply")
@ApiModel(value = "ErpDepotSkuApplyPo对象", description = "仓库货品申领单表")
public class ErpDepotSkuApplyPo extends BaseEtyPo {

    @ApiModelProperty("申领单编码")
    private String applyCode;

    @ApiModelProperty("申领仓库(调出仓)名称")
    private String depotOutName;

    @ApiModelProperty("申领仓库(调出仓)编码")
    private String depotOutCode;

    @ApiModelProperty("申领仓库(调出仓)名称")
    private String depotInName;

    @ApiModelProperty("领货仓库(调入仓)编码")
    private String depotInCode;

    @ApiModelProperty("申领人id")
    private String applicantId;

    @ApiModelProperty("申领人名称")
    private String applicantName;

    @ApiModelProperty("申领数量")
    private BigDecimal applyCount;

    /**
     * 申领单状态 {@link com.chongho.erp.common.enums.depotSku.DepotSkuApplyStateEnum}，
     */
    private Integer applyState;

    /**
     * 申领单货品状态 {@link com.chongho.erp.common.enums.depotSku.DepotSkuApplyGoodsStateEnum}，
     */
    private Integer goodsState;


    @ApiModelProperty("领货方式 默认1:仓库自提")
    private Integer applyWay;

    @ApiModelProperty("申领备注")
    private String remark;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("领货备注")
    private String takeDeliveryRemark;

    @ApiModelProperty("创建人id")
    private String createUserId;

    @ApiModelProperty("创建人名称")
    private String createUserName;

    @ApiModelProperty("更新人id")
    private String updateUserId;

    @ApiModelProperty("更新人名称")
    private String updateUserName;

    /**
     * 操作类型：0-人工 1-自动 {@link OperationTypeEnum}，
     */
    private Integer operationType;

    /**
     * 操作类型：0-人工 1-自动 {@link OperationTypeEnum}，
     */
    private String operationTypeDesc;

    /**
     * 申领操作角色
     */
    private String operationRoleName;

    /**
     * 系统自动处理节点 {@link com.chongho.erp.common.enums.depotSku.AutoHandleNodeEnum}，
     */
    private String autoHandleNode;

    /**
     * 申领金额
     */
    private BigDecimal applyAmount;

}
