package com.chongho.erp.po.inventoryCapacity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;

import com.cfpamf.framework.autoconfigure.mybatis.BaseEtyEnhancePo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 机构库存总余量变更快照表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-04 11:45:54
 */
@Getter
@Setter
@TableName("erp_org_total_inventory_capacity_flow")
@ApiModel(value = "ErpOrgTotalInventoryCapacityFlowPO对象", description = "机构库存总余量变更快照表")
public class OrgTotalInventoryCapacityFlowPO extends BaseEtyEnhancePo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("机构库存总余量概览表的主键id")
    private Long headId;

    @ApiModelProperty("库存流水编号")
    private String flowNo;

    @ApiModelProperty("库存流水变更类型")
    private Integer stockBizType;

    @ApiModelProperty("库存变动方向")
    private Integer runType;

    @ApiModelProperty("仓库编码")
    private String deptCode;

    @ApiModelProperty("库存类型：1-销仓 2-实仓")
    private Integer stockType;

    @ApiModelProperty("变动内容编码")
    private String changedContentCode;

    @ApiModelProperty("变动内容名称")
    private String changedContentName;

    @ApiModelProperty("期初数量")
    private BigDecimal openingCount;

    @ApiModelProperty("变动数量")
    private BigDecimal changedCount;

    @ApiModelProperty("期末数量")
    private BigDecimal closingCount;

    @ApiModelProperty("期末剩余可入库数量")
    private BigDecimal remainStorableCount;

    @ApiModelProperty("业务场景编码")
    private Integer bizTypeSceneCode;

    @ApiModelProperty("业务场景外部编号")
    private String bizSceneNo;

    @ApiModelProperty("发车单号")
    private String deliveryNo;

    @ApiModelProperty("是否跨区域")
    private Boolean crossAreaFlag;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("租户id")
    private Long tenantId;

}
