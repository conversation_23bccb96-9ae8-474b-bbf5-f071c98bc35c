package com.chongho.erp.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.cfpamf.framework.autoconfigure.mybatis.BaseEtyEnhancePo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 单据明细扩展表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06 15:11:38
 */
@Getter
@Setter
@TableName("erp_depot_item_extend")
@ApiModel(value = "ErpDepotItemExtendPO对象", description = "单据明细扩展表")
public class ErpDepotItemExtendPO  extends BaseEtyEnhancePo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("明细id")
    private Long itemId;

    @ApiModelProperty("要货单号")
    private String requireNo;

    @ApiModelProperty("批次号")
    private String batchNumber;

    @ApiModelProperty("skuId")
    private String skuId;

    @ApiModelProperty("库存分类1-销售仓库 2-实物仓库")
    private Integer stockType;

    @ApiModelProperty("业务类型 1-销售单 2-采购提报单 3-拼车单 4-人工 5-调拨 6-盘点 7-取消占用")
    private Integer businessType;

    @ApiModelProperty("类型：1-入库 2-出库")
    private Integer runType;

    @ApiModelProperty("数量")
    private BigDecimal changeNumber;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("渠道 1.ERP 2.stock")
    private Integer channel;

    @ApiModelProperty("仓库ID")
    private Long depotId;

    @ApiModelProperty(value = "仓库编号")
    private String depotCode;

}
