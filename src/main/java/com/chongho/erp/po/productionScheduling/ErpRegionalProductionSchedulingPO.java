package com.chongho.erp.po.productionScheduling;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cfpamf.framework.autoconfigure.mybatis.BaseEtyEnhancePo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("erp_regional_production_scheduling")
@ApiModel(value = "ErpRegionalProductionSchedulingPO对象", description = "排产单表")
public class ErpRegionalProductionSchedulingPO extends BaseEtyEnhancePo {


    @ApiModelProperty("排产编号")
    private String productionSchedulingNo;

    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("状态：1-待审核、2-待受理、3-待生产、4-部分生产、5-已完成、6-已关闭")
    private Integer status;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("机构编码")
    private String orgCode;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("租户id")
    private Long tenantId;



}
