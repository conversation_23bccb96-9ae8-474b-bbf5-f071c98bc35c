package com.chongho.erp.req;

import com.alibaba.excel.annotation.ExcelProperty;
import com.cfpamf.framework.autoconfigure.mybatis.page.PageQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CheckDiffStockQuery extends PageQuery {

    @ApiModelProperty(value = "盘点任务编码，精确搜索")
    private String bizNo;

    @ApiModelProperty(value = "机构编码集合")
    private List<String> orgCodeList;

    @ApiModelProperty(value = "权限集合",hidden = true)
    private List<String> permissionOrgCodeList;

    @ApiModelProperty(value = "登录人工号")
    private String loginEmployeeNo;

    @ApiModelProperty(value = "监盘人任务编码集合",hidden = true)
    private List<String> monitoringPersonnelBizNoList;

    @ApiModelProperty(value = "组织编码")
    private String companyCode;

    @ApiModelProperty(value = "货品名称，模糊搜索")
    private String productName;

    @ApiModelProperty("规格物料编码，模糊搜索")
    private String skuMaterialCode;

    @ApiModelProperty("规格编码，精确搜索")
    private String skuId;

    @ApiModelProperty(value = "盘点状态 10-待盘点 20-已盘点 30-已过期")
    private List<Integer> statusList;

    @ApiModelProperty("任务类型 1-周任务 2-月任务")
    private List<Integer> typeList;

    @ApiModelProperty("盘点当时差异数量运算符号:<,<=,>,>=,=,≠")
    private String operatorSymbol;

    @ApiModelProperty("盘点当时差异数量，操作值")
    private BigDecimal operatorValue;

    @ApiModelProperty("盘点当时结余差异数量运算符号:<,<=,>,>=,=,≠")
    private String operatorSettleSymbol;

    @ApiModelProperty("盘点结余差异数量，操作值")
    private BigDecimal operatorSettleValue;

    @ApiModelProperty("货品分类末级编码集合")
    private List<String> categoryCodeList;

    @ApiModelProperty(value = "库存结余开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date settleStartTime;

    @ApiModelProperty(value = "库存结余结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date settleEndTime;

    @ApiModelProperty(value = "skuId集合",hidden = true)
    private List<String> skuIdList;

    @ApiModelProperty(value = "库存结余开始时间")
    private String settleStartTimeStr;

    @ApiModelProperty(value = "库存结余结束时间")
    private String settleEndTimeStr;

    @ApiModelProperty(value = "盘点开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date checkStockTimeStartStr;

    @ApiModelProperty(value = "盘点结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date checkStockTimeEndStr;

    @ApiModelProperty(value = "库存结余开始时间")
    private String checkStockTimeStart;

    @ApiModelProperty(value = "库存结余结束时间")
    private String checkStockTimeEnd;

    @ApiModelProperty("是否有差异=0:否,1:是")
    private Integer isDiff;

    @ApiModelProperty("货品分类")
    private List<Integer> productType;

    @ApiModelProperty("是否导出模版文件")
    private boolean isTemplateExport;

    @ApiModelProperty(value = "是否是导出")
    private boolean isExport = false;

    @ApiModelProperty("差异类型")
    private String diffType;

}
