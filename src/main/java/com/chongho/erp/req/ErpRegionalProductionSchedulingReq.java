package com.chongho.erp.req;

import com.cfpamf.framework.autoconfigure.common.exception.BusinessException;
import com.chongho.erp.dto.productionScheduling.ErpRegionalProductionSchedulingProductDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NonNull;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
public class ErpRegionalProductionSchedulingReq {

    @ApiModelProperty("机构编码")
    @NotBlank(message = "机构编码不能为空")
    private String orgCode;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("排产编号")
    private String productionSchedulingNo;

    @ApiModelProperty("供应商编码")
    @NotBlank(message = "供应商编码不能为空")
    private String supplierCode;

    @ApiModelProperty("供应商编码")
    private String supplierName;

    @ApiModelProperty("货品类型：1-单品 2-套装")
    @NotNull(message = "货品类型不能为空")
    private Integer productType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("货品信息")
    @Valid
    @NotEmpty(message = "货品信息不能为空")
    private List<ErpRegionalProductionSchedulingProductDTO> productList;

    @ApiModelProperty("排产函提报-附件")
    private List<Long> fileIds;

    public void validData() {
        if (this.productList != null && !this.productList.isEmpty()) {
            // 校验productList，skuNo + procurementSubmissionCode不允许重复
            Map<String, Long> countMap = this.productList.stream()
                .collect(Collectors.groupingBy(detail -> detail.getSkuNo() + "_" + detail.getProcurementSubmissionCode(), Collectors.counting()));
            Optional<Map.Entry<String, Long>> duplicateEntry = countMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .findFirst();
            if (duplicateEntry.isPresent()) {
                throw new BusinessException("明细中存在重复的规格编码 + 采购提报单编码组合");
            }

            for (ErpRegionalProductionSchedulingProductDTO dto : productList) {
                dto.validData();
            }
        }
    }
}
