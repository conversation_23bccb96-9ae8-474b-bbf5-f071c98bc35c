package com.chongho.erp.req;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CheckStockDepotQueryReq implements java.io.Serializable {
	
	private static final long serialVersionUID = -5044847689499905708L;

	@NotBlank(message = "组织编码不能为空")
	@ApiModelProperty(value = "组织编码")
    private String companyCode;

	@NotBlank(message = "机构编码不能为空")
    @ApiModelProperty("机构编码")
    private String orgCode;

}
