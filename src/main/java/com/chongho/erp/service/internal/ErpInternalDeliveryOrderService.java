package com.chongho.erp.service.internal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.chongho.erp.dto.depot.ErpDepotHeadAndItemDTO;
import com.chongho.erp.dto.internalDeliverOrder.*;
import com.chongho.erp.dto.stock.actual.ErpApplyDTO;
import com.chongho.erp.po.ErpOperateLogPO;
import com.chongho.erp.po.internal.ErpInternalDeliveryOrderDetailPO;
import com.chongho.erp.po.internal.ErpInternalDeliveryOrderPO;
import com.chongho.erp.po.internal.ErpInternalOrderDeliverySummaryPO;
import com.chongho.erp.vo.internalDeliverOrder.*;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Created by ls  on 2025-03-17 20:10:41
 */

public interface ErpInternalDeliveryOrderService extends IService<ErpInternalDeliveryOrderPO>{
    /**
     * 内采发货单-分页列表
     * @param query
     * @return
     */
    Page<ErpInternalDeliverOrderListVO> internalDeliverOrderPage(ErpInternalDeliverOrderListQuery query) ;
    /**
     * 内采发货单-详情
     * @param id
     * @return
     */
    ErpInternalDeliverOrderDetailVO internalDeliverOrderDetail(Long id);
    /**
     * 内采发货单-新增
     * @param addDTO
     */
    Long internalDeliverOrderAdd(ErpInternalDeliverOrderAddDTO addDTO);
    /**
     * 内采发货单-修改
     * @param editDTO
     */
    void internalDeliverOrderEdit(ErpInternalDeliverOrderAddDTO editDTO);
    /**
     * 内采发货单-审批
     * @param applyDTO
     */
    void internalDeliverOrderApply(ErpApplyDTO applyDTO);
    /**
     * 内采发货单-导出
     * @param query
     */
    FileDTO internalDeliverOrderExport(ErpInternalDeliverOrderListQuery query);
    /**
     * 修改或保存
     * */
    Long saveOrUpdateInternalDeliveryOrder(ErpInternalDeliverOrderDataDTO dataDTO);
    /**
     * 审批内采发货单流水生成及状态变更
     * */
    void applyInternalDeliverOrder(ErpDepotHeadAndItemDTO depotHeadAndItemDTO, ErpInternalDeliveryOrderPO deliveryOrder
            , List<ErpInternalDeliveryOrderDetailPO> internalDeliveryOrderDetailList, ErpOperateLogPO operateLog);
    /**
     * 内采发货单出库-物料
     * */
    Page<ErpInternalOutStockInfoVO> getInternalOutStockProductSkuList(ErpInternalOutStockOrderProductSkuQuery query);
   /**
     * 内采发货单入库-物料
     * */
    Page<ErpInternalInStockInfoVO> getInternalInStockProductSkuList(ErpInternalInStockOrderProductSkuQuery query);
    /**
     * 内采发货单出库-汇总
     * */
    Page<ErpInternalDeliverOrderInfoVO> exportinternalDeliverOrderInfoPage(ErpInternalDeliverOrderListQuery query);
    /**
     * 内采发货单出库数量校验
     * */
    void checkInternalDeliverOrderNum(Integer direction,String tradeCompanyCode,String relatedDeliveryOrderCode,List<ErpInternalDeliveryOrderDetailPO> detailList);
    /**
     * 获取内采销售发运汇总数据
     * */
    Map<String, ErpInternalOrderDeliverySummaryPO> getInternalOrderDeliverySummary(List<String> skuIdList, String tradeCompanyCode);
    /**
     * 获取内采发货出库数
     * */
    Map<String, BigDecimal> getDeliveryOrderOutQty(String deliveryOrderCode, List<String> skuIdList, String tradeCompanyCode);
}
