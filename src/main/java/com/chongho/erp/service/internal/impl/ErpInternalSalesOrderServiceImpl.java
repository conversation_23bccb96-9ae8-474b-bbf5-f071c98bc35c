package com.chongho.erp.service.internal.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.framework.autoconfigure.common.exception.BusinessException;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileOperationVO;
import com.chongho.erp.common.enums.*;
import com.chongho.erp.common.enums.flow.StockBusinessEnum;
import com.chongho.erp.common.enums.internal.InternalGenerateMethodEnum;
import com.chongho.erp.common.smartid.SmartIdHelper;
import com.chongho.erp.common.util.AsyncExecutorUtil;
import com.chongho.erp.common.util.DateUtil;
import com.chongho.erp.convertor.internal.ErpInternalDeliveryOrderConvertor;
import com.chongho.erp.dto.depot.DepotQuery;
import com.chongho.erp.dto.internal.*;
import com.chongho.erp.dto.material.EcpProductSkuSupDTO;
import com.chongho.erp.dto.material.ProductSkuDetailDTO;
import com.chongho.erp.dto.query.ErpSkuDepotItemQuery;
import com.chongho.erp.po.*;
import com.chongho.erp.po.internal.*;
import com.chongho.erp.po.purchase.ErpPurchasePricesItemPO;
import com.chongho.erp.repository.ErpOperateLogRepo;
import com.chongho.erp.repository.internal.ErpInternalOrderDeliverySummaryRepo;
import com.chongho.erp.repository.internal.ErpInternalSalesOrderDetailRepo;
import com.chongho.erp.repository.internal.ErpInternalSalesOrderFileRepo;
import com.chongho.erp.repository.internal.ErpInternalSalesOrderRepo;
import com.chongho.erp.repository.material.EcpProductSkuRepo;
import com.chongho.erp.repository.purchase.ErpPurchasePricesItemRepo;
import com.chongho.erp.service.*;
import com.chongho.erp.service.internal.ErpInternalSalesOrderService;
import com.chongho.erp.service.material.IProductSkuService;
import com.chongho.erp.service.stock.sell.IInternalOrderSellFlowService;
import com.chongho.erp.thirdpart.api.file.FileCenterIntegration;
import com.chongho.erp.vo.internal.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ErpInternalSalesOrderServiceImpl implements ErpInternalSalesOrderService {
    @Autowired
    private ErpInternalSalesOrderRepo internalSalesOrderRepo;

    @Autowired
    private ErpInternalSalesOrderDetailRepo internalSalesOrderDetailRepo;

    @Autowired
    private ErpInternalSalesOrderFileRepo internalSalesOrderFileRepo;

    @Autowired
    private EcpProductSkuRepo ecpProductSkuRepo;

    @Autowired
    private ErpPurchasePricesItemRepo purchasePricesItemRepo;

    @Autowired
    private ErpProcurementSubmissionHeadRepo erpProcurementSubmissionHeadRepo;

    @Autowired
    private ErpProcurementReturnHeadRepo erpProcurementReturnHeadRepo;

    @Autowired
    private ErpProcurementSubmissionItemRepo erpProcurementSubmissionItemRepo;

    @Autowired
    private ErpInternalOrderDeliverySummaryRepo erpInternalOrderDeliverySummaryRepo;

    @Autowired
    private IInternalOrderSellFlowService internalOrderSellFlowService;

    @Autowired
    private IProductSkuService productSkuService;

    @Autowired
    private ErpDepotRepo erpDepotRepo;

    @Autowired
    private ErpOperateLogRepo erpOperateLogRepo;

    @Autowired
    private ErpTenantRepo erpTenantRepo;

    @Autowired
    private SmartIdHelper smartIdHelper;

    @Autowired
    private FileCenterIntegration fileCenterIntegration;

    @Autowired
    private AsyncExecutorUtil executorUtil;

    @Override
    public Page<ErpInternalSalesOrderVo> queryInternalSalesOrders(ErpInternalSalesOrderQuery queryParams) {
        // 处理查询条件
        boolean isNoData = processCondition(queryParams);
        if (isNoData) {
            return new Page<>();
        }
        Page<ErpInternalSalesOrderPO> poPage = new Page<>(queryParams.getNumber(), queryParams.getPageSize());
        poPage = internalSalesOrderRepo.queryInternalSalesOrders(poPage, queryParams);
        if (CollectionUtil.isEmpty(poPage.getRecords())) {
            return new Page<>();
        }

        return ErpInternalSalesOrderVo.convertPoToVoPage(poPage);
    }

    private boolean processCondition(ErpInternalSalesOrderQuery queryParams) {
        // 通过内采单明细，查询内采单号集合
        List<String> orderCodes = new ArrayList<>();
        // 处理查询条件
        SalesOrderConditionDTO conditionDto = new SalesOrderConditionDTO();
        if (processCommonCondition(queryParams, conditionDto)) {
            return true;
        }

        if (StringUtils.isNotBlank(queryParams.getPurchaseSubCode()) ||
                CollectionUtil.isNotEmpty(conditionDto.getSubCodes()) ||
                CollectionUtil.isNotEmpty(conditionDto.getSkuIdList()) ||
                CollectionUtil.isNotEmpty(conditionDto.getDepotIds())) {
            orderCodes = internalSalesOrderDetailRepo.getOrderCodeList(conditionDto.getSubCodes(), conditionDto.getSkuIdList(), conditionDto.getDepotIds(), queryParams.getPurchaseSubCode());
            if (CollectionUtil.isEmpty(orderCodes)) {
                return true;
            }
        }
        if (StringUtils.isNotBlank(queryParams.getOrderCode()) && CollectionUtil.isNotEmpty(orderCodes)) {
            if (!orderCodes.contains(queryParams.getOrderCode())) {
                return true;
            }
        }
        queryParams.setOrderCodes(orderCodes);
        return false;
    }

    private boolean processCommonCondition(ErpInternalSalesOrderQuery queryParams, SalesOrderConditionDTO conditionDto) {
        if (queryParams.isSearchSku()) {
            ErpSkuDepotItemQuery skuQuery = ErpInternalDeliveryOrderConvertor.CONVERTOR.convertSalesOrderQueryToSkuQuery(queryParams);
            List<EcpProductSkuSupDTO> productSkuSupDTOList = ecpProductSkuRepo.getProSkuSupInfoList(skuQuery);
            if(CollectionUtil.isEmpty(productSkuSupDTOList)){
                return true;
            }
            List<String> skuIdList = productSkuSupDTOList.stream()
                    .map(EcpProductSkuSupDTO::getSkuId)
                    .collect(Collectors.toList());
            conditionDto.setSkuIdList(skuIdList);
        }
        if (StringUtils.isNotBlank(queryParams.getPurchaseCode())
                || StringUtils.isNotBlank(queryParams.getPurchaseSubCode())) {
            //根据主价格单号，获取子价格单号集合
            List<String> subCodes = purchasePricesItemRepo.getSubCodesByOtherCode(queryParams.getPurchaseCode(), queryParams.getPurchaseSubCode());
            if (CollectionUtil.isEmpty(subCodes)) {
                return true;
            }
            conditionDto.setSubCodes(subCodes);
        }
        List<Long> depotIds = new ArrayList<>();
        if (StringUtils.isNotBlank(queryParams.getDepotName())) {
            DepotQuery depotQuery = new DepotQuery();
            depotQuery.setName(queryParams.getDepotName());
            depotQuery.setStockType(DepotStockTypeEnum.SELL_STOCK.getValue());
            List<ErpDepotPO> depotList = erpDepotRepo.getDepotPOList(depotQuery);
            if(CollectionUtil.isEmpty(depotList)){
                return true;
            }
            depotIds = depotList.stream().map(ErpDepotPO::getId).collect(Collectors.toList());
        }
        // 前端仓库Id集合和仓库名称取到的Id集合取交集
        if (CollectionUtil.isNotEmpty(depotIds) && CollectionUtil.isNotEmpty(queryParams.getDepotIds())) {
            depotIds = depotIds.stream().filter(queryParams.getDepotIds()::contains).collect(Collectors.toList());
        } else {
            depotIds = CollectionUtil.isNotEmpty(depotIds) ? depotIds : queryParams.getDepotIds();
        }
        conditionDto.setDepotIds(depotIds);
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createInternalSalesOrder(@Valid ErpInternalSalesOrderDTO order, ErpUserPO user) {
        // 内采销售退订单，路由到退订单创建
        if (StockBusinessEnum.INTERNAL_SALES_ORDER_IN_TYPE.getValue().equals(order.getOrderType())) {
            createSalesRefundOrder(order, user);
            return;
        }
        // 参数验证
        order.validData();
        // 组装内采单主表
        ErpInternalSalesOrderPO orderPo = null;
        if (InternalGenerateMethodEnum.HAND_ADD.getValue().equals(order.getGenerateMethod())) {
            // 根据价格子单号，获取内采供货价
            List<String> subCodes = order.getDetailList().stream().map(ErpInternalSalesOrderDetailDTO::getPurchaseSubCode).collect(Collectors.toList());
            Map<String, BigDecimal> unitPriceMap = purchasePricesItemRepo.getUnitPriceMap(subCodes);
            // 获取采购价
            ErpTenantPO tradeTenant = erpTenantRepo.getTenantByCompanyCode(order.getTradeCompanyCode());
            List<ErpProcurementSubmissionItemPO> itemList = erpProcurementSubmissionItemRepo.getListByCodeWithoutTenant(order.getProcurementSubmissionCode(), tradeTenant.getTenantId());
            // 检查内采供货价是否等于采购价
            order.checkUnitPrice(itemList, unitPriceMap);
            orderPo = order.convertToPo(smartIdHelper, erpTenantRepo, user.getTenantId(), unitPriceMap);
        } else {
            orderPo = order.convertToPo(smartIdHelper, erpTenantRepo, user.getTenantId(), null);
        }
        // 组装内采单详情
        List<ErpInternalSalesOrderDetailPO> detailPos = order.convertToDetailPos(orderPo.getOrderCode());
        // 组装内采单附件
        List<ErpInternalSalesOrderFilePO> filePos = order.convertToFilePos(orderPo.getOrderCode());
        // 保存内采单主表
        internalSalesOrderRepo.saveWithoutLoginTenant(orderPo);
        if (CollectionUtil.isNotEmpty(detailPos)) {
            internalSalesOrderDetailRepo.saveBatch(detailPos);
        }
        if (CollectionUtil.isNotEmpty(filePos)) {
            internalSalesOrderFileRepo.saveBatch(filePos);
        }
        // 记录操作轨迹
        ErpOperateLogPO createLog = createSalesOrderLog(orderPo.getOrderCode(), "创建", "成功", orderPo.getRemark(), user, orderPo.getTenantId());
        erpOperateLogRepo.saveWithoutLoginTenant(createLog);
    }

    @Override
    public ErpInternalSalesOrderDetailVo detail(String orderCode) {
        ErpInternalSalesOrderPO orderPo = internalSalesOrderRepo.getByCode(orderCode);
        if (ObjectUtil.isNull(orderPo)) {
            throw new BusinessException(ErpBizErrorEnum.PARAM_INVALID.getCode(), "参数错误，内采销售单不存在");
        }
        ErpInternalSalesOrderDetailVo vo = ErpInternalDeliveryOrderConvertor.CONVERTOR.salesOrderPoToDetailVo(orderPo);
        // 查询内采单明细
        List<ErpInternalSalesOrderDetailPO> detailPos = internalSalesOrderDetailRepo.getListByOrderCode(orderCode);
        if (CollectionUtil.isNotEmpty(detailPos)) {
            List<ErpInternalSalesOrderDetailItemVo> detailVos = ErpInternalDeliveryOrderConvertor.CONVERTOR.salesOrderDetailPoToVoList(detailPos);
            if (StockBusinessEnum.INTERNAL_SALES_ORDER_IN_TYPE.getValue().equals(orderPo.getOrderType())) {
                // 逆向内采销售单，需要获取正向的采购数量和采购金额
                List<ErpInternalSalesOrderDetailPO> forwardDetailPos = internalSalesOrderDetailRepo.getListByOrderCode(orderPo.getRelatedOrderCode());
                for (ErpInternalSalesOrderDetailItemVo detailVo : detailVos) {
                    ErpInternalSalesOrderDetailPO forwardPo = forwardDetailPos.stream().filter(d -> d.getSkuId().equals(detailVo.getSkuId())
                                    && d.getPurchaseSubCode().equals(detailVo.getPurchaseSubCode())
                                    && d.getDepotId().equals(detailVo.getDepotId()))
                            .findFirst()
                            .orElseThrow(() -> new BusinessException(ErpBizErrorEnum.PARAM_INVALID.getCode(), "参数错误，正向内采销售单明细不存在"));
                    detailVo.setSalesQty(forwardPo.getQuantity());
                    detailVo.setSalesAmount(forwardPo.getAmount());
                }
            }
            //查询货品列表信息
            List<String> skuIdList = detailVos.stream()
                    .map(ErpInternalSalesOrderDetailItemVo::getSkuId)
                    .distinct().collect(Collectors.toList());
            List<ProductSkuDetailDTO> productSkuList = productSkuService.getProductSkuBySkuId(skuIdList,orderPo.getTenantId());
            // productSkuList 转map, skuId为key
            Map<String, ProductSkuDetailDTO> productSkuMap = productSkuList.stream()
                    .collect(Collectors.toMap(ProductSkuDetailDTO::getSkuId, Function.identity()));
            for (ErpInternalSalesOrderDetailItemVo detailVo : detailVos) {
                ProductSkuDetailDTO skuDetail = productSkuMap.get(detailVo.getSkuId());
                if (Objects.nonNull(skuDetail)) {
                    detailVo.setProductName(skuDetail.getProductName());
                    detailVo.setCategoryName(skuDetail.getCategoryName());
                    detailVo.setSkuName(skuDetail.getSkuName());
                    detailVo.setUnitName(skuDetail.getUnitName());
                    detailVo.setSupplierCode(skuDetail.getSupplierCode());
                    detailVo.setSupplierName(skuDetail.getSupplierName());
                }
            }
            vo.setDetailList(detailVos);
        }
        // 查询内采单附件
        List<ErpInternalSalesOrderFilePO> filePos = internalSalesOrderFileRepo.getListByOrderCode(orderCode);
        if (CollectionUtil.isNotEmpty(filePos)) {
            vo.setFileIdList(filePos.stream().map(ErpInternalSalesOrderFilePO::getFileId).collect(Collectors.toList()));
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditInternalSalesOrder(SalesOrderAuditDTO auditDto, ErpUserPO user) {
        // 参数校验
        auditDto.validData();
        // 查询内采单
        ErpInternalSalesOrderPO orderPo = internalSalesOrderRepo.getByCode(auditDto.getOrderCode());
        if (ObjectUtil.isNull(orderPo)) {
            throw new BusinessException(ErpBizErrorEnum.PARAM_INVALID.getCode(), "参数错误，内采销售单不存在");
        }
        // 待审批状态才允许进行审批操作
        if (!AuditStatusEnum.WAIT_APPLY.getValue().equals(orderPo.getApprovalStatus())) {
            throw new BusinessException(ErpBizErrorEnum.PARAM_INVALID.getCode(), "参数错误，内采销售单状态不允许审批");
        }
        // 更新内采单审批状态等
        boolean success = internalSalesOrderRepo.audit(auditDto, user);
        if (!success) {
            throw new BusinessException(ErpBizErrorEnum.PARAM_INVALID.getCode(), "内采销售单审批失败");
        }
        // 记录操作轨迹
        ErpOperateLogPO createLog = createSalesOrderLog(orderPo.getOrderCode(), "审批",  AuditStatusEnum.getDesc(auditDto.getApprovalStatus()), auditDto.getAuditNotes(), user, orderPo.getTenantId());
        erpOperateLogRepo.saveWithoutLoginTenant(createLog);
        boolean isSuccess = false;
        try {
            // 审批通过相关处理
            if (AuditStatusEnum.APPLY.getValue().equals(auditDto.getApprovalStatus())) {
                // 过去内采单明细列表
                List<ErpInternalSalesOrderDetailPO> detailPos = internalSalesOrderDetailRepo.getListByOrderCode(auditDto.getOrderCode());
                // 对比detailPos和auditDto.getDetailList()
                for (ErpInternalSalesOrderDetailPO detailPo : detailPos) {
                    Optional<SalesOrderAuditDetailDTO> detailOptional = auditDto.getDetailList().stream().filter(dto -> ObjectUtil.equal(dto.getDetailId(), detailPo.getId())).findFirst();
                    if (!detailOptional.isPresent()) {
                        throw new BusinessException(ErpBizErrorEnum.PARAM_INVALID.getCode(), "参数错误，内采销售单明细缺失");
                    }
                    // 审批时修改仓库，价格单，备注等信息
                    SalesOrderAuditDetailDTO auditDetailDTO = detailOptional.get();
                    detailPo.setDepotId(auditDetailDTO.getDepotId());
                    detailPo.setNotes(auditDetailDTO.getNotes());
                    detailPo.setPurchaseSubCode(auditDetailDTO.getPurchaseSubCode());
                }
                // 更新明细信息，存库
                internalSalesOrderDetailRepo.updateBatchById(detailPos);
                // auditDto.getDetailList() 中没有 detailId 的为新增明细
                List<ErpInternalSalesOrderDetailPO> newDetails = new ArrayList<>();
                for (SalesOrderAuditDetailDTO auditDetailDTO : auditDto.getDetailList()) {
                    if (ObjectUtil.isNull(auditDetailDTO.getDetailId()) ||
                          Objects.equals(auditDetailDTO.getDetailId(), 0L)) {
                        auditDetailDTO.validData();
                        ErpInternalSalesOrderDetailPO detailPo = ErpInternalDeliveryOrderConvertor.CONVERTOR.auditDetailDtoToDetailPo(auditDetailDTO);
                        detailPo.setAmount(auditDetailDTO.getUnitPrice().multiply(auditDetailDTO.getQuantity()));
                        detailPo.setOrderCode(auditDto.getOrderCode());
                        detailPo.setId(null);
                        newDetails.add(detailPo);
                    }
                }
                // 新增明细信息
                if (CollectionUtil.isNotEmpty(newDetails)) {
                    internalSalesOrderDetailRepo.saveBatch(newDetails);
                    // 新增的明细添加到detailPos中
                    detailPos.addAll(newDetails);
                }
                // 组装汇总表参数
                List<ErpInternalOrderDeliverySummaryPO> summaryPos = buildOrderDeliverySummaryPos(orderPo, detailPos);

                // 记录内采汇总表
                if (DepotHeadTypeEnum.OUT_DEPOT.getValue().equals(orderPo.getDirection())) {
                    // 内采销售单正向，销仓出库，记录内采单汇总，有效销售数量增加
                    erpInternalOrderDeliverySummaryRepo.insertSummary(summaryPos);
                    // 扣减销仓库存, 且计算销仓库存快照放到detailPos
                    isSuccess = internalOrderSellFlowService.stockOut(orderPo, detailPos);
                    // 更新detailPos中的库存快照stockQty
                    internalSalesOrderDetailRepo.saveStockQty(detailPos);
                    // 审核通过，处理采购单数据
                    erpProcurementSubmissionHeadRepo.signPassProcurement(orderPo.getProcurementSubmissionCode());
                } else {
                    // 内采销售单逆向，退订单，销仓入库，需要检查是否有足够的余量，有效销售数量减少
                    // 本次入库数量 不能大于 有效销售数量 - 累计已出库数量
                    erpInternalOrderDeliverySummaryRepo.updateSummary(summaryPos);
                    // 退掉销仓库存，且计算可退数量快照放到detailPos
                    isSuccess = internalOrderSellFlowService.stockIn(orderPo, detailPos);
                    // 更新detailPos中的可退数量快照canRefundQty
                    internalSalesOrderDetailRepo.saveCanRefundQty(detailPos);
                    // 审核通过，处理退采订单数据
                    erpProcurementReturnHeadRepo.auditPassProcurement(orderPo.getProcurementReturnCode());
                }
            } else {
                if (DepotHeadTypeEnum.OUT_DEPOT.getValue().equals(orderPo.getDirection())) {
                    // 内采销售单正向
                    // 审批不通过，作废对应的采购订单
                    erpProcurementSubmissionHeadRepo.signRefuseProcurementRefuse(orderPo.getProcurementSubmissionCode(), auditDto.getAuditNotes());
                } else {
                    // 内采销售单逆向
                    // 审批不通过，作废对应的退采订单
                    erpProcurementReturnHeadRepo.auditRefuseProcurementReturn(orderPo.getProcurementReturnCode(),auditDto.getAuditNotes());
                }
            }
        } catch (Exception ex) {
            log.error("内采销售单审批失败，订单号：{}。", auditDto.getOrderCode(), ex);
            if (isSuccess) {
                internalOrderSellFlowService.stockRollback(orderPo);
            }
            throw ex;
        }

    }

    @Override
    public List<SalesOrderRefundQtyDetailDTO> queryCanRefundQty(String orderCode) {
        ErpInternalSalesOrderPO orderPo = internalSalesOrderRepo.getByCode(orderCode);
        // 正向订单号
        String relatedOrderCode = orderPo.getOrderCode();
        // 退订单，需计算可退数量
        if (StockBusinessEnum.INTERNAL_SALES_ORDER_IN_TYPE.getValue().equals(orderPo.getOrderType()) && ObjectUtil.isNotNull(orderPo.getRelatedOrderCode())) {
            orderPo = internalSalesOrderRepo.getByCode(orderPo.getRelatedOrderCode());
            relatedOrderCode = orderPo.getOrderCode();
        }
        // 获取正向订单的明细列表
        List<ErpInternalSalesOrderDetailPO> detailPos = internalSalesOrderDetailRepo.getListByOrderCode(relatedOrderCode);
        List<SalesOrderRefundQtyDetailDTO> dtoList = detailPos.stream().map(detailPo -> {
            SalesOrderRefundQtyDetailDTO refundQtyDetailDTO = new SalesOrderRefundQtyDetailDTO();
            refundQtyDetailDTO.setDepotId(detailPo.getDepotId());
            refundQtyDetailDTO.setSkuId(detailPo.getSkuId());
            refundQtyDetailDTO.setPurchaseSubCode(detailPo.getPurchaseSubCode());
            refundQtyDetailDTO.setCanRefundQty(detailPo.getQuantity());
            return refundQtyDetailDTO;
        }).collect(Collectors.toList());
        // 获取逆向订单的明细列表
        List<ErpInternalSalesOrderPO> refundOrderPos = internalSalesOrderRepo.getListByRelatedOrderCode(relatedOrderCode);
        // refundOrderPos 过滤掉审批不通过的
        refundOrderPos = refundOrderPos.stream().filter(t -> Objects.equals(t.getApprovalStatus(), AuditStatusEnum.APPLY.getValue())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(refundOrderPos)) {
            return dtoList;
        }
        List<String> refundOrderCodes = refundOrderPos.stream().map(ErpInternalSalesOrderPO::getOrderCode).distinct().collect(Collectors.toList());
        List<ErpInternalSalesOrderDetailPO> refundDetailPos = internalSalesOrderDetailRepo.getListByOrderCodes(refundOrderCodes);
        // 逆向订单明细列表，按skuId_depotId_purchaseSubCode 分组，获取退订单数量
        Map<String, BigDecimal> refundDetailMap = refundDetailPos.stream()
                .collect(Collectors.groupingBy(
                        detailPo -> detailPo.getSkuId() + "_" + detailPo.getDepotId() + "_" + detailPo.getPurchaseSubCode(),
                        Collectors.reducing(BigDecimal.ZERO, ErpInternalSalesOrderDetailPO::getQuantity, BigDecimal::add)));
        for (SalesOrderRefundQtyDetailDTO resDto : dtoList) {
            String mapKey = resDto.getSkuId() + "_" + resDto.getDepotId() + "_" + resDto.getPurchaseSubCode();
            if (refundDetailMap.containsKey(mapKey)) {
                resDto.setCanRefundQty(resDto.getCanRefundQty().subtract(refundDetailMap.get(mapKey)));
            }
        }
        return dtoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSalesRefundOrder(ErpInternalSalesOrderDTO order, ErpUserPO user) {
        // 参数校验
        order.validData();
        // 正向订单状态校验
        ErpInternalSalesOrderPO orderPo = internalSalesOrderRepo.getByCode(order.getRelatedOrderCode());
        if (ObjectUtil.isNull(orderPo) || !AuditStatusEnum.APPLY.getValue().equals(orderPo.getApprovalStatus())) {
            throw new BusinessException(ErrorCodeEnum.ORDER_NOT_EXIST.getCode(), "正向订单不存在或未审批通过，单号：" + order.getRelatedOrderCode());
        }
        // 可退数量校验
        List<SalesOrderRefundQtyDetailDTO> canRefundQtyList = queryCanRefundQty(order.getRelatedOrderCode());
        for (ErpInternalSalesOrderDetailDTO detailDto : order.getDetailList()) {
            // 从skuId_depotId_purchaseSubCode维度，校验detailDto中的退货数量，是否小于等于canRefundQtyList中的可退数量
            String key = detailDto.getSkuId() + "_" + detailDto.getDepotId() + "_" + detailDto.getPurchaseSubCode();
            SalesOrderRefundQtyDetailDTO refundQtyDetailDTO = canRefundQtyList.stream()
                    .filter(dto -> key.equals(dto.getSkuId() + "_" + dto.getDepotId() + "_" + dto.getPurchaseSubCode()))
                    .findFirst()
                    .orElseThrow(() -> new BusinessException(ErrorCodeEnum.ORDER_NOT_EXIST.getCode(), "未找到对应的可退数量信息，skuId：" + key));
            if (detailDto.getQuantity().compareTo(refundQtyDetailDTO.getCanRefundQty()) > 0) {
                throw new BusinessException(ErrorCodeEnum.ORDER_NOT_EXIST.getCode(), "退货数量超过可退数量，skuId：" + key);
            }
        }
        // 根据正向订单明细，给逆向订单明细赋值
        List<ErpInternalSalesOrderDetailPO> detailPos = internalSalesOrderDetailRepo.getListByOrderCode(order.getRelatedOrderCode());
        for (ErpInternalSalesOrderDetailDTO detailDto : order.getDetailList()) {
            String key = detailDto.getSkuId() + "_" + detailDto.getDepotId() + "_" + detailDto.getPurchaseSubCode();
            ErpInternalSalesOrderDetailPO detail = detailPos.stream().filter(detailPo -> Objects.equals(detailPo.getSkuId(), detailDto.getSkuId())
                            && Objects.equals(detailPo.getDepotId(), detailDto.getDepotId())
                            && Objects.equals(detailPo.getPurchaseSubCode(), detailDto.getPurchaseSubCode()))
                    .findFirst()
                    .orElseThrow(() -> new BusinessException(ErrorCodeEnum.ORDER_NOT_EXIST.getCode(), "未找到对应的正向订单明细，skuId：" + key));
            detailDto.setUnitPrice(detail.getUnitPrice());
            detailDto.setAmount(detailDto.getUnitPrice().multiply(detailDto.getQuantity()));
        }
        // 创建退订单
        ErpInternalSalesOrderPO orderRefundPo = order.convertToPo(smartIdHelper, erpTenantRepo, user.getTenantId(), null);
        // 组装内采退订单详情
        List<ErpInternalSalesOrderDetailPO> refundDetailPos = order.convertToDetailPos(orderRefundPo.getOrderCode());
        // 组装内采退订单附件
        List<ErpInternalSalesOrderFilePO> filePos = order.convertToFilePos(orderRefundPo.getOrderCode());
        // 保存内采退订单主表
        internalSalesOrderRepo.saveWithoutLoginTenant(orderRefundPo);
        if (CollectionUtil.isNotEmpty(refundDetailPos)) {
            internalSalesOrderDetailRepo.saveBatch(refundDetailPos);
        }
        if (CollectionUtil.isNotEmpty(filePos)) {
            internalSalesOrderFileRepo.saveBatch(filePos);
        }
        // 记录操作轨迹
        ErpOperateLogPO createLog = createSalesOrderLog(orderRefundPo.getOrderCode(), "创建", "成功", orderRefundPo.getRemark(), user, orderRefundPo.getTenantId());
        erpOperateLogRepo.saveWithoutLoginTenant(createLog);
    }

    @Override
    public FileDTO export(ErpInternalSalesOrderQuery query, ErpUserPO user) {
        query.setTenantId(user.getTenantId());
        // 生成文件地址
        String orderName = StockBusinessEnum.INTERNAL_SALES_ORDER_OUT_TYPE.getValue().equals(query.getOrderType()) ? "内采销售单" : "内采销售退订单";
        String bizModule = "内采管理-" + orderName + "导出";
        String fileName = String.format(orderName + "导出_%s_%s", DateUtil.format(new Date(), "yyyyMMddHHmmss"), new Random().nextInt(9999)) + ".xlsx";

        FileDTO fileDTO = fileCenterIntegration.generateFileURLDTO(bizModule, fileName);
        log.info("asyncSalesOrderExport fileName = {} , fileUrl = {}", fileName, fileDTO.getFileUrl());
        // 插入流水记录
        FileOperationVO fileOperationVO = new FileOperationVO();
        fileOperationVO.setFileUrl(fileDTO.getFileUrl());
        fileOperationVO.setApplicationCondition("");
        fileOperationVO.setFileName(fileName);
        fileOperationVO.setUserType(1);
        fileOperationVO.setUserId(user.getId());
        fileOperationVO.setBizModule(bizModule);
        fileOperationVO.setUserName(user.getUsername());
        Long fileRecordPkId = fileCenterIntegration.insertFileDownloadRecords(fileOperationVO);
        fileDTO.setFileOperationRecordsPkId(fileRecordPkId);
        // 异步导出
        executorUtil.doMethodWithRequest(() -> fileCenterIntegration.uploadInternalSalesOrderToOSS(query, bizModule, fileName, fileRecordPkId));
        return fileDTO;
    }

    @Override
    public Integer getOrderDetailCounts(ErpInternalSalesOrderQuery query) {
        // 导出订单明细数量
        // 先根据条件，过滤出订单主表的订单号集合
        List<String> orderCodes = new ArrayList<>();
        if (query.isSearchOrder()) {
            orderCodes = internalSalesOrderRepo.queryOrderCodes(query);
            if (CollectionUtil.isEmpty(orderCodes)) {
                return 0;
            }
            query.setOrderCodes(orderCodes);
        }
        // 处理查询条件
        SalesOrderConditionDTO conditionDto = new SalesOrderConditionDTO();
        if (processCommonCondition(query, conditionDto)) {
            return 0;
        }
        return internalSalesOrderDetailRepo.getOrderDetailCounts(orderCodes, conditionDto.getSkuIdList(), conditionDto.getSubCodes(), conditionDto.getDepotIds());
    }

    @Override
    public List<InternalSalesOrderExportVo> getSalesOrderDetailPage(Page<ErpInternalSalesOrderDetailPO> page, ErpInternalSalesOrderQuery query) {
        List<ErpInternalSalesOrderDetailPO> detailPos = getExportDetailPoPage(page, query);
        log.info("InternalSalesOrderExportServiceImpl getSalesOrderDetailPage detailPos: {}", JSONArray.toJSONString(detailPos));
        //查询货品列表信息
        Map<String, ProductSkuDetailDTO> productSkuMap = getSkuMapForExport(detailPos,query.getTenantId());
        // 内采销售单主信息
        Map<String, ErpInternalSalesOrderPO> salesOrderMap = getOrderMapForExport(detailPos);
        // 仓库名称信息
        Map<Long, ErpDepotPO> depotMap = getDepotMapForExport(detailPos);
        // 价格单信息
        Map<String, ErpPurchasePricesItemPO> pricesItemPOMap = getPricesMapForExport(detailPos);
        return InternalSalesOrderExportVo.convertPoToVoList(detailPos, salesOrderMap, productSkuMap, depotMap, pricesItemPOMap, erpTenantRepo);
    }

    private Map<String, ErpPurchasePricesItemPO> getPricesMapForExport(List<ErpInternalSalesOrderDetailPO> detailPos) {
        List<String> subCodesList = detailPos.stream()
                .map(ErpInternalSalesOrderDetailPO::getPurchaseSubCode)
                .distinct().collect(Collectors.toList());
        Map<String, ErpPurchasePricesItemPO> pricesItemPOMap = purchasePricesItemRepo.getPurchasePricesItemMap(subCodesList);
        return pricesItemPOMap;
    }

    private Map<Long, ErpDepotPO> getDepotMapForExport(List<ErpInternalSalesOrderDetailPO> detailPos) {
        List<Long> deptIds = detailPos.stream()
                .map(ErpInternalSalesOrderDetailPO::getDepotId)
                .distinct().collect(Collectors.toList());
        Map<Long, ErpDepotPO> depotMap = erpDepotRepo.getDepotMap(deptIds);
        return depotMap;
    }

    private Map<String, ErpInternalSalesOrderPO> getOrderMapForExport(List<ErpInternalSalesOrderDetailPO> detailPos) {
        List<String> orderCodeList = detailPos.stream()
                .map(ErpInternalSalesOrderDetailPO::getOrderCode)
                .distinct().collect(Collectors.toList());
        Map<String, ErpInternalSalesOrderPO> salesOrderMap = internalSalesOrderRepo.getSalesOrderMap(orderCodeList);
        return salesOrderMap;
    }

    private Map<String, ProductSkuDetailDTO> getSkuMapForExport(List<ErpInternalSalesOrderDetailPO> detailPos,Long tenantId) {
        List<String> skuIds = detailPos.stream()
                .map(ErpInternalSalesOrderDetailPO::getSkuId)
                .distinct().collect(Collectors.toList());
        List<ProductSkuDetailDTO> productSkuList = productSkuService.getProductSkuBySkuId(skuIds,tenantId);
        if (CollectionUtil.isEmpty(productSkuList)) {
            return new HashMap<>();
        }
        // productSkuList 转map, skuId为key
        Map<String, ProductSkuDetailDTO> productSkuMap = productSkuList.stream()
                .collect(Collectors.toMap(ProductSkuDetailDTO::getSkuId, Function.identity()));
        return productSkuMap;
    }

    @Override
    public List<InternalSalesRefundOrderExportVo> getSalesRefundDetailPage(Page<ErpInternalSalesOrderDetailPO> page, ErpInternalSalesOrderQuery query) {
        List<ErpInternalSalesOrderDetailPO> detailPos = getExportDetailPoPage(page, query);
        log.info("InternalSalesRefundOrderExportServiceImpl getSalesRefundDetailPage detailPos: {}", JSONArray.toJSONString(detailPos));
        //查询货品列表信息
        Map<String, ProductSkuDetailDTO> productSkuMap = getSkuMapForExport(detailPos,query.getTenantId());
        // 内采销售单主信息
        Map<String, ErpInternalSalesOrderPO> salesOrderMap = getOrderMapForExport(detailPos);
        // 仓库名称信息
        Map<Long, ErpDepotPO> depotMap = getDepotMapForExport(detailPos);
        // 正向订单明细
        Map<String, ErpInternalSalesOrderDetailPO> forwardDetailMap = getForwardDetailMapForExport(salesOrderMap);
        // 正向订单主信息
        Map<String, ErpInternalSalesOrderPO> forwardOrderMap = getForwardOrderMapForExport(salesOrderMap);

        return InternalSalesRefundOrderExportVo.convertPoToVoList(detailPos, salesOrderMap, productSkuMap, depotMap, forwardDetailMap, forwardOrderMap, erpTenantRepo);
    }

    private Map<String, ErpInternalSalesOrderPO> getForwardOrderMapForExport(Map<String, ErpInternalSalesOrderPO> salesOrderMap) {
        // 从salesOrderMap 中提取正向订单号的List
        List<String> forwardOrderCodes = salesOrderMap.values().stream()
                .map(ErpInternalSalesOrderPO::getRelatedOrderCode)
                .collect(Collectors.toList());
        return internalSalesOrderRepo.getSalesOrderMap(forwardOrderCodes);
    }

    private Map<String, ErpInternalSalesOrderDetailPO> getForwardDetailMapForExport(Map<String, ErpInternalSalesOrderPO> salesOrderMap) {
        // 从salesOrderMap 中提取正向订单号的List
        List<String> forwardOrderCodes = salesOrderMap.values().stream()
                .map(ErpInternalSalesOrderPO::getRelatedOrderCode)
                .collect(Collectors.toList());
        List<ErpInternalSalesOrderDetailPO> forwardDetailPos = internalSalesOrderDetailRepo.getListByCodes(forwardOrderCodes);
        // forwardDetailPos 使用 orderCode_skuId_depotId_purchaseSubCode进行分组
        Map<String, ErpInternalSalesOrderDetailPO> forwardDetailMap = forwardDetailPos.stream()
                .collect(Collectors.toMap(detail -> detail.getOrderCode() + "_" + detail.getSkuId() + "_" + detail.getDepotId() + "_" + detail.getPurchaseSubCode(), Function.identity()));
        return forwardDetailMap;
    }

    private List<ErpInternalSalesOrderDetailPO> getExportDetailPoPage(Page<ErpInternalSalesOrderDetailPO> page, ErpInternalSalesOrderQuery query) {
        // 先根据条件，过滤出订单主表的订单号集合
        List<String> orderCodes = new ArrayList<>();
        if (query.isSearchOrder()) {
            orderCodes = internalSalesOrderRepo.queryOrderCodes(query);
            if (CollectionUtil.isEmpty(orderCodes)) {
                return Lists.newArrayListWithCapacity(0);
            }
            query.setOrderCodes(orderCodes);
        }
        // 处理查询条件
        SalesOrderConditionDTO conditionDto = new SalesOrderConditionDTO();
        if (processCommonCondition(query, conditionDto)) {
            return Lists.newArrayListWithCapacity(0);
        }
        // 分页查询订单明细
        Page<ErpInternalSalesOrderDetailPO> poPage = internalSalesOrderDetailRepo.getSalesOrderDetailPage(page, orderCodes, conditionDto.getSkuIdList(), conditionDto.getSubCodes(), conditionDto.getDepotIds());
        if (CollectionUtil.isEmpty(poPage.getRecords())) {
            return Lists.newArrayListWithCapacity(0);
        }
        return poPage.getRecords();
    }

    private List<ErpInternalOrderDeliverySummaryPO> buildOrderDeliverySummaryPos(ErpInternalSalesOrderPO orderPo, List<ErpInternalSalesOrderDetailPO> detailPos) {
        // detailPos 按 skuId 进行分组，获取每个skuId的出库数量
        Map<String, BigDecimal> skuIdQtyMap = detailPos.stream().collect(Collectors.groupingBy(ErpInternalSalesOrderDetailPO::getSkuId, Collectors.reducing(BigDecimal.ZERO, ErpInternalSalesOrderDetailPO::getQuantity, BigDecimal::add)));

        List<ErpInternalOrderDeliverySummaryPO> list = new ArrayList<>(skuIdQtyMap.size());
        skuIdQtyMap.forEach((skuId, qty) -> {
            ErpInternalOrderDeliverySummaryPO summaryPo = new ErpInternalOrderDeliverySummaryPO();
            summaryPo.setTenantId(orderPo.getTenantId());
            summaryPo.setTradeCompanyCode(orderPo.getTradeCompanyCode());
            summaryPo.setSkuId(skuId);
            summaryPo.setDeliveryQty(BigDecimal.ZERO);
            if (DepotHeadTypeEnum.OUT_DEPOT.getValue().equals(orderPo.getDirection())) {
                summaryPo.setSalesQty(qty);
            } else {
                summaryPo.setSalesQty(qty.negate());
            }
            list.add(summaryPo);
        });

        return list;
    }

    private ErpOperateLogPO createSalesOrderLog(String bizNo, String operateAction, String handlerResult, String remark, ErpUserPO userPo, Long tenantId) {
        ErpOperateLogPO log = new ErpOperateLogPO();
        log.setBusinessId(bizNo);
        log.setType(OperateLogTypeEnum.INTERNAL_SALES_ORDER.getCode());
        log.setTypeDesc(OperateLogTypeEnum.INTERNAL_SALES_ORDER.getValue());
        log.setOperateAction(operateAction);
        log.setHandlerResult(handlerResult);
        log.setOperateTime(LocalDateTime.now());
        log.setTenantId(tenantId);

        if (ObjectUtil.isNotNull(userPo)){
            log.setHandlerId(userPo.getId());
            log.setHandlerName(userPo.getUsername());
        }
        log.setRemark(remark);
        return log;
    }
}
