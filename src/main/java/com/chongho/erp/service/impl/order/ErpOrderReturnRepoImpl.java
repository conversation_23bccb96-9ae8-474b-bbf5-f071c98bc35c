package com.chongho.erp.service.impl.order;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.framework.autoconfigure.common.exception.BusinessException;
import com.chongho.erp.common.constant.ErpConstants;
import com.chongho.erp.common.enums.*;
import com.chongho.erp.common.util.DateUtil;
import com.chongho.erp.common.util.EasyPoiUtil;
import com.chongho.erp.common.util.OrderResult;
import com.chongho.erp.common.util.TenantUtil;
import com.chongho.erp.config.BasicsConfig;
import com.chongho.erp.domain.builder.ErpSkuUnitBuilder;
import com.chongho.erp.dto.material.EcpProductSkuSupDTO;
import com.chongho.erp.dto.material.ProductSkuDetailDTO;
import com.chongho.erp.dto.order.*;
import com.chongho.erp.dto.query.ErpSkuDepotItemQuery;
import com.chongho.erp.dto.query.ProductSkuQuery;
import com.chongho.erp.mapper.order.ErpOrderItemMapper;
import com.chongho.erp.po.material.EcpProductSkuUnitPO;
import com.chongho.erp.po.order.ErpOrderItemPO;
import com.chongho.erp.po.order.ErpOrderReturnPO;
import com.chongho.erp.mapper.order.ErpOrderReturnMapper;
import com.chongho.erp.repository.material.EcpProductSkuRepo;
import com.chongho.erp.repository.material.EcpProductSkuUnitRepo;
import com.chongho.erp.service.ErpProcurementSubmissionHeadRepo;
import com.chongho.erp.service.ErpProcurementSubmissionItemRepo;
import com.chongho.erp.service.bill.IErpOutBillService;
import com.chongho.erp.service.bill.impl.ErpOutBillServiceImpl;
import com.chongho.erp.service.material.IProductSkuService;
import com.chongho.erp.service.order.ErpOrderReturnService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chongho.erp.vo.material.ProductSkuVO;
import com.google.common.collect.Lists;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-25 13:54:56
 */
@Slf4j
@Service
public class ErpOrderReturnRepoImpl extends ServiceImpl<ErpOrderReturnMapper, ErpOrderReturnPO> implements ErpOrderReturnService {

    @Autowired
    private ErpOrderReturnMapper orderReturnMapper;
    @Autowired
    private ErpOrderItemMapper orderItemMapper;
    @Autowired
    private EcpProductSkuRepo productSkuRepo;
    @Autowired
    private IProductSkuService productSkuService;
    @Autowired
    private ErpProcurementSubmissionHeadRepo procurementSubmissionHeadRepo;
    @Autowired
    private IErpOutBillService erpOutBillService;
    @Autowired
    private TenantUtil tenantUtil;
    @Autowired
    private BasicsConfig basicsConfig;
    @Autowired
    private EcpProductSkuUnitRepo productSkuUnitRepo;
    @Override
    public Page<OrderReturnItemResponse> getOrderReturnByExample(QueryOrderReturnRequest queryOrderReturnRequest) {
        log.info("#getOrderReturnByExample {}", JSON.toJSONString(queryOrderReturnRequest));
        return getPageOrderByExample(queryOrderReturnRequest);
    }

    @Override
    public void exportOrderReturnByExample(HttpServletResponse response, QueryOrderReturnRequest queryOrderReturnRequest) {
        queryOrderReturnRequest.setPageSize(-1);
        Page<OrderReturnItemResponse> page = getPageOrderByExample(queryOrderReturnRequest);
        List<OrderReturnItemResponse> returnItemResponseList = page.getRecords();

        if (CollectionUtils.isEmpty(returnItemResponseList)){
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.getCode(),"销售退货订单数据不存在");
        }
        EasyPoiUtil easyPoiUtil = new EasyPoiUtil();
        try {
            String fileName = String.format("销售管理-销售退货订单导出_%s", DateUtil.format(new Date(), "yyyyMMdd"));
            easyPoiUtil.downloadField(response, fileName, OrderReturnItemResponse.class, returnItemResponseList, null);
        } catch (Exception e) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_EXCEPTION.getCode(), "销售管理-销售退货订单导出异常"+e);
        }
    }

    @Override
    public Page<ErpOrderReturnPO> pageBySkuId(String skuId, int pageNum, int pageSize) {
        Page<ErpOrderReturnPO> erpOrderReturnPOPage = new Page(pageNum, pageSize);
        erpOrderReturnPOPage = orderReturnMapper.pageBySkuId(erpOrderReturnPOPage, skuId);
        return erpOrderReturnPOPage;
    }

    public OrderResult querySkuIdListByCondition(QueryOrderReturnRequest queryOrderReturnRequest) {
        // 校验根据货品参数是否全部为空
        Boolean isNeedQuery = ObjectUtil.isNotEmpty(queryOrderReturnRequest.getSkuMaterialCode()) || ObjectUtil.isNotEmpty(queryOrderReturnRequest.getSpuId())
                || ObjectUtil.isNotEmpty(queryOrderReturnRequest.getProductName()) || ObjectUtil.isNotEmpty(queryOrderReturnRequest.getSkuName()) || ObjectUtil.isNotEmpty(queryOrderReturnRequest.getSkuId());
        if (!isNeedQuery) {
            return OrderResult.ok(Boolean.FALSE,"不需要查询sku信息",null);
        }
        ErpSkuDepotItemQuery productSkuQuery = new ErpSkuDepotItemQuery();
        productSkuQuery.setErpNo(queryOrderReturnRequest.getSkuMaterialCode());
        productSkuQuery.setSpuId(queryOrderReturnRequest.getSpuId());
        productSkuQuery.setSkuId(queryOrderReturnRequest.getSkuId());
        productSkuQuery.setProductName(queryOrderReturnRequest.getProductName());
        productSkuQuery.setSkuName(queryOrderReturnRequest.getSkuName());
        List<EcpProductSkuSupDTO> productSkuList = productSkuRepo.getProSkuSupInfoList(productSkuQuery);
        List<String> list = productSkuList.stream().map(EcpProductSkuSupDTO::getSkuId).collect(Collectors.toList());
        log.info("###查询sku信息 参数{} 结果 {} skuId {}", JSON.toJSONString(productSkuQuery), JSON.toJSONString(productSkuList), JSON.toJSONString(list));
        return OrderResult.ok(Boolean.TRUE,"查询sku信息",list);
    }

    public OrderResult getOrderByStoreNameOrSupplierName(QueryOrderReturnRequest queryOrderReturnRequest,List<String> skuIds) {
        Boolean isNeedQuery = ObjectUtil.isNotEmpty(queryOrderReturnRequest.getSupplierCode()) || ObjectUtil.isNotEmpty(queryOrderReturnRequest.getMerchantName())
                || CollectionUtils.isNotEmpty(skuIds);
        if (!isNeedQuery) {
            return OrderResult.ok(Boolean.FALSE,"不需要查子单信息",null);
        }
        LambdaQueryWrapper<ErpOrderItemPO> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(queryOrderReturnRequest.getSupplierName())) {
            queryWrapper.like(ErpOrderItemPO::getSupplierName, queryOrderReturnRequest.getSupplierName());
        }
        if (ObjectUtil.isNotEmpty(queryOrderReturnRequest.getSupplierCode())) {
            queryWrapper.eq(ErpOrderItemPO::getSupplierCode, queryOrderReturnRequest.getSupplierCode());
        }
        if (ObjectUtil.isNotEmpty(queryOrderReturnRequest.getMerchantName())) {
            queryWrapper.like(ErpOrderItemPO::getMechantName, queryOrderReturnRequest.getMerchantName());
        }
        if (CollectionUtils.isNotEmpty(skuIds)) {
            queryWrapper.in(ErpOrderItemPO::getChannelSkuId, skuIds);
        }
        List<ErpOrderItemPO> erpOrderItemPOS = orderItemMapper.selectList(queryWrapper);
        List<String> orderSnList = erpOrderItemPOS.stream().map(orderItem -> orderItem.getOrderSn()).distinct().collect(Collectors.toList());
        return OrderResult.ok(Boolean.TRUE,"查询子单信息",orderSnList);
    }

    Page<OrderReturnItemResponse> getPageOrderByExample(QueryOrderReturnRequest queryOrderReturnRequest) {

        // 根據商品信息查詢符合條件的skuId
        OrderResult<List<String>> result = querySkuIdListByCondition(queryOrderReturnRequest);
        if(result.getFlag()&&CollectionUtils.isEmpty(result.getData())){
            return new Page<>();
        }
        // 根据店铺名称和供应商名称查询符合条件的订单号
        OrderResult<List<String>> orderResult = getOrderByStoreNameOrSupplierName(queryOrderReturnRequest,result.getData());
        if(orderResult.getFlag()&&CollectionUtils.isEmpty(orderResult.getData())){
            return new Page<>();
        }
        List<String> orderSnList = orderResult.getData();
        // 根据支付时间查询主订单的单号，筛选出主订单后，然后将单号作为条件查询明细
        if (ObjectUtil.isNotEmpty(orderSnList)) {
            queryOrderReturnRequest.setOrderSnList(orderSnList);
        }
        if (CollectionUtils.isNotEmpty(result.getData())) {
            queryOrderReturnRequest.setSkuIdList(result.getData());
        }
        Page<ErpOrderReturnPO> erpOrderReturnPOPage = new Page(queryOrderReturnRequest.getNumber(), queryOrderReturnRequest.getPageSize());
        erpOrderReturnPOPage = orderReturnMapper.pageOrderReturn(erpOrderReturnPOPage, queryOrderReturnRequest);
        // 结果转换
        List<ErpOrderReturnPO> records = erpOrderReturnPOPage.getRecords();
        if(CollectionUtils.isEmpty(records)){
            return new Page<>();
        }
        List<String> orderSn = records.stream().map(ErpOrderReturnPO::getOrderSn).collect(Collectors.toList());
        List<Long> productIdList = records.stream().map(ErpOrderReturnPO::getOrderProductId).collect(Collectors.toList());
        // 查询店铺名称和id
        LambdaQueryWrapper<ErpOrderItemPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ErpOrderItemPO::getOrderSn, orderSn)
                .in(ErpOrderItemPO::getOrderProductId, productIdList);
        List<ErpOrderItemPO> orderItemPOList = orderItemMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(orderItemPOList)){
            return new Page<>();
        }
        List<String> skuIdList = orderItemPOList.stream().map(ErpOrderItemPO::getChannelSkuId).collect(Collectors.toList());
        List<ProductSkuVO> productSkuList = productSkuService.getProductSkuListBySkuIds(skuIdList, orderItemPOList.get(0).getTenantId());
        Map<String, ProductSkuVO> proSkuMap=new HashMap<>();
        if (CollectionUtils.isNotEmpty(productSkuList)) {
            proSkuMap = productSkuList.stream().collect(Collectors.toMap(ProductSkuVO::getSkuId,Function.identity(),(o,n)->n));
        }
        Map<String, ErpOrderItemPO> orderItemPoMap = orderItemPOList.stream().collect(Collectors.toMap(key -> String.format("%s:%s", key.getOrderSn()
                , key.getOrderProductId()), Function.identity(), (o, n) -> n));
        Long tenantId=tenantUtil.getTenantIdFromRequest();
        Map<String, List<EcpProductSkuUnitPO>> skuUnitRatioMap;
        if(basicsConfig.getTenantIdList().contains(tenantId)){
            //获取规格单位信息
            List<EcpProductSkuUnitPO> productSkuUintList = productSkuUnitRepo.batchFindUnitListBySkuId(skuIdList);
            skuUnitRatioMap=productSkuUintList.stream().collect(Collectors.groupingBy(EcpProductSkuUnitPO::getSkuId));
        } else {
            skuUnitRatioMap = null;
        }
        List<OrderReturnItemResponse> list = new ArrayList<>();
        for (ErpOrderReturnPO orderReturnPO : records) {
            OrderReturnItemResponse orderReturnItemResponse = new OrderReturnItemResponse();
            BeanUtils.copyProperties(orderReturnPO, orderReturnItemResponse);
            ErpOrderItemPO orderItemPO = orderItemPoMap.get(String.format("%s:%s", orderReturnPO.getOrderSn()
                    , orderReturnPO.getOrderProductId()));
            if(Objects.isNull(orderItemPO)){
                continue;
            }
            ProductSkuVO proSku = proSkuMap.get(orderItemPO.getChannelSkuId());
            if(Objects.isNull(proSku)){
                continue;
            }
            orderReturnItemResponse.setProductInfo(proSku.getProductName()+" "+proSku.getSkuName());
            if(basicsConfig.getTenantIdList().contains(tenantId)){
                orderReturnItemResponse.setReturnFinanceNum(ErpSkuUnitBuilder.stockUnitConvertDesc(skuUnitRatioMap.get(orderItemPO.getChannelSkuId())
                        ,orderReturnPO.getReturnFinanceNum(),
                        ErpConstants.STORAGE_UNIT));
            }else{
                orderReturnItemResponse.setReturnFinanceNum(Optional.ofNullable(orderReturnPO.getReturnFinanceNum()).orElse(BigDecimal.ZERO)
                        .stripTrailingZeros().toPlainString()+proSku.getUnitName());
            }
            orderReturnItemResponse.setMerchantId(orderItemPO.getMerchantId());
            orderReturnItemResponse.setMechantName(orderItemPO.getMechantName());
            orderReturnItemResponse.setOrderChannelDesc(OrderChannelEnum.getDescByValue(orderReturnPO.getOrderChannel()));
            orderReturnItemResponse.setReturnTypeDesc(ReturnTypeEnum.getDescByValue(orderReturnPO.getReturnType()));
            orderReturnItemResponse.setProductStateDesc(ProductStateEnum.getDescByValue(orderReturnPO.getProductState()));
            orderReturnItemResponse.setReturnStateDesc(ReturnStateEnum.getDesc(orderReturnPO.getReturnState()));
            list.add(orderReturnItemResponse);
        }
        Page<OrderReturnItemResponse> pageResult = new Page(erpOrderReturnPOPage.getCurrent(), erpOrderReturnPOPage.getSize());
        pageResult.setTotal(erpOrderReturnPOPage.getTotal());
        pageResult.setRecords(list);
        return pageResult;
    }

    private ProductSkuDetailDTO getProductSkuDetailBySkuCode(String skuCode,Long tenantId) {
        // 根据sku编码查询货品信息
        List<String> skuIdList = new ArrayList<>();
        skuIdList.add(skuCode);
        List<ProductSkuDetailDTO> productSkuBySkuList = productSkuService.getProductSkuBySkuId(skuIdList,tenantId);
        AssertUtil.isTrue(CollectionUtils.isEmpty(productSkuBySkuList), new MallException(skuCode + "未查询到对应的sku信息！"));
        return productSkuBySkuList.get(0);
    }
}
