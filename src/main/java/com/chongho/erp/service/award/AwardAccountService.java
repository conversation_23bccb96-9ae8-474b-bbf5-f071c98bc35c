package com.chongho.erp.service.award;

import com.chongho.erp.oms.domain.dto.OrderEventDTO;
import com.chongho.erp.thirdpart.api.vo.AwardAccountBizCodeRelationVO;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface AwardAccountService {

	/**
	 * 消费
	 * 
	 * @param orderProcess
	 * @return
	 */
	boolean consumeAccountBalanceProcess(OrderEventDTO orderProcess);

	/**
	 * 冲正
	 * 
	 * @param orderProcess
	 * @return
	 */
	boolean reversalAccountBalanceProcess(OrderEventDTO orderProcess);
	
	
	/**
	 * 冲正远程 api
	 * 
	 * @param orderProcess
	 * @return
	 */
	boolean reversalAccountBalanceProcessRemoteApi(OrderEventDTO orderProcess);

	/**
	 * 根据账户编码查询账户绑定的业务关系
	 * @param accountCode 账户编码
	 * @return AwardAccountBizCodeRelationVO
	 */
	List<AwardAccountBizCodeRelationVO> queryBizCodeRelationByAccountCode(@RequestParam("accountCode") String accountCode);
}
