package com.chongho.erp.service.contract.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdfinance.hrms.facade.vo.OnBoardEmployeeVO;
import com.cdfinance.ms.facade.model.enums.BusinessResourceEnum;
import com.cdfinance.ms.facade.model.request.signBefore.ContractFindByIdentityRequest;
import com.cdfinance.ms.facade.model.request.signBefore.ContractFindRequest;
import com.cdfinance.ms.facade.model.request.signBefore.ContractGenerateRequest;
import com.cdfinance.ms.facade.model.request.signing.ContractESignHeadCustomerRequest;
import com.cdfinance.ms.facade.model.request.signing.ContractESignHeadFindRequest;
import com.cdfinance.ms.facade.model.response.signBefore.ContractFindResponse;
import com.cdfinance.ms.facade.model.response.signBefore.ContractGenPreResponse;
import com.cdfinance.ms.facade.model.response.signing.ContractESignHeadFindResponse;
import com.cdfinance.ms.facade.model.vo.ContractCompanySignVO;
import com.cdfinance.ms.facade.model.vo.ContractCustomerVO;
import com.cdfinance.ms.facade.model.vo.ContractParamVo;
import com.cfpamf.framework.autoconfigure.common.exception.BusinessException;
import com.cfpamf.framework.autoconfigure.common.result.Result;
import com.cfpamf.framework.autoconfigure.redis.lock.DistributeLock;
import com.cfpamf.framework.autoconfigure.web.bms.JwtUserInfo;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.bms.facade.vo.OrganizationBaseVO;
import com.cfpamf.ms.bms.facade.vo.UserListVO;
import com.chongho.erp.common.constant.*;
import com.chongho.erp.common.enums.*;
import com.chongho.erp.common.enums.msContract.ContractSignRecordStatusEnum;
import com.chongho.erp.common.enums.msg.SmsBizTypeEnum;
import com.chongho.erp.common.smartid.SmartIdHelper;
import com.chongho.erp.common.smartid.SmartIdTypeEnum;
import com.chongho.erp.common.util.*;
import com.chongho.erp.config.AgricServiceConfig;
import com.chongho.erp.config.BasicsConfig;
import com.chongho.erp.config.LifeServiceConfig;
import com.chongho.erp.convertor.contract.ContractConvertor;
import com.chongho.erp.convertor.contract.ContractSignRecordConvertor;
import com.chongho.erp.dto.bill.ErpBillDataDTO;
import com.chongho.erp.dto.contract.*;
import com.chongho.erp.dto.depot.OrgDepotListInfoDto;
import com.chongho.erp.dto.depotSku.BranchDepotCurrentStockDto;
import com.chongho.erp.dto.msg.DingDingMsgDTO;
import com.chongho.erp.dto.query.ContractBillQuery;
import com.chongho.erp.dto.query.ContractInfoQuery;
import com.chongho.erp.dto.query.ContractQuery;
import com.chongho.erp.dto.query.SignContractQuery;
import com.chongho.erp.helper.DingDingMessageHelper;
import com.chongho.erp.mapper.contract.ErpContractMapper;
import com.chongho.erp.po.ErpContractSignRecordPO;
import com.chongho.erp.po.ErpTenantPO;
import com.chongho.erp.po.ErpUserPO;
import com.chongho.erp.po.contract.ErpContractBillPO;
import com.chongho.erp.po.contract.ErpContractPO;
import com.chongho.erp.po.material.ErpSupplierContactPO;
import com.chongho.erp.po.material.SupplierPO;
import com.chongho.erp.repository.ErpContractSignRecordRepo;
import com.chongho.erp.repository.ErpOperateLogRepo;
import com.chongho.erp.repository.contract.ErpContractBillRepo;
import com.chongho.erp.repository.contract.ErpContractRepo;
import com.chongho.erp.repository.material.ErpSupplierContactRepo;
import com.chongho.erp.repository.material.SupplierRepo;
import com.chongho.erp.req.fdd.ContractESignCallbackRequest;
import com.chongho.erp.service.BaseBmsTokenService;
import com.chongho.erp.service.ErpDepotService;
import com.chongho.erp.service.IBmsIntegration;
import com.chongho.erp.service.bill.IErpOutBillService;
import com.chongho.erp.service.common.IErpAttachService;
import com.chongho.erp.service.contract.ErpContractService;
import com.chongho.erp.thirdpart.api.IBmsOrganizationFacade;
import com.chongho.erp.thirdpart.api.IHrmsIntegration;
import com.chongho.erp.thirdpart.api.MsContractFacade;
import com.chongho.erp.thirdpart.api.contract.ContractCommonFacade;
import com.chongho.erp.thirdpart.api.vo.OutOrganizationVO;
import com.chongho.erp.thirdpart.helper.contract.ContractCustVerifyFacadeHelper;
import com.chongho.erp.vo.OrganizationParentVO;
import com.chongho.erp.vo.contract.*;
import com.chongho.erp.vo.contract.signRecord.ContractSignRecordVo;
import com.chongho.erp.vo.contract.signRecord.ContractSignStatusVO;
import com.chongho.erp.vo.material.ExportContractVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-12
 */
@SuppressWarnings("unused")
@Slf4j
@Service
public class ErpContactServiceImpl extends ServiceImpl<ErpContractMapper, ErpContractPO> implements ErpContractService {

    @Resource
    private ErpContractRepo erpContractRepo;
    @Autowired
    private SupplierRepo supplierRepo;
    @Autowired
    private ErpContractBillRepo erpContractBillRepo;
    @Autowired
    private IErpOutBillService erpOutBillService;
    @Autowired
    private ErpOperateLogRepo erpOperateLogRepo;
    @Autowired
    private TenantUtil tenantUtil;
    @Autowired
    private BasicsConfig basicsConfig;
    @Autowired
    private IBmsIntegration bmsIntegration;
    @Autowired
    private ErpSupplierContactRepo supplierContactRepo;
    @Autowired
    private SmartIdHelper smartIdHelper;
    @Autowired
    private ErpContractService contractService;
    @Autowired
    private DistributeLock distributeLock;
    @Autowired
    private BaseBmsTokenService baseBmsTokenService;
    @Resource
    private IBmsOrganizationFacade bmsOrganizationFacade;
    @Resource
    private MsContractFacade msContractFacade;
    @Resource
    private DingDingMessageHelper dingDingMessageHelper;
    @Resource
    private ErpContractSignRecordRepo erpContractSignRecordRepo;
    @Resource
    private ContractCustVerifyFacadeHelper contractCustVerifyFacadeHelper;
    @Resource
    private LifeServiceConfig lifeServiceConfig;
    @Resource
    private AgricServiceConfig agricServiceConfig;
    @Autowired
    private ContractCommonFacade contractCommonFacade;
    @Resource
    private ErpDepotService erpDepotService;
    @Resource
    private IErpAttachService erpAttachService;
    @Autowired
    private IHrmsIntegration hrmsIntegration;
    @Autowired
    private ErpContractService erpContractService;

    @Value("${contact.dingdingUrl}")
    private String dingdingUrl;

    @Override
    public Page<ContractVo> pageList(ContractQuery query) {


        Page<ContractVo> page = new Page<>(query.getNumber(), query.getPageSize());
        page = erpContractRepo.pageList(page, query);
        LocalDate now = LocalDate.now();
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            //查询pid的数据
            List<Long> pidList = page.getRecords().stream().map(ContractVo::getPid).filter(pid -> pid > 0).collect(Collectors.toList());
            Map<Long, String> contractMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(pidList)) {
                List<ErpContractPO> pContractList = erpContractRepo.listByIds(pidList);
                if (CollectionUtils.isNotEmpty(pContractList)) {
                    contractMap = pContractList.stream().collect(Collectors.toMap(ErpContractPO::getId, ErpContractPO::getContractNo, (o, n) -> n));
                }
            }
            Map<Long, String> finalContractMap = contractMap;
            page.setRecords(page.getRecords().stream().peek(item -> {
                //母合同编码
                item.setParContractNo(Optional.ofNullable(finalContractMap.get(item.getPid())).orElse("/"));
                item.setValidDay("/");
                item.setIsValid(2);
                //有效天数计算
                if (!item.getStartTime().isAfter(now)) {
                    //如果终止时间小于当前时间，则已过期
                    item.setValidDay("0");
                    item.setIsValid(1);
                    //如果终止时间大于当前时间
                    if (item.getEndTime().isAfter(now)) {
                        long day = item.getEndTime().toEpochDay() - now.toEpochDay();
                        item.setValidDay(String.valueOf(day));
                        item.setIsValid(0);
                    }
                }
            }).collect(Collectors.toList()));
        }
        return page;
    }

    @Override
    public Long create(ContractDTO request) {
        //有效期条件判断
        contractJudge(request);
        ErpContractPO po = erpContractRepo.existContract(request.getContractNo(), request.getStartTime());
        if (Objects.nonNull(po)) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "请勿添加已存在相同合同编码和起始有效期！");
        }
        //数据转化
        ErpContractPO contractPo = ContractConvertor.CONVERTOR.convertContractDTOToContractPo(request);
        try {
            boolean result = erpContractRepo.save(contractPo);
            if (result) {
                //添加操作记录
                erpOperateLogRepo.saveLog(tenantUtil.getUser(), OperateLogTypeEnum.CONTRACT, contractPo.getId().toString(),
                        "创建", "通过", "", null, null);
            }
        } catch (DuplicateKeyException e) {
            log.warn("contractNo:{},startTime:{}", contractPo.getContractNo(), contractPo.getStartTime(), e);
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "请勿添加已存在相同合同编码和起始有效期！");
        }
        return contractPo.getId();
    }

    @Override
    public ContractDetailVo detail(Long contractId) {
        ContractDetailVo contractDetailVo = erpContractRepo.detail(contractId);
        if (Objects.isNull(contractDetailVo) || StringUtils.isBlank(contractDetailVo.getSupplierCode())) {
            return contractDetailVo;
        }
        //供应商联系人
        List<ErpSupplierContactPO> supplierContactList = supplierContactRepo.getErpSupplierContactList(Collections.singletonList(contractDetailVo.getSupplierCode()));
        if (CollectionUtils.isNotEmpty(supplierContactList)) {
            contractDetailVo.setSupplierContact(supplierContactList.get(0).getContact());
            contractDetailVo.setSupplierMobile(supplierContactList.get(0).getContactPhone());
        }
        return contractDetailVo;
    }

    //新增/修改条件判断
    private void contractJudge(ContractDTO request) {
        //终止有效期>起始有效期
        if (request.getEndTime().isBefore(request.getStartTime())) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "终止有效期必须大于起始有效期！");
        }
        ErpContractPO po = erpContractRepo.existContract(request.getContractNo(), request.getStartTime());
        if (Objects.nonNull(po) && !Objects.equals(request.getId(), po.getId())) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "请勿添加已存在相同合同编码和起始有效期！");
        }
        if (StringUtils.isNotBlank(request.getContractFileIds())) {
            if (request.getContractFileIds().split(",").length > basicsConfig.getFileMaxSize()) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "最多上传" + basicsConfig.getFileMaxSize() + "个附件");
            }
        }
        List<SupplierPO> supplierList = supplierRepo.getSupplierBatch(Collections.singletonList(request.getSupplierCode()));
        if (CollectionUtils.isEmpty(supplierList)) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "供应商不存在或已禁用！");
        }
    }

    @Override
    public Boolean modify(ContractDTO request) {
        if (Objects.isNull(request.getId())) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "修改合同信息，合同ID不能为空");
        }
        //子合同选择判断
        if (Objects.equals(request.getId(), request.getPid())) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "子合同不能选择自身合同！");
        }
        //有效期条件判断
        contractJudge(request);
        //数据转化
        ErpContractPO contractPo = ContractConvertor.CONVERTOR.convertContractDTOToContractPo(request);
        try {
            ErpContractPO oldContract = erpContractRepo.getById(request.getId());
            boolean result = erpContractRepo.updateById(contractPo);
            if (result) {
                StringBuilder remark = new StringBuilder();
                if (!oldContract.getContractNo().equals(request.getContractNo())) {
                    remark.append("合同编号：").append(oldContract.getContractNo()).append("变更为").append(request.getContractNo()).append("<br/>");
                }
                if (!oldContract.getContractName().equals(request.getContractName())) {
                    remark.append("合同名称：").append(oldContract.getContractName()).append("变更为").append(request.getContractName()).append("<br/>");
                }
                if (!Objects.equals(oldContract.getPid(), request.getPid())) {
                    if ((oldContract.getPid() != 0 && request.getPid() == 0) ||
                            (oldContract.getPid() == 0 && request.getPid() != 0)) {
                        remark.append("合同类型：").append(oldContract.getPid() == 0 ? "母合同" : "子合同").append("变更为").append(request.getPid() == 0 ? "母合同" : "子合同").append("<br/>");
                    }
                    List<ErpContractPO> contractList = erpContractRepo.listByIds(Arrays.asList(oldContract.getPid(), request.getPid()));
                    if (CollectionUtils.isNotEmpty(contractList)) {
                        Map<Long, String> contractMap = contractList.stream().collect(Collectors.toMap(ErpContractPO::getId, ErpContractPO::getContractNo));
                        remark.append("母合同编码：").append(Optional.ofNullable(contractMap.get(oldContract.getPid())).orElse("")).append("变更为").append(Optional.ofNullable(contractMap.get(request.getPid())).orElse("")).append("<br/>");
                    }
                }
                if (!oldContract.getSubject().equals(request.getSubject())) {
                    remark.append("供应商：").append(oldContract.getSubject()).append("变更为").append(request.getSubject()).append("<br/>");
                }
                if (!Objects.equals(oldContract.getCooperationMode(), request.getCooperationMode())) {
                    remark.append("合作模式：").append(CooperationModeEnum.getDescByValue(oldContract.getCooperationMode())).append("变更为").append(CooperationModeEnum.getDescByValue(request.getCooperationMode())).append("<br/>");
                }
                if (!Objects.equals(oldContract.getPartner(), request.getPartner())) {
                    remark.append("合作对象：").append(CooperationObjectEnum.valueOf(oldContract.getPartner()).getDesc()).append("变更为").append(CooperationObjectEnum.valueOf(request.getPartner()).getDesc()).append("<br/>");
                }
                if (!oldContract.getStartTime().toLocalDate().equals(request.getStartTime())) {
                    remark.append("开始时间：").append(oldContract.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))).append("变更为").append(request.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))).append("<br/>");
                }
                if (!oldContract.getEndTime().toLocalDate().equals(request.getEndTime())) {
                    remark.append("结束时间：").append(oldContract.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))).append("变更为").append(request.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))).append("<br/>");
                }
                if (StringUtils.isNotBlank(request.getRemark()) && !request.getRemark().equals(oldContract.getRemark())) {
                    remark.append("备注：").append(Optional.ofNullable(oldContract.getRemark()).orElse("")).append("变更为").append(request.getRemark()).append("<br/>");
                }
                //添加操作记录
                erpOperateLogRepo.saveLog(tenantUtil.getUser(), OperateLogTypeEnum.CONTRACT, request.getId().toString(),
                        "编辑", "通过", remark.toString(), null, null);
            }
        } catch (DuplicateKeyException e) {
            log.warn("contractNo:{},startTime:{}", contractPo.getContractNo(), contractPo.getStartTime(), e);
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "请勿编辑成已存在相同合同编码和起始有效期！");
        }
        return Boolean.TRUE;
    }

    @Override
    public Page<SignContractVo> signContractList(SignContractQuery query) {
        Page<SignContractVo> page = new Page<>(query.getNumber(), query.getPageSize());
        page = erpContractRepo.pageContract(page, query);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return page;
        }
        List<Long> contractIdList = page.getRecords().stream().map(SignContractVo::getId).collect(Collectors.toList());
        //获取合同对应的付款单金额
        Map<Long, ErpBillDataDTO> contractBillDataMap = getBillData(contractIdList);
        //付款单总额与余额
        page.setRecords(page.getRecords().stream().peek(item -> {
            ErpBillDataDTO contractBill = contractBillDataMap.get(item.getId());
            if (Objects.nonNull(contractBill)) {
                item.setPayBillAmount(contractBill.getNeedAmount());
                item.setPayBillBalance(contractBill.getBalance());
            }
        }).collect(Collectors.toList()));
        return page;
    }

    //通过合同ID获取对应的付款单金额
    private Map<Long, ErpBillDataDTO> getBillData(List<Long> contractIdList) {
        Map<Long, ErpBillDataDTO> contractBillDataMap = new HashMap<>();
        //通过合同获取付款单ID
        List<ErpContractBillPO> contractBillList = erpContractBillRepo.getByContractId(contractIdList, BillTypeEnum.PAY_BILL.getValue());
        if (CollectionUtils.isEmpty(contractBillList)) {
            return contractBillDataMap;
        }
        Map<Long, List<ErpContractBillPO>> contractBillMap = contractBillList.stream().collect(Collectors.groupingBy(ErpContractBillPO::getContractId));
        List<Long> billIdList = contractBillList.stream().map(ErpContractBillPO::getBillId).collect(Collectors.toList());
        //获取付款单信息
        List<ErpBillDataDTO> billUseList = erpOutBillService.getBillUseList(billIdList);
        if (CollectionUtils.isEmpty(billUseList)) {
            return contractBillDataMap;
        }
        //过滤未审核的付款单
        billUseList = billUseList.stream().filter(item -> Objects.equals(item.getStatus(), 2)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(billUseList)) {
            return contractBillDataMap;
        }
        Map<Long, ErpBillDataDTO> billUseMap = billUseList.stream().collect(Collectors.toMap(ErpBillDataDTO::getBillId, Function.identity()));
        //遍历
        for (Map.Entry<Long, List<ErpContractBillPO>> entry : contractBillMap.entrySet()) {
            List<ErpContractBillPO> contractBills = entry.getValue();
            ErpBillDataDTO billDataDTO = new ErpBillDataDTO();
            //应付金额
            billDataDTO.setNeedAmount(BigDecimal.ZERO);
            //余额金额
            billDataDTO.setBalance(BigDecimal.ZERO);
            //计算：每个合同绑定的多个付款单的金额
            for (ErpContractBillPO cBill : contractBills) {
                ErpBillDataDTO bill = billUseMap.get(cBill.getBillId());
                if (bill != null) {
                    billDataDTO.setNeedAmount(billDataDTO.getNeedAmount().add(bill.getNeedAmount()));
                    billDataDTO.setBalance(billDataDTO.getBalance().add(bill.getBalance()));
                }
            }
            contractBillDataMap.put(entry.getKey(), billDataDTO);
        }
        return contractBillDataMap;
    }

    @Override
    public List<SignContractVo> getSignContractList(List<Long> billIdList) {
        ContractBillQuery query = new ContractBillQuery();
        query.setBillType(BillTypeEnum.REPORT_BILL.getValue());
        query.setBillIdList(billIdList);
        //通过提报单ID获取合同信息
        List<ContractBillDetailDTO> contractList = erpContractRepo.getContractByBillId(query);
        if (CollectionUtils.isEmpty(contractList)) {
            return null;
        }

        List<Long> contractIdList = contractList.stream().map(ContractBillDetailDTO::getId).collect(Collectors.toList());
        //获取合同对应的付款单金额
        Map<Long, ErpBillDataDTO> billUseMap = getBillData(contractIdList);
        return contractList.stream().map(item -> {
            SignContractVo vo = new SignContractVo();
            BeanUtils.copyProperties(item, vo);
            ErpBillDataDTO contractBill = billUseMap.get(item.getId());
            if (Objects.nonNull(contractBill)) {
                vo.setPayBillAmount(contractBill.getNeedAmount());
                vo.setPayBillBalance(contractBill.getBalance());
            }
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public Boolean batchAddContractBill(List<ContractBillDTO> cBillList) {
        //数据校验
        contractParamCheck(cBillList);
        //获取账单ID集合绑定的合同信息
        List<ErpContractBillPO> contractBillList = erpContractBillRepo.getContractBillList(cBillList.stream().map(ContractBillDTO::getBillId).collect(Collectors.toList()), cBillList.get(0).getBillType());
        if (CollectionUtils.isNotEmpty(contractBillList)) {
            //匹配账单ID跟合同ID绑定是否已存在
            Optional<ContractBillDTO> billFlag = cBillList.stream().filter(t -> contractBillList.stream().anyMatch(b -> Objects.equals(t.getContractId(), b.getContractId()) && Objects.equals(t.getBillId(), b.getBillId()))).findFirst();
            if (billFlag.isPresent()) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "绑定合同的已存在！");
            }
        }
        List<ErpContractBillPO> contractBillPOList = ContractConvertor.CONVERTOR.convertDTOToContractBillPOList(cBillList);
        return erpContractBillRepo.saveBatch(contractBillPOList);
    }

    //付款单、提报单绑定合同的参数校验
    private void contractParamCheck(List<ContractBillDTO> cBillList) {
        if (CollectionUtils.isEmpty(cBillList)) {
            throw new BusinessException(ErrorCodeEnum.EMPTY_PARAM.code(), "参数不能为空！");
        }
        Optional<ContractBillDTO> billFlag = cBillList.stream().filter(item -> Objects.isNull(item.getContractId()) || Objects.isNull(item.getBillId())
                || Objects.isNull(item.getBillType())).findFirst();
        if (billFlag.isPresent()) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "付款单、提报单绑定合同的参数不完整！");
        }
        //提报单类型，需要校验是否已经绑定了合同
        List<ContractBillDTO> contractBillList = cBillList.stream().filter(item -> Objects.equals(BillTypeEnum.REPORT_BILL.getValue(), item.getBillType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(contractBillList)) {
            List<ErpContractBillPO> billList = erpContractBillRepo.getContractBillList(contractBillList.stream().map(ContractBillDTO::getBillId).collect(Collectors.toList()),
                    BillTypeEnum.REPORT_BILL.getValue());
            if (CollectionUtils.isNotEmpty(billList)) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "提报单已绑定合同，不允许绑定多个合同！");
            }
        }
        //判断合同是否已存在
        List<Long> contractIdList = cBillList.stream().map(ContractBillDTO::getContractId).distinct().collect(Collectors.toList());
        List<ErpContractPO> contractList = erpContractRepo.listByIds(contractIdList);
        if (CollectionUtils.isEmpty(contractList) || contractList.size() != contractIdList.size()) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "合同不存在！");
        }
        //合同状态为待执行、执行中才可以操作
        List<ContractStateEnum> state = Arrays.asList(ContractStateEnum.WAIT_DEAL,
                ContractStateEnum.RUNNING_DEAL);
        Optional<ErpContractPO> contractStateFlag = contractList.stream().filter(item -> !state.contains(ContractStateEnum.valueOf(item.getState()))).findFirst();
        if (contractStateFlag.isPresent()) {
            throw new BusinessException(ErrorCodeEnum.EMPTY_PARAM.code(), contractStateFlag.get().getContractName() + "合同状态为" + ContractStateEnum.valueOf(contractStateFlag.get().getState()).getDesc() + "，请修改");
        }
    }

    @Override
    public Boolean batchUpdateContractBill(List<ContractBillDTO> cBillList) {
        //数据校验
        contractParamCheck(cBillList);
        //获取账单ID集合绑定的合同信息
        List<ErpContractBillPO> contractBillList = erpContractBillRepo.getContractBillList(cBillList.stream().map(ContractBillDTO::getBillId).collect(Collectors.toList()), cBillList.get(0).getBillType());
        if (CollectionUtils.isEmpty(contractBillList)) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "修改失败，绑定相应的合同不存在！");
        }
        //匹配被删除的数据
        List<ErpContractBillPO> toDel = contractBillList.stream().filter(item -> cBillList.stream().noneMatch(b -> Objects.equals(item.getContractId(), b.getContractId()))).collect(Collectors.toList());
        //过滤已存在
        List<ContractBillDTO> toAdd = cBillList.stream().filter(item -> contractBillList.stream().noneMatch(b -> Objects.equals(item.getContractId(), b.getContractId()))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(toDel)) {
            erpContractBillRepo.removeByIds(toDel.stream().map(ErpContractBillPO::getId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(toAdd)) {
            List<ErpContractBillPO> contractBillPOList = ContractConvertor.CONVERTOR.convertDTOToContractBillPOList(toAdd);
            erpContractBillRepo.saveBatch(contractBillPOList);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean delContractBill(ContractBillDTO contractBillDTO) {
        if (Objects.isNull(contractBillDTO)) {
            throw new BusinessException(ErrorCodeEnum.EMPTY_PARAM.code(), "参数不能为空！");
        }
        if (Objects.isNull(contractBillDTO.getBillType()) ||
                Objects.isNull(contractBillDTO.getBillId())) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "付款单、提报单解绑合同的参数不完整！");
        }
        return erpContractBillRepo.delContractBill(contractBillDTO);
    }

    @Override
    public Boolean changeState(ContractStateDTO contractStateDTO) {
        //合同状态判断
        if (Objects.isNull(ContractStateEnum.valueOf(contractStateDTO.getContractState()))) {
            throw new BusinessException(ErrorCodeEnum.EMPTY_PARAM.code(), "合同状态不正确！");
        }
        //获取合同列表
        List<ErpContractPO> contractList = erpContractRepo.listByIds(contractStateDTO.getIdList());
        if (CollectionUtils.isEmpty(contractList)) {
            throw new BusinessException(ErrorCodeEnum.EMPTY_PARAM.code(), "合同信息不存在！");
        }
        //合同模板类型判断
        boolean allDefaultContract = contractList.stream()
                .allMatch(contract -> ContractTemplateTypeEnum.DEFAULT_CONTRACT.equals(contract.getContractTemplateType()));
        if (!allDefaultContract) {
            throw new BusinessException(ErrorCodeEnum.EMPTY_PARAM.code(), "只能操作默认类型合同！");
        }
        //如果更新状态是执行中、执行完成，需要判断合同当前状态
        List<ContractStateEnum> state = Arrays.asList(ContractStateEnum.RUNNING_DEAL,
                ContractStateEnum.FINISH_DEAL, ContractStateEnum.STOP_DEAL, ContractStateEnum.RELIEVE_DEAL);
        if (state.contains(ContractStateEnum.valueOf(contractStateDTO.getContractState()))) {
            List<ContractStateEnum> dealState = new ArrayList<>();
            String stateDesc = "";
            switch (ContractStateEnum.valueOf(contractStateDTO.getContractState())) {
                case RUNNING_DEAL:
                    stateDesc = "待执行";
                    dealState.add(ContractStateEnum.WAIT_DEAL);
                    break;
                case FINISH_DEAL:
                    stateDesc = "执行中";
                    dealState.add(ContractStateEnum.RUNNING_DEAL);
                    break;
                case STOP_DEAL:
                case RELIEVE_DEAL:
                    stateDesc = "待执行、执行中";
                    dealState.addAll(Arrays.asList(ContractStateEnum.WAIT_DEAL, ContractStateEnum.RUNNING_DEAL));
                    break;
                default:
                    break;
            }
            Optional<ErpContractPO> stateFlag = contractList.stream().filter(item -> !dealState.contains(ContractStateEnum.valueOf(item.getState()))).findFirst();
            if (stateFlag.isPresent()) {
                throw new BusinessException(ErrorCodeEnum.EMPTY_PARAM.code(), "合同状态不是" + stateDesc +
                        ",不能进行" + ContractStateEnum.valueOf(contractStateDTO.getContractState()).getDesc() + "操作！");
            }
        }
        //合同状态修改
        boolean result = erpContractRepo.updateBatchById(contractList.stream().peek(item -> item.setState(contractStateDTO.getContractState())).collect(Collectors.toList()));
        if (result) {
            //添加操作记录
            List<String> stringList = contractStateDTO.getIdList().stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());
            erpOperateLogRepo.saveBatchLog(tenantUtil.getUser(), OperateLogTypeEnum.CONTRACT, stringList,
                    ContractStateEnum.valueOf(contractStateDTO.getContractState()).getDesc(), "通过", contractStateDTO.getRemark(), null, null);
        }
        return result;
    }

    public Integer getContractNumByBranchCode(String branchCode, String companyCode) {
        ContractInfoQuery query = new ContractInfoQuery();
        query.setBranchCodeList(Collections.singletonList(branchCode));
        query.setContractState(Arrays.asList(ContractStateEnum.WAIT_DEAL.getValue(),
                ContractStateEnum.RUNNING_DEAL.getValue()));
        query.setPartner(CooperationObjectEnum.ONE_TIME_PAY.getValue());
        query.setEndTime(LocalDate.now());
        ErpTenantPO tenant = tenantUtil.getTenantByCompanyCodeOrName(companyCode);
        if (Objects.nonNull(tenant)) {
            query.setTenantId(tenant.getTenantId());
        }
        List<ErpContractPO> contractList = erpContractRepo.getContractList(query);
        if (CollectionUtils.isEmpty(contractList)) {
            return 0;
        }
        return contractList.size();
    }

    @Override
    public void exportContractList(HttpServletResponse response, ContractQuery contractQuery) {
        List<ExportContractVO> exportContractVOList = Lists.newArrayList();
        //查询供应商
        contractQuery.setPageSize(-1);
        Page<ContractVo> page = this.pageList(contractQuery);
        List<ContractVo> contractVoList = page.getRecords();
        if (CollectionUtils.isNotEmpty(contractVoList)) {
            List<String> orgCodes = contractVoList.stream().map(ContractVo::getBranchCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            List<OrganizationBaseVO> organizationBaseVOList = bmsIntegration.listOrganizationBaseVOByOrgCodes(orgCodes);
            Map<String, String> orgNameMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(organizationBaseVOList)) {
                orgNameMap = organizationBaseVOList.stream().collect(Collectors.toMap(OrganizationBaseVO::getOrgCode, OrganizationBaseVO::getOrgName, (o, n) -> o));
            }
            for (ContractVo contractVo : contractVoList) {
                ExportContractVO exportContractVO = new ExportContractVO();
                BeanUtils.copyProperties(contractVo, exportContractVO);
                if (0 == exportContractVO.getPid()) {
                    exportContractVO.setContractTypeName(ErpConstants.PAR_CONTRACT);
                } else {
                    exportContractVO.setContractTypeName(ErpConstants.CHILD_CONTRACT);
                }
                exportContractVO.setCooperationModeName(CooperationModeEnum.getDescByValue(exportContractVO.getCooperationMode()));

                ContractStateEnum contractStateEnum = ContractStateEnum.valueOf(contractVo.getState());
                if (Objects.nonNull(contractStateEnum)) {
                    exportContractVO.setStateDesc(contractStateEnum.getDesc());
                }
                CooperationObjectEnum cooperationObjectEnum = CooperationObjectEnum.valueOf(contractVo.getPartner());
                if (Objects.nonNull(cooperationObjectEnum)) {
                    exportContractVO.setPartnerName(cooperationObjectEnum.getDesc());
                }
                exportContractVO.setOrgName(orgNameMap.get(contractVo.getBranchCode()));
                exportContractVOList.add(exportContractVO);
            }
        }

        EasyPoiUtil easyPoiUtil = new EasyPoiUtil();
        try {
            String fileName = String.format("财务管理-合同管理导出_%s", DateUtil.format(new Date(), "yyyyMMdd"));
            easyPoiUtil.downloadField(response, fileName, ExportContractVO.class, exportContractVOList, null);
        } catch (Exception e) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_EXCEPTION.getCode(), "合同导出异常" + e);
        }
    }

    @Override
    public void batchCreateContract(List<ContractImportDTO> contractImportDTOList) {
        //参数校验
        contractBatchJudge(contractImportDTOList);
        distributeLock.lockAndProcess(CommonConstants.CONTRACT_IMPORT_REDIS_LOCK, 2, -1, TimeUnit.SECONDS, () -> {
            //分批处理
            List<List<ContractImportDTO>> partitions = CommonUtil.splitIntoBatches(contractImportDTOList, 200);
            for (List<ContractImportDTO> partition : partitions) {
                contractService.batchCreateContractHandle(partition);
            }
            return true;
        });

    }

    @Transactional
    @Override
    public void batchCreateContractHandle(List<ContractImportDTO> contractImportDTOList) {
        ErpTenantPO tenant = tenantUtil.getTenantInfo();
        //获取供应商信息
        List<String> supplierCodeList = contractImportDTOList.stream().map(ContractImportDTO::getSupplierCode).distinct().collect(Collectors.toList());
        List<SupplierPO> supplierList = supplierRepo.getSupplierBatch(supplierCodeList);
        if (CollectionUtils.isEmpty(supplierList)) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "供应商信息不存在：" + String.join(",", supplierCodeList));
        }
        Map<String, SupplierPO> supplierMap = supplierList.stream().collect(Collectors.toMap(SupplierPO::getSupplierCode, Function.identity()));
        //获取母合同信息
        List<String> parentContractNoList = contractImportDTOList.stream().map(ContractImportDTO::getParentContractNo).distinct().collect(Collectors.toList());
        Map<String, ErpContractPO> parentContractMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(parentContractNoList)) {
            List<ErpContractPO> parentContractList = erpContractRepo.getContractListByNo(parentContractNoList);
            if (CollectionUtils.isEmpty(parentContractList)) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "母合同信息不存在：" + String.join(",", parentContractNoList));
            }
            parentContractMap = parentContractList.stream().collect(Collectors.toMap(ErpContractPO::getContractNo, Function.identity()));
        }
        Map<String, ErpContractPO> finalParentContractMap = parentContractMap;
        List<String> bizNoList = smartIdHelper.batchGeneratorDateCode("HT", SmartIdTypeEnum.FLOW_CODE, contractImportDTOList.size());
        AtomicInteger index = new AtomicInteger();
        List<ErpContractPO> contractPoList = contractImportDTOList.stream().map(item -> {
            ErpContractPO contractPo = new ErpContractPO();
            contractPo.setContractName(item.getContractName());
            contractPo.setContractNo(bizNoList.get(index.getAndIncrement()));
            contractPo.setPartner(Integer.valueOf(item.getPartner()));
            contractPo.setState(ContractStateEnum.WAIT_DEAL.getValue());
            contractPo.setEndTime(DateUtil.dateToLocalDateTime(DateUtil.parseDate(item.getEndTime(), DateUtil.FORMAT_DATE)));
            contractPo.setStartTime(DateUtil.dateToLocalDateTime(DateUtil.parseDate(item.getStartTime(), DateUtil.FORMAT_DATE)));
            contractPo.setSubject(item.getSubject());
            contractPo.setCooperationMode(Integer.valueOf(item.getCooperationMode()));
            if (Objects.equals(ErpConstants.CONTRACT_TYPE_DESC, item.getContractTypeDesc())) {
                ErpContractPO parentContract = finalParentContractMap.get(item.getParentContractNo());
                if (Objects.isNull(parentContract)) {
                    throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "母合同信息不存在：" +
                            item.getParentContractNo());
                }
                contractPo.setPid(parentContract.getId());
            }
            SupplierPO supplier = supplierMap.get(item.getSupplierCode());
            if (Objects.isNull(supplier)) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "供应商不存在或已禁用：" +
                        item.getSupplierCode());
            }
            if (Objects.equals(ContractPartnerEnum.ORG_PARTNER.getValue(), Integer.valueOf(item.getPartner()))) {
                contractPo.setBranchCode(item.getBranchCode());
                if (!Objects.equals(tenant.getCompanyName(), item.getSubject())) {
                    throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "合同主体不正确：" +
                            item.getContractName());
                }
            } else {
                if (!Objects.equals(supplier.getSupplierName(), item.getSubject())) {
                    throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "合同主体不正确：" +
                            item.getContractName());
                }
            }
            contractPo.setSupplierCode(item.getSupplierCode());
            return contractPo;
        }).collect(Collectors.toList());
        boolean result = erpContractRepo.saveBatch(contractPoList);
        if (result) {
            //添加操作记录
            erpOperateLogRepo.saveBatchLog(tenantUtil.getUser(), OperateLogTypeEnum.CONTRACT, contractPoList.stream().map(b -> b.getId().toString()).collect(Collectors.toList()),
                    "创建", "通过", "", null, null);
        }
    }

    //新增条件判断
    private void contractBatchJudge(List<ContractImportDTO> contractImportDTOList) {
        if (CollectionUtils.isEmpty(contractImportDTOList)) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "合同信息为空，不能创建！");
        }
        List<String> orgCodeList = contractImportDTOList.stream()
                .map(ContractImportDTO::getBranchCode)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, OrganizationParentVO> orgParentMap =new HashMap<>();
        if(CollectionUtils.isNotEmpty(orgCodeList)){
            orgParentMap = bmsIntegration.getParentOrgMapByOrgCodes(orgCodeList);
        }

        Map<String, OrganizationParentVO> finalOrgParentMap = orgParentMap;
        contractImportDTOList.forEach(item -> {
            if (StringUtils.isBlank(item.getContractName())) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "合同名称不能为空！");
            }
            if (StringUtils.isBlank(item.getSubject())) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "合同主体不能为空！");
            }
            if (StringUtils.isBlank(item.getContractTypeDesc())) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "合同类型不能为空！");
            }
            if (StringUtils.isBlank(item.getEndTime())) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "合同终止有效期不能为空！");
            }
            if (StringUtils.isBlank(item.getStartTime())) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "合同起始有效期不能为空！");
            }
            if (StringUtils.isBlank(item.getCooperationMode())) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "合作模式不能为空！");
            }
            if (StringUtils.isBlank(item.getPartner())) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "合作对象不能为空！");
            }
            if (StringUtils.isBlank(item.getSupplierCode())) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "供应商编码不能为空！");
            }
            if (Objects.equals(ErpConstants.CONTRACT_TYPE_DESC, item.getContractTypeDesc())
                    && StringUtils.isBlank(item.getParentContractNo())) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "合同类型为子合同，母合同编码不能为空！");
            }
            if (Objects.equals(ContractPartnerEnum.ORG_PARTNER.getValue(), Integer.valueOf(item.getPartner()))
                    && StringUtils.isBlank(item.getBranchCode())) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "合作对象为公司机构，机构编码不能为空！");
            }
            if (Objects.equals(ContractPartnerEnum.ORG_PARTNER.getValue(), Integer.valueOf(item.getPartner()))
                    && Objects.isNull(finalOrgParentMap.get(item.getBranchCode()))) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "合作对象为公司机构，机构编码: " + item.getBranchCode() + "，不存在");
            }
            if (Objects.isNull(ContractCooperationModeEnum.valueOf(Integer.parseInt(item.getCooperationMode())))) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), item.getContractName() + ",合作模式不存在！");
            }
            if (Objects.isNull(ContractPartnerEnum.valueOf(Integer.parseInt(item.getPartner())))) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), item.getContractName() + ",合作对象不存在！");
            }
            item.setEndTime(item.getEndTime().replace("/", "-"));
            item.setStartTime(item.getStartTime().replace("/", "-"));
            LocalDate endTime = DateUtil.dateToLocalDate(DateUtil.parseDate(item.getEndTime(), DateUtil.FORMAT_DATE));
            LocalDate startTime = DateUtil.dateToLocalDate(DateUtil.parseDate(item.getStartTime(), DateUtil.FORMAT_DATE));
            //终止有效期>起始有效期
            assert endTime != null;
            assert startTime != null;
            if (endTime.isBefore(startTime)) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), item.getContractName() + ",终止有效期必须大于起始有效期！");
            }
        });
    }

    /**
     * 清洁宝、清洁喵没有上传合同文件且未签订的机构合同，钉钉推送给机构负责人
     */
    public void pushDingding(String param) {

        String tenantId;
        String companyName;
        String templateNo;
        String date;
        String contractNo = null;
        if (StringUtils.isBlank(param)) {
            throw new BusinessException("tenantId必传");
        }
        if (!param.contains(",")) {
            throw new BusinessException("templateNo必传");
        }

        String[] array = param.trim().split(",");
        tenantId = array[0];
        companyName = array[1];
        templateNo = array[2];
        date = array[3];
        if (array.length > 4) {
            contractNo = array[4];
        }

        // 未签署合同
        LambdaQueryWrapper<ErpContractPO> qjmQueryWrapper =  new LambdaQueryWrapper<>();
        qjmQueryWrapper.eq(ErpContractPO::getTenantId, tenantId);
        qjmQueryWrapper.eq(ErpContractPO::getPartner, CooperationObjectEnum.ONE_TIME_PAY.getValue());
        qjmQueryWrapper.isNull(ErpContractPO::getContractFileIds);
        qjmQueryWrapper.notIn(ErpContractPO::getState, Arrays.asList(ContractStateEnum.STOP_DEAL.getValue(), ContractStateEnum.RELIEVE_DEAL.getValue()));

        if (StringUtils.isNotBlank(contractNo)) {
            qjmQueryWrapper.eq(ErpContractPO::getContractNo, contractNo);
        }
        List<ErpContractPO> contractList = this.list(qjmQueryWrapper);

        // 获取当前年份
        String year = DateUtil.format(new Date(), "yyyy");
        String currentDate = DateUtil.format(new Date(), DateUtil.FORMAT_DATE);

        // 开始时间:当期时间
        String startDate = DateUtil.format(new Date(), DateUtil.FORMAT_DATE);
        // 结束时间:当前时间加一年
        String endDate = DateUtil.format(DateUtil.addDate(new Date(), Calendar.YEAR, 1), DateUtil.FORMAT_DATE);
        String content = "您好，为规范" + companyName + "订货流程，确保货物保管到位，请在" + date + "之前，点击链接签署【" + companyName
                + "系列产品保管协议】： " + dingdingUrl + "?templateNo=" + templateNo + "&year=" + year + "&date=" + currentDate;
        this.pushContractDingdingMsg(contractList, templateNo, content);
    }

    @Override
    public void checkContractSigning(CheckContractDto checkContractDto) {
        log.info("合同签署校验,参数{}", JSONObject.toJSONString(checkContractDto));
        if (StringUtils.isBlank(checkContractDto.getModuleCode())) {
            return;
        }
        //获取保管合同校验模块名单（过滤无效配置）
        List<DictionaryItemVO> moduleList = bmsIntegration.getDictionaryItemsByTypeCode(ContractDicConstants.KEEP_CONTRACT_MODULE_LIST)
                .stream()
                .filter(dictionaryItemVO -> dictionaryItemVO.getItemStatus() == 1)
                .collect(Collectors.toList());
        log.info("合同签署校验,参数{},模块字典配置:{}", JSONObject.toJSONString(checkContractDto),JSONObject.toJSONString(moduleList));
        if (!CollectionUtils.isEmpty(moduleList) && moduleList.stream().anyMatch(item -> Objects.equals(item.getItemCode(), checkContractDto.getModuleCode()))) {
            checkModuleContract(checkContractDto,moduleList);
        }

        if (Objects.equals(checkContractDto.getModuleCode(), ContractCheckModelCodeEnum.ACTUAL_TRANSFER.getValue())) {
            checkTenantContract(checkContractDto);
        }
    }

    /**
     * 实物库存调拨--合同签署校验
     * @param checkContractDto 组织编码
     */
    private void checkTenantContract(CheckContractDto checkContractDto) {
        boolean result = true;
        String orgCode = checkContractDto.getBranchCode();
        //获取保管合同校验租户列表（过滤无效配置）
        List<DictionaryItemVO> dictList = bmsIntegration.getDictionaryItemsByTypeCode(ContractDicConstants.KEEP_CONTRACT_TENANTID_LIST)
                .stream()
                .filter(dictionaryItemVO -> dictionaryItemVO.getItemStatus() == 1)
                .collect(Collectors.toList());
        log.info("合同签署租户校验,分支编码:{},字典项:{}", orgCode, dictList);

        ErpTenantPO tenantPO = tenantUtil.getTenantInfo();
        log.info("合同签署租户校验,orgCode:{},租户:{}", orgCode, tenantPO);

        //只校验酒水、清洁喵、清洁宝租户是否签署合同
        if (!CollectionUtils.isEmpty(dictList) && !Objects.isNull(tenantPO) && dictList.stream().anyMatch(item -> Objects.equals(item.getItemCode(), tenantPO.getTenantId()+""))) {
            // 找到机构负责人
            List<String> orgCodeList = new ArrayList<>();
            orgCodeList.add(orgCode);
            List<OutOrganizationVO> userList = hrmsIntegration.queryOrgByCodes(orgCodeList);

            if (CollectionUtils.isEmpty(userList) || StringUtils.isEmpty(userList.get(0).getPrincipalEmployeeCode())) {
                log.warn("合同签署租户校验-{}分支没有找到机构负责人", orgCode);
                return;
            }

            //当前分支负责人合同列表为空或不存在状态为执行完成的合同时，需提示前往签署保管合同
            OutOrganizationVO userVo = userList.get(0);
            List<ContractBillDetailDTO> contractList = erpContractRepo.getContractByBranchContact(userVo.getPrincipalEmployeeCode(), userVo.getCode());
            if (CollectionUtils.isEmpty(contractList)
                    || contractList.stream().noneMatch(item -> Objects.equals(ContractStateEnum.RUNNING_DEAL.getValue(), item.getState()))) {
                log.info("合同签署租户校验,分支{}负责人{},没有签署对应的合同", userVo.getCode(), userVo.getPrincipalEmployeeCode());
                result = false;
            }
            //不存在待执行、执行中、执行完成的合同时，需出发创建合同流程
            createContract(contractList, userVo);

            if (!result) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(),
                       userVo.getName()+"还未签署保管合同，需分支负责人"+userVo.getPrincipalEmployeeName()+"前往【钉钉工作台-生服牛】进行合同签署");
            }
        }
    }

    public void checkContractSigned(PreCheckContractDTO preCheckContractDTO){
        //获取保管合同校验模块名单（过滤无效配置）
        List<DictionaryItemVO> moduleList = bmsIntegration.getDictionaryItemsByTypeCode(ContractDicConstants.KEEP_CONTRACT_MODULE_LIST)
                .stream()
                .filter(dictionaryItemVO -> dictionaryItemVO.getItemStatus() == 1)
                .collect(Collectors.toList());
        log.info("农服天杰合同签署校验,参数{},模块字典配置:{}", JSONObject.toJSONString(preCheckContractDTO),JSONObject.toJSONString(moduleList));
        distributeLock.lockAndProcess(CommonConstants.CONTRACT_SIGN_CHECK+ preCheckContractDTO.getBranchCode(),
                2, -1, TimeUnit.SECONDS, () -> {
            if (CollectionUtils.isNotEmpty(moduleList) && moduleList.stream().anyMatch(item -> Objects.equals(item.getItemCode(), preCheckContractDTO.getModuleCode()))) {
                createContractByModule(preCheckContractDTO);
            }

            List<String> modelCodeList = Arrays.asList(ContractCheckModelCodeEnum.ACTUAL_TRANSFER.getValue(), ContractCheckModelCodeEnum.SELLER_TRANSFER.getValue());
            if (modelCodeList.contains(preCheckContractDTO.getModuleCode())) {
                createContractByTenant(preCheckContractDTO);
            }
            return null;
        });
    }

    private void createContractByTenant(PreCheckContractDTO preCheckContractDTO) {
        AtomicReference<Boolean> result = new AtomicReference<>(Boolean.TRUE);
        List<AutoCreateAgricKeepContractDTO> keepContractInfoList = agricServiceConfig.getKeepContractInfoList();
        ErpTenantPO tenantPO = tenantUtil.getTenantInfo();
        if (CollectionUtils.isEmpty(keepContractInfoList) || Objects.isNull(tenantPO)){
            log.info("keepContractInfoList :{}  or tenant information:{} is null, return ", JSON.toJSONString(keepContractInfoList),JSON.toJSONString(tenantPO));
            return;
        }
        //获取保管合同校验租户开关
        List<DictionaryItemVO> dictList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.AGRIC_KEEP_CONTRACT_TENANTID_LIST)
                .stream()
                .filter(dictionaryItemVO -> dictionaryItemVO.getItemStatus() == 1)
                .collect(Collectors.toList());

        Boolean existsTenant =  CollectionUtil.isNotEmpty(dictList) && dictList.stream().anyMatch(item -> Objects.equals(item.getItemCode(), String.valueOf(tenantPO.getTenantId())));
        if(!existsTenant){
            log.info("In the absence of a configured tenant for the current tenant, contract signing validation shall not be performed.");
            return;
        }
        AutoCreateAgricKeepContractDTO autoCreateAgricKeepContractDTO = keepContractInfoList.stream().filter(item -> Objects.equals(item.getTenantId(), tenantPO.getTenantId())).findFirst().get();
        if(ObjectUtil.isEmpty(autoCreateAgricKeepContractDTO)){
            log.info("未配置当前租户：{} 的相关配置,当前登录租户不是否为农服、天杰租户,只有农服、天杰租户才能创建农服、天杰保管合同！",tenantPO.getTenantId());
            return;
        }
        OutOrganizationVO userVo = getOutOrganizationVO(preCheckContractDTO.getBranchCode(),preCheckContractDTO.getBranchName());
        List<ContractBillDetailDTO> contractList = erpContractRepo.getContractByBranchContactCode(userVo.getPrincipalEmployeeCode(), userVo.getCode(), autoCreateAgricKeepContractDTO.getContractTemplateType());
        if (CollectionUtils.isEmpty(contractList)
                || contractList.stream().noneMatch(item -> Objects.equals(ContractStateEnum.RUNNING_DEAL.getValue(), item.getState()))) {
            log.info("分支{}负责人{},没有签署对应的合同", userVo.getCode(), userVo.getPrincipalEmployeeCode());
            result.set(Boolean.FALSE);
        }
        //不存在待执行、执行中、执行完成的合同时，需出发创建合同流程
        result = erpContractService.createAgricContract(contractList, userVo, tenantPO.getTenantId(), autoCreateAgricKeepContractDTO.getContractTemplateType(),result);
        if (!result.get()) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(),
                    userVo.getName()+ "还未签署保管合同，需分支负责人" + userVo.getPrincipalEmployeeName() + "前往【钉钉工作台-中和ERP】进行合同签署。");
        }
    }

    private void createContractByModule(PreCheckContractDTO preCheckContractDTO) {
        AtomicReference<Boolean> result = new AtomicReference<>(Boolean.TRUE);
        OutOrganizationVO userVo = getOutOrganizationVO(preCheckContractDTO.getBranchCode(),preCheckContractDTO.getBranchName());
        List<ContractBillDetailDTO> contractList = erpContractRepo.getContractByBranchContactCode(userVo.getPrincipalEmployeeCode(), userVo.getCode(), preCheckContractDTO.getContractTemplateType());
        if (CollectionUtils.isEmpty(contractList)
                || contractList.stream().noneMatch(item -> Objects.equals(ContractStateEnum.RUNNING_DEAL.getValue(), item.getState()))) {
            log.info("分支{}负责人{},没有签署对应的合同", userVo.getCode(), userVo.getPrincipalEmployeeCode());
            result.set(Boolean.FALSE);
        }
        Map<Integer, List<AutoCreateAgricKeepContractDTO>> keepContractGroup = agricServiceConfig.getKeepContractInfoList().stream().collect(Collectors.groupingBy(AutoCreateAgricKeepContractDTO::getContractTemplateType));
        List<AutoCreateAgricKeepContractDTO> keepContractList = keepContractGroup.get(preCheckContractDTO.getContractTemplateType());
        if(CollectionUtils.isEmpty(keepContractList)){
            log.info("当前合同类型：{}，未配置在keepContractInfoList中", preCheckContractDTO.getContractTemplateType());
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAM.code(), "当前合同类型未配置,请联系管理员配置！" );
        }
        Long tenantId = keepContractList.get(0).getTenantId();
        //不存在待执行、执行中、执行完成的合同时，需出发创建合同流程
        result = erpContractService.createAgricContract(contractList, userVo, tenantId, preCheckContractDTO.getContractTemplateType(),result);
        if (!result.get()) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(),
                    userVo.getName()+"还未签署保管合同，需分支负责人" + userVo.getPrincipalEmployeeName() + "前往【钉钉工作台-中和ERP】进行合同签署。");
        }
    }

    /**
     * 获取分支机构负责人信息
     * @param branchCode
     * @return
     */
    private OutOrganizationVO getOutOrganizationVO(String branchCode,String branchName) {
        List<OutOrganizationVO> userList = hrmsIntegration.queryOrgByCodes(Collections.singletonList(branchCode));
        if (CollectionUtils.isEmpty(userList) || StringUtils.isEmpty(userList.get(0).getPrincipalEmployeeCode())) {
            log.warn("调拨申请-{}分支没有找到机构负责人", branchCode);
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "分支:"+ branchName+"没有找到机构负责人");
        }
        //当前分支负责人合同列表为空或不存在状态为执行完成的合同时，需提示前往签署保管合同
        OutOrganizationVO userVo = userList.get(0);
        return userVo;
    }

    /**
     * 调拨合同校验
     * @param checkContractDto 组织编码
     */
    private void checkModuleContract(CheckContractDto checkContractDto,List<DictionaryItemVO> moduleList) {
        boolean result = true;
        String orgCode = checkContractDto.getBranchCode();
        // 找到机构负责人
        List<String> orgCodeList = new ArrayList<>();
        orgCodeList.add(orgCode);
        List<OutOrganizationVO> userList = hrmsIntegration.queryOrgByCodes(orgCodeList);

        if (CollectionUtils.isEmpty(userList) || StringUtils.isEmpty(userList.get(0).getPrincipalEmployeeCode())) {
            log.error("合同签署模块校验-{}分支没有找到机构负责人", orgCode);
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(),
                    "您的分支还未签署保管合同，需分支负责人前往【钉钉工作台-生服牛】进行合同签署。");
        }

        //当前分支负责人合同列表为空或不存在状态为执行完成的合同时，需提示前往签署保管合同
        OutOrganizationVO userVo = userList.get(0);
        List<ContractBillDetailDTO> contractList = erpContractRepo.getContractByBranchContact(userVo.getPrincipalEmployeeCode(), userVo.getCode());
        log.info("合同签署模块校验,分支{}负责人{},合同列表:{}", userVo.getCode(), userVo.getPrincipalEmployeeCode(), contractList);
        if (CollectionUtils.isEmpty(contractList)
                || contractList.stream().noneMatch(item -> Objects.equals(ContractStateEnum.RUNNING_DEAL.getValue(), item.getState()))) {
            log.info("合同签署模块校验-分支{}负责人{},没有签署对应的合同", userVo.getCode(), userVo.getPrincipalEmployeeCode());
            result = false;
        }
        //不存在待执行、执行中、执行完成的合同时，需出发创建合同流程
        createContract(contractList, userVo);

        if (!result) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(),
                    "您的分支还未签署保管合同，需分支负责人" + userVo.getPrincipalEmployeeName() + "前往【钉钉工作台-生服牛】进行合同签署。");
        }
    }

    /**
     * 生成生服保管合同
     * @param keepContractFormDTO 前端用户填写表单
     * @return 生成的生服保管合同url
     */
    @Override
    public ContractGenPreResponse generateLifeServiceKeepContract(KeepContractFormDTO keepContractFormDTO) {
        String contractNo = keepContractFormDTO.getContractNo();
        ErpContractPO contractPO = erpContractRepo.getOne(new LambdaQueryWrapper<ErpContractPO>().eq(ErpContractPO::getContractNo, contractNo)
                .eq(ErpContractPO::getContractTemplateType, ContractTemplateTypeEnum.LIFE_SERVICE_KEEP_CONTRACT));
        if (Objects.isNull(contractPO)) {
            throw new BusinessException("合同编号不存在对应合同");
        }
        JwtUserInfo bmsUser = baseBmsTokenService.getUserByValid();
        ErpContractSignRecordPO signRecordPO = erpContractSignRecordRepo.getOne(new LambdaQueryWrapper<ErpContractSignRecordPO>()
                .eq(ErpContractSignRecordPO::getContractId, contractPO.getId())
                .eq(ErpContractSignRecordPO::getBranchContactCode, bmsUser.getJobNumber()));
        if (Objects.isNull(signRecordPO)) {
            throw new BusinessException("合同不需要您签署");
        }
        if (StringUtils.isNotEmpty(contractPO.getMsContractNo())) {
            log.info("contractNo:{} 合同已生成过，取数据表内url", contractNo);
            return getGeneratedContractInfo(contractPO);
        }

        KeepContractParamDTO keepContractParamDTO = new KeepContractParamDTO(keepContractFormDTO);
        //生成生效时间和终止时间
        LocalDate startDate = contractPO.getStartTime().toLocalDate();
        LocalDate endDate = contractPO.getEndTime().toLocalDate();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtil.FORMAT_DATE_CHINESE);
        keepContractParamDTO.setStartDate(startDate.format(dateTimeFormatter));
        keepContractParamDTO.setEndDate(endDate.format(dateTimeFormatter));
        //查询实物库存信息
        List<OrgDepotListInfoDto> inventoryInfoList = erpDepotService.getBranchDepotList(bmsUser.getHrOrgCode());
        keepContractParamDTO.setInventoryInfoList(inventoryInfoList);

        ContractGenerateRequest generateRequest = new ContractGenerateRequest();
        //设置业务流水号
        generateRequest.setBuzNo(contractNo);
        generateRequest.setBuzContractNo(contractNo);
        generateRequest.setTemplateNo(lifeServiceConfig.getKeepContractTemplateNo());
        generateRequest.setContractParamVo(new ContractParamVo(keepContractParamDTO));
        //设置contractType:01-进件, 02-授信, 03-放款， 04-外部合同， 05-其它
        generateRequest.setContractType(MsContraConstants.CONTRACT_TYPE);
        //设置签约类型:参考SignTypeEnum, paper-纸质合同,eSignH-客户手签,eSignA-客户默签
        generateRequest.setSignType(MsContraConstants.SIGN_TYPE);
        generateRequest.setRemark("机构生服保管协议");
        //设置业务来源,参考BusinessResourceEnum枚举类,LOAN-信贷，FRONT-前端，HR-hr系统，OTHER-其它
        generateRequest.setResource(BusinessResourceEnum.OTHER.getCode());
        generateRequest.setForceGenerate(true);

        //生成合同
        ContractGenPreResponse contractGenPreResponse = contractCustVerifyFacadeHelper.generateContract(generateRequest);

        //异步调用方法setHandSignInfo，然后返回合同生成数据
        ThreadPoolUtil.execute(() -> {
            log.info("setHandSignInfo contractGenPreResponse : {}", JSONObject.toJSONString(contractGenPreResponse));
            this.setHandSignInfo(contractPO, contractGenPreResponse);
        });
        return contractGenPreResponse;
    }

    /**
     * 生成生服保管合同
     * @param keepContractFormDTO 前端用户填写表单
     * @return 生成的生服保管合同url
     */
    @Override
    public ContractGenPreResponse generateKeepContract(KeepContractExtendFormDTO keepContractFormDTO) {
        String contractNo = keepContractFormDTO.getContractNo();
        ErpContractPO contractPO = erpContractRepo.getOne(new LambdaQueryWrapper<ErpContractPO>()
                .eq(ErpContractPO::getContractNo, contractNo));
        if (Objects.isNull(contractPO)) {
            throw new BusinessException("合同编号不存在对应合同");
        }
        JwtUserInfo bmsUser = baseBmsTokenService.getUserByValid();
        ErpContractSignRecordPO signRecordPO = erpContractSignRecordRepo.getOne(new LambdaQueryWrapper<ErpContractSignRecordPO>()
                .eq(ErpContractSignRecordPO::getContractId, contractPO.getId())
                .eq(ErpContractSignRecordPO::getBranchContactCode, bmsUser.getJobNumber()));
        if (Objects.isNull(signRecordPO)) {
            throw new BusinessException("合同不需要您签署");
        }
        if (StringUtils.isNotEmpty(contractPO.getMsContractNo())) {
            log.info("contractNo:{} 合同已生成过，取数据表内url", contractNo);
            return getGeneratedContractInfo(contractPO);
        }

        KeepContractParamDTO keepContractParamDTO = new KeepContractParamDTO(keepContractFormDTO);

        List<DictionaryItemVO> dictList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.AGRIC_TIANJIE_KEEP_CONTRACT_YEAR_CONFIG);
        Integer contractTemplateType = keepContractFormDTO.getContractTemplateType();
        DictionaryItemVO dictionaryItemVO = null;
        if(CollectionUtils.isNotEmpty(dictList)){
            dictionaryItemVO = dictList.stream().filter(item -> item.getItemCode().equals(String.valueOf(contractTemplateType))).findFirst().get();
        }
        int year = ObjectUtil.isNotEmpty(dictionaryItemVO) ? Integer.parseInt(dictionaryItemVO.getItemName()) : Constants.YEAR ;
        //生成生效时间和终止时间
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = LocalDate.of(startDate.getYear() + year, 12, 31);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtil.FORMAT_DATE_CHINESE);
        keepContractParamDTO.setStartDate(startDate.format(dateTimeFormatter));
        keepContractParamDTO.setEndDate(endDate.format(dateTimeFormatter));
        //查询实物库存信息
        List<OrgDepotListInfoDto> inventoryInfoList = erpDepotService.getBranchDepotList(bmsUser.getHrOrgCode());
        keepContractParamDTO.setInventoryInfoList(inventoryInfoList);

        ContractGenerateRequest generateRequest = new ContractGenerateRequest();
        //设置业务流水号
        generateRequest.setBuzNo(contractNo);
        generateRequest.setBuzContractNo(contractNo);
        String templateNo = Objects.equals(ContractTemplateTypeEnum.AGRIC_SERVICE_KEEP_CONTRACT.getValue(), keepContractFormDTO.getContractTemplateType()) ? agricServiceConfig.getKeepContractTemplateNo() : agricServiceConfig.getTjKeepContractTemplateNo();
        generateRequest.setTemplateNo(templateNo);
        generateRequest.setContractParamVo(new ContractParamVo(keepContractParamDTO));
        //设置contractType:01-进件, 02-授信, 03-放款， 04-外部合同， 05-其它
        generateRequest.setContractType(MsContraConstants.CONTRACT_TYPE);
        //设置签约类型:参考SignTypeEnum, paper-纸质合同,eSignH-客户手签,eSignA-客户默签
        generateRequest.setSignType(MsContraConstants.SIGN_TYPE);
        generateRequest.setRemark(Objects.equals(ContractTemplateTypeEnum.AGRIC_SERVICE_KEEP_CONTRACT.getValue(), keepContractFormDTO.getContractTemplateType()) ? "农服保管协议" : "成都天杰保管合同");
        //设置业务来源,参考BusinessResourceEnum枚举类,LOAN-信贷，FRONT-前端，HR-hr系统，OTHER-其它
        generateRequest.setResource(BusinessResourceEnum.OTHER.getCode());
        generateRequest.setForceGenerate(Boolean.TRUE);

        //生成合同
        ContractGenPreResponse contractGenPreResponse = contractCustVerifyFacadeHelper.generateContract(generateRequest);

        //异步调用方法setHandSignInfo，然后返回合同生成数据
        ThreadPoolUtil.execute(() -> {
            log.info("setHandSignInfo contractGenPreResponse : {}", JSONObject.toJSONString(contractGenPreResponse));
            this.setHandSignInfo(contractPO, contractGenPreResponse);
        });
        return contractGenPreResponse;
    }

    @Override
    public ContractGenPreResponse generateLifeServiceUnitedKeepContract(UnitedKeepContractFormDTO unitedKeepContractFormDTO) {
        log.info("generateLifeServiceUnitedKeepContract:{}",JSONObject.toJSONString(unitedKeepContractFormDTO));
        String contractNo = unitedKeepContractFormDTO.getContractNo();
        ErpContractPO contractPO = erpContractRepo.getOne(new LambdaQueryWrapper<ErpContractPO>().eq(ErpContractPO::getContractNo, contractNo)
                .eq(ErpContractPO::getContractTemplateType, ContractTemplateTypeEnum.LIFE_SERVICE_UNITED_KEEP_CONTRACT));
        if (Objects.isNull(contractPO)) {
            throw new BusinessException("合同编号不存在对应合同");
        }
        JwtUserInfo bmsUser = baseBmsTokenService.getUserByValid();
        log.info("用户信息：{}",JSONObject.toJSONString(bmsUser));
        ErpContractSignRecordPO signRecordPO = erpContractSignRecordRepo.getOne(new LambdaQueryWrapper<ErpContractSignRecordPO>()
                .eq(ErpContractSignRecordPO::getContractId, contractPO.getId())
                .eq(ErpContractSignRecordPO::getBranchContactCode, bmsUser.getJobNumber())
                .eq(ErpContractSignRecordPO::getBranchCode, bmsUser.getHrOrgCode()));
        if (Objects.isNull(signRecordPO)) {
            throw new BusinessException("合同不需要您签署");
        }
        if (StringUtils.isNotEmpty(contractPO.getMsContractNo())) {
            log.info("contractNo:{} 合同已生成过，取数据表内url", contractNo);
            return getGeneratedContractInfo(contractPO);
        }

        UnitedKeepContractParamDTO unitedKeepContractParamDTO = new UnitedKeepContractParamDTO(unitedKeepContractFormDTO);
        //生成生效时间和终止时间
        LocalDate startDate = contractPO.getStartTime().toLocalDate();
        LocalDate endDate = contractPO.getEndTime().toLocalDate();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtil.FORMAT_DATE_CHINESE);
        unitedKeepContractParamDTO.setStartDate(startDate.format(dateTimeFormatter));
        unitedKeepContractParamDTO.setEndDate(endDate.format(dateTimeFormatter));

        //查询签署人列表
        List<ErpContractSignRecordPO> signRecordPOList = erpContractSignRecordRepo.list(new LambdaQueryWrapper<ErpContractSignRecordPO>()
                .eq(ErpContractSignRecordPO::getContractId, contractPO.getId()).orderByAsc(ErpContractSignRecordPO::getSort));
        List<String> branchCodeList = signRecordPOList.stream().map(ErpContractSignRecordPO::getBranchCode).collect(Collectors.toList());
        List<OutOrganizationVO> branchList = hrmsIntegration.queryOrgByCodes(branchCodeList);
        Map<String, OutOrganizationVO> branchHeaderMap = branchList.stream().collect(Collectors.toMap(OutOrganizationVO::getCode, Function.identity()));
        List<UnitedKeepContractSignerVO> signList = new ArrayList<>();
        for (ErpContractSignRecordPO erpContractSignRecordPO : signRecordPOList) {
            UnitedKeepContractSignerVO signerVO =
                    ContractSignRecordConvertor.CONVERTOR.convertContractSignRecordToUnitedKeepContractSignerVO(erpContractSignRecordPO);
            //获取分支负责人身份证
            OutOrganizationVO organizationVO = branchHeaderMap.get(erpContractSignRecordPO.getBranchCode());
            OnBoardEmployeeVO hrmsEmployeeInfo = hrmsIntegration.queryOnboardEmployeeById(organizationVO.getPrincipalEmployeeId());
            if (Objects.isNull(hrmsEmployeeInfo)) {
                log.error("分支编码 {} 工号 {} 查询在职员工信息失败", organizationVO.getCode(), organizationVO.getPrincipalEmployeeCode());
                throw new BusinessException("在职员工信息查询失败");
            }
            signerVO.setBranchContactIdNum(hrmsEmployeeInfo.getIdNo());

            //获取分支（分支仓+个人仓）现有总库存
            List<BranchDepotCurrentStockDto> branchDepotCurrentStockList =
                    erpDepotService.getBranchDepotListBySkuIds(lifeServiceConfig.getUnitedKeepContractInfo().getTenantId(),
                            erpContractSignRecordPO.getBranchCode(), lifeServiceConfig.getUnitedKeepContractSkuIdList());
            BigDecimal stockNumber = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(branchDepotCurrentStockList)) {
                stockNumber = branchDepotCurrentStockList.stream().map(BranchDepotCurrentStockDto::getTotalStock)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            signerVO.setStockNumber(stockNumber.toPlainString());
            signList.add(signerVO);
        }

        unitedKeepContractParamDTO.setSignList(signList);
        //signList根据分支联系人证件号码去重
        List<UnitedKeepContractSignerVO> distinctSignList = signList.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(UnitedKeepContractSignerVO::getBranchContactIdNum))), ArrayList::new));
        unitedKeepContractParamDTO.setDistinctSignList(distinctSignList);
        String totalStock = signList.stream().map(signerVO -> new BigDecimal(signerVO.getStockNumber()))
                .reduce(BigDecimal.ZERO, BigDecimal::add).toPlainString();
        unitedKeepContractParamDTO.setTotalStock(totalStock);

        ContractGenerateRequest generateRequest = new ContractGenerateRequest();
        //设置业务流水号
        generateRequest.setBuzNo(contractNo);
        generateRequest.setBuzContractNo(contractNo);
        generateRequest.setTemplateNo(lifeServiceConfig.getUnitedKeepContractTemplateNo());
        generateRequest.setContractParamVo(new ContractParamVo(unitedKeepContractParamDTO));
        //设置contractType:01-进件, 02-授信, 03-放款， 04-外部合同， 05-其它
        generateRequest.setContractType(MsContraConstants.CONTRACT_TYPE);
        //设置签约类型:参考SignTypeEnum, paper-纸质合同,eSignH-客户手签,eSignA-客户默签
        generateRequest.setSignType(MsContraConstants.SIGN_TYPE);
        generateRequest.setRemark("机构生服珍酒联保协议");
        //设置业务来源,参考BusinessResourceEnum枚举类,LOAN-信贷，FRONT-前端，HR-hr系统，OTHER-其它
        generateRequest.setResource(BusinessResourceEnum.OTHER.getCode());
        generateRequest.setForceGenerate(Boolean.TRUE);


        //生成合同
        ContractGenPreResponse contractGenPreResponse = contractCustVerifyFacadeHelper.generateContract(generateRequest);

        //异步调用方法setHandSignInfo，然后返回合同生成数据
        ThreadPoolUtil.execute(() -> {
            log.info("setHandSignInfo contractGenPreResponse : {}", JSONObject.toJSONString(contractGenPreResponse));
            this.setHandSignInfo(contractPO, contractGenPreResponse);
        });
        return contractGenPreResponse;
    }

    private ContractGenPreResponse getGeneratedContractInfo(ErpContractPO contractPO) {
        ContractGenPreResponse vo = new ContractGenPreResponse();
        vo.setBuzContractNo(contractPO.getContractNo());
        vo.setContractNo(contractPO.getMsContractNo());
        vo.setTitle(contractPO.getContractName());
        //调合同服务接口获取预览地址
        ContractFindResponse msContractData = this.queryContractByMsContractNo(contractPO.getMsContractNo());
        vo.setViewURL(msContractData.getViewURL());
        vo.setDownloadURL(msContractData.getDownloadURL());
        return vo;
    }

    /**
     * 获取合同签署url
     * @param msContractNo 和前端商量传入的编码为合同服务提供
     * @return
     */
    @Override
    public ContractESignHeadFindResponse getSignUrl(String msContractNo) {
        ContractESignHeadFindRequest handSignRequest = getContractESignHeadFindRequest(msContractNo);
        //获取签署url
        return contractCustVerifyFacadeHelper.headSignURL(handSignRequest);
    }

    /**
     * 获取合同签署url
     * @param msContractNo 和前端商量传入的编码为合同服务提供
     * @return
     */
    @Override
    public ContractESignHeadFindResponse getContrictSignUrl(String msContractNo) {
        ContractESignHeadFindRequest handSignRequest = getAgricContractESignHeadFindRequest(msContractNo);
        handSignRequest.setContractSignToken(MsContraConstants.CONTRACT_SIGN_TOKEN);
        //获取签署url
        return contractCustVerifyFacadeHelper.headSignURL(handSignRequest);
    }

    private ContractESignHeadFindRequest getContractESignHeadFindRequest(String msContractNo) {
        ErpContractPO erpContractPO = erpContractRepo.getOne(new LambdaQueryWrapper<ErpContractPO>().eq(ErpContractPO::getMsContractNo, msContractNo));
        if (Objects.isNull(erpContractPO)) {
            throw new BusinessException("合同编号不存在对应合同");
        }
        // 多个分支负责人是同一个人兼容
        JwtUserInfo bmsUser = baseBmsTokenService.getUserByValid();
        ErpContractSignRecordPO signRecordPO = erpContractSignRecordRepo.getOne(new LambdaQueryWrapper<ErpContractSignRecordPO>()
                .eq(ErpContractSignRecordPO::getContractNo, erpContractPO.getContractNo())
                .eq(ErpContractSignRecordPO::getBranchCode, bmsUser.getHrOrgCode())
                .eq(ErpContractSignRecordPO::getBranchContactCode, bmsUser.getJobNumber()));
        if (Objects.isNull(signRecordPO)) {
            throw new BusinessException("合同不需要您签署");
        }
        List<OutOrganizationVO> organizationVOList = hrmsIntegration.queryOrgByCodes(Collections.singletonList(signRecordPO.getBranchCode()));
        if (CollectionUtils.isEmpty(organizationVOList) || !signRecordPO.getBranchContactCode().equals(organizationVOList.get(0).getPrincipalEmployeeCode())) {
            throw new BusinessException("分支负责人已更换");
        }
        OnBoardEmployeeVO branchHeaderVO = hrmsIntegration.queryOnboardEmployeeById(organizationVOList.get(0).getPrincipalEmployeeId());
        if (Objects.isNull(branchHeaderVO)) {
            throw new BusinessException("分支负责人信息不存在，可能已经离职");
        }

        if (!ContractSignRecordStatusEnum.ENABLED.equals(signRecordPO.getSignStatus())) {
            throw new BusinessException(String.format("您的合同签署状态为【%s】，不需要签署", signRecordPO.getSignStatus().getDesc()));
        }

        ContractESignHeadFindRequest handSignRequest = new ContractESignHeadFindRequest();
        handSignRequest.setTransactionId(signRecordPO.getTransactionId());
        handSignRequest.setContractNo(erpContractPO.getMsContractNo());
        handSignRequest.setSignKeyword(String.format("%s-sign", signRecordPO.getBranchContact()));
        handSignRequest.setCustId(signRecordPO.getBranchContactCode());
        handSignRequest.setCustName(branchHeaderVO.getEmployeeName());
        handSignRequest.setIdCardNo(branchHeaderVO.getIdNo());
        handSignRequest.setMobile(branchHeaderVO.getMobile());
        handSignRequest.setResource(BusinessResourceEnum.OTHER.getCode());
        handSignRequest.setContractSignToken(MsContraConstants.MALL_CONTRACT_SIGN_TOKEN);
        return handSignRequest;
    }

    private ContractESignHeadFindRequest getAgricContractESignHeadFindRequest(String msContractNo) {
        ErpContractPO erpContractPO = erpContractRepo.getOne(new LambdaQueryWrapper<ErpContractPO>().eq(ErpContractPO::getMsContractNo, msContractNo));
        if (Objects.isNull(erpContractPO)) {
            throw new BusinessException("合同编号不存在对应合同");
        }
        // 多个分支负责人是同一个人兼容
        JwtUserInfo bmsUser = baseBmsTokenService.getUserByValid();
        List<ErpContractSignRecordPO> signRecordList = erpContractSignRecordRepo.list(new LambdaQueryWrapper<ErpContractSignRecordPO>()
                .eq(ErpContractSignRecordPO::getContractNo, erpContractPO.getContractNo())
                .eq(ErpContractSignRecordPO::getBranchContactCode, bmsUser.getJobNumber()));
        if (CollectionUtils.isEmpty(signRecordList)) {
            throw new BusinessException("合同不需要您签署");
        }
        List<OutOrganizationVO> organizationVOList = hrmsIntegration.queryOrgByCodes(Collections.singletonList(erpContractPO.getBranchCode()));
        ErpContractSignRecordPO signRecordPO = signRecordList.stream().filter(signRecord -> signRecord.getContractNo().equals(erpContractPO.getContractNo())).findFirst().orElse(new ErpContractSignRecordPO());

        if (CollectionUtils.isEmpty(organizationVOList) || !signRecordPO.getBranchContactCode().equals(organizationVOList.get(0).getPrincipalEmployeeCode())) {
            throw new BusinessException("分支负责人已更换");
        }
        OnBoardEmployeeVO branchHeaderVO = hrmsIntegration.queryOnboardEmployeeById(organizationVOList.get(0).getPrincipalEmployeeId());
        if (Objects.isNull(branchHeaderVO)) {
            throw new BusinessException("分支负责人信息不存在，可能已经离职");
        }

        if (!ContractSignRecordStatusEnum.ENABLED.equals(signRecordPO.getSignStatus())) {
            throw new BusinessException(String.format("您的合同签署状态为【%s】，不需要签署", signRecordPO.getSignStatus().getDesc()));
        }

        ContractESignHeadFindRequest handSignRequest = new ContractESignHeadFindRequest();
        handSignRequest.setTransactionId(signRecordPO.getTransactionId());
        handSignRequest.setContractNo(erpContractPO.getMsContractNo());
        handSignRequest.setSignKeyword(String.format("%s-sign", signRecordPO.getBranchContact()));
        handSignRequest.setCustId(signRecordPO.getBranchContactCode());
        handSignRequest.setCustName(branchHeaderVO.getEmployeeName());
        handSignRequest.setIdCardNo(branchHeaderVO.getIdNo());
        handSignRequest.setMobile(branchHeaderVO.getMobile());
        handSignRequest.setResource(BusinessResourceEnum.OTHER.getCode());
        handSignRequest.setContractSignToken(MsContraConstants.MALL_CONTRACT_SIGN_TOKEN);
        return handSignRequest;
    }

    /**
     * 电签结果回调
     * @param request
     */
    @Override
    public void contractESignCallback(ContractESignCallbackRequest request) {
        if (!request.getSignResult()) {
            log.info("电签未成功，不更改合同签署记录状态 transactionId : {}", request.getTransactionId());
            return;
        }

        ErpContractSignRecordPO signRecordPO = erpContractSignRecordRepo.getOne(new LambdaQueryWrapper<ErpContractSignRecordPO>()
                .eq(ErpContractSignRecordPO::getTransactionId, request.getTransactionId()));
        if (Objects.isNull(signRecordPO)) {
            throw new BusinessException("合同签署记录不存在");
        }
        List<ErpContractSignRecordPO> changeList = new ArrayList<>();

        ErpContractSignRecordPO updateSignedRecord = new ErpContractSignRecordPO();
        updateSignedRecord.setId(signRecordPO.getId());
        updateSignedRecord.setSignStatus(ContractSignRecordStatusEnum.SIGNING);
        updateSignedRecord.setSignTime(LocalDateTime.now());
        changeList.add(updateSignedRecord);

        //寻找下一个签署人，并设置为签署中
        ErpContractSignRecordPO nextSignerPO = erpContractSignRecordRepo.getOne(new LambdaQueryWrapper<ErpContractSignRecordPO>()
                .eq(ErpContractSignRecordPO::getContractNo, signRecordPO.getContractNo())
                .eq(ErpContractSignRecordPO::getSort, signRecordPO.getSort() + 1));
        if (Objects.nonNull(nextSignerPO)) {
            ErpContractSignRecordPO updateNextSigner = new ErpContractSignRecordPO();
            updateNextSigner.setId(nextSignerPO.getId());
            updateNextSigner.setSignStatus(ContractSignRecordStatusEnum.ENABLED);
            changeList.add(updateNextSigner);
        }

        //批量更新
        erpContractSignRecordRepo.updateBatchById(changeList);
    }

    @Override
    public void initContractTemplateType() {
        List<AutoCreateKeepContractDTO> keepContractInfoList = lifeServiceConfig.getKeepContractInfoList();
        //查询合同类型为子合同 && 合作对象为公司机构的合同列表
        List<ErpContractPO> list = erpContractRepo.list(new LambdaQueryWrapper<ErpContractPO>()
                .ne(ErpContractPO::getPid, 0)
                .in(ErpContractPO::getTenantId, keepContractInfoList.stream().map(AutoCreateKeepContractDTO::getTenantId).collect(Collectors.toList()))
                .eq(ErpContractPO::getPartner, 2));
        System.out.println("list:"+list);

        if (!CollectionUtils.isEmpty(list)) {
            List<String> branchCodeList = list.stream().map(ErpContractPO::getBranchCode).collect(Collectors.toList());
            Map<String,OutOrganizationVO> userMap = hrmsIntegration.queryOrgByCodes(branchCodeList)
                    .stream()
                    .collect(Collectors.toMap(
                            OutOrganizationVO::getCode,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));
            ;

            list.forEach(contractPO -> {
                contractPO.setContractTemplateType(ContractTemplateTypeEnum.LIFE_SERVICE_KEEP_CONTRACT);
                if (userMap.containsKey(contractPO.getBranchCode())) {
                    contractPO.setBranchContact(userMap.get(contractPO.getBranchCode()).getPrincipalEmployeeName());
                    contractPO.setBranchContactMobile(userMap.get(contractPO.getBranchCode()).getPrincipalEmployeeMobile());
                    contractPO.setBranchContactCode(userMap.get(contractPO.getBranchCode()).getPrincipalEmployeeCode());
                }
            });

            erpContractRepo.updateBatchById(list);
        }
    }

    @Override
    public void initAgricContractTemplateType() {
        List<AutoCreateAgricKeepContractDTO> keepContractInfoList = agricServiceConfig.getKeepContractInfoList();
        if(CollectionUtils.isEmpty(keepContractInfoList)){
            log.info("Initialize agricultural contract type is empty");
            return;
        }
        // 把keepContractInfoList根据tenantId分组
        Map<Long, List<AutoCreateAgricKeepContractDTO>> keepContractInfoMap = keepContractInfoList.stream().collect(Collectors.groupingBy(AutoCreateAgricKeepContractDTO::getTenantId));
        //查询合同类型为子合同 && 合作对象为公司机构的合同列表
        List<Long> tenantIdList = keepContractInfoList.stream().map(AutoCreateAgricKeepContractDTO::getTenantId).collect(Collectors.toList());
        List<ErpContractPO> contractList = erpContractRepo.list(new LambdaQueryWrapper<ErpContractPO>()
                .ne(ErpContractPO::getPid, 0)
                .in(ErpContractPO::getTenantId, tenantIdList)
                .eq(ErpContractPO::getPartner, 2));
        log.info("initAgricContractTemplateType tenantIdList:{},contract result :{}",tenantIdList,contractList);
        if (CollectionUtils.isEmpty(contractList)) {
            log.info("initAgricContractTemplateType tenantIdList:{}, query contract result is empty",tenantIdList);
            return;
        }

        List<String> branchCodeList = contractList.stream().map(ErpContractPO::getBranchCode).collect(Collectors.toList());
        Map<String,OutOrganizationVO> userMap = hrmsIntegration.queryOrgByCodes(branchCodeList)
                .stream()
                .collect(Collectors.toMap(
                        OutOrganizationVO::getCode,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        contractList.forEach(contractPO -> {
            List<AutoCreateAgricKeepContractDTO> autoCreateAgricKeepContracts = keepContractInfoMap.get(contractPO.getTenantId());
            if(CollectionUtil.isNotEmpty(autoCreateAgricKeepContracts)){
                contractPO.setContractTemplateType(ContractTemplateTypeEnum.valueOf(autoCreateAgricKeepContracts.get(0).getContractTemplateType()));
            }
            if (userMap.containsKey(contractPO.getBranchCode())) {
                contractPO.setBranchContact(userMap.get(contractPO.getBranchCode()).getPrincipalEmployeeName());
                contractPO.setBranchContactMobile(userMap.get(contractPO.getBranchCode()).getPrincipalEmployeeMobile());
                contractPO.setBranchContactCode(userMap.get(contractPO.getBranchCode()).getPrincipalEmployeeCode());
            }
        });

        erpContractRepo.updateBatchById(contractList);
    }

    @Override
    public void createContract() {
        log.info("开始执行---到期前15天保管合同新建第二年合同---定时任务");
        //获取当前后15天的日期
        LocalDateTime endDate = LocalDate.now().plusDays(15).atTime(23,59,59);

        //查询合同到期时间大于15天后的待执行、执行中且租户为酒水的酒水保管合同列表
        List<Integer> stateList = Arrays.asList(ContractStateEnum.WAIT_DEAL.getValue(), ContractStateEnum.RUNNING_DEAL.getValue());
        List<ErpContractPO> contractList = erpContractRepo.list(new LambdaQueryWrapper<ErpContractPO>()
                .in(ErpContractPO::getState, stateList)
                .eq(ErpContractPO::getContractTemplateType, ContractTemplateTypeEnum.LIFE_SERVICE_KEEP_CONTRACT)
                .gt(ErpContractPO::getEndTime, endDate)
                .in(ErpContractPO::getTenantId, basicsConfig.getTenantIdList())
                .eq(ErpContractPO::getIfDelete,0));

        if (CollectionUtils.isNotEmpty(contractList)) {
            List<String> contracrOrgCodeList = contractList.stream().map(ErpContractPO::getBranchCode).collect(Collectors.toList());

            List<ContractBillDetailDTO> contractBillDetailDTOList = new ArrayList<>();
            //查询15天内到期，状态为执行中且分支编码不在contractOrgCodeList中的的保管合同列表

            List<ErpContractPO> contractPOList = erpContractRepo.list(new LambdaQueryWrapper<ErpContractPO>()
                    .eq(ErpContractPO::getState, ContractStateEnum.RUNNING_DEAL.getValue())
                    .eq(ErpContractPO::getContractTemplateType, ContractTemplateTypeEnum.LIFE_SERVICE_KEEP_CONTRACT)
                    .le(ErpContractPO::getEndTime, endDate)
                    .notIn(ErpContractPO::getBranchCode, contracrOrgCodeList)
                    .in(ErpContractPO::getTenantId, basicsConfig.getTenantIdList())
                    .eq(ErpContractPO::getIfDelete, 0));
            log.info("15天后到期的合同列表：{}",JSONObject.toJSONString(contractPOList));

            if (!CollectionUtils.isEmpty(contractPOList)) {
                List<String> orgCodeList = contractPOList.stream().map(ErpContractPO::getBranchCode).collect(Collectors.toList());
                //过滤掉分支负责人为空的分支
                List<OutOrganizationVO> branchList = hrmsIntegration.queryOrgByCodes(orgCodeList)
                        .stream()
                        .filter(item -> StringUtils.isNotBlank(item.getPrincipalEmployeeCode()))
                        .collect(Collectors.toList());
                Map<String, OutOrganizationVO> userMap = branchList.stream()
                        .collect(Collectors.toMap(
                                OutOrganizationVO::getCode,
                                Function.identity(),
                                (existing, replacement) -> existing
                        ));

                contractPOList.forEach(contractPO -> {
                    //新创建保管合同
                    log.info("合同到期前15天自动创建新合同，原合同信息{}", JSONObject.toJSONString(contractPO));
                    if (userMap.containsKey(contractPO.getBranchCode())) {
                        OutOrganizationVO outOrganizationVO = userMap.get(contractPO.getBranchCode());
                        BranchContractContactDTO branchContractContactDTO = getBranchContractContactDTO(contractPO.getBranchCode(),outOrganizationVO);
                        createLifeServiceKeepContract(branchContractContactDTO);
                    }
                });
            }
        }
        log.info("执行结束---到期前15天保管合同新建第二年合同---定时任务");
    }

    @Override
    public void createContractExpiresIn15Days() {
        log.info("开始执行---到期前15天保管合同新建第二年合同---定时任务");
        //获取当前后15天的日期
        LocalDateTime endDate = LocalDate.now().plusDays(15).atTime(23,59,59);
        List<AutoCreateAgricKeepContractDTO> keepContractInfoList = agricServiceConfig.getKeepContractInfoList();
        List<Long> tenantIds = keepContractInfoList.stream().map(AutoCreateAgricKeepContractDTO::getTenantId).collect(Collectors.toList());
        //查询合同到期时间大于15天后的待执行、执行中且租户为酒水的酒水保管合同列表
        List<Integer> stateList = Arrays.asList(ContractStateEnum.WAIT_DEAL.getValue(), ContractStateEnum.RUNNING_DEAL.getValue());
        List<ErpContractPO> contractList = erpContractRepo.list(new LambdaQueryWrapper<ErpContractPO>()
                .in(ErpContractPO::getState, stateList)
                .in(ErpContractPO::getContractTemplateType, ContractTemplateTypeEnum.AGRIC_SERVICE_KEEP_CONTRACT,ContractTemplateTypeEnum.TIANJIE_SERVICE_KEEP_CONTRACT)
                .le(ErpContractPO::getEndTime, endDate)
                .gt(ErpContractPO::getEndTime, DateUtil.format(new Date(), DateUtil.FORMAT_DATE))
                .in(ErpContractPO::getTenantId, tenantIds)
                .eq(ErpContractPO::getIfDelete,0));
        log.info("15天后到期的合同列表：{}",JSONObject.toJSONString(contractList));
        if (CollectionUtils.isEmpty(contractList)) {
            log.info("没有到期的保函合同");
            return;
        }

        List<ContractBillDetailDTO> contractBillDetailDTOList = new ArrayList<>();

        List<String> orgCodeList = contractList.stream().map(ErpContractPO::getBranchCode).collect(Collectors.toList());
        //过滤掉分支负责人为空的分支
        List<OutOrganizationVO> branchList = hrmsIntegration.queryOrgByCodes(orgCodeList)
                .stream()
                .filter(item -> StringUtils.isNotBlank(item.getPrincipalEmployeeCode()))
                .collect(Collectors.toList());
        Map<String, OutOrganizationVO> userMap = branchList.stream()
                .collect(Collectors.toMap(
                        OutOrganizationVO::getCode,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        contractList.forEach(contractPO -> {
            //新创建保管合同
            log.info("合同到期前15天自动创建新合同，原合同信息{}", JSONObject.toJSONString(contractPO));
            if (userMap.containsKey(contractPO.getBranchCode())) {
                OutOrganizationVO outOrganizationVO = userMap.get(contractPO.getBranchCode());
                ContractBillDetailDTO detailDTO = new ContractBillDetailDTO();
                BeanUtils.copyProperties(contractPO, detailDTO);
                BranchContractContactDTO branchContractContactDTO = getBranchContractDTO(detailDTO,outOrganizationVO);
                BranchContractDTO branchContractDTO = new BranchContractDTO();
                BeanUtils.copyProperties(branchContractContactDTO, branchContractDTO);
                branchContractDTO.setContractTemplateType(contractPO.getContractTemplateType().getValue());
                branchContractDTO.setTenantId(contractPO.getTenantId());
                AtomicReference<Boolean> result = new AtomicReference<>(Boolean.FALSE);
                createKeepContract(branchContractDTO,result);
            }
        });
        log.info("执行结束---到期前15天保管合同新建第二年合同---定时任务");
    }

    /**
     * 检查合同章签署是否完成
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkContractCompanySign() {
        //查询等待公司签章的合同
        List<ContractSignStatusVO> waitingCompanySignContractList = erpContractSignRecordRepo.waitingCompanySignContractList();
        int successNum = 0;
        for (ContractSignStatusVO contractSignStatusVO : waitingCompanySignContractList) {
            ContractFindResponse msContractQueryVO = this.queryContractByMsContractNo(contractSignStatusVO.getMsContractNo());
            if ("02".equals(msContractQueryVO.getSignStatus())) {
                log.info("contractNo: {} companySign completed!", contractSignStatusVO.getContractNo());
                //更新所有签署记录状态为“已签署”
                erpContractSignRecordRepo.update(new LambdaUpdateWrapper<ErpContractSignRecordPO>()
                        .eq(ErpContractSignRecordPO::getContractId, contractSignStatusVO.getContractId())
                        .set(ErpContractSignRecordPO::getSignStatus, ContractSignRecordStatusEnum.SIGNED));
                //更新合同表状态到“执行中”
                List<ErpContractPO> contractList = erpContractRepo.list(new LambdaQueryWrapper<ErpContractPO>()
                        .like(ErpContractPO::getContractNo, contractSignStatusVO.getContractNo())
                        .eq(ErpContractPO::getContractTemplateType, contractSignStatusVO.getContractTemplateType())
                        .eq(ErpContractPO::getState, ContractStateEnum.WAIT_DEAL));
                List<ErpContractPO> updateList = contractList.stream().map(contractPO -> {
                    ErpContractPO updateEntity = new ErpContractPO();
                    updateEntity.setId(contractPO.getId());
                    updateEntity.setState(ContractStateEnum.RUNNING_DEAL.getValue());
                    return updateEntity;
                }).collect(Collectors.toList());
                boolean result = erpContractRepo.updateBatchById(updateList);
                if (result) {
                    //合同表状态变更记录轨迹
                    ErpUserPO po = new ErpUserPO();
                    po.setUsername("admin");
                    erpOperateLogRepo.saveBatchLogV1(po, OperateLogTypeEnum.CONTRACT, contractList,
                            "执行中", "通过", String.format("%s签署成功，系统自动变更合同状态", contractList.get(0).getContractTemplateType().getDesc()), null, null);
                }
                successNum ++;
            }
        }
        log.info("检查合同章签署是否完成 checkNum: {} successNum: {}", waitingCompanySignContractList.size(), successNum);
    }

    @Override
    public void erpContractBranchLeaderChange() {
        //保管合同分支负责人变更处理
        handleLeaderChangeBGHT();

        //联保合同分支负责人变更处理
        handleLeaderChangeLBHT();
    }

    @Override
    public void contractBranchLeaderChange() {
        //保管合同分支负责人变更处理
        handleLeaderChangeWithContract();
    }

    /**
     * 联保合同分支负责人变更处理
     */
    private void handleLeaderChangeLBHT() {
        log.info("执行开始---合同负责人变更处理---联保合同");
        List<AutoCreateKeepContractDTO> keepContractInfoList = lifeServiceConfig.getKeepContractInfoList();
        List<Long> tenantIds = keepContractInfoList.stream().map(AutoCreateKeepContractDTO::getTenantId).collect(Collectors.toList());
        List<ContractBillDetailDTO> contractPOList = getBaseMapper().getContractByType(ContractTemplateTypeEnum.LIFE_SERVICE_UNITED_KEEP_CONTRACT.getValue(),tenantIds)
                .stream()
                .filter(item->!Objects.isNull(item.getBranchContactCode()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(contractPOList)) {
            //根据合同号分组
            Map<String, List<ContractBillDetailDTO>> contractMap = contractPOList.stream().collect(Collectors.groupingBy(ContractBillDetailDTO::getContractNo));
            //获取签署信息
            List<String> branchCodeList = contractPOList.stream().map(ContractBillDetailDTO::getBranchCode).collect(Collectors.toList());
            //根据机构编码获取对应分支负责人信息，并分组
            Map<String,OutOrganizationVO> userMap = hrmsIntegration.queryOrgByCodes(branchCodeList)
                    .stream()
                    .collect(Collectors.toMap(
                            OutOrganizationVO::getCode,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));

            //需变更状态为终止态的合同集合
            List<ContractBillDetailDTO> changeContractList = new ArrayList<>();

            //需新建的合同集合
            List<List<BranchContractContactDTO>> newContractList = new ArrayList<>();

            Map<String,String> branchContactCodeMap = new HashMap<>();

            //循环合同集合，分组保存分支负责人工号
            contractMap.forEach((contractNo, contractList) -> {
                String branchContactCodeGroup = String.join(",", contractList.stream().map(ContractBillDetailDTO::getBranchContactCode).collect(Collectors.toList()));
                if (!branchContactCodeMap.containsKey(branchContactCodeGroup)) {
                    branchContactCodeMap.put(branchContactCodeGroup, branchContactCodeGroup);
                }
            });

            contractMap.forEach((contractNo, contractList) -> {
                boolean isContractEnd = false;
                boolean isNewContract = false;
                //逗号拼接联保合同分支负责人工号
                List<BranchContractContactDTO> newBranchContractList = new ArrayList<>();
                for (ContractBillDetailDTO contractPO:contractList){

                    //判断合同机构负责人和分支当前负责人是否一致，否则需终止原始合同，新建一份新的合同
                    if (userMap.containsKey(contractPO.getBranchCode()) && !Objects.equals(contractPO.getBranchContactCode(), userMap.get(contractPO.getBranchCode()).getPrincipalEmployeeCode())) {
                        log.info("联保合同-合同号{},{}分支负责人有变更，原分支负责人：{},现分支负责人：{}", contractPO.getContractNo(), contractPO.getBranchName(), contractPO.getBranchContactCode(), userMap.get(contractPO.getBranchCode()).getPrincipalEmployeeCode());
                        isContractEnd = true;
                        isNewContract = true;
                    }

                    //未获取到分支负责人时，只终止原合同，不新建合同
                    if (!userMap.containsKey(contractPO.getBranchCode()) || StringUtils.isEmpty(userMap.get(contractPO.getBranchCode()).getPrincipalEmployeeCode())) {
                        log.error("联保合同-未获取到机构负责人，合同号：{},分支机构:{}", contractPO.getContractNo(),contractPO.getBranchCode());
                        isContractEnd = true;
                        isNewContract = false;
                    }

                    OutOrganizationVO principalEmployee = userMap.get(contractPO.getBranchCode());
                    BranchContractContactDTO newBranchContract = getBranchContractContactDTO(contractPO.getBranchCode(), principalEmployee);
                    newBranchContract.setEndDate(contractPO.getEndTime());
                    newBranchContractList.add(newBranchContract);
                };
                if (isContractEnd) {
                    log.info("联保合同-合同号{}需终止，分支负责人有变更", contractNo);
                    ContractBillDetailDTO newContract = contractList.get(0);
                    changeContractList.add(newContract);

                    if (isNewContract) {
                        log.info("联保合同-合同号{}需新建", contractNo);
                        String branchContactCodeGroup = String.join(",", newBranchContractList.stream().map(BranchContractContactDTO::getBranchContactCode).collect(Collectors.toList()));
                        //对应分支负责人已存在执行中的合同，则不重新新建，只终止原始合同
                        if (!branchContactCodeMap.containsKey(branchContactCodeGroup)) {
                            newContractList.add(newBranchContractList);
                        }
                    }
                }
            });

            if (!CollectionUtils.isEmpty(changeContractList)) {
                log.info("联保合同-需终止或新建合同数量：{},{}", changeContractList.size(),JSONObject.toJSONString(changeContractList));
                //修改合同状态为终止态
                getBaseMapper().updateStateByIds(changeContractList);
                //新增合同轨迹
                List<ErpContractPO> contractPOS = changeContractList.stream().map(contractPO -> {
                    ErpContractPO updateEntity = new ErpContractPO();
                    BeanUtils.copyProperties(contractPO, updateEntity);
                    return updateEntity;
                }).collect(Collectors.toList());

                erpOperateLogRepo.saveBatchLogV1(tenantUtil.getUser(), OperateLogTypeEnum.CONTRACT, contractPOS,
                        "合作终止", "通过", "机构分支负责人变更，系统自动变更合同状态", null, null);
            }

            if (!CollectionUtils.isEmpty(newContractList)) {
                log.info("联保合同-需新建合同数量：{},{}", newContractList.size(), JSONObject.toJSONString(newContractList));
                //新增合同
                newContractList.forEach(this::createLifeServiceUnitedKeepContract);
            }
        }
        log.info("执行结束---合同负责人变更处理---联保合同");
    }

    /**
     * 保管合同分支负责人变更处理
     */
    private void handleLeaderChangeBGHT() {
        log.info("执行开始---合同负责人变更处理---保管合同");

        //获取状态为执行中、待执行的酒水保管合同
        List<AutoCreateKeepContractDTO> keepContractInfoList = lifeServiceConfig.getKeepContractInfoList();
        List<Long> tenantIds = keepContractInfoList.stream().map(AutoCreateKeepContractDTO::getTenantId).collect(Collectors.toList());
        List<ContractBillDetailDTO> contractPOList = getBaseMapper().getContractByType(ContractTemplateTypeEnum.LIFE_SERVICE_KEEP_CONTRACT.getValue(),tenantIds)
                .stream()
                .filter(item->!Objects.isNull(item.getBranchContactCode()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(contractPOList)) {
            //获取签署信息
            List<String> branchCodeList = contractPOList.stream().map(ContractBillDetailDTO::getBranchCode).collect(Collectors.toList());
            Map<String,List<ContractBillDetailDTO>> contractMap = contractPOList.stream().collect(Collectors.groupingBy(ContractBillDetailDTO::getBranchCode));
            //根据机构编码获取对应分支负责人信息，并分组
            Map<String,OutOrganizationVO> userMap = hrmsIntegration.queryOrgByCodes(branchCodeList)
                    .stream()
                    .collect(Collectors.toMap(
                            OutOrganizationVO::getCode,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));

            //需变更状态为终止态的合同集合
            List<ContractBillDetailDTO> changeContractList = new ArrayList<>();

            //需新建的合同集合
            List<BranchContractContactDTO> newContractList = new ArrayList<>();

            //判断分支负责人是否变更
            for (ContractBillDetailDTO contractPO : contractPOList) {
                //如果是酒水保管合同，则判断负责人是否变更
                if (basicsConfig.getTenantIdList().contains(contractPO.getTenantId())) {
                    //判断合同机构负责人和分支当前负责人是否一致，否则需终止原始合同，新建一份新的合同
                    if (userMap.containsKey(contractPO.getBranchCode()) && !Objects.equals(contractPO.getBranchContactCode(),userMap.get(contractPO.getBranchCode()).getPrincipalEmployeeCode())) {
                        log.info("保管合同-合同号{},{}分支负责人有变更，原分支负责人：{},现分支负责人：{}", contractPO.getContractNo(), contractPO.getBranchName(), contractPO.getBranchContactCode(), userMap.get(contractPO.getBranchCode()).getPrincipalEmployeeCode());
                        OutOrganizationVO principalEmployee = userMap.get(contractPO.getBranchCode());

                        changeContractList.add(contractPO);

                        //合同列表中不存在该机构+机构负责人的合同，则需新建合同
                        if (!contractMap.containsKey(contractPO.getBranchCode())
                                || contractMap.get(contractPO.getBranchCode()).stream().noneMatch(
                                contract -> contract.getBranchContactCode().equals(principalEmployee.getPrincipalEmployeeCode())
                                        && basicsConfig.getTenantIdList().contains(contract.getTenantId()))) {
                            BranchContractContactDTO newBranchContract = getBranchContractContactDTO(contractPO.getBranchCode(), principalEmployee);
                            newContractList.add(newBranchContract);
                        }
                    }
                    if (!userMap.containsKey(contractPO.getBranchCode()) || StringUtils.isEmpty(userMap.get(contractPO.getBranchCode()).getPrincipalEmployeeCode())) {
                        log.error("保管合同-未获取到机构负责人，合同号：{},分支机构:{}", contractPO.getContractNo(),contractPO.getBranchCode());
                        changeContractList.add(contractPO);
                    }
                }
                //否责判断，酒水合同是否存在于changeContractList集合中，存在则需一并更改状态为终止态
                else {
                    if (changeContractList.stream().anyMatch(contract -> contractPO.getContractNo().startsWith(contract.getContractNo()))) {
                        changeContractList.add(contractPO);
                    }
                }
            }

            if (!CollectionUtils.isEmpty(changeContractList)) {
                log.info("保管合同-需终止合同数量：{},{}", changeContractList.size(), JSONObject.toJSONString(changeContractList));
                //修改合同状态为终止态
                getBaseMapper().updateStateByIds(changeContractList);
                //新增合同轨迹
                List<ErpContractPO> contractPOS = changeContractList.stream().map(contractPO -> {
                    ErpContractPO updateEntity = new ErpContractPO();
                    BeanUtils.copyProperties(contractPO, updateEntity);
                    return updateEntity;
                }).collect(Collectors.toList());

                erpOperateLogRepo.saveBatchLogV1(tenantUtil.getUser(), OperateLogTypeEnum.CONTRACT, contractPOS,
                        "合作终止", "通过", "机构分支负责人变更，系统自动变更合同状态", null, null);
            }

            if (!CollectionUtils.isEmpty(newContractList)) {
                log.info("保管合同-需新建合同数量：{},{}", newContractList.size(), JSONObject.toJSONString(newContractList));
                //新增合同
                newContractList.forEach(this::createLifeServiceKeepContract);
            }
        }
        log.info("执行结束---合同负责人变更处理---保管合同");
    }

    /**
     * 农服、成都天杰保管合同分支负责人变更处理
     */
    private void handleLeaderChangeWithContract() {
        log.info("执行开始---合同负责人变更处理---保管合同");

        //获取状态为执行中、待执行的酒水保管合同
        List<AutoCreateAgricKeepContractDTO> keepContractInfoList = agricServiceConfig.getKeepContractInfoList();
        List<Long> tenantIds = keepContractInfoList.stream().map(AutoCreateAgricKeepContractDTO::getTenantId).collect(Collectors.toList());
        List<ContractBillDetailDTO> contractPOList = getBaseMapper().getContractByTypes(Arrays.asList(ContractTemplateTypeEnum.AGRIC_SERVICE_KEEP_CONTRACT.getValue(),ContractTemplateTypeEnum.TIANJIE_SERVICE_KEEP_CONTRACT.getValue()),tenantIds);
        if (CollectionUtils.isEmpty(contractPOList)) {
            log.info("根据农服、成都天杰类型保管类型没有查询到合同，直接返回！");
            return ;
        }
        //获取签署信息
        List<String> branchCodeList = contractPOList.stream().map(ContractBillDetailDTO::getBranchCode).collect(Collectors.toList());
        Map<String,List<ContractBillDetailDTO>> contractMap = contractPOList.stream().collect(Collectors.groupingBy(ContractBillDetailDTO::getBranchCode));
        //根据机构编码获取对应分支负责人信息，并分组
        Map<String,OutOrganizationVO> userMap = hrmsIntegration.queryOrgByCodes(branchCodeList)
                .stream()
                .collect(Collectors.toMap(
                        OutOrganizationVO::getCode,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        //需变更状态为终止态的合同集合
        List<ContractBillDetailDTO> changeContractList = new ArrayList<>();

        //需新建的合同集合
        List<BranchContractDTO> newContractList = new ArrayList<>();

        //判断分支负责人是否变更
        for (ContractBillDetailDTO contractPO : contractPOList) {
            //判断合同机构负责人和分支当前负责人是否一致，否则需终止原始合同，新建一份新的合同
            OutOrganizationVO organizationVO = userMap.get(contractPO.getBranchCode());
            log.info("保管合同-合同号{},{}分支负责人有变更，原分支负责人：{},现分支信息：{}", contractPO.getContractNo(), contractPO.getBranchName(), contractPO.getBranchContactCode(), organizationVO);

            if (ObjectUtil.isNotEmpty(organizationVO) && StringUtils.isNotBlank(organizationVO.getPrincipalEmployeeCode()) && !organizationVO.getPrincipalEmployeeCode().equals(contractPO.getBranchContactCode())) {
                OutOrganizationVO principalEmployee = userMap.get(contractPO.getBranchCode());

                changeContractList.add(contractPO);

                //合同列表中不存在该机构+机构负责人的合同，则需新建合同
                if (!contractMap.containsKey(contractPO.getBranchCode())
                        || contractMap.get(contractPO.getBranchCode()).stream().noneMatch(
                        contract -> contract.getBranchContactCode().equals(principalEmployee.getPrincipalEmployeeCode()))) {
                    BranchContractDTO newBranchContract = getBranchContractDTO(contractPO, principalEmployee);
                    newContractList.add(newBranchContract);
                }
            }
            if (!userMap.containsKey(contractPO.getBranchCode()) || StringUtils.isEmpty(userMap.get(contractPO.getBranchCode()).getPrincipalEmployeeCode())) {
                log.error("保管合同-未获取到机构负责人，合同号：{},分支机构:{}", contractPO.getContractNo(),contractPO.getBranchCode());
                changeContractList.add(contractPO);
            }

        }

        if (!CollectionUtils.isEmpty(changeContractList)) {
            log.info("保管合同-需终止合同数量：{},{}", changeContractList.size(), JSONObject.toJSONString(changeContractList));
            //修改合同状态为终止态
            getBaseMapper().updateStateByIds(changeContractList);
            //新增合同轨迹
            List<ErpContractPO> contractPOS = changeContractList.stream().map(contractPO -> {
                ErpContractPO updateEntity = new ErpContractPO();
                BeanUtils.copyProperties(contractPO, updateEntity);
                return updateEntity;
            }).collect(Collectors.toList());

            erpOperateLogRepo.saveBatchLogV1(tenantUtil.getUser(), OperateLogTypeEnum.CONTRACT, contractPOS,
                    "合作终止", "通过", "机构分支负责人变更，系统自动变更合同状态", null, null);
        }
        AtomicReference<Boolean> result = new AtomicReference<>(Boolean.FALSE);
        if (!CollectionUtils.isEmpty(newContractList)) {
            log.info("保管合同-需新建合同数量：{},{}", newContractList.size(), JSONObject.toJSONString(newContractList));
            //新增合同
            newContractList.forEach(item->{
                createKeepContract(item,result);
            });
        }
        log.info("执行结束---合同负责人变更处理---保管合同");
    }

    private static BranchContractContactDTO getBranchContractContactDTO(String branchCode, OutOrganizationVO principalEmployee) {
        BranchContractContactDTO newBranchContract = new BranchContractContactDTO();
        newBranchContract.setBranchContact(principalEmployee.getPrincipalEmployeeName());
        newBranchContract.setBranchContactCode(principalEmployee.getPrincipalEmployeeCode());
        newBranchContract.setBranchContactMobile(principalEmployee.getPrincipalEmployeeMobile());
        newBranchContract.setBranchCode(branchCode);
        newBranchContract.setBranchName(principalEmployee.getName());
        return newBranchContract;
    }

    private static BranchContractDTO getBranchContractDTO(ContractBillDetailDTO contractPO, OutOrganizationVO principalEmployee) {
        BranchContractDTO newBranchContract = new BranchContractDTO();
        newBranchContract.setBranchContact(principalEmployee.getPrincipalEmployeeName());
        newBranchContract.setBranchContactCode(principalEmployee.getPrincipalEmployeeCode());
        newBranchContract.setBranchContactMobile(principalEmployee.getPrincipalEmployeeMobile());
        newBranchContract.setBranchCode(contractPO.getBranchCode());
        newBranchContract.setBranchName(principalEmployee.getName());
        newBranchContract.setTenantId(contractPO.getTenantId());
        newBranchContract.setContractTemplateType(contractPO.getContractTemplateType());
        return newBranchContract;
    }

    /**
     * 法大大签署完成合同文件转存
     */
    @Override
    public void erpContractFileUploadHandler() {
        //查询执行中状态但文件id为空的生服合同
        List<ErpContractPO> contractPOList = erpContractRepo.list(new LambdaQueryWrapper<ErpContractPO>()
                .in(ErpContractPO::getContractTemplateType,
                        Arrays.asList(ContractTemplateTypeEnum.LIFE_SERVICE_KEEP_CONTRACT, ContractTemplateTypeEnum.LIFE_SERVICE_UNITED_KEEP_CONTRACT,ContractTemplateTypeEnum.AGRIC_SERVICE_KEEP_CONTRACT,ContractTemplateTypeEnum.TIANJIE_SERVICE_KEEP_CONTRACT))
                .eq(ErpContractPO::getState, ContractStateEnum.RUNNING_DEAL.getValue())
                .isNull(ErpContractPO::getContractFileIds)
                .eq(ErpContractPO::getIfDelete, 0));

        if (!CollectionUtils.isEmpty(contractPOList)) {
            //根据
            List<ErpContractPO> msContractNoList = contractPOList.stream().filter(contractPO -> !StringUtils.isEmpty(contractPO.getMsContractNo())).collect(Collectors.toList());
            Map<String,ErpContractPO> msContractMap = msContractNoList.stream().collect(Collectors.toMap(ErpContractPO::getContractNo, Function.identity()));

            List<ErpContractPO> updateList = new ArrayList<>();
            for (ErpContractPO contractPO : contractPOList) {
                String msContractNo = contractPO.getMsContractNo();
                if (Objects.equals(ContractTemplateTypeEnum.LIFE_SERVICE_KEEP_CONTRACT.getValue(), contractPO.getContractTemplateType().getValue())
                        && !StringUtils.isEmpty(contractPO.getBghtContractCode())) {
                    msContractNo = msContractMap.containsKey(contractPO.getBghtContractCode())?msContractMap.get(contractPO.getBghtContractCode()).getMsContractNo():"";
                }
                if (StringUtils.isEmpty(msContractNo)) {
                    log.info("合同号：{} 未获取到ms合同号，跳过处理", contractPO.getContractNo());
                    continue;
                }
                ContractFindResponse msContractQueryVO = this.queryContractByMsContractNo(msContractNo);
                //转存合同文件到erp服务oss
                Long contractFileId;
                try {
                    contractFileId = erpAttachService.moveRemoteFileToOss(msContractQueryVO.getDownloadURL(), "pdf", contractPO.getTenantId());
                } catch (Exception e) {
                    log.error("contractNo: {} 转存文件失败，下次处理", contractPO.getContractNo(), e);
                    continue;
                }

                ErpContractPO updateEntity = new ErpContractPO();
                updateEntity.setId(contractPO.getId());
                updateEntity.setContractFileIds(String.valueOf(contractFileId));
                updateList.add(updateEntity);
            }

            if (CollectionUtils.isNotEmpty(updateList)) {
                //更新文件id
                erpContractRepo.updateBatchById(updateList);
            }
        }
    }

    /**
     * 联保合同自动创建
     */
    @Override
    public void unitedKeepContractAutoCreate() {
        List<DictionaryItemVO> dictList = bmsIntegration.getDictionaryItemsByTypeCode(ContractDicConstants.UNITED_KEEP_CONTRACT_LIST);

        //获取当前日期的后15天
        LocalDate expireDate = LocalDate.now().plusDays(15);

        List<ContractSignRecordVo> contractSignRecordList = erpContractSignRecordRepo.queryValidContractByOrgCodes(
                null, ContractTemplateTypeEnum.LIFE_SERVICE_UNITED_KEEP_CONTRACT.getValue());

        List<String> branchCodes = new ArrayList<>();
        for (DictionaryItemVO dict : dictList) {
            String[] orgCodeArray = dict.getItemCode().split(",");
            List<String> orgCodeList = Arrays.stream(orgCodeArray).collect(Collectors.toList());
            branchCodes.addAll(orgCodeList);
        }
        List<OutOrganizationVO> organizationVOList = hrmsIntegration.queryOrgByCodes(branchCodes);

        for (DictionaryItemVO dict : dictList) {
            boolean createNewContract = true;
            log.info("check running united keep contract branchCodes: {}", dict.getItemCode());
            String[] orgCodeArray = dict.getItemCode().split(",");
            List<String> orgCodeList = Arrays.stream(orgCodeArray).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(contractSignRecordList)) {
                //签署记录按合同编号分组，假如有一笔合同的签署名单与分支名单完全吻合的合同，就不用新创建，否则进行创建
                //过滤掉状态为执行中且失效日期在15天内的合同
                Map<String, List<ContractSignRecordVo>> existedContractMap =contractSignRecordList
                        .stream()
                        .filter(item->Objects.equals(item.getState(), ContractStateEnum.WAIT_DEAL.getValue())
                                || (Objects.equals(item.getState(), ContractStateEnum.RUNNING_DEAL.getValue()) && item.getEndTime().isAfter(expireDate)))
                        .collect(Collectors.toList())
                        .stream()
                        .collect(Collectors.groupingBy(ContractSignRecordVo::getContractNo));
                for (Map.Entry<String, List<ContractSignRecordVo>> entry : existedContractMap.entrySet()) {
                    List<ContractSignRecordVo> contractList = entry.getValue();
                    if (contractList.size() != orgCodeList.size()) {
                        continue;
                    }
                    boolean sameBranchList = true;
                    for (int i = 0; i < contractList.size(); i++) {
                        if (!contractList.get(i).getBranchCode().equals(orgCodeList.get(i))) {
                            sameBranchList = false;
                            break;
                        }
                    }
                    if (sameBranchList) {
                        log.info("branchCodes: {} 已经为这一批分支生成了有效合同，跳过自动创建", dict.getItemCode());
                        createNewContract = false;
                        break;
                    }
                }
            }

            if (createNewContract) {
                //没有分支处于负责人空缺，则自动创建新合同，否则什么都不做
                List<String> principalVacantBranchList = organizationVOList.stream()
                        .filter(organizationVO -> Objects.isNull(organizationVO.getPrincipalEmployeeId()))
                        .map(OutOrganizationVO::getCode).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(principalVacantBranchList)) {
                    log.error("branchCodes: {} 当中分支{}负责人处于空缺状态，需要补充负责人配置才能生成联保合同", dict.getItemCode(), principalVacantBranchList);
                    continue;
                }

                //自动创建生服珍酒联保合同
                List<BranchContractContactDTO> branchContractContactList = new ArrayList<>();
                Map<String, OutOrganizationVO> organizationVOMap =
                        organizationVOList.stream().collect(Collectors.toMap(OutOrganizationVO::getCode, Function.identity()));
                for (String orgCode : orgCodeArray) {
                    OutOrganizationVO organizationVO = organizationVOMap.get(orgCode);
                    BranchContractContactDTO branchContractContactDTO = new BranchContractContactDTO();
                    branchContractContactDTO.setBranchCode(organizationVO.getCode());
                    branchContractContactDTO.setBranchName(organizationVO.getName());
                    branchContractContactDTO.setBranchContact(organizationVO.getPrincipalEmployeeName());
                    branchContractContactDTO.setBranchContactCode(organizationVO.getPrincipalEmployeeCode());
                    branchContractContactDTO.setBranchContactMobile(organizationVO.getPrincipalEmployeeMobile());
                    branchContractContactList.add(branchContractContactDTO);
                }
                this.createLifeServiceUnitedKeepContract(branchContractContactList);
            }
        }
    }

    /**
     * 指定合同号终止合同
     * @param contractNo
     */
    @Override
    public void stopContractByContractNo(String contractNo) {
        //先查询合同是否存在
        ErpContractPO contractPO = erpContractRepo.getOne(new LambdaQueryWrapper<ErpContractPO>().eq(ErpContractPO::getContractNo, contractNo));
        if (contractPO == null) {
            throw new BusinessException("合同不存在");
        }

        //更新合同状态到“合作终止”
        ErpContractPO updateEntity = new ErpContractPO();
        updateEntity.setId(contractPO.getId());
        updateEntity.setState(ContractStateEnum.STOP_DEAL.getValue());
        erpContractRepo.updateById(updateEntity);
    }

    /**
     * 合同到截止日期后处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void erpContractExpireHandler() {
        LocalDateTime today = LocalDateTime.now();

        //查询生服合同，状态为【待执行】或【执行中】，截止日期在今天之前
        List<ErpContractPO> contractList = erpContractRepo.list(new LambdaQueryWrapper<ErpContractPO>()
                .in(ErpContractPO::getContractTemplateType,
                        Arrays.asList(ContractTemplateTypeEnum.LIFE_SERVICE_KEEP_CONTRACT, ContractTemplateTypeEnum.LIFE_SERVICE_UNITED_KEEP_CONTRACT,ContractTemplateTypeEnum.AGRIC_SERVICE_KEEP_CONTRACT,ContractTemplateTypeEnum.TIANJIE_SERVICE_KEEP_CONTRACT))
                .in(ErpContractPO::getState, Arrays.asList(ContractStateEnum.WAIT_DEAL, ContractStateEnum.RUNNING_DEAL))
                .le(ErpContractPO::getEndTime, today));
        if (CollectionUtils.isEmpty(contractList)) {
            log.info("{} 没有到期的合同", today);
            return;
        }

        List<ErpContractPO> updateList = contractList.stream().map(item -> {
            ErpContractPO updateEntity = new ErpContractPO();
            updateEntity.setId(item.getId());
            updateEntity.setState(ContractStateEnum.FINISH_DEAL.getValue());
            return updateEntity;
        }).collect(Collectors.toList());
        log.info("{} 处理到期合同 updateNum: {}", today, updateList.size());
        erpContractRepo.updateBatchById(updateList);

        List<ContractTemplateTypeEnum> contractTemplateTypeEnums = Arrays.asList(ContractTemplateTypeEnum.LIFE_SERVICE_KEEP_CONTRACT, ContractTemplateTypeEnum.AGRIC_SERVICE_KEEP_CONTRACT, ContractTemplateTypeEnum.TIANJIE_SERVICE_KEEP_CONTRACT);
        List<ErpContractPO> bgContractList = contractList.stream().filter(item -> contractTemplateTypeEnums.contains(item.getContractTemplateType())).collect(Collectors.toList());
        List<ErpContractPO> lbhtContractList = contractList.stream().filter(item -> item.getContractTemplateType() == ContractTemplateTypeEnum.LIFE_SERVICE_UNITED_KEEP_CONTRACT).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(bgContractList)) {
            //添加操作记录
            erpOperateLogRepo.saveBatchLogV1(tenantUtil.getUser(), OperateLogTypeEnum.CONTRACT, bgContractList,
                    "执行完成", "通过", "保管合同到期，系统自动变更合同状态", null, null);
        }

        if (!CollectionUtils.isEmpty(lbhtContractList)) {
            erpOperateLogRepo.saveBatchLogV1(tenantUtil.getUser(), OperateLogTypeEnum.CONTRACT, lbhtContractList,
                    "执行完成", "通过", "珍酒联保合同到期，系统自动变更合同状态", null, null);
        }
    }

    private ContractFindResponse queryContractByMsContractNo(String msContractNo) {
        try {
            ContractFindRequest request = new ContractFindRequest();
            request.setContractNo(msContractNo);
            com.cfpamf.common.ms.result.Result<List<ContractFindResponse>> result = contractCommonFacade.findAllContract(request);
            log.info("合同查询 请求参数：{}，返回结果:{}", request, result);
            if (Objects.isNull(result)) {
                log.error("合同查询失败!");
                throw new BusinessException(ErrorCodeEnum.OUTSIDE_EXCEPTION.getCode(), "合同查询失败");
            }
            if (!result.isSuccess()) {
                log.error("合同查询失败!" + result.getErrorMsg());
                throw new BusinessException(ErrorCodeEnum.OUTSIDE_EXCEPTION.getCode(), "合同查询失败," + result.getErrorMsg());
            }
            if (result.getData().isEmpty()) {
                log.error("合同查询失败!");
                throw new BusinessException(ErrorCodeEnum.OUTSIDE_EXCEPTION.getCode(), "合同查询失败");
            }
            return result.getData().get(0);
        } catch (UnsupportedEncodingException e) {
            log.error("合同查询异常，异常原因：" + e.getMessage(), e);
            throw new BusinessException(ErrorCodeEnum.OUTSIDE_EXCEPTION.getCode(), "合同查询异常，异常原因：" + e.getMessage());
        }
    }

    /**
     * 设置客户签约信息，于首个签署人签署时调用
     *
     * @param contractPO
     * @param contractGenPreResponse
     */
    public void setHandSignInfo(ErpContractPO contractPO, ContractGenPreResponse contractGenPreResponse) {
        log.info("开始异步设置合同签署客户 contractGenPreResponse : {}", JSONObject.toJSONString(contractGenPreResponse));
        if (Objects.isNull(contractPO)) {
            throw new BusinessException("合同编号不存在");
        }
        if (Objects.isNull(contractGenPreResponse) || StringUtils.isEmpty(contractGenPreResponse.getContractNo())) {
            throw new BusinessException("合同未生成");
        }
        List<ErpContractSignRecordPO> signRecordPOList = erpContractSignRecordRepo.list(new LambdaQueryWrapper<ErpContractSignRecordPO>()
                .eq(ErpContractSignRecordPO::getContractNo, contractPO.getContractNo()).orderByAsc(ErpContractSignRecordPO::getSort));
        if (CollectionUtils.isEmpty(signRecordPOList)) {
            throw new BusinessException("合同签署记录不存在");
        }
        //更新合同服务业务编码到合同数据中
        ErpContractPO updateEntity = new ErpContractPO();
        updateEntity.setId(contractPO.getId());
        updateEntity.setMsContractNo(contractGenPreResponse.getContractNo());
        erpContractRepo.updateById(updateEntity);
        List<String> orgCodeList = signRecordPOList.stream().map(ErpContractSignRecordPO::getBranchCode).collect(Collectors.toList());
        List<OutOrganizationVO> organizationVOList = hrmsIntegration.queryOrgByCodes(orgCodeList);
        if (CollectionUtils.isEmpty(organizationVOList)) {
            log.error("分支负责人信息获取失败, 分支编码列表：{}", orgCodeList);
        }
        Map<String, OutOrganizationVO> branchMap = organizationVOList.stream().collect(Collectors.toMap(OutOrganizationVO::getCode, Function.identity()));
        List<ContractCustomerVO> customerCustomerList = signRecordPOList.stream().map(item -> {
            OutOrganizationVO thisBranch = branchMap.get(item.getBranchCode());
            if (!StringUtils.equals(thisBranch.getPrincipalEmployeeCode(), item.getBranchContactCode())) {
                throw new BusinessException("有分支负责人发生更换，分支编码：" + item.getBranchCode());
            }
            OnBoardEmployeeVO branchHeaderVO = hrmsIntegration.queryOnboardEmployeeById(thisBranch.getPrincipalEmployeeId());
            if (Objects.isNull(branchHeaderVO)) {
                throw new BusinessException("分支负责人信息获取失败，可能已离职，分支编码：" + item.getBranchCode());
            }
            ContractCustomerVO contractCustomerVO = new ContractCustomerVO();
            contractCustomerVO.setCustId(item.getBranchContactCode());
            contractCustomerVO.setCustName(branchHeaderVO.getEmployeeName());
            contractCustomerVO.setIdCardNo(branchHeaderVO.getIdNo());
            contractCustomerVO.setMobile(branchHeaderVO.getMobile());
            return contractCustomerVO;
        }).collect(Collectors.toList());
        //customerCustomerList根据身份证去重
        customerCustomerList = customerCustomerList.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ContractCustomerVO::getCustId))), ArrayList::new));

        //公司签章信息
        List<ContractCompanySignVO> companySignList = new ArrayList<>();
        ContractESignHeadCustomerRequest headSignRequest = new ContractESignHeadCustomerRequest();
        switch (contractPO.getContractTemplateType()) {
            case LIFE_SERVICE_KEEP_CONTRACT: {
                for (CompanySignDTO companySignDTO : lifeServiceConfig.getKeepContractCompanySignList()) {
                    ContractCompanySignVO contractCompanySignVO = ContractConvertor.CONVERTOR.convertCompanySignDTOtoContractCompanySignVO(companySignDTO);
                    companySignList.add(contractCompanySignVO);
                }

                headSignRequest.setContractSignToken(MsContraConstants.MALL_CONTRACT_SIGN_TOKEN);
                headSignRequest.setCallbackURL(lifeServiceConfig.getContractCallbackUrl());
                break;
            }
            case LIFE_SERVICE_UNITED_KEEP_CONTRACT: {
                CompanySignDTO companySignDTO = lifeServiceConfig.getUnitedKeepContractCompanySign();
                ContractCompanySignVO contractCompanySignVO = ContractConvertor.CONVERTOR.convertCompanySignDTOtoContractCompanySignVO(companySignDTO);
                companySignList.add(contractCompanySignVO);

                headSignRequest.setContractSignToken(MsContraConstants.MALL_CONTRACT_SIGN_TOKEN);
                headSignRequest.setCallbackURL(lifeServiceConfig.getContractCallbackUrl());
                break;
            }
            case AGRIC_SERVICE_KEEP_CONTRACT:
                CompanySignDTO zhnf = agricServiceConfig.getKeepContractCompanySignList().stream().filter(item -> item.getCompanyCode().equals("ZHNF")).findFirst().orElse(null);
                if(ObjectUtils.isEmpty(zhnf)){
                    throw new BusinessException("中和农服的签章信息不存在，请联系管理员");
                }
                ContractCompanySignVO contractCompanySignVO = ContractConvertor.CONVERTOR.convertCompanySignDTOtoContractCompanySignVO(zhnf);
                companySignList.add(contractCompanySignVO);

                headSignRequest.setContractSignToken(MsContraConstants.CONTRACT_SIGN_TOKEN);
                headSignRequest.setCallbackURL(agricServiceConfig.getContractCallbackUrl());
                break;
            case TIANJIE_SERVICE_KEEP_CONTRACT:
                CompanySignDTO cdtj = agricServiceConfig.getKeepContractCompanySignList().stream().filter(item -> item.getCompanyCode().equals("CDTJ")).findFirst().orElse(null);
                if(ObjectUtils.isEmpty(cdtj)){
                    throw new BusinessException("成都天杰的签章信息不存在，请联系管理员");
                }
                ContractCompanySignVO contractCompanySign = ContractConvertor.CONVERTOR.convertCompanySignDTOtoContractCompanySignVO(cdtj);
                companySignList.add(contractCompanySign);
                headSignRequest.setContractSignToken(MsContraConstants.CONTRACT_SIGN_TOKEN);
                headSignRequest.setCallbackURL(agricServiceConfig.getContractCallbackUrl());
                break;
            default: throw new BusinessException("合同模板类型不支持！");
        }
        headSignRequest.setSignType(MsContraConstants.LIFE_SERVICE_CONTRACT_SIGN_TYPE);
        headSignRequest.setContractNo(contractGenPreResponse.getContractNo());
        headSignRequest.setResource(MsContraConstants.RESOURCE);
        headSignRequest.setCustomerCustomerList(customerCustomerList);
        headSignRequest.setCompanySignList(companySignList);
        contractCustVerifyFacadeHelper.headSignCustomer(headSignRequest);
    }

    /**
     * 自动创建保管合同
     * @param contractList 合同列表
     * @param userVo 分支负责人信息
     */
    private void createContract(List<ContractBillDetailDTO> contractList, OutOrganizationVO userVo) {
        //不存在待执行、执行中、执行完成的合同时，需出发创建合同流程
        if (CollectionUtils.isEmpty(contractList)
                || contractList.stream().noneMatch(item -> Objects.equals(ContractStateEnum.RUNNING_DEAL.getValue(), item.getState())
                || Objects.equals(ContractStateEnum.WAIT_DEAL.getValue(), item.getState()))) {
            //走创建保管合同逻辑
            log.info("分支{}负责人{},没有签署对应的合同，自动创建合同", userVo.getCode(), userVo.getPrincipalEmployeeCode());
            BranchContractContactDTO branchContractContactDTO = new BranchContractContactDTO();
            branchContractContactDTO.setBranchCode(userVo.getCode());
            branchContractContactDTO.setBranchName(userVo.getName());
            branchContractContactDTO.setBranchContact(userVo.getPrincipalEmployeeName());
            branchContractContactDTO.setBranchContactCode(userVo.getPrincipalEmployeeCode());
            branchContractContactDTO.setBranchContactMobile(userVo.getPrincipalEmployeeMobile());
            createLifeServiceKeepContract(branchContractContactDTO);
        }
    }

    /**
     * 自动创建保管合同
     * @param contractList 合同列表
     * @param userVo 分支负责人信息
     */
    @Transactional(rollbackFor = Exception.class)
    public  AtomicReference<Boolean> createAgricContract(List<ContractBillDetailDTO> contractList, OutOrganizationVO userVo,Long tenantId, Integer contractTemplateType,AtomicReference<Boolean> result) {
        //不存在待执行、执行中的合同时，需出发创建合同流程
        List<Integer> contractStateList = Arrays.asList(ContractStateEnum.RUNNING_DEAL.getValue(), ContractStateEnum.WAIT_DEAL.getValue());
        if (CollectionUtils.isEmpty(contractList) ||
                contractList.stream().noneMatch(item -> contractStateList.contains(item.getState()))) {
            //走创建保管合同逻辑
            log.info("分支{}负责人{},没有签署对应的合同，自动创建合同", userVo.getCode(), userVo.getPrincipalEmployeeCode());
            BranchContractDTO branchContractContactDTO = new BranchContractDTO();
            branchContractContactDTO.setBranchCode(userVo.getCode());
            branchContractContactDTO.setBranchName(userVo.getName());
            branchContractContactDTO.setBranchContact(userVo.getPrincipalEmployeeName());
            branchContractContactDTO.setBranchContactCode(userVo.getPrincipalEmployeeCode());
            branchContractContactDTO.setBranchContactMobile(userVo.getPrincipalEmployeeMobile());
            branchContractContactDTO.setTenantId(tenantId);
            branchContractContactDTO.setContractTemplateType(contractTemplateType);
            result = createKeepContract(branchContractContactDTO,result);
        }
        return result;
    }

    /**
     * 钉钉批量推送签约链接
     *
     * @param contractList 待签约合同
     * @param templateNo 合同模板编号
     * @param content 推送内容
     */
    public void pushContractDingdingMsg(List<ErpContractPO> contractList, String templateNo, String content) {
        // 开始时间: 当前年第一天
//        String date = DateUtil.format(new Date(), DateUtil.FORMAT_DATE);
//        String startTime = DateUtil.format(DateUtil.addDate(new Date(), Calendar.YEAR, -1), DateUtil.FORMAT_TIME);
        // 结束时间:当前时间
//        String endTime = DateUtil.format(new Date(), DateUtil.FORMAT_TIME);

        // 创建一个SimpleDateFormat实例，用于格式化日期
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.FORMAT_TIME);
        // 创建一个Calendar实例，并设置为当前日期和时间
        Calendar calendar = Calendar.getInstance();

        // 获取当前年份
        int year = calendar.get(Calendar.YEAR);
        // 设置为当前年的第一天
        calendar.set(Calendar.MONTH, Calendar.JANUARY);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 设置时间为0时0分0秒
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);

        // 获取当前年的第一天
        String firstDayOfYear = dateFormat.format(calendar.getTime());

        // 设置为当前年的最后一天
        calendar.set(Calendar.MONTH, Calendar.DECEMBER);
        // 获取12月的最后一天，因为不是所有年份的12月都有31天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        // 设置时间为23时59分59秒
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);

        // 获取当前年的最后一天
        String lastDayOfYear = dateFormat.format(calendar.getTime());

        for (ErpContractPO erpContractPo : contractList) {
            try {
                String branchCode = erpContractPo.getBranchCode();
                // 分支负责人
                Result<List<UserListVO>> result = bmsOrganizationFacade.listHeadersByHrOrgCodes(Collections.singletonList(branchCode));
                if (result == null || CollectionUtils.isEmpty(result.getData())) {
                    continue;
                }
                UserListVO userListVo = result.getData().get(0);
                // 分支负责人工号
                String jobNumber = userListVo.getJobNumber();
                // 分支负责人身份证
                String idCard = userListVo.getIdCard();
                if (StringUtils.isEmpty(idCard)) {
                    continue;
                }

                // 判断是否已签订
                ContractFindByIdentityRequest req = new ContractFindByIdentityRequest();
                req.setTemplateNo(Collections.singletonList(templateNo));
                req.setIdentity(idCard);
                req.setSignTimeStart(firstDayOfYear);
                req.setSignTimeEnd(lastDayOfYear);

                log.info("查询签约接口入参:" + JSONObject.toJSONString(req));
                com.cfpamf.common.ms.result.Result<List<ContractFindResponse>> contractResult = msContractFacade.findAllContractByCustInfo(req);
                log.info("查询签约接口出参:" + JSONObject.toJSONString(contractResult));
                if (!contractResult.isSuccess()) {
                    throw new BusinessException("查询签约接口报错");
                }

                boolean isSign = true;
                List<ContractFindResponse> custContractList = contractResult.getData();
                if (org.springframework.util.CollectionUtils.isEmpty(custContractList)) {
                    isSign = false;
                } else {
                    // 判断是否已签约
                    List<ContractFindResponse> signContractList = custContractList.stream()
                            .filter(contract -> "02".equals(contract.getSignStatus())).collect(Collectors.toList());
                    if (org.springframework.util.CollectionUtils.isEmpty(signContractList)) {
                        isSign = false;
                    }
                }

                if (!isSign) {
                    // 组装发送钉钉通知入参对象
                    List<DingDingMsgDTO> dingDingMsgDTOList = new ArrayList<>();
                    DingDingMsgDTO dingDingMsgDto = new DingDingMsgDTO();
                    dingDingMsgDto.setEmployeeNo(jobNumber);
                    dingDingMsgDto.setContent(content);
                    dingDingMsgDTOList.add(dingDingMsgDto);

                    // 批量发送钉钉通知
                    Boolean flag = dingDingMessageHelper.sendDingDingNotify(dingDingMsgDTOList, SmsBizTypeEnum.REQUIRE_SIGN);
                    if (!flag) {
                        log.warn("推送钉钉异常:" + erpContractPo.getContractNo());
                    }
                }
            } catch (Exception ex) {
                log.warn("推送钉钉异常:" + erpContractPo.getContractNo(), ex);
            }
        }
    }

    /**
     * 创建生服保管合同
     * @param branchContractContactDTO 分支联系人信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createLifeServiceKeepContract(BranchContractContactDTO branchContractContactDTO) {
        List<DictionaryItemVO> dictList = bmsIntegration.getDictionaryItemsByTypeCode(ContractDicConstants.KEEP_CONTRACT_YEAR_CONFIG);

        if (StringUtils.isEmpty(branchContractContactDTO.getBranchContactCode())) {
            log.info("创建保管合同，分支联系人工号不能为空，参数{}", JSONObject.toJSONString(branchContractContactDTO));
            return;
        }

        int year = CollectionUtils.isEmpty(dictList)?2:Integer.parseInt(dictList.get(0).getItemCode());
        //创建生服保管合同，针对酒水、清洁宝、清洁喵三个租户生成三条信息
        ErpContractPO templateInsertPO = new ErpContractPO();
        templateInsertPO.setContractName(String.format("%s机构生服保管协议", branchContractContactDTO.getBranchName()));
        templateInsertPO.setContractTemplateType(ContractTemplateTypeEnum.LIFE_SERVICE_KEEP_CONTRACT);
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = LocalDate.of(startDate.getYear() + year, 12, 31);
        templateInsertPO.setStartTime(startDate.atStartOfDay());
        templateInsertPO.setEndTime(endDate.atStartOfDay());
        templateInsertPO.setPartner(CooperationObjectEnum.ONE_TIME_PAY.getValue());
        templateInsertPO.setCooperationMode(CooperationModeEnum.AFTER_SALES_PAY.getValue());
        templateInsertPO.setBranchCode(branchContractContactDTO.getBranchCode());
        templateInsertPO.setBranchName(branchContractContactDTO.getBranchName());
        templateInsertPO.setBranchContact(branchContractContactDTO.getBranchContact());
        templateInsertPO.setBranchContactCode(branchContractContactDTO.getBranchContactCode());
        templateInsertPO.setBranchContactMobile(branchContractContactDTO.getBranchContactMobile());
        templateInsertPO.setState(ContractStateEnum.WAIT_DEAL.getValue());

        //与合同服务交互及首个租户合同的合同编号
        String buzContractNo = smartIdHelper.generateContractNo(false);

        //取酒水、清洁宝、清洁喵租户对应合同的信息配置
        int index = 0;
        List<ErpContractPO> insertPOList = new ArrayList<>();
        for (AutoCreateKeepContractDTO keepContractDTO : lifeServiceConfig.getKeepContractInfoList()) {
            //生成合同表数据
            //根据租户不同而不同的：合同编号、母合同id、供应商、合同主体
            ErpContractPO insertPO = new ErpContractPO();
            BeanUtils.copyProperties(templateInsertPO, insertPO);
            //排首位的租户（目前是酒水租户）用业务合同号，用于合同服务交互，其余合同号为业务合同号+租户id
            String contractNo = index == 0 ? buzContractNo : String.format("%s-%s", buzContractNo, keepContractDTO.getTenantId());
            log.info("==== 自动创建生服保管合同 contractNo: {} ====", contractNo);
            insertPO.setContractNo(contractNo);
            insertPO.setTenantId(keepContractDTO.getTenantId());
            insertPO.setPid(keepContractDTO.getPid());
            insertPO.setSupplierCode(keepContractDTO.getSupplierCode().replaceAll("'",""));
            insertPO.setSubject(keepContractDTO.getSubject());
            insertPO.setBghtContractCode(buzContractNo);
            index ++;
            insertPOList.add(insertPO);
        }
        //合同数据入库
        boolean result = erpContractRepo.saveBatch(insertPOList);
        if (result) {
            //添加操作记录
            erpOperateLogRepo.saveBatchLogV1(tenantUtil.getUser(), OperateLogTypeEnum.CONTRACT, insertPOList,
                    "自动创建", "通过", "机构发起保管合同签署，系统自动创建", null, null);
        } else {
            throw new BusinessException("合同数据入库失败");
        }

        //生成合同签署记录数据，只为机构负责人实际签署的合同生成一条记录
        ErpContractPO firstContract = insertPOList.get(0);
        ErpContractSignRecordPO signRecordPO = ContractConvertor.CONVERTOR.convertContractPoToSignRecordPo(firstContract);
        signRecordPO.setSort(1);
        signRecordPO.setSignStatus(ContractSignRecordStatusEnum.ENABLED);
        signRecordPO.setTransactionId(String.format("%s%02d", signRecordPO.getContractNo(), signRecordPO.getSort()));
        erpContractSignRecordRepo.save(signRecordPO);
    }

    /**
     * 创建保管合同
     * @param branchContractContactDTO 分支联系人信息
     */
    @Transactional(rollbackFor = Exception.class)
    public AtomicReference<Boolean> createKeepContract(BranchContractDTO branchContractContactDTO,AtomicReference<Boolean> result) {
        List<DictionaryItemVO> dictList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.AGRIC_TIANJIE_KEEP_CONTRACT_YEAR_CONFIG);

        if (StringUtils.isEmpty(branchContractContactDTO.getBranchContactCode())) {
            log.info("创建保管合同，分支联系人工号不能为空，参数{}", JSONObject.toJSONString(branchContractContactDTO));
            throw new BusinessException(ErrorCodeEnum.ILLEGAL_PARAM.code(), "创建保管合同，分支联系人工号不能为空！" );
        }
        // agricServiceConfig.getKeepContractInfoList() 根据租户id及进行分组
        Map<Integer, List<AutoCreateAgricKeepContractDTO>> keepContractGroup = agricServiceConfig.getKeepContractInfoList().stream().collect(Collectors.groupingBy(AutoCreateAgricKeepContractDTO::getContractTemplateType));
        List<AutoCreateAgricKeepContractDTO> keepContractList = keepContractGroup.get(branchContractContactDTO.getContractTemplateType());
        if(CollectionUtils.isEmpty(keepContractList)){
            log.info("==== 自动创建农服天杰保管合同，未配置该租户合同信息，请先配置 ====,当前租户id：{} ", branchContractContactDTO.getTenantId());
            result.set(Boolean.TRUE);
            return result;
        }
        Integer contractTemplateType = branchContractContactDTO.getContractTemplateType();
        DictionaryItemVO dictionaryItemVO = null;
        if(CollectionUtils.isNotEmpty(dictList)){
            dictionaryItemVO = dictList.stream().filter(item -> item.getItemCode().equals(String.valueOf(contractTemplateType))).findFirst().get();
        }
        int year = ObjectUtil.isNotEmpty(dictionaryItemVO) ? Integer.parseInt(dictionaryItemVO.getItemName()) : Constants.YEAR ;
        // PROHIBIT_PUSH_CONTRACT控制是否生成农服天杰保管合同
        //获取不创建合同的类型
        List<DictionaryItemVO> moduleList = bmsIntegration.getDictionaryItemsByTypeCode(ContractDicConstants.PROHIBIT_PUSH_CONTRACT)
                .stream()
                .filter(dictionaryItem -> dictionaryItem.getItemStatus() == 1)
                .collect(Collectors.toList());
        log.info("合止下推合同签署,模块字典配置:{}", JSONObject.toJSONString(moduleList));
        Map<String, List<DictionaryItemVO>> contractTemplateTypeMap = moduleList.stream().collect(Collectors.groupingBy(DictionaryItemVO::getItemCode));
        if(CollectionUtil.isNotEmpty(contractTemplateTypeMap.get(String.valueOf(contractTemplateType)))){
            log.info("合止下推合同签署,当前租户id:{},合同模板类型:{},字典配置信息：{}", branchContractContactDTO.getTenantId(), contractTemplateType,contractTemplateTypeMap.get(String.valueOf(contractTemplateType)));
            result.set(Boolean.TRUE);
            return result;
        }

        ErpContractPO templateInsertPO = new ErpContractPO();
        ContractTemplateDescEnum contractTemplateDescEnum = ContractTemplateDescEnum.valueOf(contractTemplateType);
        if(ObjectUtil.isEmpty(contractTemplateDescEnum)){
            log.info("createKeepContract contractTemplateType:{} is not configuration", contractTemplateType);
        }
        templateInsertPO.setContractName(String.format("%s"+ contractTemplateDescEnum.getConcatenate(), branchContractContactDTO.getBranchName()));
        templateInsertPO.setContractTemplateType(ContractTemplateTypeEnum.valueOf(contractTemplateType));
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = LocalDate.of(startDate.getYear() + year, 12, 31);
        templateInsertPO.setStartTime(startDate.atStartOfDay());
        templateInsertPO.setEndTime(endDate.atStartOfDay());
        templateInsertPO.setPartner(CooperationObjectEnum.ONE_TIME_PAY.getValue());
        templateInsertPO.setCooperationMode(CooperationModeEnum.AFTER_SALES_PAY.getValue());
        templateInsertPO.setBranchCode(branchContractContactDTO.getBranchCode());
        templateInsertPO.setBranchName(branchContractContactDTO.getBranchName());
        templateInsertPO.setBranchContact(branchContractContactDTO.getBranchContact());
        templateInsertPO.setBranchContactCode(branchContractContactDTO.getBranchContactCode());
        templateInsertPO.setBranchContactMobile(branchContractContactDTO.getBranchContactMobile());
        templateInsertPO.setState(ContractStateEnum.WAIT_DEAL.getValue());

        //与合同服务交互及首个租户合同的合同编号
        String buzContractNo = smartIdHelper.generateContractNo(false);

        List<ErpContractPO> insertPOList = new ArrayList<>();
        List<ErpContractSignRecordPO> signRecordList = new ArrayList<>();

        AtomicReference<Integer> index = new AtomicReference<>(1);
        for (AutoCreateAgricKeepContractDTO keepContractDTO : keepContractList) {
            //生成合同表数据
            //根据租户不同而不同的：合同编号、母合同id、供应商、合同主体
            ErpContractPO insertPO = new ErpContractPO();
            BeanUtils.copyProperties(templateInsertPO, insertPO);
            insertPO.setContractNo(buzContractNo);
            insertPO.setTenantId(branchContractContactDTO.getTenantId());
            insertPO.setPid(keepContractDTO.getPid());
            insertPO.setSupplierCode(keepContractDTO.getSupplierCode());
            insertPO.setSubject(keepContractDTO.getSubject());
            insertPO.setBghtContractCode(buzContractNo);
            insertPOList.add(insertPO);
        }
        //合同数据入库
        boolean saveBatch = erpContractRepo.saveBatch(insertPOList);
        if (saveBatch) {
            //添加操作记录
            erpOperateLogRepo.saveBatchLogV1(tenantUtil.getUser(), OperateLogTypeEnum.CONTRACT, insertPOList,
                    "自动创建", "通过", "机构发起保管合同签署，系统自动创建", null, null);
        } else {
            throw new BusinessException("合同数据入库失败");
        }
        insertPOList.stream().forEach(item->{
            //生成合同签署记录数据，只为机构负责人实际签署的合同生成一条记录
            ErpContractSignRecordPO signRecordPO = ContractConvertor.CONVERTOR.convertContractPoToSignRecordPo(item);
            signRecordPO.setSort(index.getAndSet(index.get() + 1));
            signRecordPO.setSignStatus(ContractSignRecordStatusEnum.ENABLED);
            signRecordPO.setTransactionId(String.format("%s%02d", signRecordPO.getContractNo(), signRecordPO.getSort()));
            signRecordList.add(signRecordPO);
        });
        erpContractSignRecordRepo.saveBatch(signRecordList);
        result.set(Boolean.FALSE);
        return result;
    }

    /**
     * 创建生服珍酒联保合同
     * @param branchContractContactList 分支联系人信息列表
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createLifeServiceUnitedKeepContract(List<BranchContractContactDTO> branchContractContactList) {
        List<DictionaryItemVO> dictList = bmsIntegration.getDictionaryItemsByTypeCode(ContractDicConstants.KEEP_CONTRACT_YEAR_CONFIG);
        int year = CollectionUtils.isEmpty(dictList)?2:Integer.parseInt(dictList.get(0).getItemCode());
        ErpContractPO insertPO = new ErpContractPO();
        String branchNames = branchContractContactList.stream().map(BranchContractContactDTO::getBranchName).collect(Collectors.joining("、"));
        insertPO.setContractName(String.format("%s机构珍酒联保协议", branchNames));
        insertPO.setContractTemplateType(ContractTemplateTypeEnum.LIFE_SERVICE_UNITED_KEEP_CONTRACT);
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = Objects.isNull(branchContractContactList.get(0).getEndDate())
                ?LocalDate.of(startDate.getYear() + year, 12, 31)
                :branchContractContactList.get(0).getEndDate().toLocalDate();
        insertPO.setStartTime(startDate.atStartOfDay());
        insertPO.setEndTime(endDate.atStartOfDay());
        insertPO.setPartner(CooperationObjectEnum.ONE_TIME_PAY.getValue());
        insertPO.setCooperationMode(CooperationModeEnum.AFTER_SALES_PAY.getValue());
        insertPO.setBranchCode(branchContractContactList.get(0).getBranchCode());
        insertPO.setBranchName(branchContractContactList.get(0).getBranchName());
        insertPO.setBranchContact(branchContractContactList.get(0).getBranchContact());
        insertPO.setBranchContactCode(branchContractContactList.get(0).getBranchContactCode());
        insertPO.setBranchContactMobile(branchContractContactList.get(0).getBranchContactMobile());
        insertPO.setState(ContractStateEnum.WAIT_DEAL.getValue());

        //生成合同编号
        String buzContractNo = smartIdHelper.generateContractNo(false);

        //生成合同表数据
        //获取主体信息
        AutoCreateKeepContractDTO unitedKeepContractInfo = lifeServiceConfig.getUnitedKeepContractInfo();
        log.info("==== 自动创建生服珍酒联保合同 contractNo: {} ====", buzContractNo);
        insertPO.setContractNo(buzContractNo);
        insertPO.setTenantId(unitedKeepContractInfo.getTenantId());
        insertPO.setPid(unitedKeepContractInfo.getPid());
        insertPO.setSupplierCode(unitedKeepContractInfo.getSupplierCode().replaceAll("'",""));
        insertPO.setSubject(unitedKeepContractInfo.getSubject());
        //合同数据入库
        boolean result = erpContractRepo.save(insertPO);
        if (result) {
            //添加操作记录
            erpOperateLogRepo.saveBatchLogV1(tenantUtil.getUser(), OperateLogTypeEnum.CONTRACT, Collections.singletonList(insertPO),
                    "自动创建", "通过", "机构发起珍酒联保合同签署，系统自动创建", null, null);
        } else {
            throw new BusinessException("合同数据入库失败");
        }

        //生成合同签署记录数据，只为机构负责人实际签署的合同生成一条记录
        List<ErpContractSignRecordPO> signRecordPOList = new ArrayList<>();
        int index = 1;
        for (BranchContractContactDTO branchContractContactDTO : branchContractContactList) {
            ErpContractSignRecordPO insertSignRecordPO = ContractConvertor.CONVERTOR.convertContractPoToSignRecordPo(insertPO);
            insertSignRecordPO.setSort(index);
            insertSignRecordPO.setBranchCode(branchContractContactDTO.getBranchCode());
            insertSignRecordPO.setBranchName(branchContractContactDTO.getBranchName());
            insertSignRecordPO.setBranchContact(branchContractContactDTO.getBranchContact());
            insertSignRecordPO.setBranchContactCode(branchContractContactDTO.getBranchContactCode());
            insertSignRecordPO.setBranchContactMobile(branchContractContactDTO.getBranchContactMobile());
            insertSignRecordPO.setTransactionId(String.format("%s%02d", insertSignRecordPO.getContractNo(), insertSignRecordPO.getSort()));
            if (index == 1) {
                insertSignRecordPO.setSignStatus(ContractSignRecordStatusEnum.ENABLED);
            }
            signRecordPOList.add(insertSignRecordPO);
            index ++;
        }
        erpContractSignRecordRepo.saveBatch(signRecordPOList);
    }

}
