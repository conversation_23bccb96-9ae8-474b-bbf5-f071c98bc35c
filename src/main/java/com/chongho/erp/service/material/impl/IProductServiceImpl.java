package com.chongho.erp.service.material.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.framework.autoconfigure.common.exception.BusinessException;
import com.cfpamf.framework.autoconfigure.redis.lock.DistributeLock;
import com.cfpamf.framework.autoconfigure.web.bms.JwtUserInfo;
import com.cfpamf.framework.autoconfigure.web.bms.JwtUserInfoService;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileOperationVO;
import com.chongho.erp.common.constant.CommonConstants;
import com.chongho.erp.common.constant.ErpConstants;
import com.chongho.erp.common.constant.MqConstants;
import com.chongho.erp.common.constant.WmsDicConstants;
import com.chongho.erp.common.enums.*;
import com.chongho.erp.common.enums.common.CommonFileTypeEnum;
import com.chongho.erp.common.enums.common.ValidEnum;
import com.chongho.erp.common.enums.material.*;
import com.chongho.erp.common.enums.product.DistributionFlagEnum;
import com.chongho.erp.common.smartid.SmartIdHelper;
import com.chongho.erp.common.smartid.SmartIdTypeEnum;
import com.chongho.erp.common.util.*;
import com.chongho.erp.config.BasicsConfig;
import com.chongho.erp.convertor.material.ProductConvertor;
import com.chongho.erp.convertor.material.ProductSkuConvertor;
import com.chongho.erp.convertor.material.SpecSpuConvertor;
import com.chongho.erp.dto.material.*;
import com.chongho.erp.dto.procurement.ErpPurchasePriceBillLinkDTO;
import com.chongho.erp.dto.query.ErpProductDetailQuery;
import com.chongho.erp.po.*;
import com.chongho.erp.po.material.*;
import com.chongho.erp.po.purchase.ErpProcurementSubmissionItemPriceBillLinkPO;
import com.chongho.erp.repository.ErpProcurementSubmissionItemPriceBillLinkRepo;
import com.chongho.erp.repository.ErpProductSkuUnitTypeRepo;
import com.chongho.erp.repository.SiteRepo;
import com.chongho.erp.repository.material.*;
import com.chongho.erp.service.*;
import com.chongho.erp.service.common.ICommonFileService;
import com.chongho.erp.service.common.ISiteService;
import com.chongho.erp.service.material.*;
import com.chongho.erp.service.redis.RedisService;
import com.chongho.erp.thirdpart.api.WmsIntegration;
import com.chongho.erp.thirdpart.api.file.FileCenterIntegration;
import com.chongho.erp.thirdpart.api.stock.StockFacade;
import com.chongho.erp.thirdpart.api.vo.StockBalance;
import com.chongho.erp.thirdpart.dto.DeliverChannelProductAddFeignDTO;
import com.chongho.erp.thirdpart.dto.DeliverChannelProductFeignDto;
import com.chongho.erp.vo.common.CommonFileVo;
import com.chongho.erp.vo.material.*;
import com.google.common.collect.Lists;
import com.slodon.bbc.core.response.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toMap;

/**
 * author:llk2021
 * date:2022/3/3
 **/
@Slf4j
@Service
@Primary
public class IProductServiceImpl implements IProductService {

    @Autowired
    private EcpProductRepo productRepo;

    @Autowired
    private EcpProductSkuRepo productSkuRepo;

    @Autowired
    private EcpProductSupplierRepo productSupplierRepo;
    @Autowired
    DistributeLock distributeLock;
    @Autowired
    private SmartIdHelper smartIdHelper;
    @Autowired
    private ICommonFileService commonFileService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private BaseBmsTokenService baseBmsTokenService;
    @Autowired
    private ISiteService siteService;
    @Autowired
    private SupplierRepo supplierRepo;
    @Autowired
    private ISuppierService suppierService;
    @Autowired
    private ICategoryService categoryService;
    @Autowired
    private EcCategoryRepo categoryRepo;
    @Autowired
    private IProductChangeLogService productChangeLogService;
    @Autowired
    private EcpProductSkuUnitRepo productSkuUnitRepo;
    @Autowired
    private EcpSpecSpuRepo ecpSpecSpuRepo;
    @Autowired
    private EcpSpecValueRepo specValueRepo;
    @Autowired
    private IBmsIntegration bmsIntegration;
    @Autowired
    private EcpProductSiteRepo productSiteRepo;
    @Autowired
    private EcpSpecSpuRepo specSpuRepo;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private TenantUtil tenantUtil;
    @Autowired
    private ErpTenantRepo erpTenantRepo;
    @Autowired
    private IProductSkuService productSkuService;

    @Autowired
    private WmsIntegration wmsIntegration;

    @Autowired
    private BasicsConfig basicsConfig;

    @Autowired
    private EcpSpecCategoryRepo specCategoryRepo;

    @Autowired
    private EcpSpecKeyRepo specKeyRepo;

    @Autowired
    private EcpProductDepotRepo productDepotRepo;

    @Autowired
    private ErpProcurementSubmissionHeadRepo procurementSubmissionHeadRepo;

    @Autowired
    private ErpMaterialCurrentStockRepo materialCurrentStockRepo;

    @Autowired
    private ErpProcurementSubmissionItemRepo procurementSubmissionItemRepo;

    @Autowired
    private StockFacade stockFacade;

    @Autowired
    private EcpProductDepotService productDepotService;

    @Autowired
    private ErpProductSkuUnitTypeRepo productSkuUnitTypeRepo;

    @Autowired
    private DistributeLockUtils distributeLockUtils;

    @Autowired
    private FileCenterIntegration fileCenterIntegration;

    @Autowired
    private AsyncExecutorUtil executorUtil;

    @Autowired
    private JwtUserInfoService jwtUserInfoService;

    @Autowired
    private ErpDepotRepo erpDepotRepo;

    @Autowired
    private EcpProductSiteRepo ecpProductSiteRepo;

    @Autowired
    private ErpProcurementSubmissionItemPriceBillLinkRepo priceBillLinkRepo;

    @Autowired
    private EcpProductTenantSettingRepo productTenantSettingRepo;


    @Autowired
    private SiteRepo siteRepo;
    @Override
    public Page<ProductPageVO> page(EcpProductQueryDTO productQueryDTO) {
        if(StringUtils.isNotEmpty(productQueryDTO.getCompanyCode())) {
            ErpTenantPO tenantPO = erpTenantRepo.getTenantByCompanyCodeOrName(productQueryDTO.getCompanyCode());
            if(tenantPO != null) {
                productQueryDTO.setCreateTenantId(tenantPO.getTenantId());
            }
        }
        Page<ProductPageVO> productPageVOPage = ProductConvertor.CONVERTOR.poToPageVo(productRepo.pageQuery(productQueryDTO));
        if (CollectionUtils.isEmpty(productPageVOPage.getRecords())) {
            return productPageVOPage;
        }
        List<Long> productIds = productPageVOPage.getRecords().stream().map(ProductPageVO::getId).collect(Collectors.toList());
        Set<Long> createTenantIdList = productPageVOPage.getRecords().stream().map(ProductPageVO::getCreateTenantId).collect(Collectors.toSet());
        Map<Long, ErpTenantPO> tenantMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(createTenantIdList)) {
            List<ErpTenantPO> tenantPOList = erpTenantRepo.batchTenantByIdSet(createTenantIdList);
            tenantMap = tenantPOList.stream().collect(toMap(ErpTenantPO::getTenantId, Function.identity()));
        }
        Map<Long, Integer> countByProductId = productSkuRepo.getCountByProductId(productIds);
        List<String> spuIds = productPageVOPage.getRecords().stream().map(ProductPageVO::getSpuId).collect(Collectors.toList());
        Map<String, String> map = suppierService.getSupplierMapBySpuId(spuIds,null);

        List<DictionaryItemVO> dicProTypeList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.WMS_PRODUCT_TYPE);
        Map<String, String> proTypeMap = dicProTypeList.stream().collect(toMap(DictionaryItemVO::getItemCode, DictionaryItemVO::getItemName, (c, n) -> n));

        Map<Long, Integer> distributionFlagMap = productTenantSettingRepo.getDistributionFlagMap(productIds);
        Map<Long, ErpTenantPO> finalTenantMap = tenantMap;
        productPageVOPage.getRecords().forEach(t->{
            t.setProductTypeName(proTypeMap.get(String.valueOf(t.getProductType())));
            t.setSkuNum(countByProductId.getOrDefault(t.getId(),0));
            t.setMainSupplier(map.getOrDefault(t.getSpuId(),""));
            if (distributionFlagMap.containsKey(t.getId())) {
                t.setDistributionFlag(distributionFlagMap.get(t.getId()));
                t.setDistributionFlagDesc(DistributionFlagEnum.getDesc(t.getDistributionFlag()));
            }
            t.setCreateCompanyName(finalTenantMap.getOrDefault(t.getCreateTenantId(),new ErpTenantPO()).getCompanyName());
        });
        return productPageVOPage;
    }
    @Override
    public void exportProductList(HttpServletResponse response, EcpProductQueryDTO productQueryDTO,Long tenantId) {
        //查询物品
        List<ErpExportProductSkuVO> exportProductVOList = Lists.newArrayList();

        productQueryDTO.setPageSize(-1);
        Page<EcpProductPO> page = productRepo.pageQuery(productQueryDTO);
        List<EcpProductPO> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.getCode(), "货品导出异常");
        }
        //处理数据
        List<Long> productIds = records.stream().map(EcpProductPO::getId).collect(Collectors.toList());
        List<EcpProductSkuPO> ecpProductSkuPos = productSkuRepo.getProSkuByProductIds(productIds);
        if (CollectionUtils.isEmpty(ecpProductSkuPos)){
            throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.getCode(), "货品规格数据异常");
        }

        //货品
        Map<Long, EcpProductPO> productPOMap = records.stream().collect(toMap(EcpProductPO::getId, Function.identity()));
        List<DictionaryItemVO> dicProductTypeList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.WMS_PRODUCT_TYPE);
        Map<String, String> dicProductTypeMap = dicProductTypeList.stream().collect(toMap(DictionaryItemVO::getItemCode, DictionaryItemVO::getItemName));

        //供应商
        List<String> spuIds = records.stream().map(EcpProductPO::getSpuId).collect(Collectors.toList());
        List<ProductSupplierDTO> supplierRepoSupperList = supplierRepo.getSupperList(spuIds,tenantId);
        if (CollectionUtils.isEmpty(supplierRepoSupperList)){
            throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.getCode(), "货品供应商数据异常");
        }
        Map<String, ProductSupplierDTO> productSupplierDTOMap = supplierRepoSupperList.stream().collect(toMap(ProductSupplierDTO::getSpuId, Function.identity()));

        //获取货品品牌字典
        List<DictionaryItemVO> dicBrandList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.WMS_BRAND);
        Map<Long, String> brandMap = dicBrandList.stream().collect(toMap(key->Long.valueOf(key.getItemCode()),DictionaryItemVO::getItemName , (c, n) -> n));
        //获取货品属性
        Map<String, String> attrMap = productSkuService.getAttribute(spuIds);

        //数据赋值
        ecpProductSkuPos.forEach(ecpProductSkuPO -> {
            ErpExportProductSkuVO exportProductVO = new ErpExportProductSkuVO();
            //货品
            EcpProductPO productPO = productPOMap.get(ecpProductSkuPO.getProductId());
            if (ObjectUtils.isNotEmpty(productPO)){
                exportProductVO.setSpuId(productPO.getSpuId());
                exportProductVO.setProductNo(productPO.getProductNo());
                exportProductVO.setProductName(productPO.getProductName());
                exportProductVO.setProductTypeName(dicProductTypeMap.get(productPO.getProductType().toString()));
                exportProductVO.setCategoryPath(productPO.getCategoryPath());
                exportProductVO.setProductStatusName(ProductStatusEnum.getEnumFromKey(productPO.getProductStatus()).getDesc());
            }
            //供应商编码和名称
            ProductSupplierDTO productSupplierDTO = productSupplierDTOMap.get(exportProductVO.getSpuId());
            if (ObjectUtils.isNotEmpty(productSupplierDTO)) {
                exportProductVO.setSupplierCode(productSupplierDTO.getSupplierCode());
                exportProductVO.setSupplierName(productSupplierDTO.getSupplierName());
            }
            //规格编号/名称
            exportProductVO.setSkuId(ecpProductSkuPO.getSkuId());
            exportProductVO.setSkuName(ecpProductSkuPO.getSkuName());
            //品牌/属性
            exportProductVO.setBrandName(brandMap.get(productPO.getBrandId()));
            exportProductVO.setAttributes(attrMap.get(productPO.getSpuId()));
            exportProductVO.setInTax(productPO.getInTax());
            exportProductVO.setOutTax(productPO.getOutTax());
            exportProductVOList.add(exportProductVO);
        });

        EasyPoiUtil easyPoiUtil = new EasyPoiUtil();
        try {
            String fileName = String.format("货品管理-货品导出_%s", DateUtil.format(new Date(), "yyyyMMdd"));
            easyPoiUtil.downloadField(response, fileName, ErpExportProductSkuVO.class, exportProductVOList, null);
        } catch (Exception e) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_EXCEPTION.getCode(), "货品导出异常"+e);
        }

    }
    @Override
    public ProductDetailVO getDetail(Long id) {
        EcpProductPO byId = productRepo.getById(id);
        if(byId == null){
            return null;
        }
        ProductDetailVO productDetailVO = ProductConvertor.CONVERTOR.poToDetailVo(byId);
        List<EcpProductSkuPO> listBySpuId = productSkuRepo.getListBySpuId(id);
        productDetailVO.setSkuNum(listBySpuId.size());
        List<CommonFileVo> filePos = commonFileService.getFileByBizId(CommonFileTypeEnum.PRODUCT_PHOTO, id);
        productDetailVO.setPhoto(filePos);
        List<SupplierPO> supplierBySpu = supplierRepo.getSupplierBySpu(byId.getSpuId());
        productDetailVO.setSupplierPOS(supplierBySpu);
        productDetailVO.setSpecValueDetailVOS(ecpSpecSpuRepo.getSpecSpuValueBySpuId(Collections.singletonList(productDetailVO.getSpuId())));
        productDetailVO.setProductSkuPOList(productSkuRepo.getProductSkuByProductId(id));
        dealChannelInfo(productDetailVO);
        LambdaQueryWrapper<EcCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EcCategoryPO::getCategoryCode, byId.getCategory3Code());
        EcCategoryPO categoryPo = categoryService.getOne(queryWrapper);
        if(Objects.nonNull(categoryPo)){
            //获取所有分类
            CategoryCompositionDTO categoryCompositionDTO = new CategoryCompositionDTO();
            categoryService.generateCategoryPath(categoryPo.getId(), categoryCompositionDTO);
            Collections.reverse(categoryCompositionDTO.getCategoryCodeList());
            productDetailVO.setCategoryCodeList(categoryCompositionDTO.getCategoryCodeList());
        }
        return productDetailVO;
    }

    /**
     * 处理渠道信息
     *
     * @param productDetailVO 货品详情vo
     */
    private void dealChannelInfo(ProductDetailVO productDetailVO) {
        List<ProductSkuDTO> skuList = productDetailVO.getProductSkuPOList();
        List<String> skuIdList = skuList.stream().map(ProductSkuDTO::getSkuId).collect(Collectors.toList());
        Map<String, DeliverChannelProductFeignDto> dtoMap = wmsIntegration.batchQueryChannelProduct(skuIdList);
        for (ProductSkuDTO dto : skuList) {
            if (!dtoMap.containsKey(dto.getSkuId())) {
                continue;
            }
            DeliverChannelProductFeignDto productDto = dtoMap.get(dto.getSkuId());
            if (null != productDto) {
                dto.setChannelId(productDto.getChannelId());
                dto.setChannelName(productDto.getChannelName());
                dto.setExternalCode(productDto.getExternalCode());
            }
        }
        productDetailVO.setProductSkuPOList(skuList);
    }

    @Override
    public List<ProductVO> batchProductInfo(List<Long> productIds) {
        List<EcpProductPO> productList = productRepo.batchProductInfo(productIds);
        return ProductConvertor.CONVERTOR.poToListVo(productList);
    }

    public ErpTenantPO getTenantInfo() {
        //获取租户信息
        ErpTenantPO tenant = erpTenantRepo.getTenantByTenantId(tenantUtil.getTenantIdFromRequest());
        if (Objects.isNull(tenant) || StringUtils.isBlank(tenant.getCompanyName())) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "该租户组织名称为空，请完善信息后，再创建货品！");
        }
        return tenant;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(EcpProductAddDTO addDTO) {
        addDTO.setProductStatus(ProductStatusEnum.OFF.getKey());
        hanlderAddData(addDTO);
    }

    private void hanlderAddData(EcpProductAddDTO addDTO) {
        //spu名称唯一判断以供应商维度，不同供应商spu名称可以相同
        List<ProductSupplierVO> productList = productSkuRepo.getProductNameList(addDTO.getSupplierList(),null);
        if(CollectionUtils.isNotEmpty(productList)&&
                productList.stream().anyMatch(p->p.getProductName().equals(addDTO.getProductName()))){
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "该供应商下货品名称已存在！");
        }
        if(CollectionUtils.isEmpty(addDTO.getSupplierList())){
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "供应商不能为空！");
        }
        List<SupplierPO> supplierList = supplierRepo.getSupplierBatch(addDTO.getSupplierList());
        if(CollectionUtils.isEmpty(supplierList)){
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "供应商不存在或已禁用！");
        }
        ErpTenantPO tenant = getTenantInfo();
        EcpProductPO ecpProductPO = ProductConvertor.CONVERTOR.addDtoToPo(addDTO);
        ecpProductPO.setCreateTenantId(tenant.getTenantId());
        List<EcpProductSkuAddDTO> skuDtos = addDTO.getAddDTOS();
        if(StringUtils.isNotEmpty(addDTO.getProductNo())){
            ecpProductPO.setProductNo(ParamUtil.getWmsCode(ecpProductPO.getProductNo()));
            Integer count = productRepo.ifProductNo(Collections.singletonList(ecpProductPO.getProductNo()));
            if(count>0){
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "系统已存在该物料编号！");
            }
        }else{
            //货品物料编码自动生成
            ecpProductPO.setProductNo(smartIdHelper.generatorMaterialCode(SmartIdTypeEnum.PRODUCT_MATERIAL_CODE));
        }
        ecpProductPO.setSpuId(smartIdHelper.generator(SmartIdTypeEnum.PRODUCT_CODE));

        CategoryQuery categoryQuery = new CategoryQuery();
        categoryQuery.setCategoryCode(addDTO.getCategory1Code());
        Optional.ofNullable(categoryService.getCategoryDetails(addDTO.getCategory1Code())).orElseThrow(() -> new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "分类不存在，请重新选择"));
        //CategoryVo categoryVo2 = Optional.ofNullable(categoryService.getCategoryDetails(addDTO.getCategory2Code())).orElseThrow(() -> new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "分类不存在，请重新选择"));
        CategoryVo categoryVo3 = Optional.ofNullable(categoryService.getCategoryDetails(addDTO.getCategory3Code())).orElseThrow(() -> new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "分类不存在，请重新选择"));
        CategoryCompositionDTO categoryCompositionDTO = new CategoryCompositionDTO();
        categoryCompositionDTO.getCategoryPath().append(categoryVo3.getCategoryName());
        categoryService.generateCategoryPath(categoryVo3.getParentId(), categoryCompositionDTO);
        ecpProductPO.setCategoryPath(categoryCompositionDTO.getCategoryPath().toString());
        ecpProductPO.setCompanyName(tenant.getCompanyName());
        ecpProductPO.setProductStatus(addDTO.getProductStatus());
        ecpProductPO.setCreateTenantId(tenantUtil.getTenantIdFromRequest());
        productRepo.save(ecpProductPO);

        // 批量附件处理
        if(CollectionUtils.isNotEmpty(addDTO.getImages())){
            commonFileService.saveFileBatch(ecpProductPO.getId(), String.valueOf(redisService.getObjectFromSessionByKey(RequestUtil.getRequest(),"userId")), addDTO.getImages());
        }

        List<String> skuIds=new ArrayList<>();
        skuDtos.forEach(t -> {
            if (!Objects.isNull(t.getTotalTons()) && t.getTotalTons().compareTo(BigDecimal.ZERO) > 0) {
                t.setAbleTons(t.getTotalTons());
                //单位转换
                BigDecimal toWeight = t.getWeight().multiply(BigDecimal.valueOf(UnitTypeEnum.getByUnit(t.getUnit()).getWeightKG()));
                t.setAbleNum(t.getAbleTons().multiply(BigDecimal.valueOf(1000)).divide(toWeight,
                        BigDecimal.ROUND_HALF_UP).intValue());
            }
            t.setErpNo(ecpProductPO.getProductNo());
            t.setSkuId(smartIdHelper.generator(SmartIdTypeEnum.PRODUCT_SKU_CODE));
            t.setProductId(ecpProductPO.getId());
            t.setTenantId(tenantUtil.getTenantIdFromRequest());
            skuIds.add(t.getSkuId());
        });
        List<EcpProductSkuPO> ecpProductSkuPOS = ProductSkuConvertor.CONVERTOR.addDtoToPoList(skuDtos);
        productSkuRepo.saveBatch(ecpProductSkuPOS);
        List<DeliverChannelProductAddFeignDTO> channelProductAddDtoList = skuDtos.stream().map(po -> {
            DeliverChannelProductAddFeignDTO dto = new DeliverChannelProductAddFeignDTO();
            dto.setExternalCode(po.getExternalCode());
            dto.setChannelId(po.getChannelId());
            dto.setSkuId(po.getSkuId());
            return dto;
        }).collect(Collectors.toList());
        wmsIntegration.batchAddChannelProduct(channelProductAddDtoList);
        List<EcpProductSupplierPO> collect = addDTO.getSupplierList().stream().map(t -> {
            EcpProductSupplierPO ecpProductSupplierPO = new EcpProductSupplierPO();
            ecpProductSupplierPO.setSupplierCode(t);
            ecpProductSupplierPO.setSpuId(ecpProductPO.getSpuId());
            return ecpProductSupplierPO;
        }).collect(Collectors.toList());
        productSupplierRepo.saveBatch(collect);
        //默认选中供应商名下全部发货站（如果货品类型为套装时，不默认选中）
        if (ObjectUtil.notEqual(ecpProductPO.getProductType(), ProductTypeEnum.SUIT_GOODS.getValue())){
            setSkuSite(skuIds, addDTO.getSupplierList());
        }
        //物流属性初始化
        List<EcpProductSkuUnitPO> ecpProductSkuUnitPOList=new ArrayList<>();
        BigDecimal flagWeight=BigDecimal.ZERO;
        //库存单位字典
        if(addDTO.getIsNew() == null || !addDTO.getIsNew()){
            List<DictionaryItemVO> dicList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.WMS_UNIT_NAME);
            for (EcpProductSkuAddDTO productSkuPO : skuDtos) {
                for (EcpProductSkuUnitPO skuUnitPO : skuUnitBuild()) {
                    if(Objects.equals(addDTO.getProductStatus(),ProductStatusEnum.ON.getKey())){
                        skuUnitPO.setIsOpen(CommonEnum.YES.getCode());
                    }
                    if (Objects.equals(skuUnitPO.getUnitType(), SkuUnitTypeEnum.STOCK.getValue())) {
                        skuUnitPO.setUnitCode(productSkuPO.getUnitCode());
                        Optional<DictionaryItemVO> dic = dicList.stream().filter(item -> item.getItemCode().equals(productSkuPO.getUnitCode())).findFirst();
                        dic.ifPresent(d ->{
                            checkUnitWeight(productSkuPO.getWeight(),productSkuPO.getUnit(), skuUnitPO, d);
                        });
                        flagWeight = skuUnitPO.getWeight();
                    } else {
                        skuUnitPO.setPackageNum(skuUnitPO.getWeight().divide(flagWeight, RoundingMode.HALF_UP));
                    }
                    skuUnitPO.setSkuId(productSkuPO.getSkuId());
                    skuUnitPO.setSkuUnitCode(smartIdHelper.generator(SmartIdTypeEnum.SKU_UNIT_CODE));
                    ecpProductSkuUnitPOList.add(skuUnitPO);
                }
            }
        }
        // 保存属性
        if(CollectionUtils.isNotEmpty(addDTO.getSpecList())) {
            updateSpec(addDTO.getSpecList(), ecpProductPO.getSpuId());
        }
        //物流属性默认设置
        log.info("ecpProductSkuUnitPOList:{}",ecpProductSkuUnitPOList);
        productSkuUnitRepo.saveBatch(ecpProductSkuUnitPOList);
        productChangeLogService.addLog(ecpProductPO.getId(), Objects.equals(addDTO.getProductStatus(),ProductStatusEnum.DRAFT.getKey()) ? ProductLogOperateType.DRAFT : ProductLogOperateType.CREATE, IProductChangeLogService.SUCCESS,
                IProductChangeLogService.SUCCESS, tenantUtil.getUserName(), null);
    }

    private void checkUnitWeight(BigDecimal skuWeight,String skuUnit, EcpProductSkuUnitPO skuUnitPO, DictionaryItemVO dic) {
        UnitTypeEnum unit = UnitTypeEnum.getUnitByDesc(dic.getItemName());

        BigDecimal skuUnitWeight = skuWeight.multiply(BigDecimal.valueOf(UnitTypeEnum.getByUnit(skuUnit).getWeightKG()));
        skuUnitPO.setUnitName(dic.getItemName());
        skuUnitPO.setWeight(skuUnitWeight);

        if (Objects.isNull(unit)) {
            return;
        }
        if (Objects.equals(unit.getValue(),UnitTypeEnum.T.getValue())) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "库存单位不能为吨！");
        }
        if (Objects.isNull(basicsConfig.getSkuUnitWeightCheckMap())) {
            return;
        }
        BigDecimal weight = basicsConfig.getSkuUnitWeightCheckMap().get(unit.getValue());
        if (Objects.isNull(weight)) {
            return;
        }
        if (weight.compareTo(skuUnitWeight) != 0) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "规格单位和重量不能设置为"+ skuWeight+ skuUnit+"/"+dic.getItemName());
        }
    }

    public void setSkuSite(List<String> skuIds,List<String> supplierCodeList){
        List<SitePo> siteList = siteService.listBySupplierList(supplierCodeList);
        if(CollectionUtils.isEmpty(siteList)){
            return;
        }
        List<EcpProductSitePO> pos=new ArrayList<>();
        for(String skuId:skuIds){
            for(SitePo site:siteList){
                EcpProductSitePO ecpProductSitePO = new EcpProductSitePO();
                ecpProductSitePO.setSiteId(site.getId());
                ecpProductSitePO.setSkuId(skuId);
                pos.add(ecpProductSitePO);
            }
        }
        productSiteRepo.saveBatch(pos);
    }
    public void updateSpec(List<ProductUpdateSpecReq> specList, String spuId) {
        Integer count = productRepo.ifSpuId(spuId);
        if (count<=0) {
            return;
        }
        List<EcpSpecSpuPO> specSkuPOS = new LinkedList<>();
        specList.forEach(
                o -> {
                    EcpSpecSpuPO specSkuPO = new EcpSpecSpuPO();
                    specSkuPO.setId(o.getId());
                    specSkuPO.setSpuId(spuId);
                    specSkuPO.setSpecKeyId(o.getSpecKeyId());
                    specSkuPO.setSpecValueId(o.getSpecValueId());
                    specSkuPOS.add(specSkuPO);
                }
        );
        try {
            ecpSpecSpuRepo.saveOrUpdateBatch(specSkuPOS);
        } catch (DuplicateKeyException duplicateKeyException) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "规格重复");
        }
    }
    public List<EcpProductSkuUnitPO> skuUnitBuild(){
        List<EcpProductSkuUnitPO> skuUnitPOList=new ArrayList<>();
        for(int i=0;i<2;i++){
            EcpProductSkuUnitPO ecpProductSkuUnitPO=new EcpProductSkuUnitPO();
            ecpProductSkuUnitPO.setUnitCode(i==0?"1":"4");
            ecpProductSkuUnitPO.setUnitName(i==0?"袋":"吨");
            ecpProductSkuUnitPO.setUnitType(i==0?1:2);
            ecpProductSkuUnitPO.setRemark(i==0?"最小计量单位，可以改单位名称":"拼车采购的单位，单位固定为吨，包装数系统根据库存单位重量自动计算");
            ecpProductSkuUnitPO.setFinancePurchaseFlag( false);
            ecpProductSkuUnitPO.setFinanceSellFlag( false);
            ecpProductSkuUnitPO.setPackageNum(i==0?BigDecimal.valueOf(1):BigDecimal.valueOf(10));
            ecpProductSkuUnitPO.setWeight(i==0?BigDecimal.valueOf(100):BigDecimal.valueOf(1000));
            skuUnitPOList.add(ecpProductSkuUnitPO);
        }
        return skuUnitPOList;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProduct(EcpProductEditDTO editDTO) {
        EcpProductPO byId = productRepo.getById(editDTO.getId());
        if(Objects.isNull(byId)){
            return;
        }
        tenantUtil.validateTenantId(byId.getCreateTenantId());
        handlerUpdateData(editDTO, byId);
    }

    private void handlerUpdateData(EcpProductEditDTO editDTO, EcpProductPO byId) {
        StringBuilder changeLog = new StringBuilder();
        //spu名称唯一判断以供应商维度，不同供应商spu名称可以相同
        if (CollectionUtils.isNotEmpty(editDTO.getSupplierList())) {
            List<ProductSupplierVO> productList = productSkuRepo.getProductNameList(editDTO.getSupplierList(),null);
            if(CollectionUtils.isNotEmpty(productList)){
                Optional<ProductSupplierVO> productFlag = productList.stream().filter(item -> !Objects.equals(item.getId(), byId.getId())
                        &&item.getProductName().equals(editDTO.getProductName())).findFirst();
                if(productFlag.isPresent()){
                    throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "该供应商下货品名称已存在！");
                }
            }
        }
        if(Objects.equals(editDTO.getProductStatus(),ProductStatusEnum.ON.getKey())) {
            List<SupplierPO> supplierList = supplierRepo.getSupplierBatch(editDTO.getSupplierList());
            if (CollectionUtils.isEmpty(supplierList)) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "供应商不存在或已禁用！");
            }
        }
        if(Objects.equals(byId.getProductStatus(),ProductStatusEnum.ON.getKey())){
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "已上架过的货品不能进行编辑！");
        }
        if(Objects.equals(CommonEnum.YES.getCode(), byId.getIsOpen())&&!byId.getProductNo().equals(editDTO.getProductNo())){
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "已上架过的货品不能进行编辑物料编码！");
        }
        Integer count = productRepo.checkProductNo(ParamUtil.getWmsCode(editDTO.getProductNo()), byId.getId());
        if(count>0){
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "系统已存在该物料编号！");
        }
        List<EcpProductSupplierPO> oldSup = productSupplierRepo.getListBySkuId(byId.getSpuId());
        List<String> addSup = editDTO.getSupplierList().stream().filter(item -> oldSup.stream().noneMatch(temp -> temp.getSupplierCode().equals(item))).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(addSup)){
            if (Objects.equals(CommonEnum.YES.getCode(), byId.getIsOpen())) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "已上架过的货品不能进行编辑供应商！");
            }
            List<SupplierPO> suppLierList = supplierRepo.lambdaQuery().in(SupplierPO::getSupplierCode, Arrays.asList(oldSup.get(0).getSupplierCode(), addSup.get(0))).list();
            changeLog.append("供应商由").append(suppLierList.stream().filter(e -> Objects.equals(e.getSupplierCode(),oldSup.get(0).getSupplierCode())).map(e -> StringUtils.defaultIfEmpty(e.getSupplierShortName(), e.getSupplierName())).findFirst().orElse("")).
                    append("修改为").append(suppLierList.stream().filter(e -> Objects.equals(e.getSupplierCode(),addSup.get(0))).map(e -> StringUtils.defaultIfEmpty(e.getSupplierShortName(), e.getSupplierName())).findFirst().orElse("")).append(";");
        }
        List<SitePo> siteList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(editDTO.getSupplierList()) && ObjectUtil.notEqual(editDTO.getProductType(), ProductTypeEnum.SUIT_GOODS.getValue())) {
            siteList = siteService.listBySupplierList(editDTO.getSupplierList());
            if(Objects.equals(editDTO.getProductStatus(),ProductStatusEnum.ON.getKey())){
                if(CollectionUtils.isEmpty(siteList)){
                    throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "货品上架发货站不能为空");
                }
            }
        }

        List<ProductSkuDTO> oldSku = productSkuRepo.getProductSkuByProductId(editDTO.getId());
        Optional<ProductSkuDTO> productSku = oldSku.stream().filter(item -> editDTO.getAddDTOS().stream().anyMatch(temp -> item.getSkuId().equals(temp.getSkuId()) &&
                Objects.equals(CommonEnum.YES.getCode(), item.getIsOpen()) &&
                (item.getWeight().compareTo(temp.getWeight()) != 0
                        || !item.getUnitCode().equals(temp.getUnitCode())))).findFirst();
        if(productSku.isPresent()&&Objects.equals(CommonEnum.YES.getCode(), byId.getIsOpen())){
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "已上架过的规格不能进行编辑重量、库存单位、物料编码！");
        }
        List<EcpProductSkuPO> old = productSkuRepo.getListBySpuId(editDTO.getId());
        //过滤删除的sku
        List<EcpProductSkuPO> delSku = old.stream().filter(item -> editDTO.getAddDTOS().stream().noneMatch(temp -> item.getSkuId().equals(temp.getSkuId()))).
                filter(sku->Objects.equals(sku.getIsOpen(),CommonEnum.YES.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(delSku)){
            String delSkuStr = delSku.stream().map(EcpProductSkuPO::getSkuId).collect(Collectors.joining(","));
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), delSkuStr+"已上架过的规格不能进行删除！");
        }
        EcpProductPO ecpProductPO = ProductConvertor.CONVERTOR.editDtoToPo(editDTO);
        Optional.ofNullable(categoryService.getCategoryDetails(editDTO.getCategory1Code())).orElseThrow(() -> new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "分类不存在，请重新选择"));
        Optional.ofNullable(categoryService.getCategoryDetails(editDTO.getCategory2Code())).orElseThrow(() -> new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "分类不存在，请重新选择"));
        CategoryVo categoryVo3 = Optional.ofNullable(categoryService.getCategoryDetails(editDTO.getCategory3Code())).orElseThrow(() -> new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "分类不存在，请重新选择"));
        CategoryCompositionDTO categoryCompositionDTO = new CategoryCompositionDTO();
        categoryCompositionDTO.getCategoryPath().append(categoryVo3.getCategoryName());
        categoryService.generateCategoryPath(categoryVo3.getParentId(), categoryCompositionDTO);
        ecpProductPO.setCategoryPath(categoryCompositionDTO.getCategoryPath().toString());
        if(editDTO.getProductStatus().equals(ProductStatusEnum.ON.getKey())){
            ecpProductPO.setIsOpen(CommonEnum.YES.getCode());
        }
        ecpProductPO.setProductNo(ParamUtil.getWmsCode(ecpProductPO.getProductNo()));

        handlerProductChangeLog(editDTO, byId, changeLog, ecpProductPO);
        productRepo.updateById(ecpProductPO);
        // 批量附件处理
        if(CollectionUtils.isNotEmpty(editDTO.getImages())){
            commonFileService.saveFileBatch(ecpProductPO.getId(), String.valueOf(redisService.getObjectFromSessionByKey(RequestUtil.getRequest(),"userId")), editDTO.getImages());
        }
        //可用吨数计算
//        List<Map<String, BigDecimal>> skuWeightList = Optional.ofNullable(orderItemRepo.batchSkuWeight(oldSku.stream().map(ProductSkuDTO::getSkuId).collect(Collectors.toList()))).orElse(new ArrayList<>());
        //筛选已存在的数据
        List<ErpProductDownVO> productDownVOS=new ArrayList<>();
        editDTO.getAddDTOS().forEach(t->{
            if(Objects.equals(ecpProductPO.getIsOpen(),CommonEnum.YES.getCode())&&Objects.equals(t.getState(),ProductStatusEnum.ON.getKey())){
                t.setIsOpen(CommonEnum.YES.getCode());
            }
            //规格下架通知
            if(Objects.equals(t.getState(),ProductStatusEnum.OFF.getKey())){
                ErpProductDownVO vo=new ErpProductDownVO();
                vo.setSkuId(t.getSkuId());
                vo.setProductNo(ecpProductPO.getProductNo());
                vo.setEventTime(new Date());
                vo.setEventType(2);
                productDownVOS.add(vo);
            }
            if(Objects.isNull(t.getId())){
                t.setProductId(editDTO.getId());
                t.setSkuId(smartIdHelper.generator(SmartIdTypeEnum.PRODUCT_SKU_CODE));
            }
            t.setErpNo(ecpProductPO.getProductNo());
            if(!Objects.isNull(t.getTotalTons())){
                //已售吨数
//                Optional<Map<String, BigDecimal>> skuWeight = skuWeightList.stream().filter(item -> !Objects.isNull(item.get(t.getSkuId()))).findFirst();
//                if(skuWeight.isPresent()){
//                    skuWeight.ifPresent(temp->t.setSoldTons(temp.get(t.getSkuId())));
//                    t.setAbleTons(t.getTotalTons().subtract(t.getSoldTons()));
//                    单位转换
//                    BigDecimal toWeight = t.getWeight().multiply(BigDecimal.valueOf(UnitTypeEnum.getByUnit(t.getUnit()).getWeightKG()));
//                    t.setAbleNum(t.getAbleTons().multiply(BigDecimal.valueOf(1000)).divide(toWeight,BigDecimal.ROUND_HALF_DOWN).intValue());
//                }
            }
        });
        List<EcpProductSkuPO> nOne = ProductSkuConvertor.CONVERTOR.editDtoToPoList(editDTO.getAddDTOS());
        handlerProductSkuChangeLog(changeLog, old, nOne);
        DataBindUtil.updateOnlySaveDel(old, nOne, productSkuRepo, (po, list) -> list.stream().anyMatch(t -> Objects.equals(t.getId(), po.getId())));
        //修改
        List<EcpProductSkuPO> proSkuList = nOne.stream().filter(o -> old.stream().anyMatch(t -> Objects.equals(t.getId(), o.getId()))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(proSkuList)) {
            handlerProductSkuInfoChangeLog(changeLog, old, proSkuList,editDTO.getAddDTOS());
            productSkuRepo.batchUpdateProSku(proSkuList);
        }
        List<DeliverChannelProductAddFeignDTO> channelProductAddFeignList = Lists.newArrayList();

        editDTO.getAddDTOS().forEach(o -> {
            DeliverChannelProductAddFeignDTO dto = new DeliverChannelProductAddFeignDTO();
            dto.setChannelId(o.getChannelId());
            dto.setExternalCode(o.getExternalCode());
            dto.setSkuId(o.getSkuId());
            channelProductAddFeignList.add(dto);
        });

        wmsIntegration.batchAddChannelProduct(channelProductAddFeignList);
        //查询物流规格
        List<String> skuIds = nOne.stream().map(EcpProductSkuPO::getSkuId).collect(Collectors.toList());
        List<EcpProductSkuUnitPO> proSkuUnitList = productSkuUnitRepo.batchFindUnitListBySkuId(skuIds);
        List<EcpProductSkuUnitPO> changeProSkuUnitList = new ArrayList<>();
        //库存单位字典
        List<DictionaryItemVO> dicList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.WMS_UNIT_NAME);
        BigDecimal flagWeight = BigDecimal.ZERO;
        if(editDTO.getIsNew() == null || !editDTO.getIsNew()){
            for (EcpProductSkuEditDTO item : editDTO.getAddDTOS()) {
                List<EcpProductSkuUnitPO> skuUnitList = proSkuUnitList.stream().filter(unit -> unit.getSkuId().equals(item.getSkuId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(skuUnitList) || skuUnitList.size() == 0) {
                    skuUnitList=skuUnitBuild().stream().peek(t->t.setSkuId(item.getSkuId())).collect(Collectors.toList());
                }else {
                    handlerUnitChangeLog(changeLog, dicList, item, skuUnitList);
                }
                for (EcpProductSkuUnitPO sku : skuUnitList){
                    if(Objects.equals(editDTO.getProductStatus(),ProductStatusEnum.ON.getKey())){
                        sku.setIsOpen(CommonEnum.YES.getCode());
                    }
                    sku.setSkuUnitCode(Optional.ofNullable(sku.getSkuUnitCode()).orElse(smartIdHelper.generator(SmartIdTypeEnum.SKU_UNIT_CODE)));
                    if (Objects.equals(sku.getUnitType(),SkuUnitTypeEnum.STOCK.getValue())) {
                        sku.setUnitCode(item.getUnitCode());
                        Optional<DictionaryItemVO> dic = dicList.stream().filter(item2 -> item2.getItemCode().equals(sku.getUnitCode())).findFirst();
                        dic.ifPresent(d ->{
                            checkUnitWeight(item.getWeight(),item.getUnit(), sku, d);
                        });
                        flagWeight=sku.getWeight();
                    }else if(Objects.equals(sku.getUnitType(),SkuUnitTypeEnum.FINANCE.getValue())){
                        sku.setPackageNum(sku.getWeight().divide(flagWeight,BigDecimal.ROUND_HALF_UP));
                    }
                    changeProSkuUnitList.add(sku);
                }
            }
            productSkuUnitRepo.saveOrUpdateBatch(changeProSkuUnitList);
        }

        // 物料变更同步交易中心
        List<ErpSyncTradingCenterProductChangeVo> syncTradingCenterProductChanges = Lists.newArrayList();
        editDTO.getAddDTOS().forEach(sku -> {
            String skuId = sku.getSkuId();
            // 封装数据
            ErpSyncTradingCenterProductChangeVo vo = new ErpSyncTradingCenterProductChangeVo();
            vo.setSkuId(skuId);
            vo.setSkuName(sku.getSkuName());
            vo.setProductName(editDTO.getProductName());
            vo.setOutTax(editDTO.getOutTax());
            vo.setInTax(editDTO.getInTax());
            List<ErpProductSkuUnitVo> skuUnitList = Lists.newArrayList();
            // 设置单位
            for (ProductSkuUnitUpdateDTO productSkuUnitUpdateDTO : sku.getUnitDTOList()) {
                ErpProductSkuUnitVo erpProductSkuUnitVo = getErpProductSkuUnitVo(productSkuUnitUpdateDTO, skuId);
                skuUnitList.add(erpProductSkuUnitVo);
            }
            vo.setSkuUnitList(skuUnitList);
            syncTradingCenterProductChanges.add(vo);
        });

        // 发送物料变更消息
        if (CollectionUtil.isNotEmpty(syncTradingCenterProductChanges)){
            sendProductChangeMessage(syncTradingCenterProductChanges);
        }

        List<EcpProductSupplierPO> newSup = editDTO.getSupplierList().stream().map(t->{
            EcpProductSupplierPO ecpProductSupplierPO = new EcpProductSupplierPO();
            ecpProductSupplierPO.setSupplierCode(t);
            ecpProductSupplierPO.setSpuId(byId.getSpuId());
            return ecpProductSupplierPO;
        }).collect(Collectors.toList());
        DataBindUtil.updateOnlySaveDel(oldSup,newSup,productSupplierRepo,(t,b)-> b.stream().anyMatch(temp->
            temp.getSupplierCode().equals(t.getSupplierCode())
        ));
        //默认供应商全部发货站
        if (CollectionUtils.isNotEmpty(siteList)) {
            List<EcpProductSitePO> newProSite = new ArrayList<>();
            List<EcpProductSitePO> oldProSite = productSiteRepo.findSiteBySkuIds(skuIds);
            for (String skuId : skuIds) {
                for (SitePo site : siteList) {
                    EcpProductSitePO ecpProductSitePO = new EcpProductSitePO();
                    ecpProductSitePO.setSiteId(site.getId());
                    ecpProductSitePO.setSkuId(skuId);
                    newProSite.add(ecpProductSitePO);
                }
            }
            DataBindUtil.updateOnlySaveDel(oldProSite,newProSite,productSiteRepo,(t,b)-> b.stream().anyMatch(temp->
                    temp.getSkuId().equals(t.getSkuId())&&Objects.equals(temp.getSiteId(),t.getSiteId())
            ));
        }
        BatchProductIds batchProductIds = new BatchProductIds();
        batchProductIds.setProductIds(Collections.singletonList(editDTO.getId()));
        if (!Objects.equals(editDTO.getProductStatus(),byId.getProductStatus()) && (editDTO.getIsNew() == null || !editDTO.getIsNew())) {
            changeStatus(batchProductIds,ProductStatusEnum.getEnumFromKey(editDTO.getProductStatus()));
        }

        List<EcpSpecSpuPO> oldList = ecpSpecSpuRepo.getSpecSpuBySpuId(byId.getSpuId());
        List<EcpSpecSpuPO> delList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(oldList)) {
            List<ProductUpdateSpecReq> specDelConditionList = Objects.isNull(editDTO.getSpecList()) ? Collections.emptyList() : editDTO.getSpecList();
            delList = oldList.stream().filter(item -> specDelConditionList.stream().noneMatch(temp ->
                    Objects.equals(temp.getId(), item.getId()))).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(delList)){
                ecpSpecSpuRepo.batchDelSpecSpu(delList.stream().map(EcpSpecSpuPO::getId).collect(Collectors.toList()));
            }
        }
        //handlerProductSpecChangeLog(editDTO, changeLog, delList);
        if(CollectionUtils.isNotEmpty(editDTO.getSpecList())){
            DataBindUtil.addUpdateById(oldList, SpecSpuConvertor.CONVERTOR.spuUpdateDtoToPoList(editDTO.getSpecList()),ecpSpecSpuRepo
                    , t->{
                        t.setSpuId(byId.getSpuId());
                    });
        }
        //货品编辑轨迹记录
        productChangeLogService.addLog(editDTO.getId(), Objects.equals(editDTO.getProductStatus(),ProductStatusEnum.DRAFT.getKey()) ? ProductLogOperateType.DRAFT : ProductLogOperateType.MODIFY,IProductChangeLogService.SUCCESS,
                changeLog.toString(), tenantUtil.getUserName(), editDTO.getChannel());
        //发送货品下架通知
        if(!Objects.equals(ecpProductPO.getProductStatus(),ProductStatusEnum.ON.getKey())){
            ErpProductDownVO vo=new ErpProductDownVO();
            vo.setEventTime(new Date());
            vo.setProductNo(ecpProductPO.getProductNo());
            vo.setEventType(1);
            this.sendProDownNotice(Collections.singletonList(vo));
            return;
        }
        //发送货品规格下架通知
        if(CollectionUtils.isNotEmpty(productDownVOS)){
            this.sendProDownNotice(productDownVOS);
        }
    }

    /**
     * 根据产品SKU单位更新DTO和SKUID创建ERP产品SKU单位VO
     * 此方法用于将产品SKU单位更新请求的数据转换为ERP系统所需的SKU单位信息格式
     * 它主要负责从请求DTO中提取单位信息，并将其设置到新的ERP产品SKU单位VO对象中
     *
     * @param productSkuUnitUpdateDTO 包含待更新SKU单位信息的DTO
     * @param skuId SKU的唯一标识符，用于标识ERP系统中的具体SKU
     * @return 返回填充了SKU单位信息的ERP产品SKU单位VO对象
     */
    private static ErpProductSkuUnitVo getErpProductSkuUnitVo(ProductSkuUnitUpdateDTO productSkuUnitUpdateDTO, String skuId) {
        ErpProductSkuUnitVo erpProductSkuUnitVo = new ErpProductSkuUnitVo();
        erpProductSkuUnitVo.setSkuId(skuId);
        erpProductSkuUnitVo.setUnitName(productSkuUnitUpdateDTO.getUnitName());
        erpProductSkuUnitVo.setUnitCode(productSkuUnitUpdateDTO.getUnitCode());
        erpProductSkuUnitVo.setUnitType(productSkuUnitUpdateDTO.getUnitType());
        erpProductSkuUnitVo.setWeight(productSkuUnitUpdateDTO.getWeight());
        erpProductSkuUnitVo.setPackageNum(productSkuUnitUpdateDTO.getPackageNum());
        erpProductSkuUnitVo.setFinanceSellFlag(productSkuUnitUpdateDTO.getFinanceSellFlag());
        erpProductSkuUnitVo.setFinancePurchaseFlag(productSkuUnitUpdateDTO.getFinancePurchaseFlag());
        erpProductSkuUnitVo.setRemark(productSkuUnitUpdateDTO.getRemark());
        return erpProductSkuUnitVo;
    }

    /**
     * 发送物料变更消息
     *
     * @param syncTradingCenterProductChanges 物料规格信息
     */
    private void sendProductChangeMessage(List<ErpSyncTradingCenterProductChangeVo> syncTradingCenterProductChanges) {
        if (syncTradingCenterProductChanges == null || syncTradingCenterProductChanges.isEmpty()) {
            log.info("物料变更消息列表为空");
            return;
        }

        for (ErpSyncTradingCenterProductChangeVo syncTradingCenterProductChange : syncTradingCenterProductChanges) {
            if (syncTradingCenterProductChange == null) {
                log.info("物料规格信息为空");
                continue;
            }

            log.info("发送物料变更消息 规格编码: {}, 详细信息：{}", syncTradingCenterProductChange.getSkuId(), syncTradingCenterProductChange);

            try {
                rabbitTemplate.convertAndSend(MqConstants.ERP_PRODUCT_CHANGE_EXCHANGE_NAME, null, JSONObject.toJSONString(syncTradingCenterProductChange));
            } catch (Exception e) {
                log.error("发送物料变更消息失败", e);
            }
        }
    }

    private void handlerUnitChangeLog(StringBuilder changeLog, List<DictionaryItemVO> dicList, EcpProductSkuEditDTO item, List<EcpProductSkuUnitPO> skuUnitList) {
        Optional<EcpProductSkuUnitPO> stockUnit = skuUnitList.stream().filter(e -> Objects.equals(e.getUnitType(), SkuUnitTypeEnum.STOCK.getValue())).findFirst();
        if (stockUnit.isPresent() && !Objects.equals(item.getUnitCode(),stockUnit.get().getUnitCode())) {
            Optional<DictionaryItemVO> dic = dicList.stream().filter(e -> e.getItemCode().equals(item.getUnitCode())).findFirst();
            dic.ifPresent(e -> {
                changeLog.append("库存单位").append(stockUnit.get().getUnitName()).append("修改为").append(e.getItemName()).append(";");
            });
        }
    }

    private void handlerProductSkuInfoChangeLog(StringBuilder changeLog, List<EcpProductSkuPO> old, List<EcpProductSkuPO> proSkuList,List<EcpProductSkuEditDTO> addDTOS) {
        Map<String, EcpProductSkuPO> oldProductSkuMap = old.stream().collect(toMap(EcpProductSkuPO::getSkuId, Function.identity()));
        for (EcpProductSkuPO ecpProductSkuPO : proSkuList) {
            EcpProductSkuPO oldProductSku = oldProductSkuMap.get(ecpProductSkuPO.getSkuId());
            if (Objects.isNull(oldProductSku)) {
                continue;
            }
            if (!Objects.equals(ecpProductSkuPO.getSkuName(),oldProductSku.getSkuName())) {
                changeLog.append(ecpProductSkuPO.getSkuId()).append("规格名称").append(oldProductSku.getSkuName()).append("修改为").append(ecpProductSkuPO.getSkuName()).append(";");
            }
            if (!Objects.equals(ecpProductSkuPO.getIsMain(),oldProductSku.getIsMain())) {
                changeLog.append(ecpProductSkuPO.getSkuId()).append("是否主规格").append(Objects.equals(oldProductSku.getIsMain(),1) ? "是":"否").append("修改为").append(Objects.equals(ecpProductSkuPO.getIsMain(),1) ? "是":"否").append(";");
            }
            if (ecpProductSkuPO.getWeight().compareTo(oldProductSku.getWeight()) != 0 || !Objects.equals(ecpProductSkuPO.getUnit(),oldProductSku.getUnit())) {
                changeLog.append(ecpProductSkuPO.getSkuId()).append("重量").append(oldProductSku.getWeight().stripTrailingZeros().toPlainString()).append(oldProductSku.getUnit()).append("修改为").append(ecpProductSkuPO.getWeight().stripTrailingZeros().toPlainString()).append(ecpProductSkuPO.getUnit()).append(";");
            }
            if (!Objects.equals(ecpProductSkuPO.getState(),oldProductSku.getState())) {
                if (Objects.equals(ecpProductSkuPO.getState(),ProductStatusEnum.ON.getKey())) {
                    changeLog.append(ecpProductSkuPO.getSkuId()).append("启用;");
                }else {
                    changeLog.append(ecpProductSkuPO.getSkuId()).append("禁用;");
                }
            }
            if (Objects.nonNull(ecpProductSkuPO.getToBPrice()) && Objects.nonNull(oldProductSku.getToBPrice()) ) {
                if (ecpProductSkuPO.getToBPrice().compareTo(oldProductSku.getToBPrice()) != 0) {
                    changeLog.append(ecpProductSkuPO.getSkuId()).append("toB供货价").append(oldProductSku.getToBPrice().stripTrailingZeros().toPlainString()).append("修改为").append(ecpProductSkuPO.getToBPrice().stripTrailingZeros().toPlainString()).append(";");
                }
            }else if (Objects.nonNull(ecpProductSkuPO.getToBPrice())) {
                changeLog.append(ecpProductSkuPO.getSkuId()).append("toB供货价修改为").append(ecpProductSkuPO.getToBPrice().stripTrailingZeros().toPlainString()).append(";");
            }else if (Objects.nonNull(oldProductSku.getToBPrice())) {
                changeLog.append(ecpProductSkuPO.getSkuId()).append("toB供货价修改为空;");
            }
            if (Objects.nonNull(ecpProductSkuPO.getToCPrice()) && Objects.nonNull(oldProductSku.getToCPrice()) ) {
                if (ecpProductSkuPO.getToCPrice().compareTo(oldProductSku.getToCPrice()) != 0) {
                    changeLog.append(ecpProductSkuPO.getSkuId()).append("toC供货价").append(oldProductSku.getToCPrice().stripTrailingZeros().toPlainString()).append("修改为").append(ecpProductSkuPO.getToCPrice().stripTrailingZeros().toPlainString()).append(";");
                }
            }else if (Objects.nonNull(ecpProductSkuPO.getToCPrice())) {
                changeLog.append(ecpProductSkuPO.getSkuId()).append("toC供货价修改为").append(ecpProductSkuPO.getToCPrice().stripTrailingZeros().toPlainString()).append(";");
            }else if (Objects.nonNull(oldProductSku.getToCPrice())) {
                changeLog.append(ecpProductSkuPO.getSkuId()).append("toC供货价修改为空;");
            }
        }
    }

    private void handlerProductSpecChangeLog(EcpProductEditDTO editDTO, StringBuilder changeLog, List<EcpSpecSpuPO> delList) {
        Set<Long> specKeySet = new HashSet<>();
        Set<Long> specValueSet = new HashSet<>();
        for (EcpSpecSpuPO ecpSpecSpuPO : delList) {
            specKeySet.add(ecpSpecSpuPO.getSpecKeyId());
            specValueSet.add(ecpSpecSpuPO.getSpecValueId());
        }
        for (ProductUpdateSpecReq ecpSpecSpuPO : editDTO.getSpecList()) {
            specKeySet.add(ecpSpecSpuPO.getSpecKeyId());
            specValueSet.add(ecpSpecSpuPO.getSpecValueId());
        }
        if (CollectionUtils.isEmpty(specKeySet)) {
            return;
        }
        List<EcpSpecKeyPO> specKeyList = specKeyRepo.lambdaQuery().in(EcpSpecKeyPO::getId, specKeySet).list();
        Map<Long, EcpSpecKeyPO> specKeyMap = specKeyList.stream().collect(toMap(EcpSpecKeyPO::getId, Function.identity()));
        List<EcpSpecValuePO> specValueList = specValueRepo.lambdaQuery().in(EcpSpecValuePO::getId, specValueSet).list();
        Map<Long, EcpSpecValuePO> specValueMap = specValueList.stream().collect(toMap(EcpSpecValuePO::getId, Function.identity()));
        if (CollectionUtils.isNotEmpty(delList)) {
            for (EcpSpecSpuPO ecpSpecSpuPO : delList) {
                EcpSpecKeyPO ecpSpecKeyPO = specKeyMap.get(ecpSpecSpuPO.getSpecKeyId());
                if (Objects.isNull(ecpSpecKeyPO)) {
                    continue;
                }
                EcpSpecValuePO ecpSpecValuePO = specValueMap.get(ecpSpecSpuPO.getSpecKeyId());
                if (Objects.isNull(ecpSpecValuePO)) {
                    continue;
                }
                changeLog.append("删除").append(ecpSpecKeyPO.getSpecType().getDesc()).append("-").append(ecpSpecKeyPO.getSpecName()).append("-").append(ecpSpecValuePO.getSpecValue()).append(";");
            }
        }
        if(CollectionUtils.isNotEmpty(editDTO.getSpecList())){
            for (ProductUpdateSpecReq productUpdateSpecReq : editDTO.getSpecList()) {
                EcpSpecKeyPO ecpSpecKeyPO = specKeyMap.get(productUpdateSpecReq.getSpecKeyId());
                if (Objects.isNull(ecpSpecKeyPO)) {
                    continue;
                }
                EcpSpecValuePO ecpSpecValuePO = specValueMap.get(productUpdateSpecReq.getSpecValueId());
                if (Objects.isNull(ecpSpecValuePO)) {
                    continue;
                }
                if (Objects.isNull(productUpdateSpecReq.getId())) {
                    changeLog.append("新增").append(ecpSpecKeyPO.getSpecType().getDesc()).append("/").append(ecpSpecKeyPO.getSpecName()).append("/").append(ecpSpecValuePO.getSpecValue()).append("/").append(ecpSpecValuePO.getValueUnit()).append(";");
                }else {
                    EcpSpecSpuPO existsSpecSpu = specSpuRepo.getById(productUpdateSpecReq.getId());
                    EcpSpecKeyPO existsecpSpecKeyPO = specKeyMap.get(existsSpecSpu.getSpecKeyId());
                    if (Objects.isNull(existsecpSpecKeyPO)) {
                        continue;
                    }
                    EcpSpecValuePO existsecpSpecValuePO = specValueMap.get(existsSpecSpu.getSpecValueId());
                    if (Objects.isNull(existsecpSpecValuePO)) {
                        continue;
                    }
                    if (!Objects.equals(productUpdateSpecReq.getSpecKeyId(),existsSpecSpu.getSpecKeyId()) || !Objects.equals(productUpdateSpecReq.getSpecValueId(),existsSpecSpu.getSpecValueId())) {
                        changeLog.append(existsecpSpecKeyPO.getSpecType().getDesc()).append("/").append(existsecpSpecKeyPO.getSpecName()).append("/").append(existsecpSpecValuePO.getSpecValue()).append("/").append(ecpSpecValuePO.getValueUnit())
                                .append("修改为").append(ecpSpecKeyPO.getSpecType().getDesc()).append("/").append(ecpSpecKeyPO.getSpecName()).append("/").append(ecpSpecValuePO.getSpecValue()).append("/").append(ecpSpecValuePO.getValueUnit()).append(";");
                    }

                }
            }

        }
    }

    private void handlerProductSkuChangeLog(StringBuilder changeLog, List<EcpProductSkuPO> old, List<EcpProductSkuPO> nOne) {
        String addSku = nOne.stream().filter(e -> old.stream().noneMatch(t -> Objects.equals(e.getId(), t.getId()))).map(EcpProductSkuPO::getSkuId).collect(Collectors.joining(","));
        String removeSku = old.stream().filter(e -> nOne.stream().noneMatch(t -> Objects.equals(e.getId(), t.getId()))).map(EcpProductSkuPO::getSkuId).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(addSku)) {
            changeLog.append("新增").append(addSku).append("规格;");
        }
        if (StringUtils.isNotBlank(removeSku)) {
            changeLog.append("删除").append(removeSku).append("规格;");
        }
    }

    private void handlerProductChangeLog(EcpProductEditDTO editDTO, EcpProductPO byId, StringBuilder changeLog, EcpProductPO ecpProductPO) {
        if (!Objects.equals(editDTO.getCategory3Code(),(byId.getCategory3Code()))) {
            changeLog.append("分类由").append(byId.getCategoryPath()).append("修改为").append(ecpProductPO.getCategoryPath()).append(";");
        }
        if (!Objects.equals(editDTO.getProductType(),(byId.getProductType()))) {
            List<DictionaryItemVO> dicProductTypeList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.WMS_PRODUCT_TYPE);
            Map<String, String> productTypeMap = dicProductTypeList.stream().collect(toMap(e -> e.getItemCode().trim(), e -> e.getItemName().trim(), (c, n) -> n));
            changeLog.append("货品类型由").append(productTypeMap.getOrDefault(byId.getProductType().toString(),"")).append("修改为").append(productTypeMap.getOrDefault(editDTO.getProductType().toString(),"")).append(";");
        }
        if (editDTO.getInTax().compareTo(byId.getInTax()) != 0) {
            changeLog.append("进项税率由").append(byId.getInTax()).append("修改为").append(editDTO.getInTax()).append(";");
        }
        if (editDTO.getOutTax().compareTo(byId.getOutTax()) != 0) {
            changeLog.append("销项税率由").append(byId.getOutTax()).append("修改为").append(editDTO.getOutTax()).append(";");
        }
        if (!Objects.equals(editDTO.getBrandId(),(byId.getBrandId()))) {
            List<DictionaryItemVO> brandList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.WMS_BRAND);
            Map<Long, String> brandMap = brandList.stream().collect(toMap(e -> Long.valueOf(e.getItemCode()), e -> e.getItemName().trim(), (c, n) -> n));
            changeLog.append("品牌由").append(brandMap.getOrDefault(byId.getBrandId(),"")).append("修改为").append(brandMap.getOrDefault(editDTO.getBrandId(),"")).append(";");
        }
        if (!Objects.equals(editDTO.getInsteadSend(),(byId.getInsteadSend()))) {
            changeLog.append("发货方式由").append(InsteadSendEnum.getDesc(byId.getInsteadSend())).append("修改为").append(InsteadSendEnum.getDesc(editDTO.getInsteadSend())).append(";");
        }
        if (!Objects.equals(editDTO.getProductName(),(byId.getProductName()))) {
            changeLog.append("货品名称由").append(byId.getProductName()).append("修改为").append(editDTO.getProductName()).append(";");
        }
        if (!Objects.equals(editDTO.getProductNo(),(byId.getProductNo()))) {
            changeLog.append("物料编码由").append(byId.getProductNo()).append("修改为").append(editDTO.getProductNo()).append(";");
        }
    }

    //发送货品/规格下架通知
    public void sendProDownNotice(List<ErpProductDownVO> erpProductDownVOS){
        /*for(ErpProductDownVO erpProductDownVO:erpProductDownVOS){
            //发送mq消息
            rabbitTemplate.convertAndSend(MqConstants.ERP_PRODUCT_DOWN_MARGIN_EXCHANGE_NAME, null, JSONObject.toJSONString(erpProductDownVO));
        }*/

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeStatus(BatchProductIds spuId, ProductStatusEnum offShelves) {
        if(CollectionUtils.isEmpty(spuId.getProductIds())){
            return;
        }
        //循环校验权限
        List<EcpProductPO> productList = productRepo.listByIds(spuId.getProductIds());
        if(CollectionUtils.isEmpty(productList)){
            return;
        }
        for (EcpProductPO productPO : productList) {
            tenantUtil.validateTenantId(productPO.getCreateTenantId());
        }

        ProductLogOperateType operateType = ProductLogOperateType.OFF_SHELVES;
        if(offShelves.equals(ProductStatusEnum.ON)){
            ProductSkuFinanceCheckVO productSkuFinanceCheckVO = productSkuService.isAllSettingFinanceFlag(spuId);
            if (Objects.nonNull(productSkuFinanceCheckVO) &&  !productSkuFinanceCheckVO.getIsAllSetting()) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), productSkuFinanceCheckVO.getErrorMsg());
            }

            // 收集货品类型为套装的productId
            List<Long> suitProductIdList = productList.stream()
                    .filter(item -> Objects.equals(item.getProductType(), ProductTypeEnum.SUIT_GOODS.getValue()))
                    .map(EcpProductPO::getId)
                    .distinct()
                    .collect(Collectors.toList());
            List<EcpProductSupplierPO> productSupplierList = productSupplierRepo.getSupplierMapBySpuId(productList.stream().map(EcpProductPO::getSpuId).collect(Collectors.toList()));
            if(CollectionUtils.isEmpty(productSupplierList)){
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "物料供应商不存在，请绑定供应商！");
            }
            Map<String, String> productSupplierMap = productSupplierList.stream().collect(toMap(EcpProductSupplierPO::getSpuId, EcpProductSupplierPO::getSupplierCode,(o, n)->n));
            List<SupplierPO> supplierList = supplierRepo.getSupplierBatch(productSupplierList.stream().map(EcpProductSupplierPO::getSupplierCode).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(supplierList)) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "供应商不存在或已禁用！");
            }
            Map<String, String> supplierMap = supplierList.stream().collect(toMap(SupplierPO::getSupplierCode, SupplierPO::getSupplierName));
            productList.forEach(item->{
                String supplierCode = productSupplierMap.get(item.getSpuId());
                if(StringUtils.isBlank(supplierCode)){
                    throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), item.getProductName()+"物料供应商不存在，请绑定供应商！");
                }
                String supplier = supplierMap.get(supplierCode);
                if (Objects.isNull(supplier)) {
                    throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), item.getProductName()+"供应商不存在或已禁用！");
                }
            });
            operateType = ProductLogOperateType.ON_SHELVES;
            //规格修改
            List<EcpProductSkuPO> productSkuPOList = productSkuRepo.getProSkuByProductIds(productList.stream().map(EcpProductPO::getId).collect(Collectors.toList()));

            if(CollectionUtils.isNotEmpty(productSkuPOList)){
                Optional<EcpProductPO> proFlag = productList.stream().filter(item ->
                        productSkuPOList.stream().noneMatch(temp ->
                                Objects.equals(item.getId(), temp.getProductId())&&
                                Objects.equals(temp.getState(),ProductStatusEnum.ON.getKey()))).findFirst();
                if(proFlag.isPresent()){
                    throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), proFlag.get().getProductName()+"货品上架规格列表不能为空或不能全部禁用！");
                }
                List<EcpProductSitePO> proSiteList = productSiteRepo.findSiteBySkuIds(productSkuPOList.stream().map(EcpProductSkuPO::getSkuId).collect(Collectors.toList()));
                Map<String, EcpProductSitePO> proSiteMap = proSiteList.stream().collect(toMap(EcpProductSitePO::getSkuId, Function.identity(), (o, n) -> n));
                productSkuPOList.forEach(item->{
                    if (Objects.equals(item.getState(),ProductStatusEnum.ON.getKey())){
                        item.setIsOpen(CommonEnum.YES.getCode());
                    }
                    if(!ObjectUtil.contains(suitProductIdList, item.getProductId()) && Objects.isNull(proSiteMap.get(item.getSkuId()))){
                        throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), item.getSkuId()+"规格上架发货站不能为空！");
                    }/*else if(ObjectUtil.contains(suitProductIdList, item.getProductId())
                            && Objects.isNull(proSiteMap.get(item.getSkuId())) && Objects.isNull(productDepotMap.get(item.getSkuId()))){
                        throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), item.getSkuId()+"规格上架发货站跟前置仓必须关联一个！");
                    }*/
                });
                productSkuRepo.updateBatchById(productSkuPOList);
                //修改全部销售单位
                productSkuUnitRepo.batchUpdateBySkuId(productSkuPOList.stream().map(EcpProductSkuPO::getSkuId).collect(Collectors.toList())
                        ,CommonEnum.YES.getCode());
            }
        }
        if(offShelves.equals(ProductStatusEnum.DEL)){
            operateType = ProductLogOperateType.DELETE;
        }
        productRepo.updateByIds(spuId.getProductIds(),offShelves.getKey());


        for(Long productId: spuId.getProductIds()){
            // 添加变更轨迹
            productChangeLogService.addLog(productId, operateType, IProductChangeLogService.SUCCESS,
                    IProductChangeLogService.SUCCESS, tenantUtil.getUserName(), spuId.getChannel());
        }
        //发送货品下架通知
        if(!offShelves.equals(ProductStatusEnum.ON)){
            Date now=new Date();
            List<ErpProductDownVO> proDownList = productList.stream().map(item -> {
                ErpProductDownVO vo = new ErpProductDownVO();
                vo.setEventTime(now);
                vo.setProductNo(item.getProductNo());
                vo.setEventType(1);
                return vo;
            }).collect(Collectors.toList());
            this.sendProDownNotice(proDownList);
        }
    }
    public Map<String, EcpProductSitePO> getSkuSite(List<String> skuIdList){
        List<EcpProductSitePO> proSiteList = productSiteRepo.findSiteBySkuIds(skuIdList);
        if(CollectionUtils.isEmpty(proSiteList)){
            return new HashMap<>();
        }
        return proSiteList.stream().collect(Collectors.toMap(EcpProductSitePO::getSkuId, Function.identity(), (o, n) -> n));
    }
    public Map<String, EcpProductDepotPO> getSkuDepot(List<String> skuIdList){
        List<EcpProductDepotPO> productDepotPOList = productDepotRepo.getProDepotList(skuIdList);
        if(CollectionUtils.isEmpty(productDepotPOList)){
            return new HashMap<>();
        }
        return productDepotPOList.stream().collect(Collectors.toMap(EcpProductDepotPO::getSkuId, Function.identity(), (o, n) -> n));
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchImportProduct(List<ProductImportDTO> reqList){
        ErpTenantPO tenant = getTenantInfo();
        if(basicsConfig.getTenantIdList().contains(tenant.getTenantId())){
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "当前租户暂不支持导入货品！");
        }
        //获取供应商列表
        List<String> supplierCodeList=reqList.stream().map(ProductImportDTO::getSupplierCode).collect(Collectors.toList());
        List<SupplierPO> supplierList =supplierRepo.getSupplierBatch(supplierCodeList);
        if(CollectionUtils.isEmpty(supplierList)){
            throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "供应商编码不存在！");
        }
        Map<String, String> supplierMap = supplierList.stream().collect(toMap(SupplierPO::getSupplierCode, SupplierPO::getSupplierName, (c, n) -> n));
        List<ProductImportDTO> materialList = reqList.stream().filter(item -> StringUtils.isNotEmpty(item.getSkuMaterialCode())).collect(Collectors.toList());
        //物料编码校验，货品名称不同，物料编码相同场景校验
        if(CollectionUtils.isNotEmpty(materialList)){
            List<String> materialCodeList = reqList.stream().map(temp->ParamUtil.getWmsCode(temp.getSkuMaterialCode())).collect(Collectors.toList());
            //对导入的物料编码，进行重复值过滤校验
            Map<String, List<ProductImportDTO>> materialGroup = materialList.stream().collect(Collectors.groupingBy(ProductImportDTO::getSkuMaterialCode));
            for(Map.Entry<String, List<ProductImportDTO>> entry:materialGroup.entrySet()){
                long duplicateNum = entry.getValue().stream().map(pro -> String.format("%s:%s:%s", pro.getProductName(),
                        pro.getCategory3Code(), pro.getSupplierCode())).distinct().count();
                if(duplicateNum>1){
                    throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(),
                            entry.getKey()+ "物料编号存在重复！");
                }
            }
            if (productRepo.ifProductNo(materialCodeList) > 0) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(),
                        materialCodeList.stream().map(String::valueOf).collect(Collectors.joining(",")) + "物料编号已存在！");
            }
        }
        //三级分类列表
        List<String> category3CodeList = reqList.stream().map(ProductImportDTO::getCategory3Code).collect(Collectors.toList());
        ErpTenantPO parentTenant = tenantUtil.subToParentCompany();
        List<EcCategoryPO> categoryList = categoryRepo.lambdaQuery().in(EcCategoryPO::getCategoryCode,category3CodeList).eq(EcCategoryPO::getCategoryLevel,parentTenant.getHighestCategoryLevel()-1).list();
        if(CollectionUtils.isEmpty(categoryList)){
           throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "末级分类编码不存在！");
        }
        Map<String, EcCategoryPO> category3Map = categoryList.stream().collect(toMap(EcCategoryPO::getCategoryCode, Function.identity(), (c, n) -> n));
        Map<String, CategoryCompositionDTO> categoryCompositionDTOMap = new HashMap<>();
        for (EcCategoryPO ecCategoryPO : categoryList) {
            CategoryCompositionDTO categoryCompositionDTO = new CategoryCompositionDTO();
            categoryCompositionDTO.getCategoryPath().append(ecCategoryPO.getCategoryName());
            categoryService.generateCategoryPath(ecCategoryPO.getParentId(), categoryCompositionDTO);
            categoryCompositionDTOMap.put(ecCategoryPO.getCategoryCode(),categoryCompositionDTO);
        }
        //二级分类列表
//        List<Long> parentIdList=categoryList.stream().map(EcCategoryPO::getParentId).collect(Collectors.toList());
//        List<EcCategoryPO> category2List = categoryRepo.getCategoryList(parentIdList);
//        if(CollectionUtils.isEmpty(category2List)){
//            throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "所属二级分类编码不存在！");
//        }
//        Map<Long, EcCategoryPO> category2Map = category2List.stream().collect(Collectors.toMap(EcCategoryPO::getId, Function.identity(), (c, n) -> n));
//        //一级分类列表
//        parentIdList=category2List.stream().map(EcCategoryPO::getParentId).collect(Collectors.toList());
//        List<EcCategoryPO> category1List = categoryRepo.getCategoryList(parentIdList);
//        if(CollectionUtils.isEmpty(category1List)){
//            throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "所属一级分类编码不存在！");
//        }
//        Map<Long, EcCategoryPO> category1Map = category1List.stream().collect(Collectors.toMap(EcCategoryPO::getId, Function.identity(), (c, n) -> n));
        //分类-规格属性
        List<String> specValueList = reqList.stream().map(ProductImportDTO::getWeight).map(String::valueOf).collect(Collectors.toList());
        reqList.forEach(req->{
            if(StringUtils.isNotEmpty(req.getNameSpec())){
                specValueList.addAll(Arrays.asList(req.getNameSpec().split(",")));
            }
            if(StringUtils.isNotEmpty(req.getOtherSpec())){
                specValueList.addAll(Arrays.asList(req.getOtherSpec().split(",")));
            }
            if(StringUtils.isNotEmpty(req.getCodeSpec())){
                String[] codes = req.getCodeSpec().split(",");
                specValueList.addAll(Arrays.stream(codes).map(item -> {
                    String[] temp = item.split(":");
                    return temp[0];
                }).collect(Collectors.toList()));
            }
        });
        List<CategorySpecValueDTO> specValuePoList = specValueRepo.getSpecValueList(categoryList.stream().map(EcCategoryPO::getId).collect(Collectors.toList()), specValueList);
        if(CollectionUtils.isEmpty(specValuePoList)){
            throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "规格值不存在！");
        }
        Map<String, List<CategorySpecValueDTO>> specValueMap = specValuePoList.stream().collect(Collectors.groupingBy(key-> String.format("%s:%s:%s:%s",key.getCategoryId(),key.getSpecType(), key.getSpecValue(),key.getValueUnit())));
        //库存单位字典
        List<DictionaryItemVO> dicList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.WMS_UNIT_NAME);
        Map<String, DictionaryItemVO> unitMap = dicList.stream().collect(toMap(DictionaryItemVO::getItemName, Function.identity(), (c, n) -> n));
        //品牌字典
        List<DictionaryItemVO> dicBrandList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.WMS_BRAND);
        Map<String, DictionaryItemVO> brandMap = dicBrandList.stream().collect(toMap(DictionaryItemVO::getItemName, Function.identity(), (c, n) -> n));
        //货品类型字典
        List<DictionaryItemVO> dicProductTypeList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.WMS_PRODUCT_TYPE);
        Map<String, DictionaryItemVO> productTypeMap = dicProductTypeList.stream().collect(toMap(key->key.getItemName().trim(), Function.identity(), (c, n) -> n));
        //货品新增
        List<AddProductDTO> addProductList=new ArrayList<>();
        Map<String,String> map=new HashMap<>();
        //product_supplier新增
        List<EcpProductSupplierPO> productSupplierPOList=new ArrayList<>();
        //product_sku_unit新增
        List<EcpProductSkuUnitPO> productSkuUnitPOList=new ArrayList<>();
        //ecp_spec_spu新增
        List<EcpSpecSpuPO> specSpuPOList=new ArrayList<>();
        //product_site新增
        Map<String,List<String>> skuBySupCode=new HashMap<>();
        //spu名称唯一判断以供应商维度，不同供应商spu名称可以相同
        List<ProductSupplierVO> productNameList = productSkuRepo.getProductNameList(reqList.stream().map(ProductImportDTO::getSupplierCode).collect(Collectors.toList()),null);
        Map<String, List<ProductSupplierVO>> productNameMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(productNameList)){
            productNameMap = productNameList.stream().collect(Collectors.groupingBy(ProductSupplierVO::getSupplierCode));
        }
        Map<String, List<ProductSupplierVO>> finalProductNameMap = productNameMap;
        distributeLock.lockAndProcess(CommonConstants.PRODUCT_IMPORT_REDIS_LOCK, 2, -1, TimeUnit.SECONDS, () -> {
            int index=0;
            for (ProductImportDTO pro : reqList) {
                index++;
                //供应商编码验证
                if (StringUtils.isEmpty(supplierMap.get(pro.getSupplierCode()))) {
                    throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "第"+index+"行,供应商编码:" + pro.getSupplierCode() + "不存在！");
                }
                //三级分类编码验证
                EcCategoryPO category3Po = category3Map.get(pro.getCategory3Code());
                if (Objects.isNull(category3Po)) {
                    throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "第"+index+"行,末级分类编码:" + pro.getCategory3Code() + "不存在！");
                }
                if (Objects.equals(0, category3Po.getCategoryStatus())) {
                    throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "第"+index+"行,末级分类编码:" + pro.getCategory3Code() + "已停用！");
                }
                //规格值验证
                List<CategorySpecValueDTO> specValueDtoList = specValueMap.get(category3Po.getId() + ":" + SpecTypeEnum.SPEC.getValue() + ":" + pro.getWeight() + ":" + pro.getWeightUnit());
                if (CollectionUtils.isEmpty(specValueDtoList)) {
                    throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "第"+index+"行,规格属性规格值:" + pro.getWeight() + "不存在！");
                }
                List<CategorySpecValueDTO> specList = new ArrayList<>(specValueDtoList);
                //名称验证
                if (StringUtils.isNotEmpty(pro.getNameSpec())) {
                    String[] name = pro.getNameSpec().split(",");
                    for (String str : name) {
                        List<CategorySpecValueDTO> specName = specValueMap.get(category3Po.getId() + ":" + SpecTypeEnum.NAME.getValue() + ":" + str + ":");
                        if (CollectionUtils.isEmpty(specName)) {
                            throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "第"+index+"行,名称属性规格值:" + str + "不存在！");
                        }
                        specList.addAll(specName);
                    }
                }
                //其他属性验证
                if (StringUtils.isNotEmpty(pro.getOtherSpec())) {
                    String[] other = pro.getOtherSpec().split(",");
                    for (String str : other) {
                        List<CategorySpecValueDTO> specOther = specValueMap.get(category3Po.getId() + ":" + SpecTypeEnum.OTHER.getValue() + ":" + str + ":");
                        if (CollectionUtils.isEmpty(specOther)) {
                            throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "第"+index+"行,其他属性规格值:" + str + "不存在！");
                        }
                        specList.addAll(specOther);
                    }
                }
                //编码属性验证
                if (StringUtils.isNotEmpty(pro.getCodeSpec())) {
                    String[] code = pro.getCodeSpec().split(",");
                    for (String str : code) {
                        List<CategorySpecValueDTO> specCode = specValueMap.get(category3Po.getId() + ":" + SpecTypeEnum.CODE.getValue() + ":" + str);
                        if (CollectionUtils.isEmpty(specCode)) {
                            throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "第"+index+"行,编码属性规格值:" + str + "不存在！");
                        }
                        specList.addAll(specCode);
                    }
                }
                //库存单位
                DictionaryItemVO unit = unitMap.get(pro.getUnitName());
                if (Objects.isNull(unit)) {
                    throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "第"+index+"行,库存单位:" + pro.getUnitName() + "不存在！");
                }
                //品牌验证
                DictionaryItemVO brand = brandMap.get(pro.getBrandName());
                if (Objects.isNull(brand)) {
                    throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "第"+index+"行,品牌:" + pro.getBrandName() + "不存在！");
                }
                //货品类型验证
                DictionaryItemVO productType=productTypeMap.get(pro.getProductType());
                if (Objects.isNull(productType)) {
                    throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "第"+index+"行,货品类型:" + pro.getProductType() + "不存在！");
                }
                //是否需要验证字典状态
                //发货方式
                InsteadSendEnum insteadSend = InsteadSendEnum.descOf(pro.getInsteadSendName());
                if (Objects.isNull(insteadSend)) {
                    throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "第"+index+"行,发货方式:" + pro.getInsteadSendName() + "不存在！");
                }
                String key = String.format("%s:%s:%s",pro.getProductName(), pro.getCategory3Code(), pro.getSupplierCode());
                String spuId = map.get(key);
                if (StringUtils.isEmpty(spuId)) {
                    List<ProductSupplierVO> productNames = finalProductNameMap.get(pro.getSupplierCode());
                    if(CollectionUtils.isNotEmpty(productNames)){
                        Optional<ProductSupplierVO> isExistName = productNames.stream().filter(p -> pro.getProductName().equals(p.getProductName())).findFirst();
                        if(isExistName.isPresent()){
                            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), pro.getSupplierCode()+"供应商编码下已存在"+pro.getProductName()+"名称！");
                        }
                        //不存在的货品名称，放入list
                        ProductSupplierVO productSupplierVO=new ProductSupplierVO();
                        productSupplierVO.setProductName(pro.getProductName());
                        productSupplierVO.setSupplierCode(pro.getSupplierCode());
                        productNames.add(productSupplierVO);
                        finalProductNameMap.put(pro.getSupplierCode(),productNames);
                    }
                    AddProductDTO addProductDTO = new AddProductDTO();
                    EcpProductPO productPO = new EcpProductPO();
                    //货品新增
                    if(StringUtils.isEmpty(pro.getSkuMaterialCode())){
                        //自动生成物料编码
                        productPO.setProductNo(smartIdHelper.generatorMaterialCode(SmartIdTypeEnum.PRODUCT_MATERIAL_CODE));
                    }else{
                        //货品物料编码自动生成
                        productPO.setProductNo(ParamUtil.getWmsCode(pro.getSkuMaterialCode()));
                    }
                    productPO.setSpuId(smartIdHelper.generator(SmartIdTypeEnum.PRODUCT_CODE));
                    //新增map-spuId
                    map.put(key, productPO.getSpuId());
                    productPO.setProductStatus(ProductStatusEnum.OFF.getKey());
                    productPO.setProductName(pro.getProductName());
                    productPO.setInsteadSend(insteadSend.getValue());
                    //组织名称
                    productPO.setCompanyName(tenant.getCompanyName());
                    //进项税率
                    productPO.setInTax(pro.getInTax());
                    //销项税率
                    productPO.setOutTax(pro.getOutTax());
                    productPO.setCategory3Code(category3Po.getCategoryCode());
                    //二级分类
//                    EcCategoryPO category2Po = category2Map.get(category3Po.getParentId());
//                    productPO.setCategory2Code(category2Po.getCategoryCode());
                    //一级分类
                    CategoryCompositionDTO categoryCompositionDTO = categoryCompositionDTOMap.get(category3Po.getCategoryCode());
                    if (Objects.isNull(categoryCompositionDTO)) {
                        throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.code(), "第"+index+"行,获取末级编码分类路径失败:" + pro.getCategory3Code() );
                    }
                    //EcCategoryPO category1Po = category1Map.get(category2Po.getParentId());
                    productPO.setCategory1Code(categoryCompositionDTO.getParentCode());
                    productPO.setCategory2Code(categoryCompositionDTO.getSecondCode());
                    productPO.setCategoryPath(categoryCompositionDTO.getCategoryPath().toString());
                    productPO.setBrandId(Long.valueOf(brand.getItemCode()));
                    productPO.setProductType(Integer.valueOf(productType.getItemCode()));
                    productPO.setCreateTenantId(tenant.getTenantId());
                    addProductDTO.setProductPO(productPO);
                    addProductList.add(addProductDTO);
                    //product_supplier新增
                    EcpProductSupplierPO productSupplierPO = new EcpProductSupplierPO();
                    productSupplierPO.setSupplierCode(pro.getSupplierCode());
                    productSupplierPO.setSpuId(productPO.getSpuId());
                    productSupplierPOList.add(productSupplierPO);
                }
                //ecp_spec_spu去除已存在的
                String finalSpuId = map.get(key);
                specList=specList.stream().filter(item->specSpuPOList.stream().noneMatch(t->t.getSpuId().equals(finalSpuId)&&
                        Objects.equals(item.getSpecId(),t.getSpecKeyId())&&Objects.equals(item.getSpecValueId(),t.getSpecValueId()))).collect(Collectors.toList());
                //ecp_spec_spu新增
                specList.forEach(spec -> {
                    if(!Objects.equals(spec.getSpecType(),SpecTypeEnum.SPEC.getValue())){
                        EcpSpecSpuPO specSpuPO = new EcpSpecSpuPO();
                        specSpuPO.setSpuId(finalSpuId);
                        specSpuPO.setSpecKeyId(spec.getSpecId());
                        specSpuPO.setSpecValueId(spec.getSpecValueId());
                        specSpuPOList.add(specSpuPO);
                    }
                });
                addProductList.forEach(item -> {
                    if (item.getProductPO().getSpuId().equals(finalSpuId)) {
                        List<EcpProductSkuPO> list = Optional.ofNullable(item.getProductSkuPOList()).orElse(new ArrayList<>());
                        //product_sku新增-缺少productId
                        String skuKey = key+":"+pro.getWeight() + pro.getWeightUnit() + "/" + pro.getUnitName();
                        String sku = map.get(skuKey);
                        if (StringUtils.isEmpty(sku)) {
                            EcpProductSkuPO productSkuPO = new EcpProductSkuPO();
                            productSkuPO.setSkuName(pro.getWeight() + pro.getWeightUnit() + "/" + pro.getUnitName());
                            productSkuPO.setSkuId(smartIdHelper.generator(SmartIdTypeEnum.PRODUCT_SKU_CODE));
                            List<String> skuIds = skuBySupCode.get(pro.getSupplierCode());
                            if(CollectionUtils.isNotEmpty(skuIds)){
                                skuIds.add(productSkuPO.getSkuId());
                            }else{
                                skuIds=new ArrayList<>();
                                skuIds.add(productSkuPO.getSkuId());
                            }
                            skuBySupCode.put(pro.getSupplierCode(),skuIds);
                            productSkuPO.setErpNo(item.getProductPO().getProductNo());
                            map.put(skuKey, productSkuPO.getSkuId());
                            productSkuPO.setIsMain(list.size() > 0 ? 0 : 1);
                            productSkuPO.setSellPrice(pro.getSellPrice());
                            productSkuPO.setState(ProductStatusEnum.ON.getKey());//默认上架
                            productSkuPO.setLandingPrice(pro.getLandingPrice());
                            productSkuPO.setCostPrice(pro.getCostPrice());
                            productSkuPO.setWeight(pro.getWeight());
                            productSkuPO.setUnit(pro.getWeightUnit());
                            list.add(productSkuPO);
                            item.setProductSkuPOList(list);
                            //product_sku_unit新增
                            BigDecimal flagWeight = BigDecimal.ZERO;
                            for (EcpProductSkuUnitPO skuUnit : skuUnitBuild()) {
                                if (Objects.equals(skuUnit.getUnitType(),SkuUnitTypeEnum.STOCK.getValue())) {
                                    skuUnit.setUnitCode(unit.getItemCode());
                                    skuUnit.setUnitName(unit.getItemName());
                                    checkUnitWeight(productSkuPO.getWeight(),productSkuPO.getUnit(), skuUnit, unit);
                                    skuUnit.setWeight(productSkuPO.getWeight().multiply(BigDecimal.valueOf(UnitTypeEnum.getByUnit(productSkuPO.getUnit()).getWeightKG())));
                                    flagWeight = skuUnit.getWeight();
                                } else {
                                    skuUnit.setPackageNum(skuUnit.getWeight().divide(flagWeight, BigDecimal.ROUND_HALF_UP));
                                }
                                skuUnit.setSkuId(productSkuPO.getSkuId());
                                skuUnit.setSkuUnitCode(smartIdHelper.generator(SmartIdTypeEnum.SKU_UNIT_CODE));
                                productSkuUnitPOList.add(skuUnit);
                            }
                        }
                    }
                });
            }
            //product新增
            List<EcpProductPO> productPOList = addProductList.stream().map(AddProductDTO::getProductPO).collect(Collectors.toList());
            productRepo.saveBatch(productPOList);
            for (EcpProductPO ecpProductPO : productPOList) {
                productChangeLogService.addLog(ecpProductPO.getId(), ProductLogOperateType.CREATE, IProductChangeLogService.SUCCESS,
                        IProductChangeLogService.SUCCESS, tenantUtil.getUserName(), null);
            }
            // 收集货品类型为套装的productId
            List<Long> suitProductIdList = productPOList.stream()
                    .filter(item -> Objects.equals(item.getProductType(), ProductTypeEnum.SUIT_GOODS.getValue()))
                    .map(EcpProductPO::getId)
                    .distinct()
                    .collect(Collectors.toList());
            List<EcpProductSkuPO> productSkuPOList = new ArrayList<>();
            addProductList.forEach(pro -> {//product_sku补充productId值
                if(CollectionUtils.isNotEmpty(pro.getProductSkuPOList())){
                    productSkuPOList.addAll(pro.getProductSkuPOList().stream().peek(sku -> sku.setProductId(pro.getProductPO().getId())).collect(Collectors.toList()));
                }
            });
            // 收集无需默认选中供应商名下全部发货站的SkuId
            List<String> skuIds = Lists.newArrayList();
            if (CollectionUtil.isNotEmpty(suitProductIdList)){
                // 收集suitProductIdList对应的所有skuId
                skuIds = productSkuPOList.stream()
                        .filter(item -> suitProductIdList.contains(item.getProductId()))
                        .map(EcpProductSkuPO::getSkuId)
                        .distinct()
                        .collect(Collectors.toList());
            }
            productSkuRepo.saveBatch(productSkuPOList);
            //物流属性默认设置
            productSkuUnitRepo.saveBatch(productSkuUnitPOList);
            //product_supplier新增
            productSupplierRepo.saveBatch(productSupplierPOList);
            //默认选中供应商名下全部发货站
            List<EcpProductSitePO> pos = new ArrayList<>();
            List<SitePo> siteList = siteService.listBySupplierList(supplierCodeList);
            if (CollectionUtils.isNotEmpty(siteList)) {
                Map<String, List<SitePo>> siteMap = siteList.stream().collect(Collectors.groupingBy(SitePo::getSupplierCode));
                for (Map.Entry<String, List<String>> entry : skuBySupCode.entrySet()) {
                    List<SitePo> sitePoList = siteMap.get(entry.getKey());
                    if(CollectionUtils.isNotEmpty(sitePoList)&&CollectionUtils.isNotEmpty(entry.getValue())){
                        for (String skuId : entry.getValue()) {
                            if (skuIds.contains(skuId)){
                                continue;
                            }
                            // 判断货品类型为套装，则不默认设置发货站
                            for (SitePo site : sitePoList) {
                                EcpProductSitePO ecpProductSitePO = new EcpProductSitePO();
                                ecpProductSitePO.setSiteId(site.getId());
                                ecpProductSitePO.setSkuId(skuId);
                                pos.add(ecpProductSitePO);
                            }
                        }
                    }
                }
            }
            productSiteRepo.saveBatch(pos);
            //保存货品属性
            ecpSpecSpuRepo.saveBatch(specSpuPOList);
            return Boolean.TRUE;
        });
        return Boolean.TRUE;
    }

    @Override
    public List<ErpProductDetailVo> getProductDetailList(ErpProductDetailQuery productDetailQuery) {
        if (buildQuery(productDetailQuery)) {
            return Collections.emptyList();
        }
        List<ErpProductDetailVo> list = productRepo.getProductDetailList(productDetailQuery);
        if(CollectionUtils.isEmpty(list)){
            return list;
        }
        //品牌字典
        return handlerResult(list);
    }

    private List<ErpProductDetailVo> handlerResult(List<ErpProductDetailVo> list) {
        if (CollectionUtils.isNotEmpty(list)) {

        }
        List<String> supplierCodeList = list.stream().map(ErpProductDetailVo::getSupplierCode).collect(Collectors.toList());
        List<SupplierPO> supplierPOList = supplierRepo.getSupplierListV2(supplierCodeList);
        Map<String, SupplierPO> supplierMap = !CollectionUtils.isEmpty(supplierPOList) ? supplierPOList.stream().collect(toMap(SupplierPO::getSupplierCode, Function.identity())) : Collections.emptyMap();
        List<DictionaryItemVO> dicBrandList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.WMS_BRAND);
        Map<String, String> brandMap = dicBrandList.stream().collect(toMap(DictionaryItemVO::getItemCode, DictionaryItemVO::getItemName, (c, n) -> n));
        return list.stream().peek(item -> {
            item.setBrandName(brandMap.get(String.valueOf(item.getBrandId())));
            if (StringUtils.isNotEmpty(item.getSupplierCode()) && Objects.nonNull(supplierMap.get(item.getSupplierCode()))) {
                SupplierPO supplierPO = supplierMap.get(item.getSupplierCode());
                item.setSupplierName(StringUtils.isEmpty(supplierPO.getSupplierShortName()) ? supplierPO.getSupplierName() :supplierPO.getSupplierShortName());
            }

        }).collect(Collectors.toList());
    }

    private boolean buildQuery(ErpProductDetailQuery productDetailQuery) {
        List<String> spuIds=new ArrayList<>();
        if(CollectionUtils.isNotEmpty(productDetailQuery.getSpuIdList())){
            spuIds.addAll(productDetailQuery.getSpuIdList());
        }
        if (CollectionUtils.isNotEmpty(productDetailQuery.getSpecValueIdList())) {
            List<String> spuIdList = specSpuRepo.getSpuIdBySpecValueId(productDetailQuery.getSpecValueIdList());
            if(CollectionUtils.isEmpty(spuIdList)){
                return true;
            }
            spuIds.addAll(spuIdList);
        }
        if (Objects.nonNull(productDetailQuery.getWeightValueId())) {
            List<EcpSpecValuePO> specList = specValueRepo.getSpecValue(Collections.singletonList(productDetailQuery.getWeightValueId()));
            if(CollectionUtils.isNotEmpty(specList)){
                productDetailQuery.setSpecValueList(specList.stream().map(EcpSpecValuePO::getSpecValue).collect(Collectors.toList()));
                productDetailQuery.setValueUnitList(specList.stream().map(EcpSpecValuePO::getValueUnit).collect(Collectors.toList()));
            }
        }
        if (StringUtils.isNotBlank(productDetailQuery.getCategoryCode())) {
            List<CategoryVo> leafCategoryList = categoryService.getLeafCategory(Collections.singletonList(productDetailQuery.getCategoryCode()),null,null);
            if (CollectionUtils.isEmpty(leafCategoryList)) {
                return true;
            }
            productDetailQuery.setParentToLeafCodeList(leafCategoryList.stream().map(CategoryVo::getCategoryCode).collect(Collectors.toList()));
        }

        productDetailQuery.setSpuIdList(spuIds);
        return false;
    }

    /**
     * description: 获取货品信息
     *
     * @return List<ProductVO>
     * @Param: skuMaterialCodes
     */
    public List<ProductVO> getProductList(EcpProductQueryDTO productQueryDTO){
        List<EcpProductPO> productList=productRepo.getProductList(productQueryDTO);
        return ProductConvertor.CONVERTOR.poToListVo(productList);
    }

    @Override
    public List<ProductVO> getProductList(List<String> skuMaterialCodes) {
        if(CollectionUtils.isEmpty(skuMaterialCodes)){
            return new ArrayList<>();
        }
        List<EcpProductPO> productList=productRepo.getProductList(skuMaterialCodes);
        List<ProductVO> productVOList = ProductConvertor.CONVERTOR.poToListVo(productList);
        if (CollectionUtils.isNotEmpty(productVOList)) {
            List<EcCategoryPO> categoryPOList = categoryService.lambdaQuery().in(EcCategoryPO::getCategoryCode, productVOList.stream().map(ProductVO::getCategory3Code).collect(Collectors.toSet())).list();
            Map<String, EcCategoryPO> categoryPOMap = CollectionUtils.isEmpty(categoryPOList) ? Collections.emptyMap() : categoryPOList.stream().collect(toMap(EcCategoryPO::getCategoryCode, Function.identity()));
            for (ProductVO productVO : productVOList) {
                EcCategoryPO ecCategoryPO = categoryPOMap.get(productVO.getCategory3Code());
                if (Objects.nonNull(ecCategoryPO) && StringUtils.isNotBlank(ecCategoryPO.getCodeTree())) {
                    productVO.setCategoryCodePath(Stream.of(ecCategoryPO.getCodeTree().split("_")).map(e -> "WMS"+e).collect(Collectors.joining("/")));
                }
            }
        }
        return productVOList;
    }

    @Override
    @Transactional
    public void saveProductDraft(EcpProductDraftDTO draftDTO) {
        // 暂存判断无id走新增逻辑有id走修改逻辑
        if (Objects.isNull(draftDTO.getId())) {
            EcpProductAddDTO addDTO = new EcpProductAddDTO();
            BeanUtils.copyProperties(draftDTO,addDTO);
            List<EcpProductSkuAddDTO> addDTOS = new ArrayList<>();
            for (EcpProductSkuEditDTO editDTO : draftDTO.getAddDTOS()) {
                EcpProductSkuAddDTO ecpProductSkuEditDTO = new EcpProductSkuAddDTO();
                BeanUtils.copyProperties(editDTO,ecpProductSkuEditDTO);
                addDTOS.add(ecpProductSkuEditDTO);
            }
            addDTO.setAddDTOS(addDTOS);
            addDTO.setProductStatus(ProductStatusEnum.DRAFT.getKey());
            hanlderAddData(addDTO);
        } else {
            EcpProductEditDTO editDTO = new EcpProductEditDTO();
            BeanUtils.copyProperties(draftDTO,editDTO);
            editDTO.setProductStatus(ProductStatusEnum.DRAFT.getKey());
            EcpProductPO productPO = productRepo.getById(editDTO.getId());
            if(Objects.isNull(productPO)){
                return;
            }
            if (!Objects.equals(productPO.getProductStatus(),ProductStatusEnum.DRAFT.getKey())) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "非暂存状态货品不能暂存！");
            }
            handlerUpdateData(editDTO, productPO);
        }

    }

    @Override
    @Transactional
    public void syncProductCategory(String rootCategoryCode) {
        List<EcpProductPO> ecpProductPOList = productRepo.lambdaQuery().eq(EcpProductPO::getCategory1Code,rootCategoryCode).list();
        if (CollectionUtils.isEmpty(ecpProductPOList)) {
            return ;
        }
        //获取分类获取
        List<String> cateCodeList = ecpProductPOList.stream().map(EcpProductPO::getCategory3Code).distinct().collect(Collectors.toList());
        List<EcCategoryPO> categoryList = categoryRepo.getCategoryCodeList(cateCodeList);
        if (CollectionUtils.isEmpty(categoryList)) {
            return ;
        }
        Map<String, CategoryCompositionDTO> categoryCompositionDTOMap = new HashMap<>();
        for (EcCategoryPO ecCategoryPO : categoryList) {
            CategoryCompositionDTO categoryCompositionDTO = new CategoryCompositionDTO();
            categoryCompositionDTO.getCategoryPath().append(ecCategoryPO.getCategoryName());
            categoryService.generateCategoryPath(ecCategoryPO.getParentId(), categoryCompositionDTO);
            categoryCompositionDTOMap.put(ecCategoryPO.getCategoryCode(),categoryCompositionDTO);
        }
        ecpProductPOList.forEach( t -> {
            CategoryCompositionDTO categoryCompositionDTO = categoryCompositionDTOMap.get(t.getCategory3Code());
            if(Objects.nonNull(categoryCompositionDTO)){
                // 货品路径不同则同步
                if (!Objects.equals(categoryCompositionDTO.getCategoryPath().toString(),t.getCategoryPath())) {
                    LambdaUpdateWrapper<EcpProductPO> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(EcpProductPO::getId,t.getId());
                    updateWrapper.set(EcpProductPO::getCategoryPath,categoryCompositionDTO.getCategoryPath().toString());
                    productRepo.update(updateWrapper);
                }
            }
        });

    }

    @Override
    @Transactional
    public boolean cleanSkuSecurityNumber(Long tenantId) {
        if (productSkuRepo.updateSecurityNumberByTenantId(tenantId) > 0) {
            return true;
        }else {
            return false;
        }
    }

    @Override
    public Page<ErpProductDetailVo> getProductDetailPageList(ErpProductDetailQuery productDetailQuery) {
        if (buildQuery(productDetailQuery)) {
            return new Page<>();
        }
        Page<ErpProductDetailVo> page = productRepo.getProductDetailPageList(productDetailQuery);
        if (Objects.isNull(page)) {
            return new Page<>();
        }
        page.setRecords(handlerResult(page.getRecords()));
        return page;
    }

    @Override
    public ErpProductSpecImportVO specBatchImport(MultipartFile file) {
        ErpTenantPO tenant = getTenantInfo();
        if(basicsConfig.getTenantIdList().contains(tenant.getTenantId())){
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "当前租户暂不支持导入货品！");
        }
        EasyPoiUtil easyPoiUtil = new EasyPoiUtil();
        Set<ProductSpecImportDTO> productSpecImportDTOSet = easyPoiUtil.readAllDatas(file, ProductSpecImportDTO.class);
        if (CollectionUtils.isEmpty(productSpecImportDTOSet)) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "上传的文件无数据！");
        }
        if (productSpecImportDTOSet.size() > 500) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "一次最多上传500条数据！");
        }
        return distributeLock.lockAndProcess(CommonConstants.PRODUCT_SPEC_IMPORT_REDIS_LOCK, 2, -1, TimeUnit.SECONDS, () -> {
            List<EcpSpecSpuPO> specSpuPOList = getAllowSaveSpecData(productSpecImportDTOSet);
            //如果全部没有错误信息，则保存数据 不返回excel表格url
            ErpProductSpecImportVO erpProductSpecImportVO = new ErpProductSpecImportVO();
            if (productSpecImportDTOSet.stream().allMatch(e -> StringUtils.isBlank(e.getErrorMsg()))) {
                erpProductSpecImportVO.setSuccess(Boolean.TRUE);
                for (EcpSpecSpuPO ecpSpecSpuPO : specSpuPOList) {
                    EcpSpecSpuPO existsSpecSpu = specSpuRepo.lambdaQuery().eq(EcpSpecSpuPO::getSpecKeyId, ecpSpecSpuPO.getSpecKeyId()).eq(EcpSpecSpuPO::getSpuId, ecpSpecSpuPO.getSpuId()).one();
                    if (Objects.isNull(existsSpecSpu)) {
                        if (!specSpuRepo.save(ecpSpecSpuPO)) {
                            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "保存属性失败！");
                        }
                    }else {
                        ecpSpecSpuPO.setId(existsSpecSpu.getId());
                        if (!specSpuRepo.updateById(ecpSpecSpuPO)) {
                            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "修改属性失败！");
                        }
                    }
                }
            }else {
                erpProductSpecImportVO.setSuccess(Boolean.FALSE);
                try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()){
                    String fileName = "货品属性批量导入错误文件"+System.currentTimeMillis()+".xlsx";
                    String folderName = "erp";
                    EasyExcel.write(outputStream, ProductSpecImportDTO.class).sheet(fileName).doWrite(productSpecImportDTOSet);
                    AliYunOssUtil.uploadobject2Oss(fileName,new ByteArrayInputStream( outputStream.toByteArray()), folderName );
                    //返回阿里云下载路径
                    erpProductSpecImportVO.setUrl(String.format("%s/%s", folderName, fileName));
                    erpProductSpecImportVO.setUrl(AliYunOssUtil.generateUrl(folderName + "/" + fileName));
                } catch (Exception e) {
                    log.error("上传附件到OSS异常", e);
                    throw new BusinessException(ErpBizErrorEnum.FILE_UPLOAD_ERROR);
                }
            }
            return erpProductSpecImportVO;
        });

    }

    private List<EcpSpecSpuPO> getAllowSaveSpecData(Set<ProductSpecImportDTO> productSpecImportDTOSet) {
        List<String> materialCodeList = productSpecImportDTOSet.stream().map(ProductSpecExportDTO::getMaterialCode).distinct().collect(Collectors.toList());
        List<EcpProductPO> ecpProductPOList = productRepo.lambdaQuery().in(EcpProductPO::getProductNo, materialCodeList).list();
        List<EcpSpecKeyPO> specKeyPOList = specKeyRepo.lambdaQuery().list();
        List<EcpSpecValuePO> specValuePOList = specValueRepo.lambdaQuery().list();
        // 属性
        Map<String, EcpProductPO> productPOMap = CollectionUtils.isEmpty(ecpProductPOList) ? Collections.emptyMap() : ecpProductPOList.stream().collect(toMap(EcpProductPO::getProductNo, Function.identity()));
        //四个属性Map
        Map<String, EcpSpecKeyPO> nameSpecMap = new HashMap<>();
        Map<String, EcpSpecKeyPO> otherSpecMap = new HashMap<>();
        Map<String, EcpSpecKeyPO> codeSpecMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(specKeyPOList)) {
            for (EcpSpecKeyPO ecpSpecKeyPO : specKeyPOList) {
                if (Objects.equals(ecpSpecKeyPO.getSpecType(),SpecTypeEnum.NAME)) {
                    nameSpecMap.put(ecpSpecKeyPO.getSpecName(),ecpSpecKeyPO);
                }
                if (Objects.equals(ecpSpecKeyPO.getSpecType(),SpecTypeEnum.OTHER)) {
                    otherSpecMap.put(ecpSpecKeyPO.getSpecName(),ecpSpecKeyPO);
                }
                if (Objects.equals(ecpSpecKeyPO.getSpecType(),SpecTypeEnum.CODE)) {
                    codeSpecMap.put(ecpSpecKeyPO.getSpecName(),ecpSpecKeyPO);
                }
            }
        }
        Map<String, EcpSpecValuePO> specValuePOMap = CollectionUtils.isEmpty(specValuePOList) ? Collections.emptyMap() : specValuePOList.stream().collect(toMap(e -> e.getSpecId()+"_"+e.getSpecValue()+"_"+(StringUtils.isNotBlank(e.getValueUnit()) ? e.getValueUnit() : ""),Function.identity()));
        EcpSpecValuePO ecpSpecValuePO = null;
        EcpSpecKeyPO ecpSpecKeyPO = null;
        List<EcpSpecSpuPO> specSpuPOList = new ArrayList<>();
        List<String> alreadyImportProductSpec = new ArrayList<>();
        for (ProductSpecImportDTO productSpecImportDTO : productSpecImportDTOSet) {
            List<Long> specIdList = new ArrayList<>();
            EcpProductPO ecpProductPO = productPOMap.get(productSpecImportDTO.getMaterialCode());
            if (Objects.isNull(ecpProductPO)) {
                productSpecImportDTO.setErrorMsg("物料编码填写错误");
                continue;
            }
            EcCategoryPO categoryCode = categoryRepo.getByCategoryCode(ecpProductPO.getCategory3Code());
            if (Objects.isNull(categoryCode)) {
                productSpecImportDTO.setErrorMsg("物料无分类");
                continue;
            }
            // 校验名称属性
            if (StringUtils.isNotBlank(productSpecImportDTO.getNameSpec())) {
                String[] nameSpecStr = productSpecImportDTO.getNameSpec().split(",");
                for (String nameSpec : nameSpecStr) {
                    String[] nameKeyValue = nameSpec.split(":");
                    if (nameKeyValue.length < 2) {
                        productSpecImportDTO.setErrorMsg("名称属性格式填写错误");
                        continue;
                    }
                    if (alreadyImportProductSpec.contains(productSpecImportDTO.getMaterialCode()+"_"+nameKeyValue[0])) {
                        productSpecImportDTO.setErrorMsg("名称属性名称重复填写");
                        continue;
                    }
                    alreadyImportProductSpec.add(productSpecImportDTO.getMaterialCode()+"_"+nameKeyValue[0]+"_");
                    ecpSpecKeyPO = nameSpecMap.get(nameKeyValue[0]);
                    if (Objects.isNull(ecpSpecKeyPO)) {
                        productSpecImportDTO.setErrorMsg("名称属性名称填写错误");
                        continue;
                    }
                    specIdList.add(ecpSpecKeyPO.getId());
                    ecpSpecValuePO = specValuePOMap.get(ecpSpecKeyPO.getId()+"_"+ nameKeyValue[1]+"_");
                    if (Objects.isNull(ecpSpecValuePO)) {
                        productSpecImportDTO.setErrorMsg("名称属性值填写错误");
                        continue;
                    }
                    if (!Objects.equals(ecpSpecValuePO.getSpecValue(), nameKeyValue[1])) {
                        productSpecImportDTO.setErrorMsg("名称属性值填写错误");
                        continue;
                    }
                    EcpSpecSpuPO ecpSpecSpuPO = new EcpSpecSpuPO();
                    ecpSpecSpuPO.setSpuId(ecpProductPO.getSpuId());
                    ecpSpecSpuPO.setSpecKeyId(ecpSpecKeyPO.getId());
                    ecpSpecSpuPO.setSpecValueId(ecpSpecValuePO.getId());
                    specSpuPOList.add(ecpSpecSpuPO);
                }
            }
            // 校验编码属性
            if (StringUtils.isNotBlank(productSpecImportDTO.getCodeSpec())) {
                String[] codeSpecStr = productSpecImportDTO.getCodeSpec().split(",");
                for (String codeSpec : codeSpecStr) {
                    String[] codeKeyValue = codeSpec.split(":");
                    if (codeKeyValue.length < 3) {
                        productSpecImportDTO.setErrorMsg("编码属性格式填写错误");
                        continue;
                    }
                    if (alreadyImportProductSpec.contains(productSpecImportDTO.getMaterialCode()+"_"+codeKeyValue[0]+"_"+codeKeyValue[1])) {
                        productSpecImportDTO.setErrorMsg("编码属性名称重复填写");
                        continue;
                    }
                    alreadyImportProductSpec.add(productSpecImportDTO.getMaterialCode()+"_"+codeKeyValue[0]+"_"+codeKeyValue[1]);
                    ecpSpecKeyPO = codeSpecMap.get(codeKeyValue[0]);
                    if (Objects.isNull(ecpSpecKeyPO)) {
                        productSpecImportDTO.setErrorMsg("编码属性名称填写错误");
                        continue;
                    }
                    specIdList.add(ecpSpecKeyPO.getId());
                    ecpSpecValuePO = specValuePOMap.get(ecpSpecKeyPO.getId()+"_"+ codeKeyValue[1]+"_"+codeKeyValue[2]);
                    if (Objects.isNull(ecpSpecValuePO)) {
                        productSpecImportDTO.setErrorMsg("编码属性值填写错误");
                        continue;
                    }
                    if (!Objects.equals(ecpSpecValuePO.getSpecValue(), codeKeyValue[1])) {
                        productSpecImportDTO.setErrorMsg("编码属性值填写错误");
                        continue;
                    }
                    EcpSpecSpuPO ecpSpecSpuPO = new EcpSpecSpuPO();
                    ecpSpecSpuPO.setSpuId(ecpProductPO.getSpuId());
                    ecpSpecSpuPO.setSpecKeyId(ecpSpecKeyPO.getId());
                    ecpSpecSpuPO.setSpecValueId(ecpSpecValuePO.getId());
                    specSpuPOList.add(ecpSpecSpuPO);
                }
            }
            // 校验其他属性值
            if (StringUtils.isNotBlank(productSpecImportDTO.getOtherSpec())) {
                String[] otherSpecStr = productSpecImportDTO.getOtherSpec().split(",");
                for (String otherSpec : otherSpecStr) {
                    String[] otherKeyValue = otherSpec.split(":");
                    if (otherKeyValue.length < 2) {
                        productSpecImportDTO.setErrorMsg("其他属性格式填写错误");
                        continue;
                    }
                    if (alreadyImportProductSpec.contains(productSpecImportDTO.getMaterialCode()+"_"+otherKeyValue[0])) {
                        productSpecImportDTO.setErrorMsg("其他属性名称重复填写");
                        continue;
                    }
                    alreadyImportProductSpec.add(productSpecImportDTO.getMaterialCode()+"_"+otherKeyValue[0]);
                    ecpSpecKeyPO = otherSpecMap.get(otherKeyValue[0]);
                    if (Objects.isNull(ecpSpecKeyPO)) {
                        productSpecImportDTO.setErrorMsg("其他属性名称填写错误");
                        continue;
                    }
                    specIdList.add(ecpSpecKeyPO.getId());
                    ecpSpecValuePO = specValuePOMap.get(ecpSpecKeyPO.getId()+"_"+ otherKeyValue[1]+"_");
                    if (Objects.isNull(ecpSpecValuePO)) {
                        productSpecImportDTO.setErrorMsg("其他属性值填写错误");
                        continue;
                    }
                    if (!Objects.equals(ecpSpecValuePO.getSpecValue(), otherKeyValue[1])) {
                        productSpecImportDTO.setErrorMsg("其他属性值填写错误");
                        continue;
                    }
                    EcpSpecSpuPO ecpSpecSpuPO = new EcpSpecSpuPO();
                    ecpSpecSpuPO.setSpuId(ecpProductPO.getSpuId());
                    ecpSpecSpuPO.setSpecKeyId(ecpSpecKeyPO.getId());
                    ecpSpecSpuPO.setSpecValueId(ecpSpecValuePO.getId());
                    specSpuPOList.add(ecpSpecSpuPO);
                }

            }
            if (CollectionUtils.isNotEmpty(specIdList)) {
                List<EcpSpecCategoryPO> specCategoryPOList = specCategoryRepo.lambdaQuery().eq(EcpSpecCategoryPO::getCategoryId, categoryCode.getId()).in(EcpSpecCategoryPO::getSpecId, specIdList).list();
                if (CollectionUtils.isEmpty(specCategoryPOList) ) {
                    productSpecImportDTO.setErrorMsg("物料分类无属性设置");
                }
            }else {
                productSpecImportDTO.setErrorMsg("未填写属性");
            }
        }
        return specSpuPOList;
    }

    @Override
    public Integer initProductChangeLog(Set<ProductChangeLogImportDTO> productChangeLogImportDTOSet) {
        List<String> skuIdList = productChangeLogImportDTOSet.stream().map(ProductChangeLogImportDTO::getSkuId).distinct().collect(Collectors.toList());
        List<EcpProductSkuPO> productSkuPOList = productSkuRepo.lambdaQuery().in(EcpProductSkuPO::getSkuId, skuIdList).list();
        if (CollectionUtils.isEmpty(productSkuPOList)) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "sku不存在！");
        }
        int total = 0;
        Map<String, EcpProductSkuPO> productSkuPOMap = productSkuPOList.stream().collect(toMap(EcpProductSkuPO::getSkuId, Function.identity()));
        for (ProductChangeLogImportDTO productChangeLogImportDTO : productChangeLogImportDTOSet) {
            EcpProductSkuPO ecpProductSkuPO = productSkuPOMap.get(productChangeLogImportDTO.getSkuId());
            if (Objects.isNull(ecpProductSkuPO)) {
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "sku不存在！");
            }
            EcpProductChangeLogPO productChangeLogPo = new EcpProductChangeLogPO();
            productChangeLogPo.setProductId(ecpProductSkuPO.getProductId());
            productChangeLogPo.setOperateType(ProductLogOperateType.MODIFY.getValue());
            productChangeLogPo.setOperateTime(new Date());
            productChangeLogPo.setOperateResult(IProductChangeLogService.SUCCESS);
            productChangeLogPo.setOperateRemark(ecpProductSkuPO.getSkuId()+"规格修改toB供货价为"+productChangeLogImportDTO.getToBPrice()+"修改toC供货价为"+productChangeLogImportDTO.getToCPrice());
            productChangeLogPo.setOperator("system");
            productChangeLogPo.setChannel("WEB");
            productChangeLogPo.setTenantId(ecpProductSkuPO.getTenantId());
            if (productChangeLogService.save(productChangeLogPo)) {
                total++;
            }
        }
        if (total != productChangeLogImportDTOSet.size()) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "初始化轨迹失败！");
        }
        return total;
    }

    @Override
    public List<ProductChangeLogVo> getProductChangeLog(Long productId) {
        return productChangeLogService.listByProductId(productId);
    }

    @Override
    public List<ProductCategoryCheckDTO> checkProductCategory(Set<ProductCategoryCheckDTO> productCategoryCheckDTOSet, long tenantId) {
        if (CollectionUtils.isEmpty(productCategoryCheckDTOSet)) {
            throw new  BusinessException(ErrorCodeEnum.INVALID_PARAM.code(), "无数据校验");
        }
        List<EcCategoryPO> categoryPOList = categoryRepo.lambdaQuery().eq(EcCategoryPO::getTenantId, tenantId).list();
        if (CollectionUtils.isEmpty(categoryPOList)) {
            throw new  BusinessException(ErrorCodeEnum.INVALID_PARAM.code(), "该租户不存在分类不存在");
        }
        Map<String, EcCategoryPO> categoryMap = categoryPOList.stream().collect(toMap(EcCategoryPO::getCategoryName, Function.identity()));
        List<EcpProductPO> ecpProductPOList = productRepo.lambdaQuery().in(EcpProductPO::getProductNo, productCategoryCheckDTOSet.stream().map(ProductCategoryCheckDTO::getMaterialCode).collect(Collectors.toSet())).list();
        if (CollectionUtils.isEmpty(ecpProductPOList)) {
            throw new  BusinessException(ErrorCodeEnum.INVALID_PARAM.code(), "导入文件物料不存在");
        }
        Map<String, EcpProductPO> productMap = ecpProductPOList.stream().collect(toMap(EcpProductPO::getProductNo, Function.identity()));
        EcCategoryPO ecCategoryPO = null;
        StringBuilder categoryPath = new StringBuilder();
        for (ProductCategoryCheckDTO productCategoryCheckDTO : productCategoryCheckDTOSet) {
            EcpProductPO ecpProductPO = productMap.get(productCategoryCheckDTO.getMaterialCode());
            if (Objects.isNull(ecpProductPO)) {
                productCategoryCheckDTO.setErrorMsg("物料不存在");
                continue;
            }
            ecCategoryPO = categoryMap.get(productCategoryCheckDTO.getCategory1Name());
            if (Objects.isNull(ecCategoryPO)) {
                productCategoryCheckDTO.setErrorMsg("1级分类不存在");
                continue;
            }
            if (!Objects.equals(ecCategoryPO.getCategoryName(),productCategoryCheckDTO.getCategory1Name())) {
                productCategoryCheckDTO.setErrorMsg("1级分类不一致");
                continue;
            }
            categoryPath.append(ecCategoryPO.getCategoryName()).append("/");
            ecCategoryPO = categoryMap.get(productCategoryCheckDTO.getCategory2Name());
            if (Objects.isNull(ecCategoryPO)) {
                productCategoryCheckDTO.setErrorMsg("2级分类不存在");
                continue;
            }
            if (!Objects.equals(ecCategoryPO.getCategoryName(),productCategoryCheckDTO.getCategory2Name())) {
                productCategoryCheckDTO.setErrorMsg("2级分类不一致");
                continue;
            }
            categoryPath.append(ecCategoryPO.getCategoryName()).append("/");
            ecCategoryPO = categoryMap.get(productCategoryCheckDTO.getCategory3Name());
            if (Objects.isNull(ecCategoryPO)) {
                productCategoryCheckDTO.setErrorMsg("3级分类不存在");
                continue;
            }
            categoryPath.append(ecCategoryPO.getCategoryName()).append("/");
            ecCategoryPO = categoryMap.get(productCategoryCheckDTO.getCategory4Name());
            if (Objects.isNull(ecCategoryPO)) {
                productCategoryCheckDTO.setErrorMsg("4级分类不存在");
                continue;
            }
            categoryPath.append(ecCategoryPO.getCategoryName()).append("/");
            ecCategoryPO = categoryMap.get(productCategoryCheckDTO.getCategory5Name());
            if (Objects.isNull(ecCategoryPO)) {
                productCategoryCheckDTO.setErrorMsg("5级分类不存在");
                continue;
            }
            categoryPath.append(ecCategoryPO.getCategoryName()).append("/");
            ecCategoryPO = categoryMap.get(productCategoryCheckDTO.getCategory6Name());
            if (Objects.isNull(ecCategoryPO)) {
                productCategoryCheckDTO.setErrorMsg("6级分类不存在");
                continue;
            }
            if (!Objects.equals(ecCategoryPO.getCategoryName(),productCategoryCheckDTO.getCategory6Name())) {
                productCategoryCheckDTO.setErrorMsg("6级分类不一致");
                continue;
            }
            categoryPath.append(ecCategoryPO.getCategoryName());
            if (categoryPath.toString().equals(ecpProductPO.getCategoryPath())) {
                productCategoryCheckDTO.setErrorMsg("分类匹配成功");
            }else {
                productCategoryCheckDTO.setErrorMsg("分类匹配失败");
            }
            categoryPath.setLength(0);
        }
        return new ArrayList<>(productCategoryCheckDTOSet);
    }

    @Override
    public List<ProductSpecCheckDTO> checkProductSpec(Set<ProductSpecCheckDTO> productSpecCheckDTOSet, long tenantId) {
        List<String> materialCodeList = productSpecCheckDTOSet.stream().map(ProductSpecCheckDTO::getMaterialCode).distinct().collect(Collectors.toList());
        List<EcpProductPO> ecpProductPOList = productRepo.lambdaQuery().in(EcpProductPO::getProductNo, materialCodeList).eq(EcpProductPO::getTenantId,tenantId).list();

        List<EcpSpecKeyPO> specKeyPOList = specKeyRepo.lambdaQuery().eq(EcpSpecKeyPO::getTenantId,tenantId).list();
        List<EcpSpecValuePO> specValuePOList = specValueRepo.lambdaQuery().eq(EcpSpecValuePO::getTenantId,tenantId).groupBy(EcpSpecValuePO::getSpecId, EcpSpecValuePO::getSpecValue).orderByAsc(EcpSpecValuePO::getCreateTime).list();
        // 属性
        Map<String, EcpProductPO> productPOMap = CollectionUtils.isEmpty(ecpProductPOList) ? Collections.emptyMap() : ecpProductPOList.stream().collect(toMap(EcpProductPO::getProductNo, Function.identity()));
        List<EcpSpecSpuPO> ecpProductSpuList = specSpuRepo.lambdaQuery().in(EcpSpecSpuPO::getSpuId, ecpProductPOList.stream().map(EcpProductPO::getSpuId).collect(Collectors.toList())).eq(EcpSpecSpuPO::getTenantId,tenantId).list();
        Map<String, EcpSpecSpuPO> productSpuMap = CollectionUtils.isEmpty(ecpProductSpuList) ? Collections.emptyMap() : ecpProductSpuList.stream().collect(toMap(e -> e.getSpuId()+"_"+e.getSpecKeyId()+"_"+e.getSpecValueId(), Function.identity()));
        //四个属性Map
        Map<String, EcpSpecKeyPO> nameSpecMap = new HashMap<>();
        Map<String, EcpSpecKeyPO> otherSpecMap = new HashMap<>();
        Map<String, EcpSpecKeyPO> codeSpecMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(specKeyPOList)) {
            for (EcpSpecKeyPO ecpSpecKeyPO : specKeyPOList) {
                if (Objects.equals(ecpSpecKeyPO.getSpecType(),SpecTypeEnum.NAME)) {
                    nameSpecMap.put(ecpSpecKeyPO.getSpecName(),ecpSpecKeyPO);
                }
                if (Objects.equals(ecpSpecKeyPO.getSpecType(),SpecTypeEnum.OTHER)) {
                    otherSpecMap.put(ecpSpecKeyPO.getSpecName(),ecpSpecKeyPO);
                }
                if (Objects.equals(ecpSpecKeyPO.getSpecType(),SpecTypeEnum.CODE)) {
                    codeSpecMap.put(ecpSpecKeyPO.getSpecName(),ecpSpecKeyPO);
                }
            }
        }
        Map<String, EcpSpecValuePO> specValuePOMap = CollectionUtils.isEmpty(specValuePOList) ? Collections.emptyMap() : specValuePOList.stream().collect(toMap(e -> e.getSpecId()+"_"+e.getSpecValue(),Function.identity()));
        EcpSpecKeyPO ecpSpecKeyPO = null;
        EcpSpecValuePO ecpSpecValuePO = null;
        for (ProductSpecCheckDTO productSpecCheckDTO : productSpecCheckDTOSet) {
            EcpProductPO ecpProductPO = productPOMap.get(productSpecCheckDTO.getMaterialCode());
            if (Objects.isNull(ecpProductPO)) {
                productSpecCheckDTO.setErrorMsg("物料编码不存在");
                continue;
            }
            // 校验名称属性
            Integer specType = SpecTypeEnum.descOf(productSpecCheckDTO.getSpecType());
            if (Objects.isNull(specType)) {
                productSpecCheckDTO.setErrorMsg("属性类型不存在");
                continue;
            }
            if (specType.equals(SpecTypeEnum.NAME.getValue())) {
                ecpSpecKeyPO = nameSpecMap.get(productSpecCheckDTO.getSpecName());
            }else if(specType.equals(SpecTypeEnum.OTHER.getValue())){
                ecpSpecKeyPO = otherSpecMap.get(productSpecCheckDTO.getSpecName());
            }else if(specType.equals(SpecTypeEnum.CODE.getValue())){
                ecpSpecKeyPO = codeSpecMap.get(productSpecCheckDTO.getSpecName());
            }

            if (Objects.isNull(ecpSpecKeyPO)) {
                productSpecCheckDTO.setErrorMsg("属性不存在");
                continue;
            }
            ecpSpecValuePO = specValuePOMap.get(ecpSpecKeyPO.getId()+"_"+productSpecCheckDTO.getSpecValue());
            if (Objects.isNull(ecpSpecValuePO)) {
                productSpecCheckDTO.setErrorMsg("属性值不存在");
                continue;
            }
            EcpSpecSpuPO ecpSpecSpuPO = productSpuMap.get(ecpProductPO.getSpuId() + "_" + ecpSpecKeyPO.getId() + "_" + ecpSpecValuePO.getId());
            if (Objects.isNull(ecpSpecSpuPO)) {
                productSpecCheckDTO.setErrorMsg("货品没有该属性");
            }else {
                productSpecCheckDTO.setErrorMsg("校验正确");
            }
        }
        return new ArrayList<>(productSpecCheckDTOSet);
    }

    @Override
    public List<ProductSpecCheckDTO> convertProductSpec(Set<ProductSpecReceiveDTO> productSpecReceiveDTOSet) {
        List<ProductSpecCheckDTO> productSpecCheckDTOList = new ArrayList<>();
        for (ProductSpecReceiveDTO productSpecReceiveDTO : productSpecReceiveDTOSet) {
            if (StringUtils.isNotBlank(productSpecReceiveDTO.getNameSpec())) {
                ProductSpecCheckDTO productSpecCheckDTO = new ProductSpecCheckDTO();
                productSpecCheckDTO.setProductName(productSpecReceiveDTO.getProductName());
                productSpecCheckDTO.setMaterialCode(productSpecReceiveDTO.getMaterialCode());
                productSpecCheckDTO.setSpecType("名称属性");
                productSpecCheckDTO.setSpecName(productSpecReceiveDTO.getNameSpec());
                productSpecCheckDTO.setSpecValue(productSpecReceiveDTO.getNameSpecValue());
                productSpecCheckDTOList.add(productSpecCheckDTO);
            }
            if (StringUtils.isNotBlank(productSpecReceiveDTO.getCodeSpec())) {
                ProductSpecCheckDTO productSpecCheckDTO = new ProductSpecCheckDTO();
                productSpecCheckDTO.setProductName(productSpecReceiveDTO.getProductName());
                productSpecCheckDTO.setMaterialCode(productSpecReceiveDTO.getMaterialCode());
                productSpecCheckDTO.setSpecType("编码属性");
                productSpecCheckDTO.setSpecName(productSpecReceiveDTO.getCodeSpec());
                productSpecCheckDTO.setSpecValue(productSpecReceiveDTO.getCodeSpecValue());
                productSpecCheckDTOList.add(productSpecCheckDTO);
            }
            if (StringUtils.isNotBlank(productSpecReceiveDTO.getOtherSpec())) {
                ProductSpecCheckDTO productSpecCheckDTO = new ProductSpecCheckDTO();
                productSpecCheckDTO.setProductName(productSpecReceiveDTO.getProductName());
                productSpecCheckDTO.setMaterialCode(productSpecReceiveDTO.getMaterialCode());
                productSpecCheckDTO.setSpecType("其他属性");
                productSpecCheckDTO.setSpecName(productSpecReceiveDTO.getOtherSpec());
                productSpecCheckDTO.setSpecValue(productSpecReceiveDTO.getOtherSpecValue());
                productSpecCheckDTOList.add(productSpecCheckDTO);
            }
        }
        return productSpecCheckDTOList;
    }

    @Override
    public Page<ProductVO> getProductPage(EcpProductQueryDTO productQueryDTO, ErpTenantPO parentTenant) {
        if (productQueryDTO.getPageSize() > 500) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "最大只支持500条分页数量查询！");
        }

        if (CollectionUtils.isNotEmpty(productQueryDTO.getSpecValueIdList())) {
            List<String> spuIdList = specSpuRepo.getSpuIdBySpecValueId(productQueryDTO.getSpecValueIdList());
            if (CollectionUtils.isEmpty(spuIdList)) {
                return new Page<>();
            }
            productQueryDTO.setSpuIdList(spuIdList);
        }
        if (CollectionUtils.isNotEmpty(productQueryDTO.getParentCodeCategoryList())) {
            List<CategoryVo> leafCategoryList = categoryService.getLeafCategory(productQueryDTO.getParentCodeCategoryList(), parentTenant.getHighestCategoryLevel(), parentTenant.getTenantId());
            if (CollectionUtils.isEmpty(leafCategoryList)) {
                return new Page<>();
            }
            productQueryDTO.setParentToLeafCodeList(leafCategoryList.stream().map(CategoryVo::getCategoryCode).collect(Collectors.toList()));
        }

        return productRepo.getProductVoPageList(productQueryDTO);
    }

    @Override
    public Page<ProductVO> getManyTenantProduct(ManyTenantProductDTO productQueryDTO) {

        if (productQueryDTO.getPageSize() > 500) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "最大只支持500条分页数量查询！");
        }
        List<ErpTenantPO> tenantPOs = erpTenantRepo.getTenantByCompanyCodeList(productQueryDTO.getCompanyCodes());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(tenantPOs)) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "租户不存在");
        }
        List<Long> tenantIds = new ArrayList<>();
        for (ErpTenantPO tenantPO : tenantPOs) {
            if (basicsConfig.getQjbstockDistributioTenantId().equals(tenantPO.getTenantId())) {
                productQueryDTO.setCategory1CodeList(basicsConfig.getQjbCategoryCodeList());
                productQueryDTO.setTenantId(tenantPO.getTenantId());
                continue;
            }
            tenantIds.add(tenantPO.getTenantId());
        }
        productQueryDTO.setTenantIds(tenantIds);
        Page<ProductVO> page = productRepo.getManyTenantProduct(productQueryDTO);
        return page;
    }

    @Override
    public List<ProductVO> getProductVoList(EcpProductQueryDTO productQueryDTO) {
        List<Long> tenantIdList = new ArrayList<>();
        if(StringUtils.isNotEmpty(productQueryDTO.getCompanyCode())) {
            ErpTenantPO tenantPO = erpTenantRepo.getTenantByCompanyCode(productQueryDTO.getCompanyCode());
            if (Objects.nonNull((tenantPO))) {
                tenantIdList.add(tenantPO.getTenantId());
            }
        }
        if (CollectionUtils.isNotEmpty(productQueryDTO.getCompanyCodes())) {
            List<ErpTenantPO> tenantPOList = erpTenantRepo.getTenantByCompanyCodeList(productQueryDTO.getCompanyCodes());
            tenantPOList.forEach(tenantPO -> {
                tenantIdList.add(tenantPO.getTenantId());
            });
        }
        productQueryDTO.setTenantIdList(tenantIdList);

        List<ProductVO> productVoList = productRepo.getProductVoList(productQueryDTO);
        if (CollectionUtils.isNotEmpty(productVoList)) {
            List<String> categoryCodes = productVoList.stream()
                    .map(ProductVO::getCategory3Code)
                    .collect(Collectors.toList());
            List<EcCategoryPO> categoryPOList = categoryRepo.getCategoryCodeList(categoryCodes);
            Map<String, EcCategoryPO> categoryPOMap = CollectionUtils.isEmpty(categoryPOList) ? Collections.emptyMap() : categoryPOList.stream().collect(toMap(EcCategoryPO::getCategoryCode, Function.identity()));
            for (ProductVO productVO : productVoList) {
                EcCategoryPO ecCategoryPO = categoryPOMap.get(productVO.getCategory3Code());
                if (Objects.nonNull(ecCategoryPO) && StringUtils.isNotBlank(ecCategoryPO.getCodeTree())) {
                    productVO.setCategoryCodePath(Stream.of(ecCategoryPO.getCodeTree().split("_")).map(e -> "WMS"+e).collect(Collectors.joining("/")));
                }

            }
        }
        return productVoList;
    }


    public StockBalanceSkuVO queryStockBalanceSkuIds(StockBalanceSkuQueryDto productQueryDTO) {
        List<Long> productIds = new ArrayList<>();
        List<String> skuIds = new ArrayList<>();
        StockBalanceSkuVO result = new StockBalanceSkuVO();
        if (StringUtils.isNotEmpty(productQueryDTO.getProductName())) {
            productIds = productRepo.lambdaQuery().select(EcpProductPO::getId)
                    .like(StringUtils.isNotEmpty(productQueryDTO.getProductName()), EcpProductPO::getProductName,
                            productQueryDTO.getProductName())
                    .eq(EcpProductPO::getIfDelete, 0)
                    .list().stream().map(EcpProductPO::getId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(productIds)){
                return null;
            }
        }

        if (StringUtils.isNotEmpty(productQueryDTO.getSkuName()) || CollectionUtils.isNotEmpty(productIds) ||
                StringUtils.isNotEmpty(productQueryDTO.getMaterialCode()) || StringUtils.isNotEmpty(productQueryDTO.getSkuId())) {
            skuIds = productSkuRepo.lambdaQuery().select(EcpProductSkuPO::getSkuId)
                    .like(StringUtils.isNotEmpty(productQueryDTO.getSkuName()), EcpProductSkuPO::getSkuName, productQueryDTO.getSkuName())
                    .in(CollectionUtils.isNotEmpty(productIds), EcpProductSkuPO::getProductId, productIds)
                    .eq(StringUtils.isNotEmpty(productQueryDTO.getMaterialCode()), EcpProductSkuPO::getErpNo, productQueryDTO.getMaterialCode())
                    .eq(StringUtils.isNotEmpty(productQueryDTO.getSkuId()),EcpProductSkuPO::getSkuId, productQueryDTO.getSkuId())
                    .eq(EcpProductSkuPO::getIfDelete, 0)
                    .list().stream().map(EcpProductSkuPO::getSkuId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(skuIds)){
                return null;
            }
        }


        if (StringUtils.isNotEmpty(productQueryDTO.getUnit()) && CollectionUtils.isNotEmpty(skuIds)) {
            skuIds = productSkuUnitRepo.lambdaQuery().select(EcpProductSkuUnitPO::getSkuId)
                    .eq(StringUtils.isNotEmpty(productQueryDTO.getUnit()), EcpProductSkuUnitPO::getUnitCode, productQueryDTO.getUnit())
                    .in(CollectionUtils.isNotEmpty(skuIds), EcpProductSkuUnitPO::getSkuId, skuIds)
                    .eq(EcpProductSkuUnitPO::getIfDelete, 0)
                    .list().stream().map(EcpProductSkuUnitPO::getSkuId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(skuIds)){
                return null;
            }
        }
        if (StringUtils.isNotEmpty(productQueryDTO.getBatchName()) || StringUtils.isNotEmpty(productQueryDTO.getBatchNumber())) {
            List<String> batchNos = procurementSubmissionHeadRepo.lambdaQuery()
                    .select(ErpProcurementSubmissionHeadPO::getProcurementSubmissionCode)
                    .like(StringUtils.isNotEmpty(productQueryDTO.getBatchName()),ErpProcurementSubmissionHeadPO::getBatchName, productQueryDTO.getBatchName())
                    .eq(StringUtils.isNotEmpty( productQueryDTO.getBatchNumber()),ErpProcurementSubmissionHeadPO::getProcurementSubmissionCode, productQueryDTO.getBatchNumber())
                    .eq(ErpProcurementSubmissionHeadPO::getIfDelete, 0)
                    .list().stream().map(ErpProcurementSubmissionHeadPO::getProcurementSubmissionCode).distinct().collect(Collectors.toList());
            if(CollectionUtils.isEmpty(batchNos)){
                return null;
            }
            result.setBatchNumbers(batchNos);
        }

        result.setSkuIds(skuIds);
        return result;
    }

    @Override
    public Page<StockBalanceSkuVO> queryStockBalanceSku(StockBalanceSkuQueryDto productQueryDTO) {
        log.info("stockBalanceSkuQueryDto:{}", JSONObject.toJSONString(productQueryDTO));
        List<String> querySkuIds = new ArrayList<>();
        List<String> batchNumbers = new ArrayList<>();
        if (StringUtils.isNotBlank(productQueryDTO.getMaterialCode()) || StringUtils.isNotBlank(productQueryDTO.getSkuName())
                || StringUtils.isNotBlank(productQueryDTO.getProductName()) || StringUtils.isNotBlank(productQueryDTO.getUnit())
                || StringUtils.isNotBlank(productQueryDTO.getBatchName()) || StringUtils.isNotBlank(productQueryDTO.getSkuId())
                || StringUtils.isNotBlank(productQueryDTO.getBatchNumber()) ) {
            StockBalanceSkuVO stockBalanceSkuVO = this.queryStockBalanceSkuIds(productQueryDTO);
            if(stockBalanceSkuVO == null){
                return new Page<>();
            }
            if (CollectionUtils.isNotEmpty(stockBalanceSkuVO.getSkuIds())) {
                querySkuIds = stockBalanceSkuVO.getSkuIds();
            }
            if (CollectionUtils.isNotEmpty(stockBalanceSkuVO.getBatchNumbers())) {
                batchNumbers = stockBalanceSkuVO.getBatchNumbers();
            }
        }
        productQueryDTO.setBatchNoList(batchNumbers);
        productQueryDTO.setSkuIdList(querySkuIds);

        Map<String, StockBalanceSkuVO> stockBalanceSkuVOMap = new HashMap<>();
        ErpTenantPO tenant = erpTenantRepo.getTenantInfoByStoreIdAndSourceCode(productQueryDTO.getStoreId(), productQueryDTO.getSystemSource());
        if(Objects.nonNull(tenant)){
            productQueryDTO.setTenantId(tenant.getTenantId());
        }
        if (StringUtils.isNotBlank(productQueryDTO.getAreaCode())) {
            // 找到区域下所有的销仓
            List<ErpDepotPO> depotList = erpDepotRepo.listSellDepotByAreaCode(productQueryDTO.getAreaCode(), tenant.getTenantId());
            if (CollectionUtils.isNotEmpty(depotList)) {
                productQueryDTO.setDepotCodeList(depotList.stream().map(ErpDepotPO::getDeptCode).collect(Collectors.toList()));
            }
        }

        JsonResult<Page<StockBalance>> stockBalancePage = stockFacade.queryStockBalanceSku(productQueryDTO);
        log.info("stockBalancePage:"+ JSON.toJSONString(stockBalancePage));
        if(CollectionUtils.isEmpty(stockBalancePage.getData().getRecords())){
            return new Page<>();
        }
        List<StockBalance> balanceList = stockBalancePage.getData().getRecords();
        // 过滤balanceList中stock>0的数据
        balanceList = balanceList.stream().filter(balance -> balance.getStock().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());

        List<String> skuIdsQuery = balanceList.stream().map(StockBalance::getSkuId).collect(Collectors.toList());
        List<String> batchNoList = balanceList.stream().map(StockBalance::getBatchNumber).collect(Collectors.toList());

        productQueryDTO.setSkuIdList(skuIdsQuery);
        productQueryDTO.setBatchNoList(batchNoList);
        List<StockBalanceSkuVO> balances = this.queryProductSkuList(productQueryDTO);
        if (balances != null) {
            stockBalanceSkuVOMap = balances.stream()
                    .filter(balance -> balance.getBatchNumber() != null && balance.getSkuId() != null)
                    .collect(Collectors.toMap(
                            balance -> balance.getBatchNumber() + "_" + balance.getSkuId(),
                            Function.identity(),
                            (a, b) -> a
                    ));
        }

        List<StockBalanceSkuVO> resultList = new ArrayList<>();
        for (StockBalance stockBalance : balanceList) {
            StockBalanceSkuVO stockBalanceSkuVO = stockBalanceSkuVOMap.get(stockBalance.getBatchNumber() + "_" + stockBalance.getSkuId());
            if (stockBalanceSkuVO != null) {
                StockBalanceSkuVO result = new StockBalanceSkuVO();
                result.setPurchaseSubCode(stockBalanceSkuVO.getPurchaseSubCode());
                result.setPurchaseName(stockBalanceSkuVO.getPurchaseName());
                result.setPurchaseCode(stockBalanceSkuVO.getPurchaseCode());
                result.setBatchNumber(stockBalanceSkuVO.getBatchNumber());
                result.setBatchName(stockBalanceSkuVO.getBatchName());
                result.setMaterialCode(stockBalanceSkuVO.getMaterialCode());
                result.setStock(stockBalance.getStock().multiply(stockBalanceSkuVO.getFinanceWeight()).divide(stockBalanceSkuVO.getWeight(), 6, RoundingMode.DOWN));
                result.setCustomerPrice(stockBalanceSkuVO.getCustomerPrice());
                result.setSkuId(stockBalanceSkuVO.getSkuId());
                result.setProductName(stockBalanceSkuVO.getProductName());
                result.setSkuName(stockBalanceSkuVO.getSkuName());
                result.setUnit(stockBalanceSkuVO.getUnit());
                result.setUnitCode(stockBalanceSkuVO.getUnitCode());
                result.setSkuUnitCode(stockBalanceSkuVO.getSkuUnitCode());
                result.setWeight(stockBalanceSkuVO.getWeight());
                result.setFinanceWeight(stockBalanceSkuVO.getFinanceWeight());
                result.setBusinessPrice(stockBalanceSkuVO.getBusinessPrice());
                result.setCostPrice(stockBalanceSkuVO.getCostPrice());
                resultList.add(result);
            }
        }

        Page<StockBalanceSkuVO> result = new Page<>();
        BeanUtils.copyProperties(stockBalancePage.getData(), result);
        result.setRecords(resultList);
        return result;
    }


    private List<StockBalanceSkuVO> queryProductSkuList(StockBalanceSkuQueryDto productQueryDTO){
        List<EcpProductSkuPO> productSkuList =
                new ArrayList<>(productSkuRepo.lambdaQuery()
                        .select(EcpProductSkuPO::getSkuId, EcpProductSkuPO::getProductId, EcpProductSkuPO::getSkuName, EcpProductSkuPO::getErpNo, EcpProductSkuPO::getTenantId)
                        .in(EcpProductSkuPO::getSkuId, productQueryDTO.getSkuIdList())
                        .eq(EcpProductSkuPO::getIfDelete, 0).list());

        if(CollectionUtils.isEmpty(productSkuList)){
            return null;
        }

        List<Long> productIds = productSkuList.stream().map(EcpProductSkuPO::getProductId).collect(Collectors.toList());
        List<EcpProductPO> productList = productRepo.lambdaQuery().select(EcpProductPO::getId, EcpProductPO::getProductName,EcpProductPO::getTenantId)
                .in(EcpProductPO::getId, productIds)
                .eq(EcpProductPO::getIfDelete, 0)
                .list();
        if(CollectionUtils.isEmpty(productList)){
            return null;
        }
        Map<Long, EcpProductPO> productMap = productList.stream().collect(toMap(EcpProductPO::getId, Function.identity(), (a, b) -> a));

        Map<String, List<ErpProcurementSubmissionItemPO>> submissionItemMap = procurementSubmissionItemRepo.lambdaQuery()
                .select(ErpProcurementSubmissionItemPO::getSkuCode, ErpProcurementSubmissionItemPO::getCustomerPrice,
                        ErpProcurementSubmissionItemPO::getProcurementSubmissionCode,ErpProcurementSubmissionItemPO::getBusinessPrice,
                        ErpProcurementSubmissionItemPO::getProcurementUnitTaxPrice)
                .in(ErpProcurementSubmissionItemPO::getSkuCode, productQueryDTO.getSkuIdList())
                .in(ErpProcurementSubmissionItemPO::getProcurementSubmissionCode, productQueryDTO.getBatchNoList())
                .eq(ErpProcurementSubmissionItemPO::getIfDelete, 0)
                .list().stream().collect(Collectors.groupingBy(ErpProcurementSubmissionItemPO::getSkuCode));

        Map<String,EcpProductSkuUnitPO> skuUnitMap = productSkuUnitRepo.lambdaQuery()
                .select(EcpProductSkuUnitPO::getSkuId, EcpProductSkuUnitPO::getUnitName,EcpProductSkuUnitPO::getUnitCode,
                        EcpProductSkuUnitPO::getSkuUnitCode,EcpProductSkuUnitPO::getWeight,EcpProductSkuUnitPO::getFinancePurchaseFlag)
                .in(CollectionUtils.isNotEmpty(productQueryDTO.getSkuIdList()), EcpProductSkuUnitPO::getSkuId, productQueryDTO.getSkuIdList())
                .eq(StringUtils.isNotBlank(productQueryDTO.getUnit()),EcpProductSkuUnitPO::getUnitCode,productQueryDTO.getUnit())
                .eq(EcpProductSkuUnitPO::getIfDelete, 0)
                .list().stream().collect(toMap(EcpProductSkuUnitPO::getSkuId,Function.identity(),(a, b)->a));

        Map<String, ErpProcurementSubmissionHeadPO> submissionHeadMap = procurementSubmissionHeadRepo.lambdaQuery()
                .select(ErpProcurementSubmissionHeadPO::getProcurementSubmissionCode,ErpProcurementSubmissionHeadPO::getBatchName)
                .in(ErpProcurementSubmissionHeadPO::getProcurementSubmissionCode, productQueryDTO.getBatchNoList())
                .eq(ErpProcurementSubmissionHeadPO::getIfDelete, 0)
                .list().stream().collect(toMap(ErpProcurementSubmissionHeadPO::getProcurementSubmissionCode, Function.identity()));

        List<StockBalanceSkuVO> balanceSkuVOS = new ArrayList<>();

        List<String> resultSkuIds = new ArrayList<>(skuUnitMap.keySet());

        Map<String, EcpProductSkuUnitPO> financeUtilMap = productSkuUnitRepo.lambdaQuery()
                .select(EcpProductSkuUnitPO::getSkuId, EcpProductSkuUnitPO::getUnitName, EcpProductSkuUnitPO::getUnitCode,
                        EcpProductSkuUnitPO::getSkuUnitCode, EcpProductSkuUnitPO::getWeight, EcpProductSkuUnitPO::getFinancePurchaseFlag)
                .in(EcpProductSkuUnitPO::getSkuId, resultSkuIds)
                .eq(EcpProductSkuUnitPO::getFinancePurchaseFlag, true)
                .eq(EcpProductSkuUnitPO::getIfDelete, 0)
                .list().stream().collect(toMap(EcpProductSkuUnitPO::getSkuId, Function.identity()));

        //查询采购单关联的价格单信息
        Map<String,ErpProcurementSubmissionItemPriceBillLinkPO> priceBillLinkMap=new HashMap<>();
        ErpTenantPO tenant = tenantUtil.getCompanyCodeOrNameByTenantId(productList.get(0).getTenantId());
        if(Objects.nonNull(basicsConfig.getPurchasePriceBillTenantList())
                && basicsConfig.getPurchasePriceBillTenantList().contains(tenant.getCompanyCode())){
            ErpPurchasePriceBillLinkDTO priceBillLinkDTO=new ErpPurchasePriceBillLinkDTO();
            priceBillLinkDTO.setProcurementSubmissionCodeList(productQueryDTO.getBatchNoList());
            priceBillLinkDTO.setBillStatusList(Collections.singletonList(ValidEnum.SUCCESS_EFFICACY.getCode()));
            List<ErpProcurementSubmissionItemPriceBillLinkPO> priceBillLinkList = priceBillLinkRepo.getProcurementSubmissionItemPriceBillLinkList(priceBillLinkDTO);
            if(CollectionUtils.isNotEmpty(priceBillLinkList)){
                priceBillLinkMap=priceBillLinkList.stream().collect(toMap(key->String.format("%s:%s",key.getProcurementSubmissionCode(),key.getSkuId()),Function.identity()));
            }

        }
        for (EcpProductSkuPO skuPO : productSkuList) {
            for (ErpProcurementSubmissionItemPO submissionItemPO : submissionItemMap.get(skuPO.getSkuId())) {
                EcpProductSkuUnitPO ecpProductSkuUnitPO = skuUnitMap.get(skuPO.getSkuId());
                StockBalanceSkuVO stockBalanceSkuVO = new StockBalanceSkuVO();
                //查询采购单关联的价格单信息
                if(Objects.nonNull(basicsConfig.getPurchasePriceBillTenantList())
                        && basicsConfig.getPurchasePriceBillTenantList().contains(tenant.getCompanyCode())){
                    ErpProcurementSubmissionItemPriceBillLinkPO priceBillLink = priceBillLinkMap.get(String.format("%s:%s", submissionItemPO.getProcurementSubmissionCode(), submissionItemPO.getSkuCode()));
                    if(Objects.nonNull(priceBillLink)){
                        stockBalanceSkuVO.setPurchaseCode(priceBillLink.getPurchaseCode());
                        stockBalanceSkuVO.setPurchaseName(priceBillLink.getPurchaseName());
                        stockBalanceSkuVO.setPurchaseSubCode(priceBillLink.getPurchaseSubCode());
                    }
                }
                stockBalanceSkuVO.setSkuName(skuPO.getSkuName());
                EcpProductPO ecpProductPO = productMap.get(skuPO.getProductId());
                stockBalanceSkuVO.setProductName(ecpProductPO.getProductName());
                stockBalanceSkuVO.setMaterialCode(skuPO.getErpNo());
                BigDecimal financeWeight = financeUtilMap.get(ecpProductSkuUnitPO.getSkuId()).getWeight();
                BigDecimal divideWeight = ecpProductSkuUnitPO.getWeight().divide(financeWeight, 6, RoundingMode.HALF_UP);
                BigDecimal customerPrice = submissionItemPO.getCustomerPrice()
                        .multiply(divideWeight)
                        .setScale(6, RoundingMode.HALF_UP);
                stockBalanceSkuVO.setBusinessPrice(submissionItemPO.getBusinessPrice()
                        .multiply(divideWeight));
                stockBalanceSkuVO.setCostPrice(submissionItemPO.getProcurementUnitTaxPrice()
                        .multiply(divideWeight));
                stockBalanceSkuVO.setCustomerPrice(customerPrice);
                stockBalanceSkuVO.setUnit(ecpProductSkuUnitPO.getUnitName());
                stockBalanceSkuVO.setUnitCode(ecpProductSkuUnitPO.getUnitCode());
                stockBalanceSkuVO.setSkuUnitCode(ecpProductSkuUnitPO.getSkuUnitCode());
                stockBalanceSkuVO.setWeight(ecpProductSkuUnitPO.getWeight());
                stockBalanceSkuVO.setFinanceWeight(financeWeight);
                stockBalanceSkuVO.setSkuId(skuPO.getSkuId());
                stockBalanceSkuVO.setBatchNumber(submissionItemPO.getProcurementSubmissionCode());
                if(submissionItemMap.get(skuPO.getSkuId()) != null && submissionItemPO.getProcurementSubmissionCode()!=null){
                    stockBalanceSkuVO.setBatchName(submissionHeadMap.get(submissionItemPO.getProcurementSubmissionCode()).getBatchName());
                }
                balanceSkuVOS.add(stockBalanceSkuVO);
            }
        }

        return balanceSkuVOS;
    }



    @Override
    public Page<ErpProductDetailVo> getProductByShiftDepot(ErpProductDetailQuery productDetailQuery) {
        if (buildQuery(productDetailQuery)) {
            return new Page<>();
        }

        ErpDepotPO depotPo = null;
        if(productDetailQuery.getSiteId() != null){
            List<String> skuIds = productDepotRepo.lambdaQuery()
                    .eq(EcpProductDepotPO::getDepotId, productDetailQuery.getSiteId())
                    .eq(EcpProductDepotPO::getIfDelete, 0).list().stream().map(EcpProductDepotPO::getSkuId).collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(skuIds)){
                if(CollectionUtils.isEmpty(productDetailQuery.getSkuIdList())){
                    productDetailQuery.setSkuIdList(skuIds);
                }else{
                    skuIds.retainAll(productDetailQuery.getSkuIdList());
                    if(CollectionUtils.isEmpty(skuIds)){
                        return new Page<>();
                    }
                    productDetailQuery.setSkuIdList(skuIds);
                }
            }else{
                return new Page<>();
            }
            depotPo = erpDepotRepo.getById(productDetailQuery.getSiteId());
            if(Objects.isNull(depotPo)) {
                return new Page<>();
            }
        }

        if(StringUtils.isNotBlank(productDetailQuery.getSupplierCode())){
            List<String> spuId = productSupplierRepo.lambdaQuery().select(EcpProductSupplierPO::getSpuId).eq(EcpProductSupplierPO::getSupplierCode, productDetailQuery.getSupplierCode())
                    .eq(EcpProductSupplierPO::getIfDelete, 0).list().stream().map(EcpProductSupplierPO::getSpuId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(productDetailQuery.getSpuIdList())){
                productDetailQuery.setSpuIdList(spuId);
            }else{
                spuId.retainAll(productDetailQuery.getSpuIdList());
                if(CollectionUtils.isEmpty(spuId)){
                    return new Page<>();
                }
                productDetailQuery.setSpuIdList(spuId);
            }
            if(CollectionUtils.isEmpty(spuId)){
                return new Page<>();
            }
        }


        Page<ErpProductDetailVo> productSkuPage = productRepo.getProductByShiftDepot(productDetailQuery);

        if(CollectionUtils.isEmpty(productSkuPage.getRecords())) {
            return new Page<>();
        }


        List<String> skuIds = productSkuPage.getRecords().stream().map(ErpProductDetailVo::getSkuId).collect(Collectors.toList());


        Map<String, EcpProductSkuUnitPO> skuUnitMap = productSkuUnitRepo.lambdaQuery().select(EcpProductSkuUnitPO::getSkuId,EcpProductSkuUnitPO::getWeight, EcpProductSkuUnitPO::getUnitName)
                .eq(EcpProductSkuUnitPO::getIfDelete, 0)
                .eq(EcpProductSkuUnitPO::getUnitType,1)
                .in(EcpProductSkuUnitPO::getSkuId, skuIds)
                .list().stream().collect(toMap(EcpProductSkuUnitPO::getSkuId, Function.identity(),(a, b)->a));

        List<String> spuIds = productSkuPage.getRecords().stream().map(ErpProductDetailVo::getSpuId).collect(Collectors.toList());

        Map<String,String> spuIdMap = productSupplierRepo.lambdaQuery().select(EcpProductSupplierPO::getSupplierCode,EcpProductSupplierPO::getSpuId)
                .in(EcpProductSupplierPO::getSpuId, spuIds).eq(EcpProductSupplierPO::getIfDelete, 0).list().stream().collect(toMap(EcpProductSupplierPO::getSpuId, EcpProductSupplierPO::getSupplierCode,(a, b)->a));
        Map<String, String> supplierMap = supplierRepo.lambdaQuery().select(SupplierPO::getSupplierCode, SupplierPO::getSupplierName)
                .in(SupplierPO::getSupplierCode, spuIdMap.values()).list().stream().collect(toMap(SupplierPO::getSupplierCode, SupplierPO::getSupplierName,(a, b)->a));

        for (String s : spuIdMap.keySet()) {
            spuIdMap.put(s,supplierMap.get(spuIdMap.get(s)));
        }

        for (ErpProductDetailVo skuPO : productSkuPage.getRecords()) {
            skuPO.setSupplierName(spuIdMap.get(skuPO.getSpuId()));
            skuPO.setBrandId(Integer.parseInt(skuPO.getBrandId().toString()));
            if(skuUnitMap.get(skuPO.getSkuId())!=null){
                skuPO.setWeight(skuUnitMap.get(skuPO.getSkuId()).getWeight());
                skuPO.setUnitName(skuUnitMap.get(skuPO.getSkuId()).getUnitName());
            }
            if(Objects.nonNull(depotPo)) {
                skuPO.setSiteId(depotPo.getId());
                skuPO.setSiteName(depotPo.getName());
                skuPO.setBranchCode(depotPo.getBranchCode());
            }
        }
        handlerResult(productSkuPage.getRecords());
        return productSkuPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addProductV2(EcpProductAddDTO addDTO) {
        boolean isMain = true;
        for (EcpProductSkuAddDTO item : addDTO.getAddDTOS()) {
            if(isMain){
                item.setIsMain(1);
                isMain = false;
            }else{
                item.setIsMain(0);
            }
            item.setSellPrice(BigDecimal.ZERO);
            item.setLandingPrice(BigDecimal.ZERO);
            item.setCostPrice(BigDecimal.ZERO);
            item.setTenantId(tenantUtil.getTenantIdFromRequest());
        }
        addDTO.setInsteadSend(0);
        addDTO.setIsNew(true);
        this.hanlderAddData(addDTO);
        for (EcpProductSkuAddDTO dto : addDTO.getAddDTOS()) {
            ProductSkuPropUpdateDTO prop = new ProductSkuPropUpdateDTO();
            prop.setSkuId(dto.getSkuId());
            prop.setChannel(addDTO.getChannel());
            prop.setOperator(addDTO.getOperator());
            prop.setUnitDTOList(dto.getUnitDTOList());
            prop.setIsNew(true);
            productSkuService.setLogisticPropUpdate(prop);
            ProductSiteEditDTO site = new ProductSiteEditDTO();
            site.setSkuId(dto.getSkuId());
            site.setChannel(addDTO.getChannel());
            site.setOperator(addDTO.getOperator());
            site.setSiteIdList(dto.getSiteIdList());
            productSkuService.siteSetUpdate(site);
            ProductDepotEditDTO depot = new ProductDepotEditDTO();
            depot.setSkuId(dto.getSkuId());
            depot.setChannel(addDTO.getChannel());
            depot.setOperator(addDTO.getOperator());
            depot.setDepotIdList(dto.getDepotIdList());
            productDepotService.depotSetUpdate(depot);
        }
    }

    @Override
    public ProductDetailVO getProductDetailV2(Long id) {
        ProductDetailVO detail = this.getDetail(id);
        List<ProductSkuDTO> productSkuPOList = detail.getProductSkuPOList();
        if(CollectionUtils.isEmpty(productSkuPOList)){
            return detail;
        }
        List<String> supplierCodeList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(detail.getSupplierPOS())) {
            supplierCodeList = detail.getSupplierPOS().stream().map(SupplierPO::getSupplierCode).collect(Collectors.toList());
        }
        for (ProductSkuDTO productSkuDTO : productSkuPOList) {
            ProductSkuLogsiticVO productSkuLogsiticVO = productSkuService.logisticQuery(productSkuDTO.getSkuId());
            if(CollectionUtils.isNotEmpty(supplierCodeList)) {
                ProductSiteDetailVo productSiteDetailVo = productSkuService.productSiteQuery(productSkuDTO.getSkuId(), supplierCodeList);
                productSkuDTO.setSiteVos(productSiteDetailVo.getSiteVos());
            }
            ProductDepotDetailVo productDepotDetailVo = productDepotService.productDepotQuery(productSkuDTO.getSkuId());
            productSkuDTO.setUnitVOS(productSkuLogsiticVO.getUnitVOS());
            productSkuDTO.setDepotVos(productDepotDetailVo.getDepotVos());
        }
        return detail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductV2(EcpProductEditDTO editDTO) {
        EcpProductPO byId = productRepo.getById(editDTO.getId());
        if(Objects.isNull(byId)){
            return;
        }
        //判断是否上架 上架则不能修改
        if(Objects.equals(byId.getProductStatus(), ProductStatusEnum.ON.getKey())){
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "货品上架中，不能编辑！");
        }
        editDTO.setIsNew(true);
        handlerUpdateData(editDTO, byId);
        for (EcpProductSkuEditDTO dto : editDTO.getAddDTOS()) {
            ProductSkuPropUpdateDTO prop = new ProductSkuPropUpdateDTO();
            prop.setSkuId(dto.getSkuId());
            prop.setChannel(editDTO.getChannel());
            prop.setOperator(editDTO.getOperator());
            prop.setUnitDTOList(dto.getUnitDTOList());
            prop.setIsNew(true);
            productSkuService.setLogisticPropUpdate(prop);
            ProductSiteEditDTO site = new ProductSiteEditDTO();
            site.setSkuId(dto.getSkuId());
            site.setChannel(editDTO.getChannel());
            site.setOperator(editDTO.getOperator());
            site.setSiteIdList(dto.getSiteIdList());
            productSkuService.siteSetUpdate(site);
            ProductDepotEditDTO depot = new ProductDepotEditDTO();
            depot.setSkuId(dto.getSkuId());
            depot.setChannel(editDTO.getChannel());
            depot.setOperator(editDTO.getOperator());
            depot.setDepotIdList(dto.getDepotIdList());
            productDepotService.depotSetUpdate(depot);
        }
        //上架校验
        BatchProductIds batchProductIds = new BatchProductIds();
        batchProductIds.setProductIds(Collections.singletonList(editDTO.getId()));
        if (!Objects.equals(editDTO.getProductStatus(),byId.getProductStatus())) {
            changeStatus(batchProductIds,ProductStatusEnum.getEnumFromKey(editDTO.getProductStatus()));
        }
    }

    @Override
    public BigDecimal getWeightByUnit(String weight,String unit){
        BigDecimal weightDecimal = new BigDecimal(weight);
        BigDecimal skuUnitWeight = weightDecimal
                .multiply(BigDecimal.valueOf(UnitTypeEnum.getByUnit(unit).getWeightKG()));
        return skuUnitWeight;
    }

    @Override
    @Transactional
    public void refreshUnit(MultipartFile file) {
        EasyPoiUtil easyPoiUtil = new EasyPoiUtil();
        Set<ErpRefreshUnitDTO> erpRefreshUnitDTOS = easyPoiUtil.readAllDatas(file, ErpRefreshUnitDTO.class);
        if (CollectionUtils.isEmpty(erpRefreshUnitDTOS)) {
            throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "上传的文件无数据！");
        }
        //库存单位字典
        List<DictionaryItemVO> dicList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.WMS_UNIT_NAME);
        Map<String, DictionaryItemVO> unitMap = dicList.stream().collect(toMap(DictionaryItemVO::getItemName, Function.identity(), (c, n) -> n));
        List<EcpProductSkuPO> skuPOS = productSkuRepo.getProSkuBySkuId(erpRefreshUnitDTOS.stream().map(ErpRefreshUnitDTO::getSkuId).collect(Collectors.toList()));
        Map<String, EcpProductSkuPO> skuMap = skuPOS.stream().collect(toMap(EcpProductSkuPO::getSkuId, Function.identity(), (c, n) -> n));
        //查看传入数据是否有箱瓶对应关系
        Map<String, List<ErpRefreshUnitDTO>> erpRefreshUnitDTOMap = erpRefreshUnitDTOS.stream().collect(Collectors.groupingBy(ErpRefreshUnitDTO::getSkuId));
        for(Map.Entry<String, List<ErpRefreshUnitDTO>> entry : erpRefreshUnitDTOMap.entrySet()){
            List<ErpRefreshUnitDTO> list = entry.getValue();
            if(list.size() != 2){
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "请检查skuId:"+ entry.getKey() +"对应箱和瓶规格数据是否都已经维护！");
            }
            Map<String, ErpRefreshUnitDTO> ErpRefreshUnitDTOMap = list.stream().collect(toMap(ErpRefreshUnitDTO::getUnitName, Function.identity(), (c, n) -> n));
            ErpRefreshUnitDTO box = ErpRefreshUnitDTOMap.get("箱");
            ErpRefreshUnitDTO bottle = ErpRefreshUnitDTOMap.get("瓶");
            if(box == null){
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "请检查skuId:"+ entry.getKey() +"箱规格数据是否都已经维护！");
            }
            if(bottle == null){
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "请检查skuId:"+ entry.getKey() +"瓶规格数据是否都已经维护！");
            }
            BigDecimal boxWeight = box.getWeight();
            BigDecimal bottleWeight = bottle.getWeight().multiply(box.getPackageNum());
            if(boxWeight.compareTo(bottleWeight) != 0){
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "请检查skuId:"+ entry.getKey() +"箱和瓶重量关系不匹配！");
            }
        }
        List<EcpProductSkuUnitPO> update = new ArrayList<>();
        List<EcpProductSkuUnitPO> insert = new ArrayList<>();
        //刷数sku不为箱和瓶的数据不能未库存单位
        for (EcpProductSkuPO ecpProductSkuPO : skuPOS) {
            List<EcpProductSkuUnitPO> skuUnitPOList = productSkuUnitRepo.lambdaQuery().eq(EcpProductSkuUnitPO::getSkuId, ecpProductSkuPO.getSkuId())
                    .eq(EcpProductSkuUnitPO::getUnitType, 1)
                    .list();
            for(EcpProductSkuUnitPO ecpProductSkuUnitPO : skuUnitPOList){
                if(!basicsConfig.getUnitNameList().contains(ecpProductSkuUnitPO.getUnitName())){
                    ecpProductSkuUnitPO.setUnitType(3);
                    update.add(ecpProductSkuUnitPO);
                }
            }
        }
        for (ErpRefreshUnitDTO erpRefreshUnitDTO : erpRefreshUnitDTOS) {

            if(!basicsConfig.getUnitNameList().contains(erpRefreshUnitDTO.getUnitName())){
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "仅支持规格为"+ JSON.toJSONString(basicsConfig.getUnitNameList()) +"的规格单位！");
            }
            DictionaryItemVO dictionaryItemVO = unitMap.get(erpRefreshUnitDTO.getUnitName());
            if(dictionaryItemVO == null){
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "上传的单位:"+ erpRefreshUnitDTO.getUnitName() +"名称有误！");
            }
            EcpProductSkuPO skuPO = skuMap.get(erpRefreshUnitDTO.getSkuId());
            if(skuPO == null){
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "上传的货品skuId:"+ erpRefreshUnitDTO.getSkuId() +"不存在！");
            }
            if(!basicsConfig.getTenantIdList().contains(skuPO.getTenantId())){
                throw new BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "上传的货品skuId:"+ erpRefreshUnitDTO.getSkuId() +"不属于酒水租户！");
            }
            erpRefreshUnitDTO.setUnitCode(dictionaryItemVO.getItemCode());
            erpRefreshUnitDTO.setTenantId(skuPO.getTenantId());
            EcpProductSkuUnitPO ecpProductSkuUnitPO = productSkuUnitRepo.lambdaQuery().eq(EcpProductSkuUnitPO::getSkuId, erpRefreshUnitDTO.getSkuId())
                    .eq(EcpProductSkuUnitPO::getUnitCode, dictionaryItemVO.getItemCode())
                    .one();
            if(ecpProductSkuUnitPO == null){
                ecpProductSkuUnitPO = new EcpProductSkuUnitPO();
                ecpProductSkuUnitPO.setSkuId(erpRefreshUnitDTO.getSkuId());
                ecpProductSkuUnitPO.setUnitCode(dictionaryItemVO.getItemCode());
                ecpProductSkuUnitPO.setUnitName(dictionaryItemVO.getItemName());
                ecpProductSkuUnitPO.setPackageNum(erpRefreshUnitDTO.getPackageNum());
                ecpProductSkuUnitPO.setWeight(erpRefreshUnitDTO.getWeight());
                ecpProductSkuUnitPO.setSkuUnitCode(smartIdHelper.generator(SmartIdTypeEnum.SKU_UNIT_CODE));
                ecpProductSkuUnitPO.setTenantId(skuPO.getTenantId());
                ecpProductSkuUnitPO.setIsOpen(1);
                if(dictionaryItemVO.getItemName().equals("箱")){
                    ecpProductSkuUnitPO.setUnitType(3);
                    ecpProductSkuUnitPO.setFinanceSellFlag(false);
                    ecpProductSkuUnitPO.setFinancePurchaseFlag(false);
                }else {
                    ecpProductSkuUnitPO.setUnitType(1);
                    ecpProductSkuUnitPO.setFinanceSellFlag(true);
                    ecpProductSkuUnitPO.setFinancePurchaseFlag(true);
                }
                insert.add(ecpProductSkuUnitPO);
            }else {
                //财务单位要与库存单位一致，把箱绑定的财务单位刷掉
                if("箱".equals(ecpProductSkuUnitPO.getUnitName())){
                    ecpProductSkuUnitPO.setUnitType(3);
                    ecpProductSkuUnitPO.setFinanceSellFlag(false);
                    ecpProductSkuUnitPO.setFinancePurchaseFlag(false);
                }else {
                    ecpProductSkuUnitPO.setUnitType(1);
                    ecpProductSkuUnitPO.setFinanceSellFlag(true);
                    ecpProductSkuUnitPO.setFinancePurchaseFlag(true);
                }
                ecpProductSkuUnitPO.setPackageNum(erpRefreshUnitDTO.getPackageNum());
                ecpProductSkuUnitPO.setWeight(erpRefreshUnitDTO.getWeight());
                update.add(ecpProductSkuUnitPO);
            }
        }
//        //还需要添加非箱非瓶数据
//        Set<String> skuIds = erpRefreshUnitDTOS.stream().map(ErpRefreshUnitDTO::getSkuId).collect(Collectors.toSet());
//        List<EcpProductSkuUnitPO> ecpProductSkuUnitPOS = productSkuUnitRepo.lambdaQuery()
//                .eq(EcpProductSkuUnitPO::getIfDelete, 0)
//                .in(EcpProductSkuUnitPO::getTenantId,  basicsConfig.getTenantIdList())
//                .notIn(EcpProductSkuUnitPO::getUnitName, basicsConfig.getUnitNameList())
//                .in(EcpProductSkuUnitPO::getSkuId, skuIds)
//                .list();
//        for(EcpProductSkuUnitPO ecpProductSkuUnitPO : ecpProductSkuUnitPOS){
//            List<String> unitTypeCodeList = productSkuUnitTypeRepo.lambdaQuery().eq(ErpProductSkuUnitTypePO::getSkuUnitCode, ecpProductSkuUnitPO.getSkuUnitCode())
//                    .list()
//                    .stream().map(ErpProductSkuUnitTypePO::getUnitTypeCode).collect(Collectors.toList());
//            ecpProductSkuUnitPO.setUnitTypeCodeList(unitTypeCodeList);
//        }
//        update.addAll(ecpProductSkuUnitPOS);

        if(CollectionUtils.isNotEmpty(insert)) {
            productSkuUnitRepo.saveBatch(insert);
        }
        if(CollectionUtils.isNotEmpty(update)) {
            productSkuUnitRepo.updateBatchById(update);
        }
        if(CollectionUtils.isNotEmpty(insert) || CollectionUtils.isNotEmpty(update)) {
            productSkuService.addErpProductSkuUnitType(insert, update, true);
        }
    }

    @Override
    public void refreshExceptionUnit(List<String> skuIds) {

        Long current=1L;
        Long pageSize=200L;
        if(CollectionUtils.isEmpty(skuIds)){
            return;
        }
        while(true){

            Page<EcpProductSkuUnitPO> ecpProductSkuUnitPOS = productSkuUnitRepo.lambdaQuery()
                    .eq(EcpProductSkuUnitPO::getIfDelete, 0)
                    .in(EcpProductSkuUnitPO::getSkuId, skuIds)
                    .in(EcpProductSkuUnitPO::getTenantId,  basicsConfig.getTenantIdList())
                    .orderByAsc(EcpProductSkuUnitPO::getId)
                    .page(new Page(current,pageSize));
            current++;
            if (ecpProductSkuUnitPOS.getRecords().isEmpty()) {
                break;
            }
            //获取规格单位类型集合
            List<ErpProductSkuUnitTypePO> productSkuUnitTypeList = productSkuUnitTypeRepo.lambdaQuery().in(ErpProductSkuUnitTypePO::getSkuUnitCode, ecpProductSkuUnitPOS.getRecords()
                    .stream().map(EcpProductSkuUnitPO::getSkuUnitCode).collect(Collectors.toList())).list();
            List<ErpProductSkuUnitTypePO> insert = new ArrayList<>();
            for(EcpProductSkuUnitPO ecpProductSkuUnitPO : ecpProductSkuUnitPOS.getRecords()){
                Integer unitType = ecpProductSkuUnitPO.getUnitType();
                ErpProductSkuUnitTypePO skuUnitTypePO = new ErpProductSkuUnitTypePO();
                skuUnitTypePO.setSkuUnitCode(ecpProductSkuUnitPO.getSkuUnitCode());
                skuUnitTypePO.setTenantId(ecpProductSkuUnitPO.getTenantId());
                if(Objects.equals(unitType, SkuUnitTypeEnum.STOCK.getValue())){
                    skuUnitTypePO.setUnitTypeCode(ErpUnitTypeEnum.STOCK_UNIT.getUnitTypeCode());
                    insert.add(skuUnitTypePO);
                }else  if(Objects.equals(unitType, SkuUnitTypeEnum.FINANCE.getValue())){
                    skuUnitTypePO.setUnitTypeCode(ErpUnitTypeEnum.CARPOOL_UNIT.getUnitTypeCode());
                    insert.add(skuUnitTypePO);
                }else  if(Objects.equals(unitType, SkuUnitTypeEnum.SALE.getValue())){
                    skuUnitTypePO.setUnitTypeCode(ErpUnitTypeEnum.SELL_UNIT.getUnitTypeCode());
                    insert.add(skuUnitTypePO);
                }
                if(ecpProductSkuUnitPO.getFinanceSellFlag()){
                    ErpProductSkuUnitTypePO skuUnitTypePO1 = new ErpProductSkuUnitTypePO();
                    skuUnitTypePO1.setSkuUnitCode(ecpProductSkuUnitPO.getSkuUnitCode());
                    skuUnitTypePO1.setUnitTypeCode(ErpUnitTypeEnum.FINANCE_UNIT.getUnitTypeCode());
                    skuUnitTypePO1.setTenantId(ecpProductSkuUnitPO.getTenantId());
                    insert.add(skuUnitTypePO1);
                }
                if(ecpProductSkuUnitPO.getFinancePurchaseFlag()){
                    ErpProductSkuUnitTypePO tansfer = new ErpProductSkuUnitTypePO();
                    tansfer.setSkuUnitCode(ecpProductSkuUnitPO.getSkuUnitCode());
                    tansfer.setUnitTypeCode(ErpUnitTypeEnum.TRANSFER_UNIT.getUnitTypeCode());
                    tansfer.setTenantId(ecpProductSkuUnitPO.getTenantId());
                    insert.add(tansfer);

                    ErpProductSkuUnitTypePO check = new ErpProductSkuUnitTypePO();
                    check.setSkuUnitCode(ecpProductSkuUnitPO.getSkuUnitCode());
                    check.setUnitTypeCode(ErpUnitTypeEnum.CHECK_UNIT.getUnitTypeCode());
                    check.setTenantId(ecpProductSkuUnitPO.getTenantId());
                    insert.add(check);

                    ErpProductSkuUnitTypePO apply = new ErpProductSkuUnitTypePO();
                    apply.setSkuUnitCode(ecpProductSkuUnitPO.getSkuUnitCode());
                    apply.setUnitTypeCode(ErpUnitTypeEnum.APPLY_UNIT.getUnitTypeCode());
                    apply.setTenantId(ecpProductSkuUnitPO.getTenantId());
                    insert.add(apply);

                    ErpProductSkuUnitTypePO skuUnitTypePO1 = new ErpProductSkuUnitTypePO();
                    skuUnitTypePO1.setSkuUnitCode(ecpProductSkuUnitPO.getSkuUnitCode());
                    skuUnitTypePO1.setUnitTypeCode(ErpConstants.PROCURE_UNIT);
                    skuUnitTypePO1.setTenantId(ecpProductSkuUnitPO.getTenantId());
                    insert.add(skuUnitTypePO1);
                    //初始调货单位
                    ErpProductSkuUnitTypePO oderUnitTypePO = new ErpProductSkuUnitTypePO();
                    oderUnitTypePO.setSkuUnitCode(ecpProductSkuUnitPO.getSkuUnitCode());
                    oderUnitTypePO.setTenantId(ecpProductSkuUnitPO.getTenantId());
                    oderUnitTypePO.setUnitTypeCode(ErpUnitTypeEnum.ORDER_UNIT.getUnitTypeCode());
                    insert.add(oderUnitTypePO);
                    //初始仓储单位
                    ErpProductSkuUnitTypePO storageUnitTypePO = new ErpProductSkuUnitTypePO();
                    storageUnitTypePO.setSkuUnitCode(ecpProductSkuUnitPO.getSkuUnitCode());
                    storageUnitTypePO.setTenantId(ecpProductSkuUnitPO.getTenantId());
                    storageUnitTypePO.setUnitTypeCode(ErpUnitTypeEnum.STORAGE_UNIT.getUnitTypeCode());
                    insert.add(storageUnitTypePO);
                }
            }
            insert=insert.stream().filter(item->productSkuUnitTypeList.stream().noneMatch(temp->
                    Objects.equals(item.getSkuUnitCode(),temp.getSkuUnitCode())
                            &&Objects.equals(item.getUnitTypeCode(),temp.getUnitTypeCode()))).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(insert)){
                productSkuUnitTypeRepo.saveBatch(insert);
            }
        }
    }

    @Override
    public FileDTO exportProductListV2(EcpProductQueryDTO productQueryDTO) {
        ErpUserPO user = tenantUtil.getUser();
        return distributeLockUtils.lockAndProcess(
                Arrays.asList("export_product_excel_lock_" + user.getEmployNo()),
                60, -1, TimeUnit.SECONDS,
                () -> asynExportProductList(productQueryDTO)
        );
    }


    private FileDTO asynExportProductList(EcpProductQueryDTO productQueryDTO) {
        validateAndAssignProductQueryDTO(productQueryDTO);
        ErpUserPO user = tenantUtil.getUser();
        Optional<JwtUserInfo> userInfo = jwtUserInfoService.getJwtUserInfo();
        productQueryDTO.setTenantId(user.getTenantId());
        // 生成文件地址
        String bizModule = "ERP货品管理";
        String fileName = "货品管理-货品导出" + DateUtil.format(new Date(), DateUtil.FORMAT_DATE_YMD) + "-" + new Random().nextInt(9999) + ".xlsx";
        FileDTO fileDTO = fileCenterIntegration.generateFileURLDTO(bizModule, fileName);
        log.info("asyncExcelExport fileName = {} , fileUrl = {}", fileName, fileDTO.getFileUrl());
        // 插入流水记录
        FileOperationVO fileOperationVO = new FileOperationVO();
        fileOperationVO.setFileUrl(fileDTO.getFileUrl());
        fileOperationVO.setApplicationCondition("");
        fileOperationVO.setFileName(fileName);
        fileOperationVO.setUserType(1);
        fileOperationVO.setUserId(Long.valueOf(userInfo.get().getUserId()));
        fileOperationVO.setBizModule(bizModule);
        fileOperationVO.setUserName(user.getEmployName());;
        Long fileRecordPkId = fileCenterIntegration.insertFileDownloadRecords(fileOperationVO);
        fileDTO.setFileOperationRecordsPkId(fileRecordPkId);
        // 异步导出
        executorUtil.doMethodWithRequest(() -> fileCenterIntegration.uploadProductToOSS(productQueryDTO, bizModule, fileName, fileRecordPkId));
        return fileDTO;
    }

    private void validateAndAssignProductQueryDTO(EcpProductQueryDTO productQueryDTO) {
        if (productQueryDTO.getMainSupplierId() != null) {
            List<String> productBySupplier = productSupplierRepo.getProductBySupplier(productQueryDTO.getMainSupplierId());
            if (CollectionUtil.isEmpty(productBySupplier)) {
                throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.getCode(), "该供应商未查询出货品");
            }
            productQueryDTO.setSupplierSpuIdList(productBySupplier);
        }
        //获取货品品牌字典
        List<DictionaryItemVO> dicBrandList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.WMS_BRAND);
        productQueryDTO.setDicBrandList(dicBrandList);

        //品牌字典
        List<DictionaryItemVO> dicProductTypeList = bmsIntegration.getDictionaryItemsByTypeCode(WmsDicConstants.WMS_PRODUCT_TYPE);
        productQueryDTO.setProductDictionaryItemVOList(dicProductTypeList);


    }

    @Override
    public Page<ErpProductDetailVo> queryProductDetailPageList(ErpProductDetailQuery productDetailQuery) {
        if (buildQuery(productDetailQuery)) {
            return new Page<>();
        }
        Page<ErpProductDetailVo> page = productRepo.queryProductDetailPageList(productDetailQuery);
        if (Objects.isNull(page)) {
            return new Page<>();
        }
        page.setRecords(handlerResult(page.getRecords()));
        return page;
    }
    public void checkInternalSupplierUpstreamProductInfo(ProductSupplierBatchDTO productSupplierBatchDTO){
        //租户信息
        ErpTenantPO tenant = tenantUtil.getCompanyCodeOrNameByTenantId(productSupplierBatchDTO.getTenantId());
        //获取货品供应商信息
        List<EcpProductSupplierPO> productSupplierList=productSupplierRepo.getProductSupplierBySupIds(productSupplierBatchDTO.getSpuIdList());
        List<String> supplierCodeList=new ArrayList<>();
        supplierCodeList.add(productSupplierBatchDTO.getSupplierCode());
        Map<String,EcpProductSupplierPO> productSupplierPOMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(productSupplierList)){
            productSupplierPOMap=productSupplierList.stream().collect(Collectors.toMap(key->
                    String.format("%s:%s",key.getTenantId(),key.getSpuId()),Function.identity()));
            supplierCodeList.addAll(productSupplierList.stream().map(EcpProductSupplierPO::getSupplierCode).distinct().collect(Collectors.toList()));
        }
        //获取供应商信息
        List<SupplierPO> supplierList = supplierRepo.batchSupplier(supplierCodeList);
        if(CollectionUtils.isEmpty(supplierList)){
            throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.getCode(),"供应商信息不存在");
        }
        Map<String, SupplierPO> supplierMap = supplierList.stream().collect(toMap(SupplierPO::getSupplierCode, Function.identity()));
        SupplierPO supplier = supplierMap.get(productSupplierBatchDTO.getSupplierCode());
        //本租户货品供应商
        if(Objects.isNull(supplier.getIsInternal())
                || Objects.equals(supplier.getIsInternal(),InternalSupplierEnum.NON_INTERNAL_SUPPLIER.getValue())
                || StringUtils.isBlank(supplier.getInternalCompanyCode())
                || Objects.equals(supplier.getInternalCompanyCode(),tenant.getCompanyCode())){
            return;
        }
        List<EcpProductPO> productPOList = productRepo.getProductBySpuIdList(productSupplierBatchDTO.getSpuIdList());
        if(CollectionUtils.isEmpty(productPOList)){
            throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.getCode(),"货品信息不存在");
        }
        Map<String, String> productMap = productPOList.stream().collect(toMap(EcpProductPO::getSpuId, EcpProductPO::getProductName));
        for(String spuId:productSupplierBatchDTO.getSpuIdList()){
            this.checkInternalSupplierUpstreamProdInfo(spuId,supplierMap,supplier.getSupplierCode(),tenant.getCompanyCode(),productMap,productSupplierPOMap);
        }
    }
    public void checkInternalSupplierUpstreamProdInfo(String spuId,Map<String, SupplierPO> supplierMap,String supplierCode
            ,String companyCode,Map<String, String> productMap,Map<String,EcpProductSupplierPO> productSupplierPOMap){
        SupplierPO supplier = supplierMap.get(supplierCode);
        //本租户货品供应商
        if(Objects.isNull(supplier.getIsInternal())
                || Objects.equals(supplier.getIsInternal(),InternalSupplierEnum.NON_INTERNAL_SUPPLIER.getValue())
                || StringUtils.isBlank(supplier.getInternalCompanyCode())
                || Objects.equals(supplier.getInternalCompanyCode(),companyCode)){
            return;
        }
        ErpTenantPO upstreamTenant = tenantUtil.getTenantByCompanyCodeOrName(supplier.getInternalCompanyCode());
        EcpProductSupplierPO productSupplier = productSupplierPOMap.get(String.format("%s:%s", upstreamTenant.getTenantId(), spuId));
        if(Objects.isNull(productSupplier)){
            throw new BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.getCode(),
                    "组织："+upstreamTenant.getCompanyName()+",需先完善"+productMap.get(spuId)+"信息");
        }else{
            this.checkInternalSupplierUpstreamProdInfo(spuId,supplierMap,productSupplier.getSupplierCode(),upstreamTenant.getCompanyCode(),productMap,productSupplierPOMap);
        }
    }
    @Override
    @Transactional
    public Boolean productSupplierBatch(@Valid ProductSupplierBatchDTO productSupplierBatchDTO) {
        List<EcpProductSupplierPO> list = productSupplierRepo.getBySupIdsAndSupplier(productSupplierBatchDTO.getSpuIdList(), productSupplierBatchDTO.getSupplierCode());
        if(CollectionUtils.isNotEmpty(list)) {
            String spuId = list.get(0).getSpuId();
            EcpProductPO productPO = productRepo.getProductBySpuId(spuId);
            throw new BusinessException(productPO.getProductName() + "货品已有供应商、不能修改，请勿勾选");
        }
        //校验内采供应商的上游货品信息
        this.checkInternalSupplierUpstreamProductInfo(productSupplierBatchDTO);
        List<EcpProductSupplierPO> addList = new ArrayList<>();
        productSupplierBatchDTO.getSpuIdList().forEach(supId -> {
            EcpProductSupplierPO ecpProductSupplierPO = new EcpProductSupplierPO();
            ecpProductSupplierPO.setSupplierCode(productSupplierBatchDTO.getSupplierCode());
            ecpProductSupplierPO.setSpuId(supId);
            addList.add(ecpProductSupplierPO);

        });
        productSupplierRepo.saveBatch(addList);
        List<EcpProductPO> productPOList = productRepo.getProductBySpuIdList(productSupplierBatchDTO.getSpuIdList());
        for(EcpProductPO ecpProductPO : productPOList) {
            productChangeLogService.addLog(ecpProductPO.getId(), ProductLogOperateType.SUPPLIER_BINDING, IProductChangeLogService.SUCCESS,
                    IProductChangeLogService.SUCCESS, tenantUtil.getUserName(), null);
        }
        //查询sup下的全部sku,供应商下面的全部发货站，排除该发货站下的绑定的sku,将sku+发货站进行绑定
        List<SitePo> siteList = siteRepo.getEnableSitesBySupplierCodes(Collections.singletonList(productSupplierBatchDTO.getSupplierCode()));
        if(CollectionUtils.isEmpty(siteList)) {
            log.warn("{},该供应商没有发货站",productSupplierBatchDTO.getSupplierCode());
            return Boolean.TRUE;
        }
        //过滤掉自动创建的发货站
        siteList=siteList.stream().filter(item->!Objects.equals("系统自动化创建",item.getCreateUserName())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(siteList)) {
            log.warn("{},该供应商没有发货站",productSupplierBatchDTO.getSupplierCode());
            return Boolean.TRUE;
        }
        List<Long> siteIdList = siteList.stream().map(SitePo::getId).collect(Collectors.toList());
        List<EcpProductSitePO> productSitePOList = ecpProductSiteRepo.findSkuIdBySiteIds(siteIdList);

        Map<String,List<EcpProductSitePO>> skuSitePOMap = productSitePOList.stream().collect(Collectors.groupingBy(x -> x.getSkuId() + "_" + x.getSiteId()));


        List<EcpProductSkuPO> skuList = productRepo.getProductSkuBySpuIdList(productSupplierBatchDTO.getSpuIdList());
        //根据spu分组
        Map<Long,List<EcpProductSkuPO>> productIdMap = skuList.stream().collect(Collectors.groupingBy(EcpProductSkuPO::getProductId));

        List<EcpProductSitePO> addSiteList = new ArrayList<>();
        for(Map.Entry<Long, List<EcpProductSkuPO>> entry : productIdMap.entrySet()) {
            Long productId = entry.getKey();
            List<EcpProductSkuPO> skuPOList = entry.getValue();
            StringBuilder remark = new StringBuilder();
            StringBuilder fhz = new StringBuilder();
            if(CollectionUtils.isEmpty(skuPOList)) {
                continue;
            }
            for(EcpProductSkuPO skuPO : skuPOList) {
                List<String> fhzSiteList = new ArrayList<>();
                for(SitePo sitePO : siteList) {
                    String key = skuPO.getSkuId() + "_" + sitePO.getId();
                    if(!skuSitePOMap.containsKey(key)) {
                        EcpProductSitePO ecpProductSitePO = new EcpProductSitePO();
                        ecpProductSitePO.setSkuId(skuPO.getSkuId());
                        ecpProductSitePO.setSiteId(sitePO.getId());
                        addSiteList.add(ecpProductSitePO);
                        fhzSiteList.add(sitePO.getSiteName());
                        fhz.append(sitePO.getSiteName()).append(",");
                    }
                }
                if(CollectionUtils.isNotEmpty(fhzSiteList)) {
                    remark.append(skuPO.getSkuId()).append("绑定了").append(fhzSiteList).append(" 发货站;");
                }
            }
            if(StringUtils.isNotBlank(remark.toString())) {
                productChangeLogService.addLog(productId, ProductLogOperateType.SKU_SETTING, IProductChangeLogService.SUCCESS,
                        remark.toString(), tenantUtil.getUserName(), null);
            }
        }
        if(CollectionUtils.isNotEmpty(addSiteList)) {
            ecpProductSiteRepo.saveBatch(addSiteList);
        }

        return Boolean.TRUE;
    }


}
