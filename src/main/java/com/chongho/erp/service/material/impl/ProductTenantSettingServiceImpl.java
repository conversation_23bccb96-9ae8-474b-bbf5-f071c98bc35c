package com.chongho.erp.service.material.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.chongho.erp.common.enums.material.OperationChannel;
import com.chongho.erp.common.enums.material.ProductLogOperateType;
import com.chongho.erp.common.enums.product.DistributionFlagEnum;
import com.chongho.erp.dto.material.ChangeDistributeFlagDTO;
import com.chongho.erp.po.ErpUserPO;
import com.chongho.erp.po.material.EcpProductTenantSettingPO;
import com.chongho.erp.repository.material.EcpProductTenantSettingRepo;
import com.chongho.erp.service.material.IProductChangeLogService;
import com.chongho.erp.service.material.ProductTenantSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ProductTenantSettingServiceImpl implements ProductTenantSettingService {
    @Autowired
    private EcpProductTenantSettingRepo productTenantSettingRepo;

    @Autowired
    private IProductChangeLogService productChangeLog;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeDistributeFlag(ChangeDistributeFlagDTO dto, ErpUserPO user) {
        List<EcpProductTenantSettingPO> list = productTenantSettingRepo.getListByProductIds(dto.getProductIds());
        List<Long> insertProductIds = dto.getProductIds();
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(po -> {
                po.setDistributionFlag(dto.getDistributionFlag());
            });
            productTenantSettingRepo.updateBatchById(list);

            List<Long> updateProductIds = list.stream().map(EcpProductTenantSettingPO::getProductId).collect(Collectors.toList());
            insertProductIds = dto.getProductIds().stream().filter(id -> !updateProductIds.contains(id)).collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(insertProductIds)) {
            List<EcpProductTenantSettingPO> insertList = insertProductIds.stream().map(id -> {
                EcpProductTenantSettingPO po = new EcpProductTenantSettingPO();
                po.setProductId(id);
                po.setDistributionFlag(dto.getDistributionFlag());
                return po;
            }).collect(Collectors.toList());
            productTenantSettingRepo.saveBatch(insertList);
        }
        // 批量写入轨迹
        ProductLogOperateType type = DistributionFlagEnum.OPEN.getValue().equals(dto.getDistributionFlag()) ? ProductLogOperateType.DISTRIBUTE_FLAG_OPEN : ProductLogOperateType.DISTRIBUTE_FLAG_CLOSE;
        productChangeLog.addLogList(dto.getProductIds(), type, IProductChangeLogService.SUCCESS,
                "置为“" + type.getDesc() + "”状态", user.getUsername(),  OperationChannel.ERP, user.getTenantId());
    }
}
