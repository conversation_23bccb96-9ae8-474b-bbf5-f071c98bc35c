package com.chongho.erp.service.stock.actual.impl;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.framework.autoconfigure.common.exception.BusinessException;
import com.chongho.erp.dto.depot.ErpDepotBatchSkuStockDTO;
import com.chongho.erp.dto.depot.ErpDepotItemDTO;
import com.chongho.erp.po.ErpDepotPO;
import com.chongho.erp.po.ErpMaterialCurrentStockPO;
import com.chongho.erp.po.actualStock.ErpDepotBatchSkuStockPO;
import com.chongho.erp.repository.actualStock.ErpDepotBatchSkuStockRepo;
import com.chongho.erp.service.ErpDepotRepo;
import com.chongho.erp.service.ErpMaterialCurrentStockRepo;
import com.chongho.erp.service.stock.actual.ErpActualStockCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by liuhao  on 2022-09-13 20:10:13
 */
@Service
@Slf4j
public class ErpActualStockCommonServiceImpl implements ErpActualStockCommonService {
    @Autowired
    private ErpDepotBatchSkuStockRepo depotBatchSkuStockRepo;
    @Autowired
    private ErpMaterialCurrentStockRepo materialCurrentStockRepo;
    @Autowired
    private ErpDepotRepo depotRepo;
    @Override
    public List<?> actualStockBatchDistribute(List<?> items,Map<String, List<ErpDepotBatchSkuStockPO>> stockMap,Map<Long,String> depotMap) {
        log.info("items:{},stockMap:{},depotMap",JSONObject.toJSONString(items),JSONObject.toJSONString(stockMap),depotMap);
        List<Object> newItems=new ArrayList<>();
        for (Object item : items) {
            BigDecimal changeNum=BigDecimal.ZERO;
            Long depotId=null;
            String skuId=null;
            if (item instanceof ErpDepotItemDTO) {
                ErpDepotItemDTO info = (ErpDepotItemDTO) item;
                depotId= info.getDepotId();
                skuId= info.getSkuId();
                changeNum=info.getOperNumber();
            }
            //获取仓库商品批次实物库存
            List<ErpDepotBatchSkuStockPO> depotBatchSkuStockList = stockMap.get(String.format("%s:%s",depotId,skuId));
            if(CollectionUtils.isEmpty(depotBatchSkuStockList)){
                throw new BusinessException("仓库："+depotMap.get(depotId)+",规格编码："+skuId+",实物库存不足!");
            }
            for (ErpDepotBatchSkuStockPO depotBatchSkuStockPO : depotBatchSkuStockList) {
                if(BigDecimal.ZERO.compareTo(changeNum)==0){
                    break;
                }
                if(BigDecimal.ZERO.compareTo(depotBatchSkuStockPO.getUseAbleStock())==0){
                    continue;
                }
                BigDecimal currentNum=BigDecimal.ZERO;
                if(depotBatchSkuStockPO.getUseAbleStock().compareTo(changeNum)>0){
                    depotBatchSkuStockPO.setUseAbleStock(depotBatchSkuStockPO.getUseAbleStock().subtract(changeNum));
                    currentNum=changeNum;
                    changeNum=BigDecimal.ZERO;
                }else{
                    currentNum=depotBatchSkuStockPO.getUseAbleStock();
                    changeNum=changeNum.subtract(depotBatchSkuStockPO.getUseAbleStock());
                    depotBatchSkuStockPO.setUseAbleStock(BigDecimal.ZERO);
                }
                if (item instanceof ErpDepotItemDTO) {
                    ErpDepotItemDTO info = (ErpDepotItemDTO) item;
                    ErpDepotItemDTO depotItemDTO =new ErpDepotItemDTO();
                    BeanUtils.copyProperties(info,depotItemDTO);
                    depotItemDTO.setBatchNumber(depotBatchSkuStockPO.getBatchNumber());
                    depotItemDTO.setOperNumber(currentNum);
                    newItems.add(depotItemDTO);
                }
            }
            if(BigDecimal.ZERO.compareTo(changeNum)!=0){
                throw new BusinessException("仓库："+depotMap.get(depotId)+",规格编码："+skuId+",实物库存不足!");
            }
        }
        log.info("newItems:{}", JSONObject.toJSONString(newItems));
        return newItems;
    }
    @Override
    public void checkDepotSkuStock(List<?> items,Map<Long,String> depotMap) {
        List<Long> depotIdList=new ArrayList<>();
        List<String> skuIdList=new ArrayList<>();
        for (Object item : items) {
            if (item instanceof ErpDepotItemDTO) {
                ErpDepotItemDTO info = (ErpDepotItemDTO) item;
                depotIdList.add(info.getDepotId());
                skuIdList.add(info.getSkuId());
            }
        }
        //获取仓库名称
        //获取仓库商品实物库存
        List<ErpMaterialCurrentStockPO> skuStockList = materialCurrentStockRepo.getDepotSkuCurrentStockList(depotIdList, skuIdList);
        if(CollectionUtils.isEmpty(skuStockList)){
            String msg="";
            if(!CollectionUtils.isEmpty(depotMap.values())){
                msg=msg+"仓库："+depotMap.values().stream().collect(Collectors.joining(","))+",";
            }
            msg=msg+"仓库商品实物库存不足";
            throw new BusinessException(msg);
        }
        Map<String, BigDecimal> skuStockMap = skuStockList.stream().collect(Collectors.toMap(key -> String.format("%s:%s", key.getDepotId(), key.getSkuId()), ErpMaterialCurrentStockPO::getCurrentNumber));
        for (Object item : items) {
            if (item instanceof ErpDepotItemDTO) {
                ErpDepotItemDTO info = (ErpDepotItemDTO) item;
                String key = String.format("%s:%s", info.getDepotId(), info.getSkuId());
                BigDecimal skuStock = skuStockMap.get(key);
                BigDecimal cutNum=info.getOperNumber();
                if(cutNum.compareTo(skuStock)>0){
                    throw new BusinessException("仓库："+depotMap.get(info.getDepotId())+",规格编码："+info.getSkuId()+",实物库存不足,实物库存为："+skuStock);
                }
                skuStockMap.put(key,skuStock.subtract(cutNum));
            }
        }
    }
}
