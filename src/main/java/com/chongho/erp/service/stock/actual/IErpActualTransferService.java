package com.chongho.erp.service.stock.actual;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cfpamf.framework.autoconfigure.common.result.Result;
import com.cfpamf.ms.mall.filecenter.domain.dto.FileDTO;
import com.chongho.erp.common.util.FlowResult;
import com.chongho.erp.dto.depot.ErpActualAndSellCutInfoDTO;
import com.chongho.erp.dto.depot.ErpDepotMerchantInfoDTO;
import com.chongho.erp.dto.depot.ErpOrgTransferInfoDTO;
import com.chongho.erp.dto.ledger.TransferByLedgerBranchQuery;
import com.chongho.erp.dto.material.ProductSkuDetailDTO;
import com.chongho.erp.dto.mq.ErpMqBizFlowDTO;
import com.chongho.erp.dto.mq.ErpSkuRecallDTO;
import com.chongho.erp.dto.query.stock.ErpActualTransferFlowQuery;
import com.chongho.erp.dto.stock.actual.ErpApplyDTO;
import com.chongho.erp.dto.stock.actual.ErpTransferFlowDTO;
import com.chongho.erp.dto.stock.actual.ErpTransferSkuDTO;
import com.chongho.erp.exception.ErpFlowLockException;
import com.chongho.erp.po.stock.actual.ErpActualTransferPO;

import com.chongho.erp.vo.depot.ErpDepotVo;
import com.chongho.erp.vo.ledger.TransferByLedgerBranchVO;
import com.chongho.erp.vo.stock.actual.ErpActualTransferFlowAndDetailVO;
import com.chongho.erp.vo.stock.actual.ErpActualTransferFlowVO;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.chongho.erp.vo.stock.actual.ExportErpActualTransferFlowVO;
import com.chongho.erp.vo.stock.actual.ExportErpWineActualTransferFlowVO;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * Created by liuhao  on 2022-09-13 20:10:29
 */

public interface IErpActualTransferService extends IService<ErpActualTransferPO>{

    /**
     * description: 实仓-调拨分页列表
     *
     * @return Page<ErpActualTransferFlowVO>
     * @Param: query
    */
    Page<ErpActualTransferFlowVO> pageTransferFlow(ErpActualTransferFlowQuery query);
    /**
     * description: 实仓-调拨新增
     *
     * @return a
     * @Param: addDTO
    */
    Long addActualTransfer(ErpTransferFlowDTO addDTO);

    void addActualTransferNew(ErpTransferFlowDTO addDTO);

    @Transactional(rollbackFor = Exception.class)
    FlowResult addActualTransferCarPool(ErpDepotMerchantInfoDTO infoDTO, ErpTransferFlowDTO addDTO, Map<String, ProductSkuDetailDTO> skuMap
            , List<ErpSkuRecallDTO> skuRecallDetailList, ErpMqBizFlowDTO sellFlowDTO);

    /**
     * description: 实仓-调拨修改
     *
     * @return a
     * @Param: addDTO
     */
    void updateActualTransfer(ErpTransferFlowDTO addDTO);
    /**
     * description: 实仓-调拨审批
     *
     * @return a
     * @Param: applyDTO
     */
    void transferApply(ErpApplyDTO applyDTO) throws ErpFlowLockException;
    /**
     * description: 实仓-调拨审批
     *
     * @return a
     * @Param: applyDTO
     */
    void transferApplyBatch(ErpApplyDTO applyDTO);
    /**
     * description: 获取调拨详情
     *
     * @return ErpActualTransferFlowAndDetailVO
     * @Param: id
     * */
    ErpActualTransferFlowAndDetailVO transferDetail(Long id, String unitTypeCode);

    /**
     * description: 删除调拨信息
     *
     * @return a
     * @Param: ids
     * */
    void transferDel(List<Long> ids);

    /**
     * description: 获取调拨复制
     *
     * @return ErpActualTransferFlowAndDetailVO
     * @Param: id
     * */
    ErpActualTransferFlowAndDetailVO transferCopy(Long id, String unitTypeCode);

    List<ExportErpActualTransferFlowVO> exportTransferFlow(HttpServletResponse response, ErpActualTransferFlowQuery query);

    List<ExportErpWineActualTransferFlowVO> exportWineTransferFlow(HttpServletResponse response, ErpActualTransferFlowQuery query);
    
    void transferApplyBatchNew(ErpApplyDTO applyDTO);

    void transferApplyNew(ErpApplyDTO applyDTO) ;

    /**
     * 添加自动审批调拨单
     */
    Integer addAndApplyActualTransfer(List<ErpTransferFlowDTO> addDTOList);

    void transferBetweenWarehouses(Long tenantId, ErpMqBizFlowDTO sellFlowDTO, List<ErpTransferSkuDTO> batchList, Map<String, ProductSkuDetailDTO> skuMap,List<String> settlementSkuIds);

    /**
     * description: 实仓-新增机构调货调拨
     *
     * @return a
     * @Param: addDTO
     */
    Long addOrgTransferGoods(ErpTransferFlowDTO addDTO);
    /**
     * 调拨信息保存
     * */
    Long saveTransferInfo(ErpActualTransferPO actualTransferPo,List<ErpTransferSkuDTO> transferSkuList);
    /**
     * 调拨信息修改
     * */
    void updateActualTransfer(ErpActualTransferPO actualTransfer,List<ErpTransferSkuDTO> transferSkuList );
    void applyTransfer(ErpActualTransferPO actualTransfer);
    /**
     * 处理调拨审批
     * */
    FlowResult syncHandleTransferApply(String bizNo);
    /**
     * 实仓/销仓库存校验及机构可入库余量校验
     * */
    ErpActualAndSellCutInfoDTO checkOrgTransfer(ErpTransferFlowDTO addDTO);
    /**
     * 机构调拨数据组装
     * */
    ErpOrgTransferInfoDTO buildOrgTransferInfo(ErpTransferFlowDTO addDTO);

    FlowResult addOrgCarTransfer(ErpTransferFlowDTO erpTransferFlowDTO,ErpActualAndSellCutInfoDTO cutInfoDTO);

    void checkTransferParam(List<ErpTransferSkuDTO> batchList);

    /**
     * 校验调拨入库仓库是否签署保管合同
     * @param addDTO 调拨信息
     */
    void checkInDepotContract(ErpTransferFlowDTO addDTO);

    void applyCheckInDepotContract(ErpApplyDTO applyDTO);

    /**
     * 校验调入仓库为实仓时，农服和天杰是否签署保管合同
     * @param depotList
     */
    void checkInDepotContractExsits(List<ErpDepotVo> depotList,String moduleCode);

    /**
     * 要货调拨查询数据
     * @param query
     * @return
     */
    Page<TransferByLedgerBranchVO> pageLedger(TransferByLedgerBranchQuery query);

    /**
     *  Wine调拨导出
     * @param query
     * @return
     */
    FileDTO exportWineTransfer(ErpActualTransferFlowQuery query);

    /**
     * 调拨导出
     * @param query
     * @return
     */
    FileDTO exportTransfer(ErpActualTransferFlowQuery query);

}
