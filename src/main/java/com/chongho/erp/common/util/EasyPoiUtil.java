package com.chongho.erp.common.util;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.AbstractCellWriteHandler;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.cfpamf.framework.autoconfigure.easyexcel.EasyExcleHelp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @Program: erp-services
 * @Description: 动态显示Excel导出列
 * @author: LS
 * @create: 2022-11-07 10:26
 **/
@Slf4j
public class EasyPoiUtil extends EasyExcleHelp {


    private static final short DEFAULT_ROW_HEIGHT = 16;


    /**
     * 动态头导出
     *
     * @param response
     * @param fileName
     * @param data
     * @param heads 表头字段
     * @throws IOException
     */
    public void downloadField(HttpServletResponse response, String fileName, Class<?> aClass, List<?> data, List<String> heads) throws IOException {
        this.setExportResp(response, fileName);
        ArrayList<WriteHandler> writeHandlers = Lists.newArrayList(this.styleWrite());
        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(response.getOutputStream(), aClass);
        for (WriteHandler writeHandler : writeHandlers) {
            excelWriterBuilder = excelWriterBuilder.registerWriteHandler(writeHandler);
        }
        if(!CollectionUtils.isEmpty(heads)){
            excelWriterBuilder.sheet(fileName).includeColumnFiledNames(heads).doWrite(data);
        }else{
            excelWriterBuilder.sheet(fileName).doWrite(data);
        }

    }

    /**
     * 动态头导出
     *
     * @param response
     * @param fileName
     * @param data
     * @param heads 表头字段
     * @throws IOException
     */
    public void downloadField(HttpServletResponse response, String fileName, Class<?> aClass, List<?> data, List<String> heads, List<String> excludesFields) throws IOException {
        this.setExportResp(response, fileName);
        ArrayList<WriteHandler> writeHandlers = Lists.newArrayList(this.styleWrite());
        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(response.getOutputStream(), aClass);
        for (WriteHandler writeHandler : writeHandlers) {
            excelWriterBuilder = excelWriterBuilder
                    .excludeColumnFiledNames(excludesFields)
                    .registerWriteHandler(writeHandler);
        }
        if(!CollectionUtils.isEmpty(heads)){
            excelWriterBuilder.sheet(fileName).includeColumnFiledNames(heads).doWrite(data);
        }else{
            excelWriterBuilder.sheet(fileName).doWrite(data);
        }
    }

    /**
     * 动态头导出
     *
     * @param response
     * @param fileName
     * @param data
     * @param heads 表头字段
     * @throws IOException
     */
    public void downloadField(HttpServletResponse response, String fileName, Class<?> aClass, List<?> data, List<String> heads,
                              List<String> excludesFields, List<String> highlightFields) throws IOException {
        this.setExportResp(response, fileName);
        ArrayList<WriteHandler> writeHandlers = Lists.newArrayList(this.styleWrite());
        ExcelWidthStyleStrategy widthStyleStrategy = new ExcelWidthStyleStrategy();
        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(response.getOutputStream(), aClass)
                .excludeColumnFiledNames(excludesFields)
                .registerWriteHandler(new SimpleRowHeightStyleStrategy(DEFAULT_ROW_HEIGHT, DEFAULT_ROW_HEIGHT))
                .registerWriteHandler(widthStyleStrategy);
        if(CollectionUtil.isNotEmpty(highlightFields)) {
            AbstractCellWriteHandler headCellStyleHandler = new HeadCellStyleHandler(DEFAULT_ROW_HEIGHT, IndexedColors.YELLOW, highlightFields);
            excelWriterBuilder = excelWriterBuilder.registerWriteHandler(headCellStyleHandler);
        }
        for (WriteHandler writeHandler : writeHandlers) {
            excelWriterBuilder = excelWriterBuilder.excludeColumnFiledNames(excludesFields)
                    .registerWriteHandler(writeHandler);
        }
        if(!CollectionUtils.isEmpty(heads)){
            excelWriterBuilder.sheet(fileName).includeColumnFiledNames(heads).doWrite(data);
        }else{
            excelWriterBuilder.sheet(fileName).doWrite(data);
        }
    }


}
