package com.chongho.erp.common.mq.consumer;

import java.util.Objects;
import java.util.UUID;

import com.chongho.erp.config.BasicsConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.MDC;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.chongho.erp.common.enums.msg.MsgReceiveBizEnum;
import com.chongho.erp.common.enums.msg.MsgReceiveEnum;
import com.chongho.erp.common.enums.msg.MsgStatusEnum;
import com.chongho.erp.common.mq.config.StandardElectricityOrderStatusChangeConfig;
import com.chongho.erp.common.util.JsonUtils;
import com.chongho.erp.dto.performance.ErpPerformanceStockOperationDTO;
import com.chongho.erp.po.msg.ErpMsgRecievePO;
import com.chongho.erp.repository.msg.ErpMsgRecieveRepo;
import com.chongho.erp.service.performance.ErpPerformanceService;
import com.rabbitmq.client.Channel;

import lombok.extern.slf4j.Slf4j;

/**
 * @Classname
 * @Description
 * <AUTHOR> @Date
 * @Version 1.0
 **/
@Component
@Slf4j
public class PerformanceStockConsumer {

    @Autowired
    private ErpPerformanceService performanceService;

    @Autowired
    private ErpMsgRecieveRepo erpMsgRecieveRepo;
    @Autowired
    private BasicsConfig basicsConfig;

    @RabbitListener(queues = StandardElectricityOrderStatusChangeConfig.ERP_PERFORMANCE_STOCK_QUEUE)
    public void onMessage(Message message, Channel channel) throws Exception {
    	MDC.put("SpanID", UUID.randomUUID().toString());
        String body = new String(message.getBody());
        ErpPerformanceStockOperationDTO performanceStockOperationDTO = null;
        try {
	        log.info("【履约出入库队列消息】Payload: {} messageId：{}", body, message.getMessageProperties().getMessageId());
	        performanceStockOperationDTO = JsonUtils.jsonToBean(body, ErpPerformanceStockOperationDTO.class);
	        if(CollectionUtils.isNotEmpty(basicsConfig.getMqFitlerBizNoList())
	                &&basicsConfig.getMqFitlerBizNoList().contains(performanceStockOperationDTO.getBizNo())){
	            channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
	            return;
	        }
        } catch (Exception e) {
        	log.info("【履约出入库队列消息】Payload: {} error：{}", body, e);
        }
        ErpMsgRecievePO erpMsgRecievePO = new ErpMsgRecievePO();
        erpMsgRecievePO.setMsgTimes(0);
        erpMsgRecievePO.setMsgChannel(MsgReceiveEnum.MQ.getValue());
        erpMsgRecievePO.setMsgType(MsgReceiveBizEnum.PERFORMANCE_STOCK.getKey());
        erpMsgRecievePO.setMsgPushStatus(MsgStatusEnum.SUCCESS.getKey());
        try {
            erpMsgRecievePO.setMsgBizId(performanceStockOperationDTO.getBizNo());
            erpMsgRecievePO.setMsgContent(JSONObject.toJSONString(performanceStockOperationDTO));
            erpMsgRecieveRepo.save(erpMsgRecievePO);
            Boolean result = performanceService.performanceStockOperation(performanceStockOperationDTO);
            if (result) {
                log.info("【履约出入库队列消息】Payload: {} messageId：{} 处理成功 手工ACK MQ中事件消息数据", body,
                        message.getMessageProperties().getMessageId());
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
                erpMsgRecievePO.setMsgPushStatus(MsgStatusEnum.SUCCESS.getKey());
            } else {
                erpMsgRecievePO.setMsgPushStatus(MsgStatusEnum.FAIL.getKey());
                log.info("【履约出入库队列消息】Payload: {} messageId：{} 处理失败 手工ACK MQ中事件消息数据", body,
                        message.getMessageProperties().getMessageId());
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), true,false);
            }
        } catch (Exception e) {
            erpMsgRecievePO.setMsgPushStatus(MsgStatusEnum.FAIL.getKey());
            log.info("【履约出入库队列消息】Payload: {} messageId：{} 消费异常：{}", new String(message.getBody()),message.getMessageProperties().getMessageId(), e.getMessage());
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
        }finally {
            ErpMsgRecievePO msg = erpMsgRecieveRepo.getMsgReceiveByBizId(erpMsgRecievePO.getMsgBizId(), MsgReceiveBizEnum.PERFORMANCE_STOCK.getKey());
            if(Objects.nonNull(msg)){
                erpMsgRecievePO.setId(msg.getId());
            }
            erpMsgRecieveRepo.saveOrUpdate(erpMsgRecievePO);
    		MDC.remove("SpanID");
        }
    }

}
