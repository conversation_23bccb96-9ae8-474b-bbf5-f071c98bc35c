package com.chongho.erp.repository;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.chongho.erp.po.ErpInquirySchemeHeadPO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.constraints.NotBlank;

import java.util.List;

/**
 * 方案询价单主表 服务类
 *
 * <AUTHOR>
 * @since 2025-02-18 16:47:43
 */
public interface ErpInquirySchemeHeadRepo extends IService<ErpInquirySchemeHeadPO> {

    ErpInquirySchemeHeadPO getOneByChannelInquiryCode(String channelInquiryCode);

    boolean saveWithoutLoginTenant(ErpInquirySchemeHeadPO inquirySchemeHead);

    ErpInquirySchemeHeadPO getOneByInquiryCode(String inquiryCode);

    List<ErpInquirySchemeHeadPO> listByInquiryCode(List<String> inquiryCodeList);
}
