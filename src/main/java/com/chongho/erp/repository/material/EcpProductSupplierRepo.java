package com.chongho.erp.repository.material;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chongho.erp.dto.supplier.SupplierDTO;
import com.chongho.erp.po.material.EcpProductSupplierPO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON>  on 2022-03-01 14:54:02
 */

public interface EcpProductSupplierRepo extends IService<EcpProductSupplierPO>{
    /**
     * 通过供应商id获取货品id
     * @param mainSupplierId
     */
    List<String> getProductBySupplier(Long mainSupplierId);
    /**
     * 查询spu下的供应商
     * @param spuId
     */
    List<EcpProductSupplierPO> getListBySkuId(String spuId);

    /**
     * 批量查询
     * @param supplierList
     * @return
     */
    List<EcpProductSupplierPO> getBySupplierCode(List<String> supplierList,Long tenantId);

    List<EcpProductSupplierPO> getBySupplierCodeList(List<String> supplierList,List<Long> tenantIdList);

    /**
     * 根据货品批量查询
     * @param collect
     * @return
     */
    List<EcpProductSupplierPO> getSupplierMapBySpuId(List<String> collect);

    void updateTenantId(String categoryCode, Long orgTenantId, Long tenantId);

    List<EcpProductSupplierPO> getBySupIdsAndSupplier(List<String> spuIdList,String supplierCode);
    @InterceptorIgnore(tenantLine = "true")
    List<EcpProductSupplierPO> getProductSupplierBySupIds(List<String> spuIdList);

    List<SupplierDTO> getSupplierListBySkuList(List<String> skuIdList, Long tenantId);
}
