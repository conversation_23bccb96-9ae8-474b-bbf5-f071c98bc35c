package com.chongho.erp.repository.material.impl;

import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;
import com.chongho.erp.mapper.material.EcpProductTenantSettingMapper;
import com.chongho.erp.po.material.EcpProductTenantSettingPO;
import com.chongho.erp.repository.material.EcpProductTenantSettingRepo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class EcpProductTenantSettingRepoImpl extends BaseRepoServiceImpl<EcpProductTenantSettingMapper, EcpProductTenantSettingPO> implements EcpProductTenantSettingRepo {

    @Override
    public List<EcpProductTenantSettingPO> getDistributionlist(Integer distributionFlag) {
        return lambdaQuery().eq(EcpProductTenantSettingPO::getDistributionFlag, distributionFlag).list();
    }

    @Override
    public Map<Long, Integer> getDistributionFlagMap(List<Long> productIds) {
        List<EcpProductTenantSettingPO> list = lambdaQuery().in(EcpProductTenantSettingPO::getProductId, productIds).list();
        // 返回map, list按productId分组, key为productId, value为distributionFlag
        return list.stream().collect(Collectors.toMap(EcpProductTenantSettingPO::getProductId, EcpProductTenantSettingPO::getDistributionFlag));
    }

    @Override
    public List<EcpProductTenantSettingPO> getListByProductIds(List<Long> productIds) {
        return lambdaQuery().in(EcpProductTenantSettingPO::getProductId, productIds).list();
    }
}
