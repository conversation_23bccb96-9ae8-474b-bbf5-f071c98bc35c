package com.chongho.erp.repository.material.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chongho.erp.common.constant.ErpConstants;
import com.chongho.erp.common.enums.AssembleAuditStatusEnum;
import com.chongho.erp.common.enums.TagLinkTypeEnum;
import com.chongho.erp.common.enums.msg.AssembleLinkBusinessEnum;
import com.chongho.erp.dto.material.ErpBomAssembleDTO;
import com.chongho.erp.dto.query.AssembleDisassembleStockQuery;
import com.chongho.erp.dto.query.DisassembleProductQuery;
import com.chongho.erp.dto.query.ErpBomAssembleQuery;
import com.chongho.erp.mapper.material.ErpBomAssembleHeadMapper;
import com.chongho.erp.po.ErpTagLinkPO;
import com.chongho.erp.po.material.ErpBomAssembleHeadPO;
import com.chongho.erp.repository.ErpTagLinkRepo;
import com.chongho.erp.repository.material.ErpBomAssembleHeadRepo;
import com.chongho.erp.vo.material.AssembleDisassembleStockVO;
import com.chongho.erp.vo.material.AssembleProductSkuVO;
import com.chongho.erp.vo.material.ErpBomAssembleExportVO;
import com.chongho.erp.vo.material.ErpBomAssembleVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author:
 * @Date: 2023/6/2
 **/
@Service
public class ErpBomAssembleHeadRepoImpl extends ServiceImpl<ErpBomAssembleHeadMapper, ErpBomAssembleHeadPO>
        implements ErpBomAssembleHeadRepo {

    @Autowired
    private ErpTagLinkRepo tagLinkRepo;

    @Override
    public Page<ErpBomAssembleVO> queryBomAssemblePage(Page<ErpBomAssembleVO> page, ErpBomAssembleQuery query) {
        return getBaseMapper().queryBomAssemblePage(page,query);
    }

    @Override
    public List<ErpBomAssembleHeadPO> queryByAssembleNoAndSkuId(String assembleNo, String skuId) {
        return lambdaQuery()
                .eq(ErpBomAssembleHeadPO::getAssembleNo,assembleNo)
                .eq(ErpBomAssembleHeadPO::getSkuId,skuId)
                .list();
    }

    @Override
    public Page<AssembleProductSkuVO> assembleProductSku(Page<AssembleProductSkuVO> page, DisassembleProductQuery query) {
        return getBaseMapper().assembleProductSku(page,query);
    }

    @Override
    public List<ErpBomAssembleExportVO> getExportBomAssembleData(ErpBomAssembleQuery query) {
        return getBaseMapper().getExportBomAssembleData(query);
    }

    @Override
    public Page<AssembleDisassembleStockVO> getAssembleDisassembleStock(Page<AssembleDisassembleStockVO> page, AssembleDisassembleStockQuery query) {
        return getBaseMapper().getAssembleDisassembleStock(page,query);
    }

    @Override
    public void updateLinkBusinessFail(ErpBomAssembleDTO assembleHeadDTO) {
        LambdaUpdateWrapper<ErpBomAssembleHeadPO> bomAssembleUpdateWrapper = new LambdaUpdateWrapper<>();
        bomAssembleUpdateWrapper.eq(ErpBomAssembleHeadPO::getAssembleNo, assembleHeadDTO.getAssembleNo())
                .set(ErpBomAssembleHeadPO::getLinkBusiness, AssembleLinkBusinessEnum.LINK_FAIL.getValue());
        update(bomAssembleUpdateWrapper);
    }

    @Override
    public List<ErpBomAssembleHeadPO> queryBySkuIds(List<String> skuIds) {
        return lambdaQuery()
                .in(ErpBomAssembleHeadPO::getSkuId,skuIds)
                .list();
    }

    @Override
    public List<ErpBomAssembleHeadPO> getBomAssembleList(List<String> assembleNoList) {
        return lambdaQuery()
                .in(CollectionUtils.isNotEmpty(assembleNoList),ErpBomAssembleHeadPO::getAssembleNo,assembleNoList)
                .list();
    }

    @Override
    public List<ErpBomAssembleHeadPO> queryByStatusAndTime(Integer status, LocalDateTime time) {
        return lambdaQuery()
                .eq(ErpBomAssembleHeadPO::getAuditStatus,status)
                .lt(ErpBomAssembleHeadPO::getCreateTime,time)
                .list();
    }

    @Override
    public List<ErpBomAssembleHeadPO> getByIds(List<Long> headIds) {
        return lambdaQuery()
                .in(ErpBomAssembleHeadPO::getId,headIds)
                .list();
    }

    @Override
    public void tagBatchSet(String operateType,List<String> assembleNos, List<String> tagCodeList) {
        if(!Objects.equals(operateType, ErpConstants.OPERATE_ADD)) {
            tagLinkRepo.remove(new LambdaQueryWrapper<ErpTagLinkPO>().in(ErpTagLinkPO::getLinkCode, assembleNos));
        }
        List<ErpTagLinkPO> tagLinkList = tagLinkRepo.getByLinkCode(assembleNos);
        if(Objects.equals(operateType, ErpConstants.OPERATE_ADD)
                && CollectionUtils.isNotEmpty(tagLinkList)
                && CollectionUtils.isNotEmpty(tagCodeList)){
            tagCodeList=tagCodeList.stream().filter(item->tagLinkList.stream().noneMatch(link->
                    Objects.equals(item,link.getTagCode()))).collect(Collectors.toList());
        }
        List<ErpTagLinkPO> linkPOList = new ArrayList<>();
        for (String assembleNo : assembleNos) {
            if (CollectionUtils.isEmpty(tagCodeList)) {
                ErpTagLinkPO linkPO = new ErpTagLinkPO();
                linkPO.setLinkCode(assembleNo);
                linkPOList.add(linkPO);
            } else {
                for (String s : tagCodeList) {
                    ErpTagLinkPO linkPO = new ErpTagLinkPO();
                    linkPO.setTagCode(s);
                    linkPO.setLinkCode(assembleNo);
                    linkPOList.add(linkPO);
                }
            }
        }
        tagLinkRepo.saveBatch(linkPOList);
    }

    @Override
    public List<ErpBomAssembleHeadPO> getHistoryTag() {
        return baseMapper.getHistoryTag();
    }

}
