package com.chongho.erp.repository.stock.actual.export;

import com.cfpamf.ms.mall.filecenter.domain.dto.ColumnDTO;
import com.cfpamf.ms.mall.filecenter.service.ExcelDataExportService;
import com.chongho.erp.common.util.TenantUtil;
import com.chongho.erp.config.BasicsConfig;
import com.chongho.erp.repository.stock.actual.ErpCheckStockTaskHeadRepo;
import com.chongho.erp.req.CheckDiffStockExportReq;
import com.chongho.erp.vo.stock.actual.ErpCheckDiffStockVo;
import com.chongho.erp.vo.stock.actual.ErpCheckStockWineVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class ExportCheckDiffStockWineServiceImpl implements ExcelDataExportService<ErpCheckStockWineVo, CheckDiffStockExportReq> {

    @Autowired
    private ErpCheckStockTaskHeadRepo erpCheckStockTaskHeadRepo;

    @Resource
    private TenantUtil tenantUtil;

    @Autowired
    private BasicsConfig basicsConfig;

    @Override
    public Integer getDataCounts(CheckDiffStockExportReq query) {
        query.setPageSize(100);
        query.setNumber(1);
        int intExact = Math.toIntExact(erpCheckStockTaskHeadRepo.pageCheckDiffStock(query).getTotal());
        log.info("ExportCheckDiffStockServiceImpl getDataCounts: " + intExact);
        return intExact;
    }

    @Override
    public List<ErpCheckStockWineVo> getData(CheckDiffStockExportReq query) {
        return null;
    }

    @Override
    public List<ColumnDTO> createTemplate(CheckDiffStockExportReq param) {
        return null;
    }

    @Override
    public List<ErpCheckStockWineVo> getDataByPage(CheckDiffStockExportReq query, Integer pageNum, Integer pageSize) {
        query.setPageSize(pageSize);
        query.setNumber(pageNum);
        List<ErpCheckDiffStockVo> records = erpCheckStockTaskHeadRepo.pageCheckDiffStock(query).getRecords();
        List<ErpCheckStockWineVo> stockRecords = new ArrayList<>();
        for (ErpCheckDiffStockVo record : records) {
            ErpCheckStockWineVo stockRecord = new ErpCheckStockWineVo();
            BeanUtils.copyProperties(record, stockRecord);
            stockRecords.add(stockRecord);
        }
        return stockRecords;
    }
}
