package com.chongho.erp.repository.stock.sell.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chongho.erp.common.constant.ErpConstants;
import com.chongho.erp.common.enums.*;
import com.chongho.erp.common.enums.flow.StockApplyStatusEnum;
import com.chongho.erp.common.util.CommonUtil;
import com.chongho.erp.common.util.DateUtil;
import com.chongho.erp.common.util.TenantUtil;
import com.chongho.erp.config.BasicsConfig;
import com.chongho.erp.domain.builder.ErpSkuUnitBuilder;
import com.chongho.erp.dto.depot.ErpTransferDepotItemDTO;
import com.chongho.erp.dto.material.EcpProductSkuSupDTO;
import com.chongho.erp.dto.material.ProductSkuDetailDTO;
import com.chongho.erp.dto.procurement.QueryProcurementSubmissionHeadRequest;
import com.chongho.erp.dto.query.BranchDepotQuery;
import com.chongho.erp.dto.query.ErpSkuDepotItemQuery;
import com.chongho.erp.dto.query.ErpSkuDepotQuery;
import com.chongho.erp.dto.query.SupplierQuery;
import com.chongho.erp.dto.query.stock.ErpDepotItemInfoQuery;
import com.chongho.erp.dto.query.stock.ErpSellTransferFlowQuery;
import com.chongho.erp.dto.stock.ErpDepotItemInfoDTO;
import com.chongho.erp.po.ErpDepotPO;
import com.chongho.erp.po.ErpTenantPO;
import com.chongho.erp.po.material.EcpProductSkuUnitPO;
import com.chongho.erp.po.material.SupplierPO;
import com.chongho.erp.po.stock.actual.ErpActualTransferDetailPO;
import com.chongho.erp.po.stock.actual.ErpActualTransferPO;
import com.chongho.erp.po.stock.sell.ErpSellTransferDetailPO;
import com.chongho.erp.repository.material.EcpProductSkuRepo;
import com.chongho.erp.repository.material.EcpProductSkuUnitRepo;
import com.chongho.erp.repository.material.SupplierRepo;
import com.chongho.erp.repository.stock.sell.ErpSellTransferDetailRepo;
import com.chongho.erp.repository.stock.sell.ErpSellTransferRepo;
import com.chongho.erp.po.stock.sell.ErpSellTransferPO;
import com.chongho.erp.mapper.stock.sell.ErpSellTransferMapper;
import com.cfpamf.framework.autoconfigure.mybatis.BaseRepoServiceImpl;

import com.chongho.erp.service.ErpDepotItemRepo;
import com.chongho.erp.service.ErpDepotRepo;
import com.chongho.erp.service.ErpProcurementSubmissionHeadRepo;
import com.chongho.erp.service.material.IProductSkuService;
import com.chongho.erp.thirdpart.api.stock.StockFacade;
import com.chongho.erp.thirdpart.api.vo.StockBalance;
import com.chongho.erp.thirdpart.api.vo.StockFlowVO;
import com.chongho.erp.thirdpart.dto.StockFlowQuery;
import com.chongho.erp.vo.depot.ErpDepotVo;
import com.chongho.erp.vo.material.ProductSkuUnitVO;
import com.chongho.erp.vo.stock.actual.ErpTransferSkuDetailVO;
import com.chongho.erp.vo.stock.sell.ErpSellTransferFlowAndDetailVO;
import com.chongho.erp.vo.stock.sell.ErpSellTransferFlowVO;
import com.alibaba.fastjson.JSON;
import com.slodon.bbc.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by liuhao  on 2022-09-13 20:11:48
 */
@Service
@Slf4j
public class ErpSellTransferRepoImpl extends BaseRepoServiceImpl<ErpSellTransferMapper, ErpSellTransferPO> implements ErpSellTransferRepo {

    @Autowired
    private SupplierRepo supplierRepo;

    @Autowired
    private ErpProcurementSubmissionHeadRepo procurementSubmissionHeadRepo;

    @Autowired
    private ErpSellTransferDetailRepo erpSellTransferDetailRepo;

    @Autowired
    private EcpProductSkuRepo ecpProductSkuRepo;

    @Autowired
    private IProductSkuService productSkuService;
    @Autowired
    private ErpDepotRepo erpDepotRepo;
    @Autowired
    private ErpDepotItemRepo erpDepotItemRepo;
    @Autowired
    private TenantUtil tenantUtil;
    @Autowired
    private StockFacade stockFacade;
    @Autowired
    private BasicsConfig basicsConfig;
    @Autowired
    private EcpProductSkuUnitRepo productSkuUnitRepo;
    //复制
    private final static String COPY = "copy";
    //操作类型
    private final static String DETAIL = "detail";

    @Override
    public Page<ErpSellTransferFlowVO> pageTransferFlow(ErpSellTransferFlowQuery query) {
        Page<ErpSellTransferFlowVO> page = new Page<>(query.getNumber(), query.getPageSize());
        setSkuIdList(query);
        setSupplieryCode(query);
        query.setApplyTimeEnd(DateUtil.incr(query.getApplyTimeEnd(), 1));
        List<ErpSellTransferFlowVO> erpTransferFlowVOS = baseMapper.pageQuery(query, page);
        if (!CollectionUtils.isEmpty(erpTransferFlowVOS)) {
            handleTransferFlows(erpTransferFlowVOS);
        }
        page.setRecords(erpTransferFlowVOS);
        return page;
    }

    @Override
    public List<ErpSellTransferFlowVO> exportTransferFlow(ErpSellTransferFlowQuery query) {
        setSkuIdList(query);
        setSupplieryCode(query);
        query.setApplyTimeEnd(DateUtil.incr(query.getApplyTimeEnd(), 1));
        List<ErpSellTransferFlowVO> erpTransferFlowVOS = baseMapper.exportTransferFlow(query);
        return erpTransferFlowVOS;
    }

    private void setSkuIdList(ErpSellTransferFlowQuery query) {
        // 校验根据货品参数是否全部为空
        Boolean isNeedQuery = ObjectUtil.isNotEmpty(query.getMaterialCode()) || ObjectUtil.isNotEmpty(query.getSpuId())
                || ObjectUtil.isNotEmpty(query.getProductName()) || ObjectUtil.isNotEmpty(query.getSkuName()) || ObjectUtil.isNotEmpty(query.getSkuId());
        // 参数全部为空时，就不查询，直接返回null
        if (isNeedQuery) {
            QueryProcurementSubmissionHeadRequest queryProcurementSubmissionHeadRequest = new QueryProcurementSubmissionHeadRequest();
            queryProcurementSubmissionHeadRequest.setSkuMaterialCode(query.getMaterialCode());
            queryProcurementSubmissionHeadRequest.setProductName(query.getProductName());
            queryProcurementSubmissionHeadRequest.setSpuId(query.getSpuId());
            queryProcurementSubmissionHeadRequest.setSkuId(query.getSkuId());
            queryProcurementSubmissionHeadRequest.setSkuName(query.getSkuName());
            List<String> skuIds = procurementSubmissionHeadRepo.querySkuIds(queryProcurementSubmissionHeadRequest);
            if (!CollectionUtils.isEmpty(skuIds)) {
                query.setSkuIdList(skuIds);
            } else {
                List notExist = new ArrayList();
                notExist.add("not exist");
                query.setSkuIdList(notExist);
            }
        }
    }

    private void setSupplieryCode(ErpSellTransferFlowQuery query) {
        if (StringUtils.isEmpty(query.getSupplierName())) {
            return;
        }
        SupplierQuery supplierQuery = new SupplierQuery();
        supplierQuery.setSupplierShortName(query.getSupplierName());
        List<SupplierPO> supplierList = supplierRepo.getSupplierList(supplierQuery);
        log.info("###根据供应商名称 {} 结果 {}", query.getSupplierName(), JSON.toJSONString(supplierList));
        List<String> supplierCodeList = supplierList.stream().map(SupplierPO::getSupplierCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(supplierCodeList)) {
            List notExist = new ArrayList();
            notExist.add("not exist");
            query.setSupplierCodeList(notExist);
        } else {
            query.setSupplierCodeList(supplierCodeList);
        }
    }

    private void handleTransferFlows(List<ErpSellTransferFlowVO> erpTransferFlowVOS) {
        List<String> bizNoList = erpTransferFlowVOS.stream().map(s -> s.getBizNo()).collect(Collectors.toList());
        List<ErpSellTransferDetailPO> detailList = erpSellTransferDetailRepo.lambdaQuery().in(ErpSellTransferDetailPO::getBizNo, bizNoList).list();
        List<String> skuIdList = detailList.stream().map(s -> s.getSkuNo()).collect(Collectors.toList());
        ErpSkuDepotItemQuery query = new ErpSkuDepotItemQuery();
        query.setSkuIdList(skuIdList);
        List<EcpProductSkuSupDTO> productSkuSupDTOList = ecpProductSkuRepo.getProSkuSupInfoList(query);
        if (CollectionUtils.isEmpty(productSkuSupDTOList)) {
            return;
        }
        //物料信息组装
        Map<String, EcpProductSkuSupDTO> proSkuSupMap = productSkuSupDTOList.stream().collect(Collectors.toMap(EcpProductSkuSupDTO::getSkuId, Function.identity()));

        //基于调拨单位获取调拨数量
        Long tenantId=tenantUtil.getTenantIdFromRequest();
        Map<String, ProductSkuUnitVO> transferUnitMap = new HashMap<>();
        Map<String, ProductSkuUnitVO> stockUnitMap = new HashMap<>();
        if(basicsConfig.getTenantIdList().contains(tenantId)){
            //获取规格单位信息
            List<ProductSkuUnitVO> transferUnitList = productSkuUnitRepo.batchFindUnitListBySkuIdAndType(skuIdList, ErpUnitTypeEnum.TRANSFER_UNIT.getUnitTypeCode());
            List<ProductSkuUnitVO> stockUnitList = productSkuUnitRepo.batchFindUnitListBySkuIdAndType(skuIdList, ErpUnitTypeEnum.STOCK_UNIT.getUnitTypeCode());
            transferUnitMap = transferUnitList.stream().collect(Collectors.toMap(ProductSkuUnitVO::getSkuId, Function.identity(), (o, n) -> n));
            stockUnitMap = stockUnitList.stream().collect(Collectors.toMap(ProductSkuUnitVO::getSkuId, Function.identity(), (o, n) -> n));
        }

        Map<String, List<ErpSellTransferDetailPO>> bizNoMap = detailList.stream().collect(Collectors.groupingBy(ErpSellTransferDetailPO::getBizNo));
        Map<String, ProductSkuUnitVO> finalTransferUnitMap = transferUnitMap;
        Map<String, ProductSkuUnitVO> finalStockUnitMap = stockUnitMap;
        erpTransferFlowVOS.forEach(s -> {
            String failDesc="";
            if(Objects.equals(StockApplyStatusEnum.APPLY_FAIL.getValue(),s.getApplyStatus())){
                failDesc=s.getMsgReason();
            }
            s.setApplyStatusName(StockApplyStatusEnum.valueOf(s.getApplyStatus()).getDesc()+failDesc);
            List<ErpSellTransferDetailPO> list = bizNoMap.get(s.getBizNo());
            List<String> itemSkuIdList = list.stream().map(s1 -> s1.getSkuNo()).distinct().collect(Collectors.toList());
            StringBuffer sb = new StringBuffer();
            for (String skuId : itemSkuIdList) {
                EcpProductSkuSupDTO proSku = proSkuSupMap.get(skuId);
                if (Objects.nonNull(proSku)) {
                    sb.append(proSku.getProductName() + " " + proSku.getSkuName()).append(",");
                }
            }
            s.setProductInfo(sb.substring(0, sb.length() - 1));

            if(basicsConfig.getTenantIdList().contains(tenantId)){
                //变更数量转换为以【箱】为单位
                BigDecimal transferChangeNum = BigDecimal.ZERO;
                for (ErpSellTransferDetailPO transferDetailPO : list) {
                    //各SKU变更数量为原数量除以转换系数，取小数点后2位
                    ProductSkuUnitVO transferUnitVO = finalTransferUnitMap.get(transferDetailPO.getSkuNo());
                    ProductSkuUnitVO stockUnitVO = finalStockUnitMap.get(transferDetailPO.getSkuNo());
                    BigDecimal unitRatio = transferUnitVO.getPackageNum()
                            .divide(stockUnitVO.getPackageNum(), RoundingMode.HALF_UP);
                    BigDecimal skuTransferChangeNumber = transferDetailPO.getChangeNumber().divide(unitRatio, 2, RoundingMode.HALF_UP);
                    transferChangeNum = transferChangeNum.add(skuTransferChangeNumber);
                    s.setTransferUnitName(transferUnitVO.getUnitName());
                }
                s.setChangeNumber(transferChangeNum);
            }
        });

    }

    @Override
    public ErpSellTransferFlowAndDetailVO transferDetail(Long id) {
        ErpSellTransferPO po = this.getById(id);
        if (po == null) {
            throw new BusinessException("数据不存在" + id);
        }
        ErpSellTransferFlowAndDetailVO vo = new ErpSellTransferFlowAndDetailVO();
        BeanUtils.copyProperties(po, vo);
//        vo.setTransferTime(po.getApplyTime());
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferDel(List<Long> ids) {
        List<ErpSellTransferPO> poList = this.listByIds(ids);
        if (CollectionUtils.isEmpty(poList)) {
            throw new BusinessException("数据不存在");
        }
        Optional<ErpSellTransferPO> tranFlag = poList.stream().filter(item -> !Objects.equals(item.getApplyStatus(), StockApplyStatusEnum.WAIT_APPLY.getValue())).findFirst();
        if (tranFlag.isPresent()) {
            throw new com.cfpamf.framework.autoconfigure.common.exception.BusinessException(ErrorCodeEnum.CHECK_FAILURE.getCode(), "已审核的数据不能删除");
        }
        List<String> bizNoList = poList.stream().map(s -> s.getBizNo()).collect(Collectors.toList());
        List<ErpSellTransferDetailPO> detailPOList = erpSellTransferDetailRepo.lambdaQuery().in(ErpSellTransferDetailPO::getBizNo, bizNoList).list();
        List<Long> detailIdList = detailPOList.stream().map(s -> s.getId()).collect(Collectors.toList());
        this.removeByIds(ids);
        erpSellTransferDetailRepo.removeByIds(detailIdList);
    }

    @Override
    public ErpSellTransferFlowAndDetailVO getTransferDetail(Long id, String type, String unitTypeCode) {
        ErpSellTransferPO po = this.getById(id);
        if (po == null) {
            throw new BusinessException("数据不存在" + id);
        }
        ErpSellTransferFlowAndDetailVO vo = new ErpSellTransferFlowAndDetailVO();
        BeanUtils.copyProperties(po, vo);
        vo.setCreateTime(Date.from(po.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));

        //业务流水
        List<ErpSellTransferDetailPO> detailList = erpSellTransferDetailRepo.lambdaQuery().eq(ErpSellTransferDetailPO::getBizNo, po.getBizNo()).list();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(detailList)) {
            throw new com.cfpamf.framework.autoconfigure.common.exception.BusinessException(ErrorCodeEnum.CHECK_FAILURE.getCode(), "明细数据不存在");
        }
        List<String> skuIds = detailList.stream().map(ErpSellTransferDetailPO::getSkuNo).collect(Collectors.toList());
        //获取物料信息
        List<ProductSkuDetailDTO> productSkuList = productSkuService.getProductSkuBySkuId(skuIds);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(productSkuList)) {
            throw new com.cfpamf.framework.autoconfigure.common.exception.BusinessException(ErrorCodeEnum.CHECK_FAILURE.getCode(), "货品规格信息不存在");
        }
        Map<String, ProductSkuDetailDTO> proSkuMap = productSkuList.stream().collect(Collectors.toMap(ProductSkuDetailDTO::getSkuId, Function.identity()));
        //获取仓库信息
        List<Long> depotIds = detailList.stream().map(ErpSellTransferDetailPO::getOutDepotId).collect(Collectors.toList());
        depotIds.addAll(detailList.stream().map(ErpSellTransferDetailPO::getInDepotId).collect(Collectors.toList()));
        List<ErpDepotPO> depotList = erpDepotRepo.listByIds(depotIds);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(depotList)) {
            throw new com.cfpamf.framework.autoconfigure.common.exception.BusinessException(ErrorCodeEnum.CHECK_FAILURE.getCode(), "仓库信息不存在");
        }
        Map<Long, String> depotMap = depotList.stream().collect(Collectors.toMap(ErpDepotPO::getId, ErpDepotPO::getName));
        //库存数量
        Map<String, BigDecimal> stockNumMap = this.getStockNum(po, detailList, type);

        Long tenantId=tenantUtil.getTenantIdFromRequest();
        Map<String, ProductSkuUnitVO> targerUnitMap = new HashMap<>();
        Map<String, ProductSkuUnitVO> stockUnitMap = new HashMap<>();
        if(basicsConfig.getTenantIdList().contains(tenantId)){
            //获取规格单位信息
            List<ProductSkuUnitVO> targerUnitList = productSkuUnitRepo.batchFindUnitListBySkuIdAndType(skuIds, unitTypeCode);
            List<ProductSkuUnitVO> stockUnitList = productSkuUnitRepo.batchFindUnitListBySkuIdAndType(skuIds, ErpUnitTypeEnum.STOCK_UNIT.getUnitTypeCode());
            targerUnitMap = targerUnitList.stream().collect(Collectors.toMap(ProductSkuUnitVO::getSkuId, Function.identity(), (o, n) -> n));
            stockUnitMap = stockUnitList.stream().collect(Collectors.toMap(ProductSkuUnitVO::getSkuId, Function.identity(), (o, n) -> n));
        }

        Map<String, ProductSkuUnitVO> finalTargerUnitMap = targerUnitMap;
        Map<String, ProductSkuUnitVO> finalStockUnitMap = stockUnitMap;
        vo.setTransferSkuDetailVOList(detailList.stream().map(item -> {
            ErpTransferSkuDetailVO skuDetailVO = new ErpTransferSkuDetailVO();
            BeanUtils.copyProperties(item, skuDetailVO);
            //物料信息绑定
            ProductSkuDetailDTO proSku = proSkuMap.get(item.getSkuNo());
            skuDetailVO.setSkuId(proSku.getSkuId());
            skuDetailVO.setSkuName(proSku.getSkuName());
            skuDetailVO.setSpuId(proSku.getSpuId());
            skuDetailVO.setProductName(proSku.getProductName());
            skuDetailVO.setAttributes(proSku.getAttribute());
            skuDetailVO.setBrandName(proSku.getBrandName());
            skuDetailVO.setMaterialCode(proSku.getSpuMaterialCode());
            skuDetailVO.setCategory3Name(proSku.getCategoryName());
            skuDetailVO.setUnitName(proSku.getUnitName());
            //仓库信息
            skuDetailVO.setInDepotName(depotMap.get(item.getInDepotId()));
            skuDetailVO.setOutDepotName(depotMap.get(item.getOutDepotId()));
            //库存
            String key = String.format("%s:%s:%s", item.getBatchNo(), item.getOutDepotId(), item.getSkuNo());

            skuDetailVO.setStockNumber(stockNumMap.get(key));
            skuDetailVO.setChangeNumber(item.getChangeNumber());
            if(basicsConfig.getTenantIdList().contains(tenantId)){
                if(org.apache.commons.lang.StringUtils.isBlank(unitTypeCode)){
                    throw new com.cfpamf.framework.autoconfigure.common.exception.BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "酒水租户单位类型不能为空");
                }
                //获取规格单位信息
                if (finalTargerUnitMap.get(item.getSkuNo()) == null) {
                    throw new com.cfpamf.framework.autoconfigure.common.exception.BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "skuId:"+ item.getSkuNo()+"未查询到盘点单位");
                }
                if (finalStockUnitMap.get(item.getSkuNo()) == null) {
                    throw new com.cfpamf.framework.autoconfigure.common.exception.BusinessException(ErrorCodeEnum.CHECK_FAILURE.code(), "skuId:"+ item.getSkuNo()+"未查询到库存单位为空");
                }
                skuDetailVO.setUnitRatio(finalTargerUnitMap.get(item.getSkuNo()).getPackageNum().divide(finalStockUnitMap.get(item.getSkuNo()).getPackageNum()));
                skuDetailVO.setTargetUnitName(finalTargerUnitMap.get(item.getSkuNo()).getUnitName());
                skuDetailVO.setStockUnitName(finalStockUnitMap.get(item.getSkuNo()).getUnitName());
            }
            return skuDetailVO;
        }).collect(Collectors.toList()));
        return vo;
    }

    @Override
    public Integer updateSellTransfer(ErpSellTransferPO sellTransfer, Integer applyStatus) {
        LambdaQueryWrapper<ErpSellTransferPO> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ErpSellTransferPO::getApplyStatus, applyStatus);
        lqw.eq(ErpSellTransferPO::getId, sellTransfer.getId());
        return getBaseMapper().update(sellTransfer, lqw);
    }

    @Override
    public ErpSellTransferPO getSellTransfer(String bizNo) {
        return lambdaQuery().eq(ErpSellTransferPO::getBizNo, bizNo).one();
    }

    //获取库存数量
    private Map<String, BigDecimal> getStockNum(ErpSellTransferPO sellTransfer, List<ErpSellTransferDetailPO> detailList, String type) {
        //审核通过
        if (DETAIL.equals(type) && Objects.equals(sellTransfer.getApplyStatus(), StockApplyStatusEnum.APPLY.getValue())) {
            StockFlowQuery stockFlowQuery = new StockFlowQuery();
            stockFlowQuery.setFlowNoList(Collections.singletonList(sellTransfer.getBizNo()));
            stockFlowQuery.setPageSize(-1);
            Page<StockFlowVO> stockFlow = stockFacade.getFlow(stockFlowQuery);
            if (CollectionUtils.isEmpty(stockFlow.getRecords())) {
                throw new com.cfpamf.framework.autoconfigure.common.exception.BusinessException(ErrorCodeEnum.CHECK_FAILURE.getCode(), "流水信息不存在");
            }
            List<StockFlowVO> stockFlowList = stockFlow.getRecords().stream().filter(item -> Objects.equals(item.getType(), DepotHeadTypeEnum.OUT_DEPOT.getValue())).collect(Collectors.toList());
            List<String> depotCodeList = stockFlowList.stream().map(StockFlowVO::getDeptCode).collect(Collectors.toList());
            Map<String, Long> depotMap = erpDepotRepo.getDepotIdByCode(depotCodeList, DepotStockTypeEnum.SELL_STOCK.getValue());
            return stockFlowList.stream().collect(Collectors.toMap(key -> String.format("%s:%s:%s"
                    , key.getBatchNumber(), depotMap.get(key.getDeptCode()), key.getSkuId()), StockFlowVO::getBeforeCount, (o, n) -> n));
//            else {
//                ErpDepotItemInfoQuery query = new ErpDepotItemInfoQuery();
//                query.setStockType(DepotStockTypeEnum.SELL_STOCK.getValue());
//                query.setBizNo(sellTransfer.getBizNo());
//                List<ErpDepotItemInfoDTO> depotItem = erpDepotItemRepo.getDepotItemInfoList(query);
//                if (CollectionUtils.isEmpty(depotItem)) {
//                    throw new com.cfpamf.framework.autoconfigure.common.exception.BusinessException(ErrorCodeEnum.CHECK_FAILURE.getCode(), "流水信息不存在");
//                }
//                depotItem = depotItem.stream().filter(item -> Objects.equals(item.getRunType(), DepotHeadTypeEnum.OUT_DEPOT.getValue())).collect(Collectors.toList());
//                return depotItem.stream().collect(Collectors.toMap(key -> String.format("%s:%s:%s"
//                        , key.getBatchNumber(), key.getDepotId(), key.getSkuId()), ErpDepotItemInfoDTO::getBeforeNumber, (o, n) -> n));
//            }
        }
        ErpSkuDepotQuery depotQuery = new ErpSkuDepotQuery();
        List<String> batchNumberList = detailList.stream().map(ErpSellTransferDetailPO::getBatchNo).collect(Collectors.toList());
        List<Long> depotIdList = detailList.stream().map(ErpSellTransferDetailPO::getOutDepotId).collect(Collectors.toList());
        List<ErpDepotPO> depotList = erpDepotRepo.listByIds(depotIdList);
        if(!CollectionUtils.isEmpty(depotList)){
            depotQuery.setDepotCodes(depotList.stream().map(ErpDepotPO::getDeptCode).collect(Collectors.toList()));
        }
        List<String> skuIdList = detailList.stream().map(ErpSellTransferDetailPO::getSkuNo).collect(Collectors.toList());
        depotQuery.setBatchNumberList(batchNumberList);
        depotQuery.setSkuIdList(skuIdList);
        depotQuery.setPageSize(10000);
        depotQuery.setSkuGroupFlag(true);
        depotQuery.setDepotGroupFlag(true);
        depotQuery.setBatchFlag(true);
        Page<StockBalance> balance = stockFacade.getBalance(depotQuery);
        if (CollectionUtils.isEmpty(balance.getRecords())) {
            throw new com.cfpamf.framework.autoconfigure.common.exception.BusinessException(ErrorCodeEnum.CHECK_FAILURE.getCode(), "流水信息不存在");
        }
        List<String> depotCodeList = balance.getRecords().stream().map(StockBalance::getDeptCode).collect(Collectors.toList());
        Map<String, Long> depotMap = erpDepotRepo.getDepotIdByCode(depotCodeList, DepotStockTypeEnum.SELL_STOCK.getValue());
        return balance.getRecords().stream().collect(Collectors.toMap(key -> String.format("%s:%s:%s"
                , key.getBatchNumber(), depotMap.get(key.getDeptCode()), key.getSkuId()), StockBalance::getStock, (o, n) -> n));
//        else {
//            List<String> batchNumberList = detailList.stream().map(ErpSellTransferDetailPO::getBatchNo).collect(Collectors.toList());
//            List<Long> depotIdList = detailList.stream().map(ErpSellTransferDetailPO::getOutDepotId).collect(Collectors.toList());
//            List<String> skuIdList = detailList.stream().map(ErpSellTransferDetailPO::getSkuNo).collect(Collectors.toList());
//            //统计当前库存余量
//            List<ErpTransferDepotItemDTO> transferNumberList = erpDepotItemRepo.getTransferNumberList(batchNumberList, depotIdList, skuIdList, null, null, true, null);
//            if (org.apache.commons.collections.CollectionUtils.isEmpty(transferNumberList)) {
//                return stockMap;
//            }
//            return transferNumberList.stream().collect(Collectors.toMap(key -> String.format("%s:%s:%s"
//                    , key.getBatchNumber(), key.getDepotId(), key.getSkuId()), ErpTransferDepotItemDTO::getTotalNum));
//        }
    }

    //获取实时库存数量
    @Override
    public Map<String, BigDecimal> getNotApplyStockNums(List<String> bizNoList,List<String> batchNumberList, List<Long> depotIdList,List<String> skuIdList){
        Map<String, BigDecimal> stockMap = new HashMap<>();

        BranchDepotQuery depotQuery = new BranchDepotQuery();
        depotQuery.setDepotIds(depotIdList);
        List<ErpDepotVo> depotList = erpDepotRepo.getDepotList(depotQuery);
        if (CollectionUtils.isEmpty(depotList)) {
            throw new com.cfpamf.framework.autoconfigure.common.exception.BusinessException(ErrorCodeEnum.DATA_NOT_FOUND.getCode(), "没有仓库信息数据");
        }

        List<String> deptCodeList = depotList.stream().map(ErpDepotVo::getDeptCode).distinct().collect(Collectors.toList());

        ErpSkuDepotQuery skuDepotQuery = new ErpSkuDepotQuery();
        skuDepotQuery.setBatchNumberList(batchNumberList);
        skuDepotQuery.setSkuIdList(skuIdList);
        skuDepotQuery.setDepotCodes(deptCodeList);
        skuDepotQuery.setPageSize(10000);
        skuDepotQuery.setSkuGroupFlag(true);
        skuDepotQuery.setDepotGroupFlag(true);
        skuDepotQuery.setBatchFlag(true);
        Page<StockBalance> balance = stockFacade.getBalance(skuDepotQuery);
        if (CollectionUtils.isEmpty(balance.getRecords())) {
            return stockMap;
        }

        List<String> depotCodeList = balance.getRecords().stream().map(StockBalance::getDeptCode).distinct().collect(Collectors.toList());
        Map<String, Long> depotMap = erpDepotRepo.getDepotIdByCode(depotCodeList, DepotStockTypeEnum.SELL_STOCK.getValue());
        return balance.getRecords().stream().collect(Collectors.toMap(key -> String.format("%s:%s:%s"
                , key.getBatchNumber(), depotMap.get(key.getDeptCode()), key.getSkuId()), StockBalance::getStock, (o, n) -> n));
    }

    @Override
    public Map<String, BigDecimal> getApplyStockNums(List<String> bizNoList, List<String> batchNumberList, List<Long> depotIdList, List<String> skuIdList){
        Map<String, BigDecimal> stockMap = new HashMap<>();
        //审核通过
        StockFlowQuery stockFlowQuery = new StockFlowQuery();
        stockFlowQuery.setFlowNoList(bizNoList);
        stockFlowQuery.setSkuIdList(skuIdList);
        stockFlowQuery.setPageSize(-1);
        Page<StockFlowVO> stockFlow = stockFacade.getFlow(stockFlowQuery);
        if (CollectionUtils.isEmpty(stockFlow.getRecords())) {
            return stockMap;
        }
        List<StockFlowVO> stockFlowList = stockFlow.getRecords().stream().filter(item -> Objects.equals(item.getType(), DepotHeadTypeEnum.OUT_DEPOT.getValue())).collect(Collectors.toList());
        List<String> depotCodeList = stockFlowList.stream().map(StockFlowVO::getDeptCode).collect(Collectors.toList());
        Map<String, Long> depotMap = erpDepotRepo.getDepotIdByCode(depotCodeList, DepotStockTypeEnum.SELL_STOCK.getValue());
        return stockFlowList.stream().collect(Collectors.toMap(key -> String.format("%s:%s:%s:%s"
                , key.getFlowNo(), key.getBatchNumber(), depotMap.get(key.getDeptCode()), key.getSkuId()), StockFlowVO::getBeforeCount, (o, n) -> n));
    }
}
