package com.chongho.erp.oms.common.utils;

import java.io.IOException;
import java.net.InetAddress;
import java.util.regex.Pattern;

/**
 * 类ComputerInfoUtils.java的实现描述：取网卡物理地址
 * 
 * <pre>
 * 1.在Windows,Linux系统下均可用； 
 * 2.通过ipconifg,ifconfig获得计算机信息； 
 * 3.再用模式匹配方式查找MAC地址，与操作系统的语言无关
 * </pre>
 *
 * <AUTHOR> 14:48
 */
public class ComputerInfoUtils {
    private static String macAddressStr = null;
    private static String computerName = System.getenv().get("COMPUTERNAME");

    private static final String[] WINDOWS_COMMAND = {"ipconfig", "/all"};
    private static final String[] LINUX_COMMAND = {"/sbin/ifconfig", "-a"};
    private static final String[] MAC_COMMAND = {"ifconfig", "-a"};
    private static final Pattern MAC_PATTERN =
        Pattern.compile(".*((:?[0-9a-f]{2}[-:]){5}[0-9a-f]{2}).*", Pattern.CASE_INSENSITIVE);

    private static final String WINDOWS = "Windows";
    private static final String LINUX = "Linux";
    private static final String MAC = "Mac";

    private static final String COMPUTERNAME = "COMPUTERNAME";

    /**
     * 限制创建实例
     */
    private ComputerInfoUtils() {}

    /**
     * 获取电脑名
     *
     * @return
     */
    public static String getComputerName() {
        if (computerName == null || computerName.equals("")) {
            computerName = System.getenv().get(COMPUTERNAME);
        }
        return computerName;
    }

    /**
     * 获取客户端IP地址
     *
     * @return
     */
    public static String getIpAddrAndName() throws IOException {
        return InetAddress.getLocalHost().toString();
    }

    /**
     * 获取客户端IP地址
     *
     * @return
     */
    public static String getIpAddr() throws IOException {
        return InetAddress.getLocalHost().getHostAddress().toString();
    }

}