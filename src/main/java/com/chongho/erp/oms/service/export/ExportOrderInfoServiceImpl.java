package com.chongho.erp.oms.service.export;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.framework.autoconfigure.common.result.Result;
import com.cfpamf.ms.mall.filecenter.domain.dto.ColumnDTO;
import com.cfpamf.ms.mall.filecenter.service.impl.AbstractExcelDataExportServiceImpl;
import com.chongho.erp.common.enums.CommonEnum;
import com.chongho.erp.common.enums.DeliverExpressStatusEnum;
import com.chongho.erp.common.enums.ErrorCodeEnum;
import com.chongho.erp.common.enums.TmsDeliverChannelEnum;
import com.chongho.erp.dto.query.ProductSkuInfoQuery;
import com.chongho.erp.oms.common.enums.DeliveryModeEnum;
import com.chongho.erp.oms.common.enums.OutboundLevelEnum;
import com.chongho.erp.oms.common.enums.PerformanceModeEnum;
import com.chongho.erp.oms.domain.po.OrderItemPO;
import com.chongho.erp.oms.domain.query.OrderInfoQuery;
import com.chongho.erp.oms.domain.vo.OrderInfoExcelVO;
import com.chongho.erp.oms.domain.vo.OrderInfoVO;
import com.chongho.erp.oms.repository.OrderItemRepository;
import com.chongho.erp.oms.repository.OrderRepository;
import com.chongho.erp.service.IBmsIntegration;
import com.chongho.erp.service.material.IProductSkuService;
import com.chongho.erp.thirdpart.api.setmeal.WmsFacade;
import com.chongho.erp.thirdpart.api.tms.TmsCarLogisticFacade;
import com.chongho.erp.thirdpart.api.vo.CarLogisticInfoVo;
import com.chongho.erp.thirdpart.api.vo.LogisticInfoQuery;
import com.chongho.erp.thirdpart.api.vo.wms.DeliverGoodsPO;
import com.chongho.erp.vo.OrganizationParentVO;
import com.chongho.erp.vo.material.ProductSkuInfoVO;
import com.chongho.erp.vo.material.ProductSkuVO;
import com.chongho.erp.vo.stock.actual.OrgExcelInfoVO;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ExportOrderInfoServiceImpl extends AbstractExcelDataExportServiceImpl<OrderInfoExcelVO, OrderInfoQuery> {

    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private IProductSkuService productSkuService;
    @Autowired
    private IBmsIntegration bmsIntegration;
    @Autowired
    private TmsCarLogisticFacade tmsCarLogisticFacade;
    @Autowired
    private WmsFacade wmsFacade;
    @Autowired
    private OrderItemRepository orderItemRepository;

    @Override
    public Integer getDataCounts(OrderInfoQuery query) {

        Page<OrderInfoVO> mybatisPage = new Page<>(query.getNumber(), query.getPageSize());
        orderRepository.pageOrderInfo(query, mybatisPage);
        int total = Math.toIntExact(mybatisPage.getTotal());
        return total;
    }


    @Override
    public List<ColumnDTO> createTemplate(OrderInfoQuery query) {

        Map<String, String> map = Maps.newLinkedHashMap();
        Field[] declaredFields = OrderInfoExcelVO.class.getDeclaredFields();
        for (Field field : declaredFields) {
            if (field.isAnnotationPresent(ExcelProperty.class)) {
                ExcelProperty excelPropertyAnnotation = field.getAnnotation(ExcelProperty.class);
                // 获取注解上的值作为键名
                String annotationName = excelPropertyAnnotation.value()[0];
                field.setAccessible(true);
                //获取类的属性名称
                String fieldName = field.getName();

                map.put(annotationName, fieldName);
            }
        }
        List<ColumnDTO> columnDTOS = new ArrayList<>();
        //创建模板 columnName - 列标题， columnProperties - 列属性值， index - 列顺序
        int i = 1;
        for (String key : map.keySet()) {
            columnDTOS.add(new ColumnDTO(key, map.get(key), i));
            i++;
        }
        return columnDTOS;
    }

    @Override
    public List<OrderInfoExcelVO> getDataByPage(OrderInfoQuery query, Integer pageNum, Integer pageSize) {

        Page<OrderInfoVO> mybatisPage = new Page<>(pageNum, pageSize);


        Boolean isNeedQuery = ObjectUtil.isNotEmpty(query.getSkuMaterialCode()) || ObjectUtil.isNotEmpty(query.getSpuId())
                || ObjectUtil.isNotEmpty(query.getProductName()) || ObjectUtil.isNotEmpty(query.getSkuName()) || ObjectUtil.isNotEmpty(query.getSkuCode()) || CollectionUtils.isNotEmpty(query.getCategory3CodeList());


        // 参数全部为空时，就不查询，直接返回null
        if (isNeedQuery) {
            ProductSkuInfoQuery erpProductSkuQuery = new ProductSkuInfoQuery();
            if (query.getSkuCode() != null) {
                erpProductSkuQuery.setSkuId(query.getSkuCode());
            }
            erpProductSkuQuery.setPageSize(-1);
            erpProductSkuQuery.setProductName(query.getProductName());
            erpProductSkuQuery.setSkuMaterialCode(query.getSkuMaterialCode());
            erpProductSkuQuery.setSkuName(query.getSkuName());
            erpProductSkuQuery.setSpuId(query.getSpuId());
            erpProductSkuQuery.setCategory3CodeList(query.getCategory3CodeList());
            erpProductSkuQuery.setTenantId(query.getTenantId());
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<ProductSkuInfoVO> skuInfoVOPage
                    = productSkuService.getProductSkuInfoList(erpProductSkuQuery);
            if(CollectionUtils.isNotEmpty(skuInfoVOPage.getRecords())) {
                List<String> skuIds = skuInfoVOPage.getRecords().stream().map(ProductSkuInfoVO::getSkuId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(skuIds)) {
                    query.setSkuIdList(skuIds);
                }else {
                    return new ArrayList<>();
                }
            }else {
                return new ArrayList<>();
            }
        }

        List<OrderInfoVO> resultList = orderRepository.pageOrderInfo(query, mybatisPage);
        //List<ErpProductSkuInfoVo> productSkuInfoVos = erpProductFacade.getProductSkuListBySkuIdsV2(skuNoList);
        List<String> skuNoList = resultList.stream().map(OrderInfoVO::getSkuCode).collect(Collectors.toList());
        //货品规格站点信息
        List<ProductSkuVO> productSkuInfoVos = productSkuService.getProductSkuListBySkuIds(skuNoList,query.getTenantId());
        Map<String, ProductSkuVO> productSkuInfoVoMap = productSkuInfoVos.stream().collect(Collectors.toMap(ProductSkuVO::getSkuId, Function.identity(), (key1, key2) -> key2));

        List<String> orgCodeList = resultList.stream().map(OrderInfoVO::getBranch).distinct().collect(Collectors.toList());
        List<String> inBoundObjectCodes = resultList.stream().filter(s -> StringUtils.isNotBlank(s.getInboundObjectCode())
                        && PerformanceModeEnum.INTERNAL.getCode().equals(s.getPerformanceModeCode()))
                .map(s -> s.getInboundObjectCode()).collect(Collectors.toList());
        orgCodeList.addAll(inBoundObjectCodes);

        //出库机构
        List<String> outBoundObjectCodes = resultList.stream().filter(s -> StringUtils.isNotBlank(s.getOutboundObjectCode())
                        && OutboundLevelEnum.BRANCH.getCode().equals(s.getOutboundObjectLevelCode()))
                .map(s -> s.getOutboundObjectCode()).collect(Collectors.toList());
        orgCodeList.addAll(outBoundObjectCodes);


        Map<String, OrganizationParentVO> orgParentMap = bmsIntegration.getParentOrgMapByOrgCodes(orgCodeList);

        List<String> orderNoList = resultList.stream().map(OrderInfoVO::getOrderNo).collect(Collectors.toList());
        Result<List<DeliverGoodsPO>> deliverGoodsResult = wmsFacade.queryDeliverGoodsListByRelationNo(StringUtils.join(orderNoList, ","));
        Map<String, String> orderNoMap = new HashMap<>();
        Map<String,CarLogisticInfoVo> carLogisticMap = new HashMap<>();
        if(deliverGoodsResult.isSuccess() && CollectionUtils.isNotEmpty(deliverGoodsResult.getData())){
            orderNoMap = deliverGoodsResult.getData()
                    .stream().collect(Collectors.toMap(DeliverGoodsPO::getRelationNo, DeliverGoodsPO::getBizNo, (a, b) -> a));
            carLogisticMap = this.getCarLogisticMap(orderNoMap.values().stream().collect(Collectors.toList()));
        }

        Map<String, CarLogisticInfoVo> finalCarLogisticMap = carLogisticMap;
        Map<String, String> finalOrderNoMap = orderNoMap;

        if(CollectionUtils.isNotEmpty(resultList)) {
            List<OrderItemPO> orderItemPOS = orderItemRepository.lambdaQuery().in(OrderItemPO::getOrderNo,
                    resultList.stream().map(OrderInfoVO::getOrderNo).collect(Collectors.toList())).list();
            Map<String, List<OrderItemPO>> orderItemMap = orderItemPOS.stream().collect(Collectors.groupingBy(OrderItemPO::getOrderNo));
            resultList.stream().forEach(s -> {

                List<OrderItemPO> itemPOS = orderItemMap.get(s.getOrderNo());
                BigDecimal orderQty = new BigDecimal(0);
                BigDecimal signQty = new BigDecimal(0);
                BigDecimal loseQty = new BigDecimal(0);
                BigDecimal shipQty = new BigDecimal(0);
                for (OrderItemPO itemPO : itemPOS){
                    orderQty = orderQty.add(itemPO.getOrderQty().divide(itemPO.getConversionFactor(), 2, RoundingMode.HALF_UP));
                    signQty = signQty.add(itemPO.getSignQty().divide(itemPO.getConversionFactor(), 2, RoundingMode.HALF_UP));
                    loseQty = loseQty.add(itemPO.getLoseQty().divide(itemPO.getConversionFactor(), 2, RoundingMode.HALF_UP));
                    shipQty = shipQty.add(itemPO.getShippedQty().divide(itemPO.getConversionFactor(), 2, RoundingMode.HALF_UP));
                }
                s.setOrderQty(orderQty);
                s.setSignQty(signQty);
                s.setLoseQty(loseQty);
                s.setShippedQty(shipQty);
                s.setOrderUnitName(itemPOS.get(0).getSkuPackUnitName());
                s.setUnitName(itemPOS.get(0).getSkuPackUnitName());
            });
        }

        List<OrderInfoExcelVO> result = resultList.stream().map(s -> {

            OrderInfoExcelVO vo = new OrderInfoExcelVO();
            BeanUtils.copyProperties(s, vo);
            OrganizationParentVO org = orgParentMap.get(s.getBranch());
            OrganizationParentVO outBoundOrg = orgParentMap.get(s.getOutboundObjectCode());
            dealOrgInfo(vo, org);

            if(MapUtils.isNotEmpty(finalCarLogisticMap) && StringUtils.isNotEmpty(finalOrderNoMap.get(s.getOrderNo())) && Objects.nonNull(finalCarLogisticMap.get(finalOrderNoMap.get(s.getOrderNo())))){
                CarLogisticInfoVo carLogisticInfoVo = finalCarLogisticMap.get(finalOrderNoMap.get(s.getOrderNo()));
                vo.setCourierNumber(carLogisticInfoVo.getCourierNumber());
                vo.setExpressStatus(DeliverExpressStatusEnum.valueOf(carLogisticInfoVo.getExpressStatus()).getDesc());
            }
            if(outBoundOrg != null){
                    vo.setOutBoundObjectName(outBoundOrg.getOrgName());
            }

            //物品货物信息
            if (MapUtils.isNotEmpty(productSkuInfoVoMap)) {
                ProductSkuVO skuInfoVo = productSkuInfoVoMap.get(s.getSkuCode());
                if (ObjectUtil.isNotEmpty(skuInfoVo)) {
                    vo.setMaterialCode(skuInfoVo.getSkuMaterialCode());
                    vo.setProductName(skuInfoVo.getProductName());
                    vo.setCategory3Name(skuInfoVo.getCategoryPath());
                    vo.setSkuName(skuInfoVo.getSkuName());
                    vo.setSkuId(skuInfoVo.getSkuId());
                }
            }
            vo.setCreateUserName(s.getCreateBy());

            if(PerformanceModeEnum.INTERNAL.getCode().equals(s.getPerformanceModeCode()) &&
                    StringUtils.isNotBlank(s.getInboundObjectCode())){
                OrganizationParentVO branch = orgParentMap.get(s.getInboundObjectCode());
                vo.setRecipient(branch.getOrgName());
            }else {
                vo.setRecipient(s.getInboundObjectCode());
            }
            vo.setRecipientType(PerformanceModeEnum.fromCode(s.getPerformanceModeCode()).getDesc());
            vo.setRemark1(s.getRemark());
            vo.setDeliveryModeDesc(DeliveryModeEnum.fromCode(s.getDeliveryModeCode()).getDescEn());
            return vo;
        }).collect(Collectors.toList());
        return result;
    }

    public Map<String, CarLogisticInfoVo> getCarLogisticMap(List<String> deliverGoodsNoList){
        //通过发货单获取发车单信息
        LogisticInfoQuery logisticInfoQuery=new LogisticInfoQuery();
        logisticInfoQuery.setDeliverNoList(deliverGoodsNoList);
        logisticInfoQuery.setChannel(TmsDeliverChannelEnum.MANUFACTURER_DELIVERY.getCode());
        Result<List<CarLogisticInfoVo>> result = tmsCarLogisticFacade.getCarLogisticList(logisticInfoQuery);
        if (result == null) {
            throw new MSBizNormalException(ErrorCodeEnum.OUTSIDE_EXCEPTION.code(), "请求tms获取发货单信息异常");
        } else if (!result.isSuccess()) {
            throw new MSBizNormalException(result.getCode(), "TMS:" + result.getMsg());
        }
        if(CollectionUtils.isEmpty(result.getData())){
            return null;
        }
        return result.getData().stream().collect(Collectors.toMap(CarLogisticInfoVo::getCno,Function.identity(),(a,b)->a));
    }

    public void dealOrgInfo(OrderInfoExcelVO vo, OrganizationParentVO org) {
        if(Objects.nonNull(org)){
            vo.setOrgName(org.getOrgName());
            if (CollectionUtils.isNotEmpty(org.getSuperOrganizations())) {
                List<OrganizationParentVO> orgList = org.getSuperOrganizations().stream().sorted(Comparator.comparing(OrganizationParentVO::getTreeLevel)).collect(Collectors.toList());
                for (int i = 0; i < orgList.size(); i++) {
                    OrganizationParentVO superOrganization = orgList.get(i);
                    switch (i) {
                        case 1:
                            vo.setFirstOrgName(superOrganization.getOrgName());
                            break;
                        case 2:
                            vo.setSecondOrgName(superOrganization.getOrgName());
                            break;
                        case 3:
                            vo.setThirdOrgName(superOrganization.getOrgName());
                            break;
                        case 4:
                            vo.setForthOrgName(superOrganization.getOrgName());
                            break;
                        case 5:
                            vo.setFifthOrgName(superOrganization.getOrgName());
                            break;
                        default:
                    }
                }
            }
        }
    }
}
