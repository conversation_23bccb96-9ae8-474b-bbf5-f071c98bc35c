package com.chongho.erp.dto.material;

import com.cfpamf.framework.autoconfigure.mybatis.page.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class StockBalanceSkuQueryDto  extends PageQuery {

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("规格编码")
    private String skuId;

    @ApiModelProperty("货品名称")
    private String productName;

    @ApiModelProperty("规格名称")
    private String skuName;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("提报单批次号")
    private String batchNumber;

    @ApiModelProperty("批次名称")
    private String batchName;

    @ApiModelProperty("规格编码集合")
    private List<String> skuIdList;

    private List<String> batchNoList;

    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("仓库编码集合")
    private List<String> depotCodeList;

    @ApiModelProperty("店铺ID")
    private Long storeId;

    @ApiModelProperty("agric_host-乡信助农 norm_mall-标准电商 self_study_mall-自研电商")
    private String systemSource;

    @ApiModelProperty(value = "租户ID",hidden = true)
    private Long tenantId;
}
