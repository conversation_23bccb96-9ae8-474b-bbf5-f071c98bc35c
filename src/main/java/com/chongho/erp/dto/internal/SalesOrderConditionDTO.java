package com.chongho.erp.dto.internal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class SalesOrderConditionDTO {
    @ApiModelProperty(value = "处理过滤sku相关的条件")
    private List<String> skuIdList = new ArrayList<>();
    @ApiModelProperty(value = "处理价格单相关条件")
    private List<String> subCodes = new ArrayList<>();
    @ApiModelProperty(value = "处理仓库名称")
    private List<Long> depotIds = new ArrayList<>();
}
