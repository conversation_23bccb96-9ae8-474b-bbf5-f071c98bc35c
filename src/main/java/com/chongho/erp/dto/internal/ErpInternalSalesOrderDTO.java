package com.chongho.erp.dto.internal;

import com.chongho.erp.common.enums.flow.StockBusinessEnum;
import org.apache.commons.lang3.StringUtils;
import cn.hutool.core.collection.CollectionUtil;
import com.cfpamf.framework.autoconfigure.common.exception.BusinessException;
import com.chongho.erp.common.enums.AuditStatusEnum;
import com.chongho.erp.common.enums.internal.InternalDirectionEnum;
import com.chongho.erp.common.enums.internal.InternalGenerateMethodEnum;
import com.chongho.erp.common.enums.internal.InternalWarehouseTypeEnum;
import com.chongho.erp.common.smartid.SmartIdHelper;
import com.chongho.erp.common.smartid.SmartIdTypeEnum;
import com.chongho.erp.convertor.internal.ErpInternalDeliveryOrderConvertor;
import com.chongho.erp.po.ErpProcurementSubmissionItemPO;
import com.chongho.erp.po.ErpTenantPO;
import com.chongho.erp.po.internal.ErpInternalSalesOrderDetailPO;
import com.chongho.erp.po.internal.ErpInternalSalesOrderFilePO;
import com.chongho.erp.po.internal.ErpInternalSalesOrderPO;
import com.chongho.erp.service.ErpTenantRepo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Data
public class ErpInternalSalesOrderDTO implements Serializable {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("单据编号")
    private String orderCode;

    @ApiModelProperty("单据类型 1-供货销售订单 2-供货销售退订单‌")
    @NotNull(message = "单据类型不能为空")
    private Integer orderType;

    @ApiModelProperty("其他优惠金额")
    @NotNull(message = "其他优惠金额不能为空")
    private BigDecimal discountAmount;

    @ApiModelProperty("往来组织")
    @NotBlank(message = "往来组织不能为空")
    private String tradeCompanyCode;

    @ApiModelProperty("关联原单号")
    private String relatedOrderCode;

    @ApiModelProperty("采购单号")
    private String procurementSubmissionCode;

    @ApiModelProperty("退采单据编号")
    private String procurementReturnCode;

    @ApiModelProperty("新增方式 1-人工新增 2-自动新增")
    private Integer generateMethod;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("内采销售单详情")
    @NotEmpty(message = "内采销售单详情不能为空")
    @Valid
    private List<ErpInternalSalesOrderDetailDTO> detailList;

    @ApiModelProperty("内采销售单附件Ids")
    private List<Long> fileIdList;

    public void validData() {
        // 校验 detailList， skuId + depotId + purchaseSubCode 不允许重复
        if (this.detailList != null && !this.detailList.isEmpty()) {
            Map<String, Long> countMap = this.detailList.stream()
                .collect(Collectors.groupingBy(detail -> 
                    detail.getSkuId() + "_" + detail.getDepotId() + "_" + detail.getPurchaseSubCode(), 
                    Collectors.counting()));
            Optional<Map.Entry<String, Long>> duplicateEntry = countMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .findFirst();
            if (duplicateEntry.isPresent()) {
                throw new BusinessException("明细中存在重复的规格编码 + 出入库仓库 + 价格单组合");
            }
        }

        if (StockBusinessEnum.INTERNAL_SALES_ORDER_OUT_TYPE.getValue().equals(this.orderType)) {
            if (StringUtils.isBlank(this.procurementSubmissionCode)) {
                throw new BusinessException("采购单号不能为空");
            }
            if (Objects.nonNull(this.generateMethod)) {
                for (ErpInternalSalesOrderDetailDTO detailDto : this.detailList) {
                    InternalGenerateMethodEnum generateMethodEnum = InternalGenerateMethodEnum.valueOf(this.generateMethod);
                    assert generateMethodEnum != null;
                    detailDto.validData(generateMethodEnum);
                }
            }
        } else {
            if (StringUtils.isBlank(this.relatedOrderCode)) {
                throw new BusinessException("关联原单号不能为空");
            }
            if (StringUtils.isBlank(this.procurementReturnCode)) {
                throw new BusinessException("退采单据编号不能为空");
            }
            for (ErpInternalSalesOrderDetailDTO detail : this.detailList) {
                detail.validRefundData();
            }
        }
    }

    public ErpInternalSalesOrderPO convertToPo(SmartIdHelper smartIdHelper, ErpTenantRepo erpTenantRepo, Long tenantId, Map<String, BigDecimal> unitPriceMap) {
        checkAndInitDetails(unitPriceMap);

        ErpInternalSalesOrderPO po = ErpInternalDeliveryOrderConvertor.CONVERTOR.salesOrderDtoToPo(this);
        if (Objects.isNull(this.id) || this.id <= 0) {
            po.setId(null);
            if (StockBusinessEnum.INTERNAL_SALES_ORDER_OUT_TYPE.getValue().equals(this.orderType)) {
                //正向
                String ghxsCode = smartIdHelper.generatorCode("GHXS", SmartIdTypeEnum.INTERNAL_SALES_ORDER_CODE);
                po.setOrderCode(ghxsCode);
                this.detailList.forEach(item ->{
                    item.setOrderCode(ghxsCode);
                });
            } else {
                //逆向
                String ghxstCode = smartIdHelper.generatorCode("GHXST", SmartIdTypeEnum.INTERNAL_SALES_REFUND_ORDER_CODE);
                po.setOrderCode(ghxstCode);
                this.detailList.forEach(item ->{
                    item.setOrderCode(ghxstCode);
                });
            }
        }
        po.setOrderTime(LocalDateTime.now());
        po.setWarehouseType(InternalWarehouseTypeEnum.SALES_INVENTORY.getValue());
        if (StockBusinessEnum.INTERNAL_SALES_ORDER_OUT_TYPE.getValue().equals(this.orderType)) {
            po.setDirection(InternalDirectionEnum.STOCK_OUT.getValue());
        } else {
            po.setDirection(InternalDirectionEnum.STOCK_IN.getValue());
        }
        // 金额计算
        po.setTotalAmount(this.detailList.stream().map(ErpInternalSalesOrderDetailDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        po.setFinalAmount(po.getTotalAmount().subtract(po.getDiscountAmount()));
        po.setApprovalStatus(AuditStatusEnum.WAIT_APPLY.getValue());
        // 自动创建，登录信息中的租户Id为子公司，需要替换为子公司
        if (InternalGenerateMethodEnum.AUTO_ADD.getValue().equals(this.generateMethod)) {
            ErpTenantPO subTenant = erpTenantRepo.getTenantByTenantId(tenantId);
            ErpTenantPO parentTenant = erpTenantRepo.getTenantByCompanyCode(this.tradeCompanyCode);
            if (Objects.nonNull(parentTenant)) {
                po.setTenantId(parentTenant.getTenantId());
            }
            if (Objects.nonNull(subTenant)) {
                po.setTradeCompanyCode(subTenant.getCompanyCode());
            }
        } else {
            // 手动创建，在供货总公司创建，直接取登录用户的租户Id
            po.setTenantId(tenantId);
        }
        return po;
    }

    private void checkAndInitDetails(Map<String, BigDecimal> unitPriceMap) {
        if (InternalGenerateMethodEnum.HAND_ADD.getValue().equals(this.generateMethod)) {
            // 手工新增, 需要计算单价和金额
            if (Objects.nonNull(unitPriceMap)) {
                // 给detailList赋值
                for (ErpInternalSalesOrderDetailDTO detailDto : this.detailList) {
                    detailDto.setUnitPrice(unitPriceMap.get(detailDto.getPurchaseSubCode()));
                    detailDto.setAmount(detailDto.getUnitPrice().multiply(detailDto.getQuantity()));
                }
            }
        } else {
            // 自动新增, 需要计算金额
            for (ErpInternalSalesOrderDetailDTO detailDto : this.detailList) {
                detailDto.setAmount(detailDto.getUnitPrice().multiply(detailDto.getQuantity()));
            }
        }
    }

    public void checkUnitPrice(List<ErpProcurementSubmissionItemPO> itemList, Map<String, BigDecimal> unitPriceMap) {
        for (ErpInternalSalesOrderDetailDTO detailDto : this.detailList) {
            Optional<ErpProcurementSubmissionItemPO> itemOptional = itemList.stream().filter(item -> item.getSkuCode().equals(detailDto.getSkuId())).findFirst();
            if (!itemOptional.isPresent()) {
                throw new BusinessException("采购单：" + this.procurementSubmissionCode + ",规格编码：" + detailDto.getSkuId() + ",不存在明细");
            }
            ErpProcurementSubmissionItemPO itemPo = itemOptional.get();
            BigDecimal unitPrice = unitPriceMap.get(detailDto.getPurchaseSubCode());
            if (Objects.isNull(unitPrice)) {
                throw new BusinessException("价格单：" + detailDto.getPurchaseSubCode() + ",规格编码：" + detailDto.getSkuId() + ",单价为空");
            }
            if (unitPrice.compareTo(itemPo.getProcurementUnitTaxPrice()) != 0) {
                throw new BusinessException("采购单和价格单的单价不一致, 规格编码：" + detailDto.getSkuId());
            }
        }
    }

    public List<ErpInternalSalesOrderDetailPO> convertToDetailPos(String orderCode) {
        if (CollectionUtil.isEmpty(this.detailList)) {
            return Collections.emptyList();
        }
        this.detailList.forEach(item ->{
            item.setOrderCode(orderCode);
        });
        return ErpInternalDeliveryOrderConvertor.CONVERTOR.salesOrderDetailDtoToPo(this.detailList);
    }

    public List<ErpInternalSalesOrderFilePO> convertToFilePos(String orderCode) {
        if(CollectionUtil.isEmpty(this.fileIdList)) {
            return new ArrayList<>();
        }
        return this.fileIdList.stream().map(item -> {
            ErpInternalSalesOrderFilePO po = new ErpInternalSalesOrderFilePO();
            po.setFileId(item);
            po.setOrderCode(orderCode);
            return po;
        }).collect(Collectors.toList());
    }
}
