package com.chongho.erp.dto.productionScheduling;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.slodon.bbc.core.exception.BusinessException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

@Data
public class ErpRegionalProductionSchedulingProductDTO {

    @ApiModelProperty("排产明细id")
    private Long productionSchedulingDetailId;

    @ApiModelProperty("排产函编号")
    private String productionSchedulingNo;

    @ApiModelProperty("规格sku编码")
    @NotBlank(message = "规格sku编码不能为空")
    private String skuNo;

    @ApiModelProperty("货品分类")
    private String categoryPath;

    @ApiModelProperty("货品编码")
    private String spuId;

    @ApiModelProperty("采购提报单编码")
    private String procurementSubmissionCode;

    @ApiModelProperty("货品品牌名称")
    private String brandName;

    @ApiModelProperty("货品名称")
    private String productName;

    @ApiModelProperty("物料编码")
    @NotBlank(message = "物料编码不能为空")
    private String materialCode;

    @ApiModelProperty("规格名称")
    private String skuName;

    @ApiModelProperty("财务单位")
    @NotBlank(message = "财务单位不能为空")
    private String financeUnitCode;

    @ApiModelProperty("财务单位")
    private String financeUnitName;

    @ApiModelProperty("发货站")
    private Long siteId;

    @ApiModelProperty("最晚交货时间")
    @NotNull(message = "最晚交货时间不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date latestDeliveryTime;

    @ApiModelProperty("排产函-排产数量")
    @NotNull(message = "排产数量不能为空")
    private BigDecimal productionNumber;

    @ApiModelProperty("备注")
    private String remark;

    /// ////////////
    @ApiModelProperty("排产函-已生产数量")
    private BigDecimal produceNumber;

    @ApiModelProperty("排产函-撤单数量")
    private BigDecimal cancelNumber;

    @ApiModelProperty("排产函-待生产量=排产数量-撤单数量-已生产数量-未审核的生产量")
    private BigDecimal outboundNumber;

    public BigDecimal getOutboundNumber() {
        return productionNumber.subtract(cancelNumber).subtract(produceNumber).subtract(Optional.ofNullable(unAuditProduceNumber).orElse(BigDecimal.ZERO));
    }

    ///////////////// 单品排产函-新增字段 /////////////////
    @ApiModelProperty("采购数量")
    private BigDecimal procurementNums;

    @ApiModelProperty("采购含税单价")
    private BigDecimal procurementUnitTaxPrice;

    @ApiModelProperty("价税合计")
    private BigDecimal totalTaxPrice;

    @ApiModelProperty("采购金额，总价")
    private BigDecimal procurementTotalPrice;

    @ApiModelProperty("采购退货数量")
    private BigDecimal procurementReturnNums;

    @ApiModelProperty(value = "采购单 已排产数量=排产单排产数量+三方仓手动入库数量")
    private BigDecimal schedulingNums;

    @ApiModelProperty("实物入库数量，拼车、中转入库数量")
    private BigDecimal inStockNums;

    @ApiModelProperty("采购剩余排产量，提报时快照")
    private BigDecimal productionNumberBalance;

    @ApiModelProperty("待审核生产数量，排产数量-未审核生产数量+已生产数量")
    private BigDecimal unAuditProduceNumber;

    public void validData() {
        if (StringUtils.isBlank(procurementSubmissionCode)) {
            throw new BusinessException("采购提报单编码不能为空");
        }
    }
}
