package com.chongho.erp.dto.depot;

import com.chongho.erp.po.SitePo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "DepotDTO对象", description = "仓库数据传输对象")
public class AutoCreateDepotAndSiteDTO implements Serializable {

    private static final long serialVersionUID = -8687083839106496728L;

    @ApiModelProperty("发货站")
    private SitePo sitePo;

    @ApiModelProperty("租户ID")
    private Long tenantId;

    @ApiModelProperty("仓库编码")
    private String depotCode;
}
