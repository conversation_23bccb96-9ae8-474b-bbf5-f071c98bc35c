package com.chongho.erp.dto.depot;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Program: erp-services
 * @Description: 库存类
 * @author: LS
 * @create: 2022-08-14 10:56
 **/
@Data
public class ErpDepotSkuStockTotalDTO {

    @ApiModelProperty("skuId")
    private String skuId;

    @ApiModelProperty("仓库ID")
    private Long depotId;

    @ApiModelProperty("仓库编码")
    private String depotCode;

    @ApiModelProperty("仓库名称")
    private String depotName;

    @ApiModelProperty("父级仓库编码")
    private String rootDepotCode;

    @ApiModelProperty("数量")
    private BigDecimal totalNum;

    private String branchCode;

}
