package com.chongho.erp.dto.procurement;

import com.cfpamf.framework.autoconfigure.mybatis.page.PageQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Classname ProcurementSubmissionRequest
 * @Description
 * <AUTHOR>
 * @Date 2022/7/8 13:44
 * @Version 1.0
 **/
@Data
public class DepotSkuProcurementQuery extends PageQuery {

    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("供应商编码集合")
    private List<String> supplierCodeList;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("采购提报单编码")
    private String procurementSubmissionCode;

    @ApiModelProperty("单据开始日期")
    private String createTime;

    @ApiModelProperty("单据结束日期")
    private String endTime;

    @ApiModelProperty(value = "仓库ID")
    //@NotNull(message = "仓库ID不能为空")
    private Long depotId;

    @ApiModelProperty(value = "仓库ID集合")
    private List<Long> depotIdList;

    @ApiModelProperty(value = "货品规格skuId")
    @NotEmpty(message = "skuId不能为空")
    private String skuId;

    @ApiModelProperty(value = "批次号集合")
    private List<String> batchNoList;

    @ApiModelProperty(value = "skuId集合")
    private List<String> skuIdList;

    @ApiModelProperty(value = "租户ID",hidden = true)
    private Long tenantId;

    @ApiModelProperty(value = "仓库分组")
    private Boolean depotFlag=true;

    @ApiModelProperty(value = "sku分组")
    private Boolean skuFlag=true;

    @ApiModelProperty(value = "库存过滤")
    private Boolean stockFilter=false;

    @ApiModelProperty(value = "业务类型")
    private List<Integer> businessTypeList;

    @ApiModelProperty(value = "单位规格类型")
    private String unitTypeCode;
}
