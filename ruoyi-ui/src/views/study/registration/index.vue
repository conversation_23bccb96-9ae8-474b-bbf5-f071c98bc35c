<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="班次名称" prop="classId">
        <el-select v-model="queryParams.classId" placeholder="请选择班次" clearable filterable>
          <el-option
            v-for="item in classListOptions"
            :key="item.classId"
            :label="item.className"
            :value="item.classId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学员姓名" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入学员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          placeholder="请输入手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报名状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.study_registration_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['study:registration:add']"
        >新增报名</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['study:registration:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['study:registration:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['study:registration:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['study:registration:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleBatchApprove"
          v-hasPermi="['study:registration:approve']"
        >批量审批</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="registrationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="报名编号" align="center" prop="registrationNo" width="180"/>
      <el-table-column label="班次名称" align="center" prop="studyClass.className" />
      <el-table-column label="学员姓名" align="center" prop="sysUser.nickName" />
      <el-table-column label="手机号" align="center" prop="sysUser.phonenumber" width="120" />
      <el-table-column label="需完成学时" align="center" prop="requiredHours" />
      <el-table-column label="已获得学时" align="center" prop="achievedHours" />
      <el-table-column label="报名状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.study_registration_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="报名时间" align="center" prop="registrationTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.registrationTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <!-- 待审核状态的操作 -->
          <template v-if="scope.row.status === '0'">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleApprove(scope.row)"
              v-hasPermi="['study:registration:approve']"
            >批准</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-close"
              @click="handleReject(scope.row)"
              v-hasPermi="['study:registration:reject']"
            >拒绝</el-button>
          </template>
          
          <!-- 已通过状态的操作 -->
          <template v-if="scope.row.status === '1'">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-video-play"
              @click="handleStartStudy(scope.row)"
              v-hasPermi="['study:registration:edit']"
            >开始学习</el-button>
          </template>
          
          <!-- 学习中状态的操作 -->
          <template v-if="scope.row.status === '3'">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-medal"
              @click="handleGraduate(scope.row)"
              v-hasPermi="['study:registration:edit']"
            >结业</el-button>
          </template>
          
          <!-- 通用操作 -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['study:registration:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['study:registration:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改学员报名对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="选择班次" prop="classId">
          <el-select v-model="form.classId" placeholder="请选择班次" filterable :disabled="!!form.registrationId">
            <el-option
              v-for="item in classListOptions"
              :key="item.classId"
              :label="item.className"
              :value="item.classId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选择学员" prop="userId">
           <el-select v-model="form.userId" placeholder="请选择学员" @change="handleUserChange" filterable remote :remote-method="searchUser" :loading="userLoading" :disabled="!!form.registrationId">
             <el-option
               v-for="item in userListOptions"
               :key="item.userId"
               :label="item.nickName"
               :value="item.userId"
             >
                <span style="float: left">{{ item.nickName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.phonenumber }}</span>
             </el-option>
           </el-select>
           <div v-if="isNonAdminUser" style="color: #909399; font-size: 12px; margin-top: 4px;">非管理员用户只能为自己报名</div>
        </el-form-item>
        <el-form-item label="报名状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.study_registration_status"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 报名管理的逻辑应该是存入一个需完成学时，课程进行打卡，则对已获学时、最终得分进行更新 -->
<!--        <el-form-item label="已获学时" prop="achievedHours">
          <el-input-number v-model="form.achievedHours" controls-position="right" :precision="2" :step="0.1" :min="0" />
        </el-form-item>
         <el-form-item label="最终得分" prop="score">
          <el-input-number v-model="form.score" controls-position="right" :precision="2" :step="0.1" :min="0" />
        </el-form-item>-->
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 报名导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的报名数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRegistration, getRegistration, delRegistration, addRegistration, updateRegistration, 
         approveRegistration, rejectRegistration, batchApproveRegistration, startStudy, graduate } from "@/api/study/registration";
import { listClass } from "@/api/study/class";
import { listUser } from "@/api/system/user";
import { getToken } from "@/utils/auth";

export default {
  name: "Registration",
  dicts: ['study_registration_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      userLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 学员报名表格数据
      registrationList: [],
      // 班次列表
      classListOptions: [],
      // 用户列表
      userListOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 报名导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/study/registration/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        classId: null,
        userName: null,
        phonenumber: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        classId: [
          { required: true, message: "班次不能为空", trigger: "change" }
        ],
        userId: [
          { required: true, message: "学员不能为空", trigger: "change" }
        ],
      }
    };
  },
  computed: {
    // 判断是否为非admin用户
    isNonAdminUser() {
      const roles = this.$store.getters.roles || [];
      // 更严格的管理员判断：只有包含admin角色的才是管理员
      return !roles.some(role => role === 'admin' || role.toLowerCase().includes('admin'));
    },
    // 获取当前登录用户ID
    currentUserId() {
      return this.$store.getters.id;
    }
  },
  created() {
    this.getList();
    this.getClassList();
  },
  methods: {
    /** 查询学员报名列表 */
    getList() {
      this.loading = true;
      // 构造查询参数，处理一下，把userName和phonenumber放到params里
      const query = {
          ...this.queryParams,
          params: {
              nickName: this.queryParams.nickName,
              phonenumber: this.queryParams.phonenumber
          }
      };
      // delete query.userName;
      delete query.nickName;
      delete query.phonenumber;

      listRegistration(query).then(response => {
        this.registrationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询班次列表 */
    getClassList() {
        listClass({pageNum: 1, pageSize: 1000}).then(response => {
            this.classListOptions = response.rows;
        });
    },
    /** 搜索用户 */
    searchUser(query) {
      if (query !== '') {
        this.userLoading = true;
        listUser({ pageNum: 1, pageSize: 1000, nickName: query }).then(response => {
          this.userListOptions = response.rows;
          this.userLoading = false;
        });
      } else {
        this.userListOptions = [];
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        registrationId: null,
        classId: null,
        userId: null,
        achievedHours: 0,
        score: null,
        status: "0",
        remark: null,
        phonenumber: null,
      };
      this.userListOptions = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.registrationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    async handleAdd() {
      this.reset();
      this.title = "新增学员报名";

      // 如果是非admin用户，默认选择当前登录用户
      if (this.isNonAdminUser && this.currentUserId) {
        await this.loadCurrentUserInfo(); // 等待用户信息加载和设置完成
      } else {
        // 管理员用户，加载默认的用户列表
        this.loadDefaultUsers();
      }
      this.open = true; // 在数据准备好之后再打开弹窗
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const registrationId = row.registrationId || this.ids[0]
      getRegistration(registrationId).then(response => {
        this.form = response.data;
        // 手动填充用户列表，以便下拉框能显示当前值
        if (response.data.sysUser) {
            this.userListOptions = [response.data.sysUser];
        }
        this.open = true;
        this.title = "修改学员报名";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.registrationId != null) {
            updateRegistration(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRegistration(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    
    /** 批准报名申请 */
    handleApprove(row) {
      const registrationId = row.registrationId;
      this.$modal.confirm('是否确认批准该报名申请？').then(function() {
        return approveRegistration(registrationId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("批准成功");
      }).catch(() => {});
    },
    
    /** 拒绝报名申请 */
    handleReject(row) {
      const registrationId = row.registrationId;
      this.$prompt('请输入拒绝原因', '拒绝报名申请', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '拒绝原因不能为空'
      }).then(({ value }) => {
        return rejectRegistration(registrationId, value);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("拒绝成功");
      }).catch(() => {});
    },
    
    /** 批量审批 */
    handleBatchApprove() {
      const registrationIds = this.ids;
      this.$modal.confirm('是否确认批量批准选中的报名申请？').then(function() {
        return batchApproveRegistration(registrationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("批量审批完成");
      }).catch(() => {});
    },
    
    /** 开始学习 */
    handleStartStudy(row) {
      const registrationId = row.registrationId;
      this.$modal.confirm('是否确认该学员开始学习？').then(function() {
        return startStudy(registrationId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("操作成功，学员已开始学习");
      }).catch(() => {});
    },
    
    /** 学员结业 */
    handleGraduate(row) {
      const registrationId = row.registrationId;
      this.$modal.confirm('是否确认该学员结业？').then(function() {
        return graduate(registrationId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("结业成功");
      }).catch(() => {});
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const registrationIds = row.registrationId || this.ids;
      this.$modal.confirm('是否确认删除学员报名编号为"' + registrationIds + '"的数据项？').then(function() {
        return delRegistration(registrationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('study/registration/export', {
        ...this.queryParams
      }, `registration_${new Date().getTime()}.xlsx`)
    },
    handleUserChange(userId) {
      // 根据userId在userListOptions中找到对应的学员
      const user = this.userListOptions.find(item => String(item.userId) === String(userId));
      if (user) {
        this.form.phonenumber = user.phonenumber;
      } else {
        this.form.phonenumber = ''; // 没找到则置空
      }
    },
    /** 加载当前用户信息 */
    async loadCurrentUserInfo() {
      if (!this.currentUserId) {
        return;
      }
      try {
        const response = await listUser({ pageNum: 1, pageSize: 1, userId: this.currentUserId });
        if (response && response.rows && response.rows.length > 0) {
          const currentUser = response.rows[0];
          this.userListOptions = [currentUser];
          this.form.phonenumber = currentUser.phonenumber;

          // 使用 $nextTick 确保选项渲染完成后再设置值
          await this.$nextTick();
          this.form.userId = currentUser.userId;
        }
      } catch (error) {
        console.error("加载当前用户信息失败", error);
        this.$modal.msgError("加载当前用户信息失败");
      }
    },
    /** 加载默认用户列表（管理员使用） */
    loadDefaultUsers() {
      this.userLoading = true;
      // 加载前20个用户作为默认选项
      listUser({ pageNum: 1, pageSize: 1000 }).then(response => {
        this.userListOptions = response.rows || [];
        this.userLoading = false;
      }).catch(() => {
        this.userListOptions = [];
        this.userLoading = false;
      });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "报名信息导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('/study/registration/importTemplate', {
      }, `user_registration_template_${new Date().getTime()}.xlsx`)
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    }
  }
};
</script>
