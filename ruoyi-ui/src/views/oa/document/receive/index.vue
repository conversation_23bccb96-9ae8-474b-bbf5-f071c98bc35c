<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="文档标题" prop="docTitle">
        <el-input
          v-model="queryParams.docTitle"
          placeholder="请输入文档标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文档编号" prop="docNumber">
        <el-input
          v-model="queryParams.docNumber"
          placeholder="请输入文档编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="来文单位" prop="sourceUnit">
        <el-input
          v-model="queryParams.sourceUnit"
          placeholder="请输入来文单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.oa_doc_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['oa:document:receive:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['oa:document:receive:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['oa:document:receive:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['oa:document:receive:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="documentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文档编号" align="center" prop="docNumber" />
      <el-table-column label="文档标题" align="center" prop="docTitle" show-overflow-tooltip />
      <el-table-column label="来文单位" align="center" prop="sourceUnit" show-overflow-tooltip />
      <el-table-column label="收文日期" align="center" prop="receiveDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.receiveDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="紧急程度" align="center" prop="urgencyLevel">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.oa_urgency_level" :value="scope.row.urgencyLevel"/>
        </template>
      </el-table-column>
      <el-table-column label="密级" align="center" prop="securityLevel">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.oa_security_level" :value="scope.row.securityLevel"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.oa_doc_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['oa:document:receive:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['oa:document:receive:edit']"
            v-if="scope.row.status === '1'"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-promotion"
            @click="handleSubmit(scope.row)"
            v-hasPermi="['oa:document:receive:submit']"
            v-if="scope.row.status === '1'"
          >提交审批</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleExportPdf(scope.row)"
            v-hasPermi="['oa:document:receive:export']"
            v-if="scope.row.status !== '1'"
          >导出PDF</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改收文对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="文档标题" prop="docTitle">
              <el-input v-model="form.docTitle" placeholder="请输入文档标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文档编号" prop="docNumber">
              <el-input v-model="form.docNumber" placeholder="请输入文档编号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="来文单位" prop="sourceUnit">
              <el-input v-model="form.sourceUnit" placeholder="请输入来文单位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收文日期" prop="receiveDate">
              <el-date-picker clearable
                v-model="form.receiveDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择收文日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="紧急程度" prop="urgencyLevel">
              <el-select v-model="form.urgencyLevel" placeholder="请选择紧急程度">
                <el-option
                  v-for="dict in dict.type.oa_urgency_level"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密级" prop="securityLevel">
              <el-select v-model="form.securityLevel" placeholder="请选择密级">
                <el-option
                  v-for="dict in dict.type.oa_security_level"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="文档内容" prop="content">
          <el-input v-model="form.content" type="textarea" :rows="6" placeholder="请输入文档内容" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listReceiveDocument, getDocument, delDocument, addDocument, updateDocument, submitDocument, exportDocumentPdf } from "@/api/oa/document";

export default {
  name: "ReceiveDocument",
  dicts: ['oa_doc_status', 'oa_urgency_level', 'oa_security_level'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 收文表格数据
      documentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        docTitle: null,
        docNumber: null,
        sourceUnit: null,
        status: null,
        docType: '1' // 收文
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        docTitle: [
          { required: true, message: "文档标题不能为空", trigger: "blur" }
        ],
        sourceUnit: [
          { required: true, message: "来文单位不能为空", trigger: "blur" }
        ],
        receiveDate: [
          { required: true, message: "收文日期不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询收文列表 */
    getList() {
      this.loading = true;
      listReceiveDocument(this.queryParams).then(response => {
        this.documentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        docId: null,
        docTitle: null,
        docNumber: null,
        docType: '1',
        sourceUnit: null,
        receiveDate: null,
        urgencyLevel: '2',
        securityLevel: '1',
        content: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.docId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加收文";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const docId = row.docId || this.ids
      getDocument(docId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改收文";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.$router.push({
        path: '/oa/document/view',
        query: { docId: row.docId }
      });
    },
    /** 提交表单 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.docId != null) {
            updateDocument(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDocument(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const docIds = row.docId || this.ids;
      this.$modal.confirm('是否确认删除收文编号为"' + docIds + '"的数据项？').then(function() {
        return delDocument(docIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 提交审批操作 */
    handleSubmit(row) {
      this.$modal.confirm('是否确认提交审批收文"' + row.docTitle + '"？').then(function() {
        return submitDocument(row.docId, {});
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("提交成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('oa/document/export', {
        ...this.queryParams
      }, `document_${new Date().getTime()}.xlsx`)
    },
    /** 导出PDF操作 */
    handleExportPdf(row) {
      exportDocumentPdf(row.docId).then(response => {
        this.download(response, row.docTitle + '.pdf');
      });
    }
  }
};
</script>
