<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 工作台概览 -->
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>OA工作台</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-icon">
                  <i class="el-icon-document" style="color: #409EFF;"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ dashboardData.todoCount }}</div>
                  <div class="stat-label">待办任务</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-icon">
                  <i class="el-icon-finished" style="color: #67C23A;"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ dashboardData.doneCount }}</div>
                  <div class="stat-label">已办任务</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-icon">
                  <i class="el-icon-files" style="color: #E6A23C;"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ dashboardData.documentCount }}</div>
                  <div class="stat-label">公文数量</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-icon">
                  <i class="el-icon-date" style="color: #F56C6C;"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ dashboardData.scheduleCount }}</div>
                  <div class="stat-label">今日日程</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 待办任务 -->
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>待办任务</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="goToTodoList">查看更多</el-button>
          </div>
          <el-table :data="todoTasks" style="width: 100%" max-height="300">
            <el-table-column prop="taskName" label="任务名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="workflowName" label="流程名称" width="120"></el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="120">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button size="mini" type="primary" @click="handleTask(scope.row)">处理</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <!-- 最新公文 -->
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>最新公文</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="goToDocumentList">查看更多</el-button>
          </div>
          <el-table :data="recentDocuments" style="width: 100%" max-height="300">
            <el-table-column prop="docTitle" label="文档标题" show-overflow-tooltip></el-table-column>
            <el-table-column prop="docType" label="类型" width="80">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.oa_doc_type" :value="scope.row.docType"/>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="120">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="viewDocument(scope.row)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 今日日程 -->
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>今日日程</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="goToSchedule">管理日程</el-button>
          </div>
          <el-timeline>
            <el-timeline-item
              v-for="schedule in todaySchedules"
              :key="schedule.scheduleId"
              :timestamp="schedule.startTime"
              placement="top"
            >
              <el-card>
                <h4>{{ schedule.title }}</h4>
                <p>{{ schedule.content }}</p>
                <p v-if="schedule.location">地点：{{ schedule.location }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getTodoTasks } from "@/api/oa/workflow";
import { getRecentDocuments } from "@/api/oa/document";
import { getTodaySchedules } from "@/api/oa/schedule";

export default {
  name: "OaDashboard",
  dicts: ['oa_doc_type'],
  data() {
    return {
      dashboardData: {
        todoCount: 0,
        doneCount: 0,
        documentCount: 0,
        scheduleCount: 0
      },
      todoTasks: [],
      recentDocuments: [],
      todaySchedules: []
    };
  },
  created() {
    this.loadDashboardData();
  },
  methods: {
    loadDashboardData() {
      // 加载待办任务
      getTodoTasks().then(response => {
        this.todoTasks = response.rows.slice(0, 5); // 只显示前5条
        this.dashboardData.todoCount = response.total;
      });

      // 加载最新公文
      getRecentDocuments().then(response => {
        this.recentDocuments = response.rows.slice(0, 5);
        this.dashboardData.documentCount = response.total;
      });

      // 加载今日日程
      getTodaySchedules().then(response => {
        this.todaySchedules = response.rows;
        this.dashboardData.scheduleCount = response.total;
      });
    },
    handleTask(row) {
      this.$router.push({
        path: '/oa/workflow/task/handle',
        query: { taskId: row.taskId }
      });
    },
    viewDocument(row) {
      this.$router.push({
        path: '/oa/document/view',
        query: { docId: row.docId }
      });
    },
    goToTodoList() {
      this.$router.push('/oa/workflow/task/todo');
    },
    goToDocumentList() {
      this.$router.push('/oa/document/list');
    },
    goToSchedule() {
      this.$router.push('/oa/personal/schedule');
    }
  }
};
</script>

<style scoped>
.stat-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-icon {
  font-size: 40px;
  margin-right: 15px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.box-card {
  margin-bottom: 20px;
}
</style>
