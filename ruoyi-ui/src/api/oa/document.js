import request from '@/utils/request'

// 查询公文列表
export function listDocument(query) {
  return request({
    url: '/oa/document/list',
    method: 'get',
    params: query
  })
}

// 查询公文详细
export function getDocument(docId) {
  return request({
    url: '/oa/document/' + docId,
    method: 'get'
  })
}

// 新增公文
export function addDocument(data) {
  return request({
    url: '/oa/document',
    method: 'post',
    data: data
  })
}

// 修改公文
export function updateDocument(data) {
  return request({
    url: '/oa/document',
    method: 'put',
    data: data
  })
}

// 删除公文
export function delDocument(docId) {
  return request({
    url: '/oa/document/' + docId,
    method: 'delete'
  })
}

// 查询收文列表
export function listReceiveDocument(query) {
  return request({
    url: '/oa/document/receive/list',
    method: 'get',
    params: query
  })
}

// 查询发文列表
export function listSendDocument(query) {
  return request({
    url: '/oa/document/send/list',
    method: 'get',
    params: query
  })
}

// 提交公文审批
export function submitDocument(docId, data) {
  return request({
    url: '/oa/document/submit/' + docId,
    method: 'post',
    data: data
  })
}

// 审批公文
export function approveDocument(docId, data) {
  return request({
    url: '/oa/document/approve/' + docId,
    method: 'post',
    data: data
  })
}

// 发布公文
export function publishDocument(docId) {
  return request({
    url: '/oa/document/publish/' + docId,
    method: 'post'
  })
}

// 归档公文
export function archiveDocument(docId) {
  return request({
    url: '/oa/document/archive/' + docId,
    method: 'post'
  })
}

// 导出公文PDF
export function exportDocumentPdf(docId) {
  return request({
    url: '/oa/document/export/pdf/' + docId,
    method: 'get',
    responseType: 'blob'
  })
}

// 查询最新公文
export function getRecentDocuments(query) {
  return request({
    url: '/oa/document/recent',
    method: 'get',
    params: query
  })
}

// 上传附件
export function uploadAttachment(data) {
  return request({
    url: '/oa/document/attachment/upload',
    method: 'post',
    data: data
  })
}

// 删除附件
export function deleteAttachment(fileId) {
  return request({
    url: '/oa/document/attachment/' + fileId,
    method: 'delete'
  })
}

// 下载附件
export function downloadAttachment(fileId) {
  return request({
    url: '/oa/document/attachment/download/' + fileId,
    method: 'get',
    responseType: 'blob'
  })
}
