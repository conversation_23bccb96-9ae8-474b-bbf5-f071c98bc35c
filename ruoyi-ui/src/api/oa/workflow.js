import request from '@/utils/request'

// 查询工作流定义列表
export function listWorkflowDefinition(query) {
  return request({
    url: '/oa/workflow/definition/list',
    method: 'get',
    params: query
  })
}

// 查询工作流定义详细
export function getWorkflowDefinition(workflowId) {
  return request({
    url: '/oa/workflow/definition/' + workflowId,
    method: 'get'
  })
}

// 新增工作流定义
export function addWorkflowDefinition(data) {
  return request({
    url: '/oa/workflow/definition',
    method: 'post',
    data: data
  })
}

// 修改工作流定义
export function updateWorkflowDefinition(data) {
  return request({
    url: '/oa/workflow/definition',
    method: 'put',
    data: data
  })
}

// 删除工作流定义
export function delWorkflowDefinition(workflowId) {
  return request({
    url: '/oa/workflow/definition/' + workflowId,
    method: 'delete'
  })
}

// 启动工作流程
export function startProcess(data) {
  return request({
    url: '/oa/workflow/process/start',
    method: 'post',
    data: data
  })
}

// 完成任务
export function completeTask(taskId, data) {
  return request({
    url: '/oa/workflow/task/complete/' + taskId,
    method: 'post',
    data: data
  })
}

// 查询待办任务列表
export function getTodoTasks(query) {
  return request({
    url: '/oa/workflow/task/todo',
    method: 'get',
    params: query
  })
}

// 查询已办任务列表
export function getDoneTasks(query) {
  return request({
    url: '/oa/workflow/task/done',
    method: 'get',
    params: query
  })
}

// 查询流程实例列表
export function listWorkflowInstance(query) {
  return request({
    url: '/oa/workflow/instance/list',
    method: 'get',
    params: query
  })
}

// 查询流程实例详细
export function getWorkflowInstance(instanceId) {
  return request({
    url: '/oa/workflow/instance/' + instanceId,
    method: 'get'
  })
}

// 终止流程实例
export function terminateProcess(instanceId, data) {
  return request({
    url: '/oa/workflow/instance/terminate/' + instanceId,
    method: 'post',
    data: data
  })
}

// 转办任务
export function delegateTask(taskId, data) {
  return request({
    url: '/oa/workflow/task/delegate/' + taskId,
    method: 'post',
    data: data
  })
}

// 获取流程图
export function getProcessDiagram(processInstanceId) {
  return request({
    url: '/oa/workflow/diagram/' + processInstanceId,
    method: 'get'
  })
}

// 查询流程历史
export function getProcessHistory(processInstanceId) {
  return request({
    url: '/oa/workflow/history/' + processInstanceId,
    method: 'get'
  })
}

// 部署流程定义
export function deployProcess(data) {
  return request({
    url: '/oa/workflow/deploy',
    method: 'post',
    data: data
  })
}
