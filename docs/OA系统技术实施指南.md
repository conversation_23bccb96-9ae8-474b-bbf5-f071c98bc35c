# OA系统技术实施指南

## 1. 环境准备

### 1.1 开发环境要求
- **JDK**: 1.8 或以上版本
- **Maven**: 3.6.0 或以上版本
- **Node.js**: 14.0 或以上版本
- **MySQL**: 8.0 或以上版本
- **Redis**: 6.0 或以上版本
- **IDE**: IntelliJ IDEA 或 Eclipse

### 1.2 生产环境要求
- **服务器**: Linux CentOS 7+ 或 Ubuntu 18+
- **内存**: 最低 8GB，推荐 16GB
- **存储**: 最低 100GB，推荐 500GB SSD
- **网络**: 千兆网络
- **域名**: 配置域名和SSL证书

## 2. 项目搭建步骤

### 2.1 克隆项目
```bash
# 克隆若依框架
git clone https://gitee.com/y_project/RuoYi-Vue.git oa-hs
cd oa-hs

# 创建OA模块目录
mkdir ruoyi-oa
```

### 2.2 创建OA模块
```bash
# 在ruoyi-oa目录下创建标准Maven项目结构
mkdir -p ruoyi-oa/src/main/java/com/ruoyi/oa
mkdir -p ruoyi-oa/src/main/resources
mkdir -p ruoyi-oa/src/test/java
```

### 2.3 配置pom.xml
```xml
<!-- 在根目录pom.xml中添加OA模块 -->
<modules>
    <module>ruoyi-admin</module>
    <module>ruoyi-framework</module>
    <module>ruoyi-system</module>
    <module>ruoyi-common</module>
    <module>ruoyi-generator</module>
    <module>ruoyi-oa</module>
</modules>

<!-- 在ruoyi-admin/pom.xml中添加OA依赖 -->
<dependency>
    <groupId>com.ruoyi</groupId>
    <artifactId>ruoyi-oa</artifactId>
</dependency>
```

## 3. 数据库配置

### 3.1 创建数据库
```sql
-- 创建数据库
CREATE DATABASE `ry-oa` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 使用数据库
USE `ry-oa`;
```

### 3.2 导入基础数据
```bash
# 导入若依框架基础数据
mysql -u root -p ry-oa < sql/quartz.sql
mysql -u root -p ry-oa < sql/ry_20240701.sql

# 导入OA系统数据
mysql -u root -p ry-oa < sql/oa_system_init.sql
```

### 3.3 配置数据源
```yaml
# application-druid.yml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      master:
        url: **********************************************************************************************************************************************
        username: root
        password: password
```

## 4. 工作流引擎配置

### 4.1 添加Flowable依赖
```xml
<!-- ruoyi-oa/pom.xml -->
<dependency>
    <groupId>org.flowable</groupId>
    <artifactId>flowable-spring-boot-starter</artifactId>
    <version>6.7.2</version>
</dependency>
```

### 4.2 配置Flowable
```java
@Configuration
public class FlowableConfig {
    
    @Autowired
    private DataSource dataSource;
    
    @Bean
    public ProcessEngine processEngine() {
        ProcessEngineConfiguration cfg = new StandaloneProcessEngineConfiguration()
            .setDataSource(dataSource)
            .setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_TRUE)
            .setAsyncExecutorActivate(false)
            .setActivityFontName("宋体")
            .setLabelFontName("宋体")
            .setAnnotationFontName("宋体");
        
        return cfg.buildProcessEngine();
    }
}
```

### 4.3 部署流程定义
```java
// 自动部署流程定义
@PostConstruct
public void deployProcesses() {
    repositoryService.createDeployment()
        .addClasspathResource("processes/receive_document_parallel.bpmn20.xml")
        .addClasspathResource("processes/receive_document_special.bpmn20.xml")
        .deploy();
}
```

## 5. 文件存储配置

### 5.1 创建存储目录
```bash
# 创建文件存储目录
sudo mkdir -p /opt/oa/upload
sudo mkdir -p /opt/oa/upload/document
sudo mkdir -p /opt/oa/upload/signature
sudo mkdir -p /opt/oa/upload/seal
sudo mkdir -p /opt/oa/upload/meeting
sudo mkdir -p /opt/oa/upload/report

# 设置权限
sudo chown -R tomcat:tomcat /opt/oa
sudo chmod -R 755 /opt/oa
```

### 5.2 配置文件上传
```yaml
# application.yml
ruoyi:
  profile: /opt/oa/upload

spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 500MB
```

## 6. 前端项目配置

### 6.1 安装依赖
```bash
cd ruoyi-ui
npm install

# 安装额外依赖
npm install @fullcalendar/vue @fullcalendar/daygrid
npm install echarts vue-echarts
```

### 6.2 配置路由
```javascript
// router/index.js
{
  path: '/oa',
  component: Layout,
  redirect: '/oa/dashboard',
  name: 'OA',
  meta: { title: 'OA办公', icon: 'office' },
  children: [
    {
      path: 'dashboard',
      name: 'OaDashboard',
      component: () => import('@/views/oa/dashboard/index'),
      meta: { title: '工作台', icon: 'dashboard' }
    }
  ]
}
```

### 6.3 配置API基础路径
```javascript
// utils/request.js
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 10000
})
```

## 7. 开发调试

### 7.1 后端启动
```bash
# 启动后端服务
cd ruoyi-admin
mvn spring-boot:run

# 或者使用IDE直接运行RuoYiApplication.java
```

### 7.2 前端启动
```bash
# 启动前端开发服务器
cd ruoyi-ui
npm run dev
```

### 7.3 访问系统
- 前端地址: http://localhost:80
- 后端地址: http://localhost:8080
- 默认账号: admin/admin123

## 8. 生产部署

### 8.1 后端打包部署
```bash
# 打包应用
mvn clean package -Dmaven.test.skip=true

# 部署到服务器
scp ruoyi-admin/target/ruoyi-admin.jar user@server:/opt/oa/

# 启动应用
nohup java -jar ruoyi-admin.jar --spring.profiles.active=prod > /opt/oa/logs/app.log 2>&1 &
```

### 8.2 前端打包部署
```bash
# 构建生产版本
npm run build:prod

# 部署到Nginx
sudo cp -r dist/* /var/www/html/oa/
```

### 8.3 Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /var/www/html/oa;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    location /prod-api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    location /upload/ {
        alias /opt/oa/upload/;
    }
}
```

## 9. 性能优化

### 9.1 JVM参数优化
```bash
# 启动参数
java -Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 \
     -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/opt/oa/logs/ \
     -jar ruoyi-admin.jar
```

### 9.2 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_oa_document_creator_id ON oa_document(creator_id);
CREATE INDEX idx_oa_document_create_time ON oa_document(create_time);
CREATE INDEX idx_oa_workflow_task_assignee_id ON oa_workflow_task(assignee_id);
CREATE INDEX idx_oa_workflow_task_status ON oa_workflow_task(status);

-- 优化配置
SET GLOBAL innodb_buffer_pool_size = **********;  -- 2GB
SET GLOBAL query_cache_size = 268435456;          -- 256MB
```

### 9.3 Redis配置优化
```conf
# redis.conf
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## 10. 监控配置

### 10.1 应用监控
```yaml
# application.yml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
```

### 10.2 日志配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/opt/oa/logs/oa.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/opt/oa/logs/oa.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <root level="INFO">
        <appender-ref ref="FILE" />
    </root>
</configuration>
```

## 11. 安全配置

### 11.1 HTTPS配置
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
}
```

### 11.2 防火墙配置
```bash
# 开放必要端口
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

## 12. 备份策略

### 12.1 数据库备份
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u root -p ry-oa > /opt/backup/oa_backup_$DATE.sql
find /opt/backup -name "oa_backup_*.sql" -mtime +7 -delete
```

### 12.2 文件备份
```bash
#!/bin/bash
# file_backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /opt/backup/files_backup_$DATE.tar.gz /opt/oa/upload
find /opt/backup -name "files_backup_*.tar.gz" -mtime +7 -delete
```

## 13. 故障排查

### 13.1 常见问题
1. **启动失败**: 检查端口占用、数据库连接
2. **内存溢出**: 调整JVM参数
3. **文件上传失败**: 检查目录权限
4. **流程启动失败**: 检查流程定义和变量

### 13.2 日志分析
```bash
# 查看应用日志
tail -f /opt/oa/logs/oa.log

# 查看错误日志
grep ERROR /opt/oa/logs/oa.log

# 查看访问日志
tail -f /var/log/nginx/access.log
```

## 14. 维护指南

### 14.1 定期维护
- 每日检查系统运行状态
- 每周清理临时文件
- 每月数据库优化
- 每季度安全更新

### 14.2 版本升级
1. 备份数据库和文件
2. 测试环境验证
3. 停机维护窗口
4. 回滚方案准备

这份技术实施指南提供了从开发到生产的完整部署流程，确保OA系统能够稳定可靠地运行。
