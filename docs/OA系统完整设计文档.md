# OA办公自动化系统完整设计文档

## 1. 项目概述

### 1.1 项目背景
基于若依框架开发的OA办公自动化系统，旨在提高办公效率，实现无纸化办公，支持收文发文审批、公文管理、个人办公等核心功能。

### 1.2 项目目标
- 实现收文并行审批流程，提高审批效率
- 建立完整的公文管理体系
- 提供便捷的个人办公功能
- 支持印章电子化管理
- 实现会议室预约和管理
- 建立统一的文件存储和管理系统

### 1.3 技术选型
- **后端框架**: Spring Boot 2.5.15
- **前端框架**: Vue 2.6.12 + Element UI 2.15.14
- **数据库**: MySQL 8.0+
- **工作流引擎**: Flowable 6.7.2
- **权限框架**: Spring Security + JWT
- **文档处理**: iText PDF、Apache POI
- **缓存**: Redis 6.0+

## 2. 系统架构设计

### 2.1 总体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层 (Vue + Element UI)              │
├─────────────────────────────────────────────────────────────┤
│                    API接口层 (Spring MVC)                    │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Service)                       │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (MyBatis)                       │
├─────────────────────────────────────────────────────────────┤
│  工作流引擎    │  文件存储    │  权限管理    │  缓存服务      │
│  (Flowable)   │  (Local)     │  (Security)  │  (Redis)      │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层 (MySQL)                         │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 模块架构
```
OA系统
├── 工作台模块 (Dashboard)
├── 流程管理模块 (Workflow)
├── 公文管理模块 (Document)
├── 个人办公模块 (Personal)
├── 会议管理模块 (Meeting)
├── 行政管理模块 (Administrative)
└── 系统管理模块 (System)
```

### 2.3 技术架构层次
```
┌─────────────────────────────────────────────────────────────┐
│  表现层: Vue.js + Element UI + Axios                        │
├─────────────────────────────────────────────────────────────┤
│  控制层: Spring MVC + RESTful API                           │
├─────────────────────────────────────────────────────────────┤
│  业务层: Spring Service + 事务管理                           │
├─────────────────────────────────────────────────────────────┤
│  持久层: MyBatis + MySQL                                    │
├─────────────────────────────────────────────────────────────┤
│  基础设施: Spring Security + Flowable + Redis + 文件存储     │
└─────────────────────────────────────────────────────────────┘
```

## 3. 数据库设计

### 3.1 数据库ER图概述
```
用户表 (sys_user) ──┐
                   │
部门表 (sys_dept) ──┼── 工作流实例表 (oa_workflow_instance)
                   │
角色表 (sys_role) ──┘
                   │
                   ├── 工作流任务表 (oa_workflow_task)
                   │
                   ├── 公文表 (oa_document)
                   │
                   ├── 个人日程表 (oa_personal_schedule)
                   │
                   ├── 工作报告表 (oa_work_report)
                   │
                   ├── 会议表 (oa_meeting)
                   │
                   ├── 印章申请表 (oa_seal_application)
                   │
                   └── 文件附件表 (oa_file_attachment)
```

### 3.2 核心表结构

#### 3.2.1 工作流相关表
- `oa_workflow_definition`: 工作流定义表
- `oa_workflow_instance`: 工作流实例表
- `oa_workflow_task`: 工作流任务表

#### 3.2.2 公文管理相关表
- `oa_document`: 公文基础表
- `oa_document_flow`: 公文流程关联表
- `oa_file_attachment`: 文件附件表

#### 3.2.3 个人办公相关表
- `oa_personal_schedule`: 个人日程表
- `oa_work_report`: 工作报告表
- `oa_personal_contact`: 个人通讯录表

#### 3.2.4 会议管理相关表
- `oa_meeting_room`: 会议室表
- `oa_meeting`: 会议表

#### 3.2.5 印章管理相关表
- `oa_seal_certificate`: 印章证照表
- `oa_seal_application`: 印章使用申请表

## 4. 业务流程设计

### 4.1 收文并行审批流程
```
开始
  ↓
办公室主任登记
  ↓
书记审批
  ↓
并行网关开始
  ├── 分管领导1审阅 → 科室负责人1确认 → 经办人1确认
  ├── 分管领导2审阅 → 科室负责人2确认 → 经办人2确认
  ├── 分管领导3审阅 → 科室负责人3确认 → 经办人3确认
  ├── 分管领导4审阅 → 科室负责人4确认 → 经办人4确认
  └── 分管领导5审阅 → 科室负责人5确认 → 经办人5确认
  ↓
并行网关结束
  ↓
办公室汇总督办
  ↓
结束
```

### 4.2 收文特办流程
```
开始
  ↓
书记收文
  ↓
分发给任意人员
  ↓
结束
```

### 4.3 印章使用申请流程
```
开始
  ↓
申请人提交申请
  ↓
部门负责人审批
  ↓
印章保管人确认
  ↓
印章使用
  ↓
印章归还
  ↓
结束
```

## 5. 功能模块设计

### 5.1 工作台模块
- **功能概述**: 系统主界面，展示各模块核心信息
- **主要功能**:
  - 待办任务统计和展示
  - 最新公文展示
  - 今日日程展示
  - 工作统计图表
  - 快速操作入口

### 5.2 流程管理模块
- **功能概述**: 工作流程的定义、监控和管理
- **主要功能**:
  - 流程定义管理
  - 流程实例监控
  - 任务分配与处理
  - 流程统计分析
  - 异常监控

### 5.3 公文管理模块
- **功能概述**: 收文发文的全生命周期管理
- **主要功能**:
  - 收文管理（支持并行审批）
  - 发文管理
  - 公文查询与统计
  - 文档模板管理
  - PDF文档生成
  - 公文分发
  - 督察督办

### 5.4 个人办公模块
- **功能概述**: 个人日常办公功能
- **主要功能**:
  - 个人信息管理
  - 修改密码
  - 签名图片上传
  - 日程管理（支持日历视图）
  - 工作报告（日报、周报、月报、年报）
  - 个人通讯录
  - 内部通讯录

### 5.5 会议管理模块
- **功能概述**: 会议室和会议的全面管理
- **主要功能**:
  - 会议室信息维护
  - 会议预约
  - 会议冲突检查
  - 会议提醒
  - 会议纪要
  - 参会人员管理

### 5.6 行政管理模块
- **功能概述**: 印章证照等行政事务管理
- **主要功能**:
  - 印章证照管理
  - 印章使用申请
  - 申请审批流程
  - 使用记录查询

### 5.7 系统管理模块
- **功能概述**: 系统基础配置和管理
- **主要功能**:
  - 用户管理
  - 角色权限管理
  - 菜单管理
  - 部门管理
  - 字典管理
  - 系统监控

## 6. 接口设计

### 6.1 RESTful API设计规范
```
GET    /api/oa/{module}/list        # 查询列表
GET    /api/oa/{module}/{id}        # 查询详情
POST   /api/oa/{module}             # 新增
PUT    /api/oa/{module}             # 修改
DELETE /api/oa/{module}/{id}        # 删除
```

### 6.2 核心API接口

#### 6.2.1 工作流接口
```
POST   /oa/workflow/process/start           # 启动流程
POST   /oa/workflow/task/complete/{taskId}  # 完成任务
GET    /oa/workflow/task/todo               # 待办任务
GET    /oa/workflow/task/done               # 已办任务
```

#### 6.2.2 公文管理接口
```
GET    /oa/document/receive/list            # 收文列表
POST   /oa/document/submit/{docId}          # 提交审批
POST   /oa/document/approve/{docId}         # 审批公文
GET    /oa/document/export/pdf/{docId}      # 导出PDF
```

#### 6.2.3 个人办公接口
```
GET    /oa/personal/schedule/list           # 日程列表
GET    /oa/personal/schedule/today          # 今日日程
POST   /oa/personal/signature/upload        # 上传签名
GET    /oa/personal/report/subordinate      # 下属报告
```

## 7. 安全设计

### 7.1 认证授权
- **认证方式**: JWT Token认证
- **权限控制**: RBAC基于角色的访问控制
- **接口权限**: 基于注解的方法级权限控制
- **数据权限**: 基于部门的数据权限控制

### 7.2 数据安全
- **数据加密**: 敏感数据加密存储
- **SQL注入防护**: MyBatis参数化查询
- **XSS防护**: 前端输入过滤和后端验证
- **文件上传安全**: 文件类型和大小限制

### 7.3 系统安全
- **密码策略**: 强密码策略和定期更换
- **登录安全**: 登录失败锁定和验证码
- **操作日志**: 完整的操作审计日志
- **备份策略**: 定期数据备份和恢复

## 8. 性能设计

### 8.1 缓存策略
- **Redis缓存**: 用户信息、字典数据、菜单权限
- **本地缓存**: 静态配置数据
- **数据库缓存**: 查询结果缓存

### 8.2 数据库优化
- **索引优化**: 关键字段建立索引
- **查询优化**: 避免N+1查询问题
- **分页查询**: 大数据量分页处理
- **连接池**: 数据库连接池优化

### 8.3 文件存储优化
- **本地存储**: 高性能本地文件系统
- **文件分类**: 按业务类型分目录存储
- **文件压缩**: 大文件自动压缩
- **清理策略**: 定期清理临时文件

## 9. 部署架构

### 9.1 单机部署架构
```
┌─────────────────────────────────────────┐
│  Nginx (反向代理 + 静态文件服务)          │
├─────────────────────────────────────────┤
│  Spring Boot Application                │
├─────────────────────────────────────────┤
│  MySQL Database                        │
├─────────────────────────────────────────┤
│  Redis Cache                           │
├─────────────────────────────────────────┤
│  File Storage (Local)                  │
└─────────────────────────────────────────┘
```

### 9.2 集群部署架构
```
┌─────────────────────────────────────────┐
│  Load Balancer (Nginx)                 │
├─────────────────────────────────────────┤
│  App Server 1  │  App Server 2         │
├─────────────────────────────────────────┤
│  MySQL Master  │  MySQL Slave          │
├─────────────────────────────────────────┤
│  Redis Cluster                         │
├─────────────────────────────────────────┤
│  Shared File Storage (NFS)             │
└─────────────────────────────────────────┘
```

## 10. 监控运维

### 10.1 系统监控
- **应用监控**: Spring Boot Actuator
- **性能监控**: JVM性能指标
- **数据库监控**: 连接池、慢查询
- **缓存监控**: Redis性能指标

### 10.2 日志管理
- **应用日志**: Logback日志框架
- **访问日志**: Nginx访问日志
- **错误日志**: 异常堆栈跟踪
- **审计日志**: 用户操作记录

### 10.3 备份策略
- **数据库备份**: 每日全量备份
- **文件备份**: 重要文件定期备份
- **配置备份**: 系统配置文件备份
- **恢复测试**: 定期恢复测试

## 11. 项目实施计划

### 11.1 开发阶段
- **第一阶段**: 基础框架搭建和核心模块开发（已完成）
- **第二阶段**: 工作流引擎集成和流程开发
- **第三阶段**: 前端页面开发和接口联调
- **第四阶段**: 系统测试和性能优化

### 11.2 测试阶段
- **单元测试**: 核心业务逻辑测试
- **集成测试**: 模块间接口测试
- **系统测试**: 完整业务流程测试
- **性能测试**: 并发和压力测试

### 11.3 上线阶段
- **环境准备**: 生产环境搭建
- **数据迁移**: 历史数据导入
- **用户培训**: 系统使用培训
- **试运行**: 小范围试运行

## 12. 风险评估

### 12.1 技术风险
- **工作流引擎**: Flowable学习成本和复杂度
- **并发处理**: 高并发场景下的性能问题
- **数据一致性**: 分布式事务处理

### 12.2 业务风险
- **需求变更**: 业务需求频繁变更
- **用户接受度**: 用户对新系统的接受程度
- **数据安全**: 敏感数据泄露风险

### 12.3 运维风险
- **系统稳定性**: 7x24小时稳定运行
- **数据备份**: 数据丢失风险
- **安全防护**: 网络安全攻击

## 13. 项目文件结构

### 13.1 后端项目结构
```
oa-hs/
├── ruoyi-admin/                        # 主启动模块
├── ruoyi-common/                       # 通用模块
├── ruoyi-framework/                    # 框架模块
├── ruoyi-generator/                    # 代码生成模块
├── ruoyi-system/                       # 系统模块
├── ruoyi-oa/                          # OA核心模块
│   ├── src/main/java/com/ruoyi/oa/
│   │   ├── config/                     # 配置类
│   │   │   ├── FlowableConfig.java     # 工作流配置
│   │   │   └── FileStorageConfig.java  # 文件存储配置
│   │   ├── common/                     # 通用组件
│   │   │   └── service/
│   │   │       └── FileService.java    # 文件服务
│   │   ├── workflow/                   # 工作流管理
│   │   │   ├── controller/
│   │   │   ├── service/
│   │   │   ├── mapper/
│   │   │   └── domain/
│   │   ├── document/                   # 公文管理
│   │   │   ├── controller/
│   │   │   ├── service/
│   │   │   ├── mapper/
│   │   │   └── domain/
│   │   ├── personal/                   # 个人办公
│   │   │   ├── controller/
│   │   │   ├── service/
│   │   │   ├── mapper/
│   │   │   └── domain/
│   │   ├── meeting/                    # 会议管理
│   │   │   ├── controller/
│   │   │   ├── service/
│   │   │   ├── mapper/
│   │   │   └── domain/
│   │   └── seal/                       # 印章管理
│   │       ├── controller/
│   │       ├── service/
│   │       ├── mapper/
│   │       └── domain/
│   ├── src/main/resources/
│   │   ├── processes/                  # 流程定义文件
│   │   │   ├── receive_document_parallel.bpmn20.xml
│   │   │   └── receive_document_special.bpmn20.xml
│   │   ├── mapper/                     # MyBatis映射文件
│   │   └── application-oa.yml         # OA配置文件
│   └── pom.xml
├── ruoyi-ui/                          # 前端项目
└── sql/                               # 数据库脚本
    └── oa_system_init.sql
```

### 13.2 前端项目结构
```
ruoyi-ui/
├── src/
│   ├── api/oa/                        # API接口
│   │   ├── workflow.js                # 工作流接口
│   │   ├── document.js                # 公文接口
│   │   ├── schedule.js                # 日程接口
│   │   ├── meeting.js                 # 会议接口
│   │   └── seal.js                    # 印章接口
│   ├── views/oa/                      # 页面组件
│   │   ├── dashboard/                 # 工作台
│   │   │   └── index.vue
│   │   ├── workflow/                  # 流程管理
│   │   │   ├── definition/
│   │   │   ├── monitor/
│   │   │   └── task/
│   │   ├── document/                  # 公文管理
│   │   │   ├── receive/
│   │   │   ├── send/
│   │   │   └── view/
│   │   ├── personal/                  # 个人办公
│   │   │   ├── profile/
│   │   │   ├── schedule/
│   │   │   ├── report/
│   │   │   └── contact/
│   │   ├── meeting/                   # 会议管理
│   │   │   ├── room/
│   │   │   ├── booking/
│   │   │   └── my-meeting/
│   │   └── seal/                      # 印章管理
│   │       ├── certificate/
│   │       └── application/
│   ├── components/oa/                 # OA专用组件
│   └── assets/oa/                     # OA资源文件
```

## 14. 核心代码示例

### 14.1 工作流服务核心代码
```java
@Service
public class OaWorkflowServiceImpl implements IOaWorkflowService {

    @Autowired
    private ProcessEngine processEngine;

    @Override
    @Transactional
    public String startProcess(String workflowKey, String businessKey,
                              Map<String, Object> variables) {
        // 启动流程实例
        ProcessInstance processInstance = runtimeService
            .startProcessInstanceByKey(workflowKey, businessKey, variables);

        // 保存流程实例信息到数据库
        OaWorkflowInstance instance = new OaWorkflowInstance();
        instance.setBusinessKey(businessKey);
        instance.setStartUserId(SecurityUtils.getUserId());
        instance.setStatus("1"); // 进行中
        instance.setStartTime(new Date());

        workflowInstanceMapper.insertOaWorkflowInstance(instance);

        return processInstance.getId();
    }
}
```

### 14.2 文件服务核心代码
```java
@Service
public class FileService {

    public String uploadFile(MultipartFile file, String subPath) throws IOException {
        // 验证文件类型和大小
        assertAllowed(file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);

        // 生成新文件名
        String newFileName = generateFileName(getExtension(file.getOriginalFilename()));

        // 构建文件路径
        String datePath = DateUtils.datePath();
        String uploadPath = FileStorageConfig.getOaUploadPath() + "/" + subPath + "/" + datePath;

        // 保存文件
        String filePath = uploadPath + "/" + newFileName;
        File dest = new File(filePath);
        file.transferTo(dest);

        return "/" + subPath + "/" + datePath + "/" + newFileName;
    }
}
```

### 14.3 前端日程管理核心代码
```vue
<template>
  <div class="app-container">
    <el-calendar v-model="currentDate">
      <template slot="dateCell" slot-scope="{date, data}">
        <div class="calendar-day">
          <p>{{ data.day.split('-').slice(2).join('-') }}</p>
          <div class="schedule-items">
            <div
              v-for="schedule in getSchedulesForDate(date)"
              :key="schedule.scheduleId"
              class="schedule-item"
              @click="viewSchedule(schedule)"
            >
              {{ schedule.title }}
            </div>
          </div>
        </div>
      </template>
    </el-calendar>
  </div>
</template>
```

## 15. 配置文件示例

### 15.1 应用配置文件
```yaml
# OA系统配置
oa:
  file:
    upload-path: /opt/oa/upload
    max-file-size: 100
  workflow:
    enabled: true
    activity-font-name: 宋体
  document:
    number-prefix: 红管
    number-format: "{prefix}〔{year}〕{serial}号"

# Flowable配置
flowable:
  database-schema-update: true
  async-executor-activate: false
  activity-font-name: 宋体
```

### 15.2 数据库配置
```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************
    username: root
    password: password
```

## 16. 测试策略

### 16.1 单元测试
- **测试范围**: 核心业务逻辑、工具类方法
- **测试框架**: JUnit 5 + Mockito
- **覆盖率要求**: 核心业务代码覆盖率 > 80%

### 16.2 集成测试
- **测试范围**: 模块间接口、数据库操作
- **测试工具**: Spring Boot Test
- **测试数据**: 使用H2内存数据库

### 16.3 系统测试
- **功能测试**: 完整业务流程测试
- **性能测试**: 并发用户、响应时间
- **安全测试**: 权限控制、数据安全

## 17. 运维指南

### 17.1 部署步骤
1. **环境准备**: JDK 1.8+、MySQL 8.0+、Redis 6.0+
2. **数据库初始化**: 执行SQL脚本
3. **应用部署**: 打包部署Spring Boot应用
4. **前端部署**: 构建并部署Vue应用
5. **配置检查**: 验证各项配置

### 17.2 监控指标
- **应用指标**: CPU、内存、线程数
- **业务指标**: 用户数、文档数、流程数
- **性能指标**: 响应时间、吞吐量
- **错误指标**: 错误率、异常数量

### 17.3 故障处理
- **应用重启**: 自动重启机制
- **数据恢复**: 定期备份恢复
- **日志分析**: 错误日志分析
- **性能调优**: JVM参数调优

## 18. 总结

本OA系统采用现代化的技术架构，实现了完整的办公自动化功能，特别是创新性地实现了收文并行审批流程，大大提高了审批效率。系统具有以下特点：

### 18.1 技术特点
- **微服务架构**: 模块化设计，便于扩展
- **工作流引擎**: 强大的流程管理能力
- **前后端分离**: 良好的用户体验
- **安全可靠**: 完善的权限控制机制

### 18.2 业务特点
- **并行审批**: 提高审批效率
- **全程电子化**: 减少纸质文档
- **移动办公**: 支持移动端访问
- **数据统计**: 完善的统计分析

### 18.3 管理特点
- **标准化流程**: 规范业务流程
- **权限管控**: 细粒度权限控制
- **审计追踪**: 完整的操作记录
- **系统监控**: 实时系统状态监控

该系统能够满足企业日常办公需求，为数字化办公提供了强有力的支撑，具有良好的扩展性和可维护性。
